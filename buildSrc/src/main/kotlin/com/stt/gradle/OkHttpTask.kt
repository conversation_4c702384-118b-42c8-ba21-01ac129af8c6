package com.stt.gradle

import com.squareup.moshi.Moshi
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import org.gradle.api.DefaultTask
import org.gradle.api.logging.Logger
import org.gradle.api.tasks.Internal

abstract class OkHttpTask : DefaultTask() {
    @Internal
    protected var okHttpClient: OkHttpClient? = null
        get() {
            if (field == null) {
                val logger = HttpLoggingInterceptor(HttpGradleLogger(logger)).apply { level = HttpLoggingInterceptor.Level.BODY }
                logger.level = HttpLoggingInterceptor.Level.BODY
                field = OkHttpClient.Builder().addInterceptor(logger).build()
            }
            return field
        }
        private set

    private val moshi: Moshi = Moshi.Builder().add(KotlinJsonAdapterFactory()).build()

    fun <T> toJson(clazz: Class<T>, value: T): String = moshi.adapter(clazz).toJson(value)

    companion object {
        val JSON = "application/json".toMediaTypeOrNull()
    }
}

private class HttpGradleLogger(
    private val logger: Logger
) : HttpLoggingInterceptor.Logger {
    override fun log(message: String) {
        logger.debug(message)
    }
}
