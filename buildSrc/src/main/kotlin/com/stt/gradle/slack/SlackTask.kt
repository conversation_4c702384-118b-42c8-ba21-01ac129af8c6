package com.stt.gradle.slack

import com.stt.gradle.OkHttpTask
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.gradle.api.tasks.Input
import org.gradle.api.tasks.TaskAction

open class SlackTask : OkHttpTask() {
    @Input
    lateinit var messageText: String
    @Input
    lateinit var webhookUrl: String

    @TaskAction
    fun sendMessage() {
        val json = toJson(Message::class.java, Message(messageText.replace("\"", "\\\"")))
        val body = json.toRequestBody(JSON)
        val request = Request.Builder()
                .url(webhookUrl)
                .post(body)
                .build()
        okHttpClient?.newCall(request)?.execute()
    }
}
