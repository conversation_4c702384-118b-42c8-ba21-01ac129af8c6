package com.stt.gradle.github

import com.stt.gradle.OkHttpTask
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.gradle.api.tasks.Input
import org.gradle.api.tasks.TaskAction

open class PullRequestTask : OkHttpTask() {
    @Input
    lateinit var message: String
    @Input
    lateinit var title: String
    @Input
    lateinit var head: String
    @Input
    lateinit var base: String

    @TaskAction
    fun createPullRequest() {
        val prInfo = PullRequest(title, message, head, base)
        val ext = project.extensions.getByType(GithubExtensions::class.java)
        val json = toJson(PullRequest::class.java, prInfo)
        val body = json.toRequestBody(JSON)
        val request = Request.Builder()
                .addHeader("Authorization", "token ${ext.token}")
                .url("https://api.github.com/repos/${ext.user}/${ext.repo}/pulls")
                .post(body)
                .build()
        okHttpClient?.newCall(request)?.execute()
    }
}
