package com.stt.gradle.translation

import javax.xml.stream.XMLStreamConstants.END_ELEMENT
import javax.xml.stream.XMLStreamConstants.START_ELEMENT
import javax.xml.stream.XMLStreamReader
import javax.xml.stream.XMLStreamWriter
import kotlin.collections.iterator

internal fun XMLStreamReader.attributes() = LinkedHashMap<String, String>().apply {
    for (i in 0 until attributeCount) {
        if (!getAttributePrefix(i).isNullOrBlank()) {
            // Attribute has prefix such as 'tools:ignore'
            put("${getAttributePrefix(i)}:${getAttributeLocalName(i)}", getAttributeValue(i))
        } else {
            // Attribute has no prefix such as 'name' or 'translatable'
            put(getAttributeLocalName(i), getAttributeValue(i))
        }
    }
}

internal fun XMLStreamReader.isResourcesElement() =
    eventType == START_ELEMENT && localName == "resources"

internal fun XMLStreamReader.isStringResourceElement() =
    eventType == START_ELEMENT && (localName == "string" || localName == "plurals")

internal fun XMLStreamReader.flattenedNamespaceDefinition(): String =
    (0 until namespaceCount).joinToString(" ") {
        "xmlns:${getNamespacePrefix(it)}=\"${getNamespaceURI(it)}\""
    }

internal fun XMLStreamReader.skipUntilEndOfCurrentTag() {
    var nestedTagCount = 0
    while (hasNext()) {
        next()
        when (eventType) {
            START_ELEMENT -> nestedTagCount++
            END_ELEMENT -> {
                nestedTagCount--
                if (nestedTagCount < 0) {
                    next()
                    break
                }
            }

            else -> {
                // pass
            }
        }
    }
}

internal fun XMLStreamWriter.writeAttributes(attributes: LinkedHashMap<String, String>) {
    for ((name, value) in attributes) {
        if (name.contains(":")) {
            // Attribute name has prefix
            val (prefix, localName) = name.split(":")
            writeAttribute(prefix, "", localName, value)
        } else {
            writeAttribute(name, value)
        }
    }
}
