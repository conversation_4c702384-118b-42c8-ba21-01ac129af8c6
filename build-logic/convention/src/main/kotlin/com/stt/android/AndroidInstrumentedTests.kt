package com.stt.android

import com.android.build.api.variant.AndroidComponentsExtension
import com.android.build.api.variant.HasAndroidTestBuilder
import com.android.build.api.variant.VariantBuilder
import org.gradle.api.Project
import java.io.File

/**
 * Disable unnecessary Android instrumented tests for the [project] if there is no `androidTest` folder.
 * Otherwise, these projects would be compiled, packaged, installed and ran only to end-up with the following message:
 *
 * > Starting 0 tests on AVD
 *
 * Note: this could be improved by checking other potential sourceSets based on buildTypes and flavors.
 */
internal fun <T> AndroidComponentsExtension<*, T, *>.disableUnnecessaryAndroidTests(
    project: Project,
) where T : VariantBuilder, T : HasAndroidTestBuilder = beforeVariants {
    it.enableAndroidTest = it.enableAndroidTest
        && File(project.projectDir, "src").listFiles { file: File ->
        file.name.startsWith("androidTest")
    }?.isNotEmpty() == true
}
