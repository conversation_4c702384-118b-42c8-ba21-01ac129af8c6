package com.stt.android

import com.android.build.api.dsl.ApplicationExtension
import com.android.build.api.dsl.ApplicationProductFlavor
import com.android.build.api.dsl.CommonExtension
import com.android.build.api.dsl.ProductFlavor
import com.android.build.api.variant.AndroidComponentsExtension
import org.gradle.api.Project
import org.gradle.kotlin.dsl.provideDelegate

@Suppress("EnumEntryName")
enum class FlavorDimension {
    brand, store
}

enum class AbiFilter(val abiName: String, val is64Bit: Boolean) {
    ARM64_V8A("arm64-v8a", true),
    ARMEABI_V7A("armeabi-v7a", false),
}

data class BuildConfigField(
    val type: String,
    val name: String,
    val value: String,
)

@Suppress("EnumEntryName")
enum class STTFlavor(
    val dimension: FlavorDimension,
    val applicationIdSuffix: String? = null,
    val buildConfigFields: List<BuildConfigField> = listOf(),
    val abiFilters: Set<AbiFilter>? = null
) {
    sportstracker(
        dimension = FlavorDimension.brand,
        buildConfigFields = listOf(
            BuildConfigField("String", "PUSH_APP", "\"sportstracker\""),
            BuildConfigField("String", "DIRECTORY_APPLICATION", "\"SportsTracker\""),
        ),
        abiFilters = setOf(AbiFilter.ARM64_V8A, AbiFilter.ARMEABI_V7A)
    ),
    suunto(
        dimension = FlavorDimension.brand,
        applicationIdSuffix = ".suunto",
        buildConfigFields = listOf(
            BuildConfigField("String", "PUSH_APP", "\"suuntoapp\""),
            BuildConfigField("String", "DIRECTORY_APPLICATION", "\"Suunto\""),
        ),
        abiFilters = setOf(AbiFilter.ARM64_V8A, AbiFilter.ARMEABI_V7A)
    ),
    playstore(
        dimension = FlavorDimension.store,
        buildConfigFields = listOf(
            BuildConfigField("Boolean", "BACKEND_POINTING_TO_CHINA", "false"),
            BuildConfigField("String", "MC_REGION", "\"europe\""),
        )
    ),
    china(
        dimension = FlavorDimension.store,
        applicationIdSuffix = ".china",
        buildConfigFields = listOf(
            BuildConfigField( "Boolean", "BACKEND_POINTING_TO_CHINA", "true"),
            BuildConfigField( "String", "MC_REGION", "\"china\""),
        )
    );
}

fun Project.configureFlavors(
    commonExtension: CommonExtension<*, *, *, *, *, *>,
    flavorConfigurationBlock: ProductFlavor.(flavor: STTFlavor) -> Unit = {}
) {
    commonExtension.apply {
        flavorDimensions.addAll(FlavorDimension.entries.map { it.name })
        productFlavors {
            STTFlavor.entries.forEach {
                create(it.name) {
                    dimension = it.dimension.name
                    flavorConfigurationBlock(this, it)
                    for (field in it.buildConfigFields) {
                        buildConfigField(field.type, field.name, field.value)
                    }
                    // true if we are configuring flavors in app module
                    if (this@apply is ApplicationExtension && this is ApplicationProductFlavor) {
                        if (it.applicationIdSuffix != null) {
                            applicationIdSuffix = it.applicationIdSuffix
                        }
                        // build flag in gradle.properties to exclude 32bit binaries from apk
                        val exclude32bit: String? by project
                        val only64bit = exclude32bit.toBoolean()
                        it.abiFilters?.let { abis ->
                            ndk {
                                abiFilters.addAll(
                                    abis.filter { filter -> filter.is64Bit || !only64bit }
                                        .map { filter -> filter.abiName }
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * SportsTracker + china variant should be disabled
 */
fun AndroidComponentsExtension<*, *, *>.disableSelectedVariants() =
    beforeVariants(
        selector = selector()
            .withFlavor(STTFlavor.sportstracker.dimension.name, STTFlavor.sportstracker.name)
            .withFlavor(STTFlavor.china.dimension.name, STTFlavor.china.name)
    ) { it.enable = false }
