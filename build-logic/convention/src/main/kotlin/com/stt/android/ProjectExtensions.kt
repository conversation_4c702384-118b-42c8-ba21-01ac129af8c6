package com.stt.android

import org.codehaus.groovy.runtime.ProcessGroovyMethods
import org.gradle.api.Project
import org.gradle.api.artifacts.VersionCatalog
import org.gradle.api.artifacts.VersionCatalogsExtension
import org.gradle.api.plugins.ExtensionContainer
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.getByType
import org.gradle.kotlin.dsl.typeOf
import org.jetbrains.kotlin.gradle.plugin.extraProperties

val Project.libs
    get(): VersionCatalog = extensions.getByType<VersionCatalogsExtension>().named("libs")

/**
 * Shortcut for library dependency declarations
 */
operator fun VersionCatalog.invoke(library: String) = findLibrary(library).get()

val Project.workingBranch: String
    get() {
        // Global variable to cache git result
        if (extraProperties.has("workingBranch")) {
            return extraProperties.get("workingBranch").toString()
        }
        val workingBranch = providers.exec {
            commandLine("git","--git-dir=${rootDir}/.git", "--work-tree=${rootDir}/", "rev-parse", "--abbrev-ref", "HEAD")
        }.standardOutput.asText.get().trim()
        extraProperties.set("workingBranch", workingBranch)
        return workingBranch
    }

val isRunningOnCi: Boolean
    get() = System.getenv("TEAMCITY_VERSION") != null

/**
 * Looks for the extension of the specified type and if found, configures it with the supplied action.
 *
 * @param T the extension type
 * @param action the configuration action
 *
 * @see [ExtensionContainer.configure]
 * @since 5.0
 */
inline fun <reified T : Any> ExtensionContainer.configureIfPresent(noinline action: T.() -> Unit) {
    val type = typeOf<T>()
    findByType(type)?.let {
        configure(type, action)
    }
}

fun String.execute(): Process = ProcessGroovyMethods.execute(this)

val Process.text: String get() = ProcessGroovyMethods.getText(this)
