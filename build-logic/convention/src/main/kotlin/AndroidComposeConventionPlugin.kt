import com.android.build.api.dsl.ApplicationExtension
import com.android.build.api.dsl.CommonExtension
import com.android.build.gradle.LibraryExtension
import com.stt.android.invoke
import com.stt.android.libs
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies
import org.gradle.kotlin.dsl.findByType

class AndroidComposeConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply("org.jetbrains.kotlin.plugin.compose")
            }

            val androidCommonExtension: CommonExtension<*, *, *, *, *, *> =
                extensions.findByType<LibraryExtension>()
                    ?: extensions.findByType<ApplicationExtension>()
                    ?: throw RuntimeException("Compose convention plugin needs Android Library or Application plugin")
            configureAndroidCompose(androidCommonExtension)
        }
    }

    private fun Project.configureAndroidCompose(commonExtension: CommonExtension<*, *, *, *, *, *>) {
        commonExtension.apply {
            buildFeatures {
                compose = true
            }
        }

        dependencies {
            if (name != "composeui") "implementation"(project(":composeui"))
            "implementation"(platform(libs("androidx.compose.bom")))
            "debugImplementation"(libs("androidx.compose.ui.test.manifest"))

            "implementation"(libs("accompanist.drawablepainter"))
            "implementation"(libs("accompanist.systemuicontroller"))
            "implementation"(libs("androidx.activity.compose"))
            "implementation"(libs("androidx.compose.foundation"))
            "implementation"(libs("androidx.compose.material"))
            "implementation"(libs("androidx.compose.material3"))
            "implementation"(libs("androidx.compose.runtime"))
            "implementation"(libs("androidx.compose.runtime.livedata"))
            "implementation"(libs("androidx.compose.ui"))
            "implementation"(libs("androidx.compose.ui.viewbinding"))
            "implementation"(libs("androidx.constraintlayout.compose"))
            "implementation"(libs("androidx.fragment.compose"))
            "implementation"(libs("androidx.lifecycle.viewmodel.compose"))
            "implementation"(libs("androidx.lifecycle.lifecycle.runtime.compose"))
            "implementation"(libs("androidx.navigation.compose"))
            "implementation"(libs("coil.compose"))
            "implementation"(libs("kotlinx.collections.immutable"))
            "implementation"(libs("revealswipe"))

            // For Compose tooling support in Android Studio
            "implementation"(libs("androidx.compose.ui.tooling.preview"))
            "debugImplementation"(libs("androidx.compose.ui.tooling"))

            // Workaround for a Compose preview issue in AS:
            // https://issuetracker.google.com/issues/227767363
            "debugImplementation"(libs("androidx.customview"))
            "debugImplementation"(libs("androidx.customview.poolingcontainer"))

            "androidTestImplementation"(libs("androidx.compose.ui.test.junit4"))
        }
    }
}
