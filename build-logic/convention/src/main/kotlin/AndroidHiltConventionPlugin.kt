import com.google.devtools.ksp.gradle.KspExtension
import com.stt.android.invoke
import com.stt.android.libs
import dagger.hilt.android.plugin.HiltExtension
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.dependencies

class AndroidHiltConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply("dagger.hilt.android.plugin")
                apply("com.google.devtools.ksp")
            }

            extensions.configure<KspExtension> {
                arg("dagger.hilt.disableModulesHaveInstallInCheck", "true")
            }

            extensions.configure<HiltExtension> {
                enableAggregatingTask = true
            }

            dependencies {
                "implementation"(libs("dagger"))
                "implementation"(libs("dagger.hilt"))
                "implementation"(libs("androidx.dagger.hilt.navigation.compose"))
                // To override transitive dependency from daggerHiltNavigationCompose
                "implementation"(libs("androidx.lifecycle.viewmodel.ktx"))
                "ksp"(libs("dagger.compiler"))
                "ksp"(libs("dagger.hilt.compiler"))
                "kspAndroidTest"(libs("dagger.hilt.compiler"))
                "kspTest"(libs("dagger.hilt.compiler"))
            }
        }
    }

}
