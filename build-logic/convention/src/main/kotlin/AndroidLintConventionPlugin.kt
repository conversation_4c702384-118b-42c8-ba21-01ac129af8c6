import com.android.build.api.dsl.ApplicationExtension
import com.android.build.api.dsl.LibraryExtension
import com.android.build.api.dsl.Lint
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import java.io.File

class AndroidLintConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            val lintConfigFile = File(rootDir, "lint-config.xml")
            when {
                pluginManager.hasPlugin("com.android.application") ->
                    configure<ApplicationExtension> {
                        lint { configure(lintConfigFile) }
                    }

                pluginManager.hasPlugin("com.android.library") ->
                    configure<LibraryExtension> {
                        lint { configure(lintConfigFile) }
                    }

                else -> {
                    pluginManager.apply("com.android.lint")
                    configure<Lint> {
                        configure(lintConfigFile)
                    }
                }
            }
        }
    }
}

private fun Lint.configure(lintConfigFile: File) {
    abortOnError = true
    checkDependencies = true
    checkReleaseBuilds = false
    disable.add("ObsoleteLintCustomCheck")
    disable.add("ImpliedQuantity")
    disable.add("UnusedQuantity")
    disable.add("UsingMaterialAndMaterial3Libraries")
    disable.add("StateFlowValueCalledInComposition")
    disable.add("FlowOperatorInvokedInComposition")
    disable.add("CoroutineCreationDuringComposition")
    ignoreTestSources = true
    lintConfig = lintConfigFile
    warningsAsErrors = true
}
