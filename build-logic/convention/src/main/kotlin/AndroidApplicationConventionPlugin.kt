import com.android.build.api.dsl.ApplicationExtension
import com.android.build.api.variant.ApplicationAndroidComponentsExtension
import com.stt.android.Versions
import com.stt.android.configureCommonAndroidDependencies
import com.stt.android.configureCommonKapt
import com.stt.android.configureKotlinAndroid
import com.stt.android.disableSelectedVariants
import com.stt.android.disableUnnecessaryAndroidTests
import com.stt.android.workingBranch
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.get
import java.io.File

/**
 * Generic plugin configuration for Android application modules, like app, SCSampleApp and
 * STTAndroidWear.
 * This plugin does not contain flavor-specific configuration (see [AndroidApplicationFlavorsConventionPlugin]).
 */
class AndroidApplicationConventionPlugin : Plugin<Project> {

    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply("com.android.application")
                apply("org.jetbrains.kotlin.android")
                apply("stt.android.plugin.lint")
                apply("org.jetbrains.kotlin.kapt")
                apply("com.google.devtools.ksp")
                apply("org.jetbrains.kotlin.plugin.allopen")
                apply("org.jmailen.kotlinter")
                apply("kotlin-parcelize")
            }

            extensions.configure<ApplicationExtension> {
                configureKotlinAndroid(this)
                defaultConfig {
                    targetSdk = Versions.targetSdk
                    multiDexEnabled = true
                }

                signingConfigs {
                    getByName("debug") {
                        storeFile = File(rootDir, "keystore/debug.keystore")
                        storePassword = "android"
                        keyAlias = "androiddebugkey"
                        keyPassword = "android"
                    }
                }

                buildTypes {
                    debug {
                        // remove .debug suffix if you are testing huawei tencent SDK integration in china
                        applicationIdSuffix = ".debug"
                        versionNameSuffix = workingBranch
                        isMinifyEnabled = false
                        proguardFile(getDefaultProguardFile("proguard-android.txt"))
                        // common debug signingConfig for all our apps
                        signingConfig = signingConfigs["debug"]
                    }
                    release {
                        isMinifyEnabled = true
                        isShrinkResources = true
                        proguardFile(getDefaultProguardFile("proguard-android-optimize.txt"))
                    }
                }
            }

            extensions.configure<ApplicationAndroidComponentsExtension> {
                disableUnnecessaryAndroidTests(target)
                disableSelectedVariants()
            }

            configureCommonKapt()
            configureCommonAndroidDependencies()
        }
    }

}
