import com.android.build.api.dsl.ApplicationExtension
import com.android.build.api.dsl.CommonExtension
import com.android.build.gradle.LibraryExtension
import com.stt.android.configureCommonKapt
import com.stt.android.invoke
import com.stt.android.libs
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies
import org.gradle.kotlin.dsl.findByType

class EpoxyConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            pluginManager.apply("org.jetbrains.kotlin.kapt")

            val androidCommonExtension: CommonExtension<*, *, *, *, *, *> =
                extensions.findByType<LibraryExtension>()
                    ?: extensions.findByType<ApplicationExtension>()
                    ?: throw RuntimeException("Epoxy convention plugin needs Android Library or Application plugin")

            androidCommonExtension.buildTypes.forEach { buildType ->
                buildType.javaCompileOptions.annotationProcessorOptions.arguments["epoxyDisableDslMarker"] =
                    "true"
            }

            configureCommonKapt()

            dependencies {
                "implementation"(libs("epoxy"))
                "implementation"(libs("epoxy.databinding"))
                "implementation"(libs("epoxy.paging"))
                "implementation"(libs("epoxy.viewbinder"))
                "implementation"(libs("epoxy.compose"))
                "kapt"(libs("epoxy.processor")) {
                    exclude(mapOf("group" to "org.jetbrains.kotlin", "module" to "kotlin-compiler-embeddable"))
                }
            }
        }
    }
}
