import com.android.build.api.variant.LibraryAndroidComponentsExtension
import com.android.build.gradle.LibraryExtension
import com.stt.android.Versions
import com.stt.android.configureCommonAndroidDependencies
import com.stt.android.configureCommonKapt
import com.stt.android.configureFlavors
import com.stt.android.configureKotlinAndroid
import com.stt.android.disableSelectedVariants
import com.stt.android.disableUnnecessaryAndroidTests
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure

class AndroidLibraryConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with(target) {
            with(pluginManager) {
                apply("com.android.library")
                apply("org.jetbrains.kotlin.android")
                apply("stt.android.plugin.lint")
                apply("org.jetbrains.kotlin.kapt")
                apply("com.google.devtools.ksp")
                apply("org.jetbrains.kotlin.plugin.allopen")
                apply("org.jmailen.kotlinter")
                apply("kotlin-parcelize")
            }

            extensions.configure<LibraryExtension> {
                configureKotlinAndroid(this)
                with(defaultConfig) {
                    targetSdk = Versions.targetSdk
                }

                buildTypes {
                    debug {  }
                    release { }
                }

                configureFlavors(this)
            }

            extensions.configure<LibraryAndroidComponentsExtension> {
                disableUnnecessaryAndroidTests(target)
                disableSelectedVariants()
            }

            configureCommonKapt()
            configureCommonAndroidDependencies()
        }
    }
}
