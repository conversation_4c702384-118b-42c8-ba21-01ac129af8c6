import com.android.build.api.dsl.ApplicationExtension
import com.github.triplet.gradle.play.PlayPublisherExtension
import com.stt.android.isRunningOnCi
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.get
import org.gradle.kotlin.dsl.provideDelegate
import java.io.File

class AndroidApplicationPublishConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        // these settings do not apply to local builds
        if (!isRunningOnCi) return

        with(target) {
            pluginManager.apply("com.github.triplet.play")

            extensions.configure<ApplicationExtension> {
                signingConfigs {
                    create("release") {
                        val keyStore: String by project
                        val storePass: String by project
                        val alias: String by project
                        val keyPass: String by project
                        storeFile = File(keyStore)
                        storePassword = storePass
                        keyAlias = alias
                        keyPassword = keyPass
                        enableV1Signing = true
                        enableV2Signing = true
                    }
                }

                buildTypes {
                    release {
                        signingConfig = signingConfigs["release"]
                    }
                }
            }

            extensions.configure<PlayPublisherExtension> {
                track.set("alpha")
                val playJsonPath: String by project
                serviceAccountCredentials.set(File(playJsonPath))
            }
        }
    }
}
