import com.google.protobuf.gradle.ProtobufExtension
import com.stt.android.invoke
import com.stt.android.libs
import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.dependencies

class ProtobufConventionPlugin : Plugin<Project> {
    override fun apply(target: Project) {
        with (target) {
            pluginManager.apply("com.google.protobuf")

            // configuration guide at https://github.com/google/protobuf-gradle-plugin/blob/master/README.md
            extensions.configure<ProtobufExtension> {
                protoc {
                    // You still need protoc like in the non-Android case
                    // we need to extract exact dependency string, as this is not standard gradle dependency {} block
                    artifact = libs("protobuf.protoc").get().toString()
                }
                generateProtoTasks {
                    all().configureEach {
                        builtins {
                            create("java") {
                                option("lite")
                            }
                        }
                    }
                }
            }

            dependencies {
                // You need to depend on the lite runtime library, not protobuf-java
                "implementation"(libs("protobuf.lite"))
            }
        }
    }
}
