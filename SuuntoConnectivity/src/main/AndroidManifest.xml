<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation"
        tools:targetApi="s" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT"/>
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.READ_CALL_LOG" />
    <uses-permission android:name="android.permission.REQUEST_COMPANION_RUN_IN_BACKGROUND" />
    <uses-permission android:name="android.permission.REQUEST_COMPANION_USE_DATA_IN_BACKGROUND" />
    <uses-permission android:name="android.permission.REQUEST_COMPANION_START_FOREGROUND_SERVICES_FROM_BACKGROUND" />

    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />
    <uses-permission android:name="android.permission.REQUEST_COMPANION_PROFILE_WATCH" />
    <uses-permission android:name="android.permission.MANAGE_ONGOING_CALLS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.REQUEST_OBSERVE_COMPANION_DEVICE_PRESENCE" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <!-- Needed by SuuntoRepositoryService -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />

    <application>
        <service
            android:name=".notifications.AppCompanionDeviceService"
            android:exported="true"
            android:permission="android.permission.BIND_COMPANION_DEVICE_SERVICE"
            tools:ignore="NewApi">
            <intent-filter>
                <action android:name="android.companion.CompanionDeviceService" />
            </intent-filter>
        </service>
        <service
            android:name="com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityService"
            android:process=":suuntoconnectivity"
            android:exported="false"/>

        <service
            android:name="com.suunto.connectivity.notifications.AncsService"
            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE"
            android:process=":suuntoconnectivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.service.notification.NotificationListenerService" />
            </intent-filter>
        </service>

        <service
            android:name=".repository.SuuntoRepositoryService"
            android:foregroundServiceType="connectedDevice|location"
            android:process=":suuntoconnectivity"
            android:exported="false" />

        <!-- This service intentionally runs on the app process -->
        <service android:name=".sync.SyncResultService"
            android:exported="false" />

        <receiver android:name=".suuntoconnectivity.SuuntoServiceStartOnBootReceiver"
            android:process=":suuntoconnectivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <provider
            android:authorities="${applicationId}.suuntoconnectivityinitprovider"
            android:process=":suuntoconnectivity"
            android:exported="false"
            android:initOrder="100"
            android:name="com.suunto.connectivity.repository.SuuntoRepositoryInitProvider" />
    </application>

</manifest>
