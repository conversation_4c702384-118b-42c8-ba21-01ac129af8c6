package com.suunto.connectivity;

import androidx.annotation.NonNull;
import androidx.annotation.RestrictTo;
import com.stt.android.logbook.SmlZip;
import com.stt.android.logbook.SuuntoLogbookSummaryContent;
import com.suunto.connectivity.logbook.Logbook.Entry;
import com.stt.android.logbook.SuuntoLogbookSamples;
import com.suunto.connectivity.repository.SuuntoRepositoryClient;
import java.io.File;
import rx.Single;

/**
 * Uses local copy of Logbook entries to fetch the summary and moves. Used by clients of the ScLib
 */
@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
public class FileBasedLogbookEntry implements Entry {
    private final long id;
    private final long modificationTimestamp;
    private final int size;
    private final SuuntoRepositoryClient suuntoRepositoryClient;
    private final String macAddress;

    public FileBasedLogbookEntry(@NonNull Entry original, @NonNull String macAddress,
        @NonNull SuuntoRepositoryClient suuntoRepositoryClient) {
        this.id = original.getId();
        this.modificationTimestamp = original.getModificationTimestamp();
        this.size = original.getSize();
        this.suuntoRepositoryClient = suuntoRepositoryClient;
        this.macAddress = macAddress;
    }

    @Override
    public long getId() {
        return id;
    }

    @Override
    public long getModificationTimestamp() {
        return modificationTimestamp;
    }

    @Override
    public int getSize() {
        return size;
    }

    @Override
    public Single<SuuntoLogbookSummaryContent> getSummary() {
        return suuntoRepositoryClient.getEntrySummaryContent(getMacAddress(), getId());
    }

    private String getMacAddress() {
        return macAddress;
    }

    @Override
    public Single<SuuntoLogbookSamples> getSamples() {
        return suuntoRepositoryClient.getEntrySamples(getMacAddress(), getId());
    }

    @Override
    public Single<SmlZip> getLogbookSmlZip() {
        return suuntoRepositoryClient.getLogbookSMLJson(getMacAddress(), getId());
    }

    public File getSummaryJsonFile() {
        return suuntoRepositoryClient.getEntrySummaryFile(getMacAddress(), getId());
    }

    public File getSamplesJsonFile() {
        return suuntoRepositoryClient.getEntrySamplesFile(getMacAddress(), getId());
    }
}
