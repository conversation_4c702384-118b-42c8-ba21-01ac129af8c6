package com.suunto.connectivity

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Response status code 200 indicating success
 */
const val STATUS_OK: Int = 200

/**
 * Response status code 202 indicating there's another page
 */
const val STATUS_NEXT_PAGE: Int = 202

/**
 * Response status code 100 indicating there's more data for recovery data (e.g. moments) sync
 */
const val STATUS_CONTINUE: Int = 100

/**
 * Response status code 204 indicating no content
 */
const val STATUS_NO_CONTENT: Int = 204

/**
 * Response status code 503 indicating service is not available, i.e. the watch
 * doesn't have such resource
 */
const val SERVICE_UNAVAILABLE: Int = 503

@JsonClass(generateAdapter = true)
data class SuuntoGetIntResponse(@Json(name = "Content") val content: Int)

@JsonClass(generateAdapter = true)
data class SuuntoGetStringResponse(@Json(name = "Content") val content: String)

@JsonClass(generateAdapter = true)
data class SuuntoGetLongResponse(@Json(name = "Content") val content: Long)
