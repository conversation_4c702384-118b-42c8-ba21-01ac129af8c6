package com.suunto.connectivity.suuntoconnectivity;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType;
import com.suunto.connectivity.suuntoconnectivity.utils.ConnectMetadata;
import java.util.List;
import rx.Completable;
import rx.Single;

/**
 * BLE interface to be used by BleCentral (Practically whiteboard).
 */
interface BleServiceCentralInterface {

    List<DeviceHandle> getHandles();

    boolean isConnected(@NonNull DeviceHandle handle);

    String getDeviceAddress(@NonNull DeviceHandle handle);

    String getDeviceName(@NonNull DeviceHandle handle);

    DeviceHandle getDeviceHandleFromAddress(@NonNull String deviceAddress);

    /**
     * Connects to device including both Suunto and ANCS connections. Connection will
     * not reconnect automatically! Connect must be called again after connection is lost which
     * happens when BLE device is out of coverage or user has turned BLE off.
     *
     * @return Connection promise return connection success value. Returns BleCore.BLE_OK when
     * <PERSON><PERSON> actually connects to device. This may take long time if device is out of BLE coverage.
     * Connection is cancelled and error is returned if pairing can not be confirmed
     * on time while connecting (about one minute).
     * Pairing state can not be confirmed if bluetooth is not enabled or pairing has actually
     * failed.
     * This means that device can actually be paired even if connect returns with an error.
     */
    Single<Integer> connect(
        @NonNull String deviceAddress,
        SuuntoDeviceType deviceType,
        @NonNull ConnectMetadata connectMetadata);

    Single<Integer> disconnect(@NonNull String deviceAddress);

    Single<Integer> startDataNotify(@NonNull DeviceHandle handle);

    Single<Integer> dataWrite(@NonNull DeviceHandle handle, @NonNull byte[] data);

    @Nullable byte[] getData(@NonNull DeviceHandle handle);

    Completable destroyBleDevice(String deviceAddress);
}
