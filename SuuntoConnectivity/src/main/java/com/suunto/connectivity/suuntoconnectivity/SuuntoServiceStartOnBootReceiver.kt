package com.suunto.connectivity.suuntoconnectivity

import android.companion.CompanionDeviceManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import com.suunto.connectivity.repository.SuuntoRepositoryService

class SuuntoServiceStartOnBootReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action != Intent.ACTION_BOOT_COMPLETED) {
            return
        }

        if (SuuntoRepositoryService.isServiceRunning.get()) {
            return
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (context.packageManager.hasSystemFeature(PackageManager.FEATURE_COMPANION_DEVICE_SETUP)) {
                val deviceManager = context.getSystemService(CompanionDeviceManager::class.java)
                if (deviceManager.associations.isEmpty()) {
                    // user has not enabled companion, we cannot start the service from the background
                    return
                }
            } else {
                // companion is not available on this device, we cannot start the service from the background
                return
            }
        }
        SuuntoRepositoryService.startService(context, false, "SuuntoServiceStartOnBootReceiver")
    }
}
