package com.suunto.connectivity

import com.google.gson.annotations.SerializedName
import com.suunto.connectivity.settings.Gender
import com.suunto.connectivity.settings.UnitSystem

data class SpartanUserSettings internal constructor(
    @SerializedName(value = "gender")
    val gender: Gender,
    @SerializedName(value = "birthYear")
    val birthYear: Int,
    @SerializedName(value = "heightInMeter")
    val heightInMeter: Float,
    @SerializedName(value = "weightInKilograms")
    val weightInKilograms: Float,
    @SerializedName(value = "maxHR")
    val maxHR: Int,
    @SerializedName(value = "restHR")
    val restHR: Int,
    @SerializedName(value = "unitSystem")
    val unitSystem: UnitSystem,
) {

    @Suppress("LongParameterList")
    class Builder internal constructor(
        private var gender: Gender? = null,
        private var birthYear: Int = 0,
        private var heightInMeter: Float = 0f,
        private var weightInKilograms: Float = 0f,
        private var maxHR: Int = 0,
        private var restHR: Int = 0,
        private var unitSystem: UnitSystem? = null,
    ) {
        fun gender(value: Gender): Builder = apply { this.gender = value }

        fun birthYear(value: Int): Builder = apply { this.birthYear = value }

        fun heightInMeter(value: Float): Builder = apply {
            this.heightInMeter = value
        }

        fun weightInKilograms(value: Float): Builder = apply {
            this.weightInKilograms = value
        }

        fun maxHR(value: Int): Builder = apply { this.maxHR = value }

        fun restHR(value: Int): Builder = apply { this.restHR = value }

        fun unitSystem(value: UnitSystem): Builder = apply { this.unitSystem = value }

        fun build(): SpartanUserSettings = SpartanUserSettings(
            gender = gender ?: error("gender == null"),
            birthYear = birthYear,
            heightInMeter = heightInMeter,
            weightInKilograms = weightInKilograms,
            maxHR = maxHR,
            restHR = restHR,
            unitSystem = unitSystem ?: error("unitSystem == null"),
        )
    }

    companion object {
        @JvmStatic
        fun builder(): Builder = Builder()
    }
}
