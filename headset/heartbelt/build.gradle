plugins {
    id 'stt.android.plugin.library'
    id "stt.android.plugin.hilt"
    id "stt.android.plugin.moshi"
    id "stt.android.plugin.compose"
}
android {
    namespace 'com.suunto.heartbelt'
    buildFeatures.buildConfig = true
}

dependencies {
    implementation project(Deps.HeadsetSoaLibrary)
    implementation project(Deps.smartDevice)
    implementation project(Deps.appBase)
    implementation libs.lottie
    implementation libs.lottie.compose
}
