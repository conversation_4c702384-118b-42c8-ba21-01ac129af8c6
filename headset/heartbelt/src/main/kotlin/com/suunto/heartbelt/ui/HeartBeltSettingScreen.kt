package com.suunto.heartbelt.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.component.SuuntoTopBar
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.utils.BatteryLevelUtil
import com.suunto.heartbelt.R
import com.suunto.heartbelt.model.HeartBeltSetting
import com.suunto.heartbelt.model.HeartBeltSettingItemType
import com.suunto.smartdevice.model.SettingItem
import com.suunto.smartdevice.model.SettingItemType
import com.suunto.smartdevice.model.SmartDevice
import com.suunto.smartdevice.ui.SmartDeviceSettingContent
import com.suunto.soa.ble.service.BleState
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableMap

@Composable
fun HeartBeltSettingScreen(
    heartBeltSetting: HeartBeltSetting,
    onHandleClickEvent: (SettingItemType) -> Unit,
    onBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    Scaffold(
        modifier = modifier,
        topBar = {
            SuuntoTopBar(title = heartBeltSetting.smartDevice.name, onNavigationClick = onBack)
        }
    ) { innerPadding ->
        ContentCenteringColumn(
            modifier = Modifier
                .padding(innerPadding)
        ) {
            SmartDeviceSettingContent(
                settingItems = heartBeltSetting.settingItems,
                onClickItem = onHandleClickEvent,
                topContent = {
                    TopContent(heartBeltSetting = heartBeltSetting)
                }
            )
        }
    }
}

@Composable
private fun TopContent(heartBeltSetting: HeartBeltSetting, modifier: Modifier = Modifier) {
    Column(modifier = modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally) {
        Box(modifier = Modifier.height(360.dp), contentAlignment = Alignment.Center) {
            Image(
                modifier = Modifier.size(250.dp),
                painter = painterResource(heartBeltSetting.image),
                contentDescription = null
            )
        }
        when (heartBeltSetting.connectionState) {
            BleState.Connecting -> {
                Row(
                    modifier = Modifier.padding(vertical = MaterialTheme.spacing.medium),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    CircularProgressIndicator(modifier = Modifier.size(MaterialTheme.iconSizes.small))
                    Text(
                        modifier = Modifier.padding(start = MaterialTheme.spacing.xsmall),
                        text = stringResource(id = R.string.connecting).uppercase(),
                        style = MaterialTheme.typography.bodyLargeBold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
            }

            BleState.Ready -> {
                Text(
                    modifier = Modifier.padding(top = MaterialTheme.spacing.medium),
                    text = stringResource(id = R.string.connected),
                    style = MaterialTheme.typography.bodyLargeBold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Row(
                    modifier = Modifier.padding(vertical = MaterialTheme.spacing.medium),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        modifier = Modifier.size(MaterialTheme.iconSizes.small),
                        painter = painterResource(
                            BatteryLevelUtil.getBatteryLevelIconRes(
                                heartBeltSetting.batteryValue
                            )
                        ),
                        contentDescription = ""
                    )
                    Text(
                        modifier = Modifier.padding(start = MaterialTheme.spacing.xsmall),
                        text = "${heartBeltSetting.batteryValue}%",
                        style = MaterialTheme.typography.bodyLargeBold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
            }

            is BleState.Disconnected -> {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Icon(
                        painter = painterResource(R.drawable.icon_belt_not_connected),
                        contentDescription = ""
                    )
                    Text(
                        modifier = Modifier.padding(start = MaterialTheme.spacing.xsmall),
                        text = stringResource(id = R.string.notConnected).uppercase(),
                        style = MaterialTheme.typography.bodyLargeBold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
                Text(
                    modifier = Modifier.padding(
                        top = MaterialTheme.spacing.large,
                        bottom = MaterialTheme.spacing.smaller
                    ),
                    text = stringResource(
                        id = com.suunto.smartdevice.R.string.need_help
                    ).uppercase(),
                    style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Bold),
                    color = MaterialTheme.colorScheme.onSurface
                )
            }

            else -> {
                // do nothing
            }
        }
    }
}

@Preview
@Composable
private fun HeartBeltSettingScreenPreview() {
    M3AppTheme {
        HeartBeltSettingScreen(
            heartBeltSetting = HeartBeltSetting(
                image = R.drawable.icon_heart_belt,
                smartDevice = SmartDevice(
                    name = "Suunto Smart Heart Rate Belt",
                    macAddress = "address",
                    serialNumber = "1234565"
                ),
                connectionState = BleState.Connecting,
                batteryValue = 20,
                settingItems = buildMap<Int, ImmutableList<SettingItem>> {
                    put(
                        1,
                        persistentListOf(
                            SettingItem.StateItem(
                                titleResId = R.string.function_introduction_title,
                                settingItemType = HeartBeltSettingItemType.FUNCTION_INTRODUCTION
                            ),
                            SettingItem.ActionItem(
                                titleResId = R.string.user_guide_title,
                                settingItemType = HeartBeltSettingItemType.USER_GUIDE
                            ),
                        )
                    )
                }.toImmutableMap()
            ),
            onHandleClickEvent = {},
            onBack = {}
        )
    }
}
