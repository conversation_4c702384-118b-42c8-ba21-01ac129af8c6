package com.suunto.heartbelt.ui

import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.core.tween
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.suunto.heartbelt.viewmodel.HeartBeltViewModel

private object NavDestinations {
    const val FIRST_PAIR_GUIDE = "FIRST_PAIR_GUIDE"
    const val SEARCHING = "DEVICE_SEARCHING"
    const val PAIRING = "DEVICE_PAIRING"
    const val FOUND = "DEVICE_FOUND"
    const val PAIR_FAILED = "DEVICE_PAIR_FAILED"
    const val SETTINGS = "SETTINGS"
}

private const val TRANSITION_DURATION_MS = 300

@Composable
fun HeartBeltNavGraph(
    viewModel: HeartBeltViewModel,
    onBack: () -> Unit,
    modifier: Modifier = Modifier,
    navController: NavHostController = rememberNavController()
) {
    NavHost(
        navController = navController,
        modifier = modifier,
        startDestination = NavDestinations.FIRST_PAIR_GUIDE,
        enterTransition = {
            slideIntoContainer(
                AnimatedContentTransitionScope.SlideDirection.Left,
                tween(TRANSITION_DURATION_MS)
            )
        },
        exitTransition = {
            slideOutOfContainer(
                AnimatedContentTransitionScope.SlideDirection.Left,
                tween(TRANSITION_DURATION_MS)
            )
        },
        popEnterTransition = {
            slideIntoContainer(
                AnimatedContentTransitionScope.SlideDirection.Right,
                tween(TRANSITION_DURATION_MS)
            )
        },
        popExitTransition = {
            slideOutOfContainer(
                AnimatedContentTransitionScope.SlideDirection.Right,
                tween(TRANSITION_DURATION_MS)
            )
        }
    ) {
        composable(NavDestinations.SEARCHING) {
            // TODO add DeviceSearchingScreen
        }
        composable(NavDestinations.FOUND) {
            // TODO add DeviceFoundScreen
        }
        composable(NavDestinations.PAIRING) {
            // TODO add DevicePairingScreen
        }
        composable(NavDestinations.PAIR_FAILED) {
            // TODO add DevicePairFailedScreen
        }
        composable(NavDestinations.SETTINGS) {
            // TODO add HeartBeltSettingsScreen
        }
    }
}
