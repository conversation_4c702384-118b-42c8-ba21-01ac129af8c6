package com.suunto.heartbelt.model

import androidx.annotation.DrawableRes
import com.suunto.smartdevice.model.SettingItem
import com.suunto.smartdevice.model.SmartDevice
import com.suunto.soa.ble.service.BleState
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.ImmutableMap

data class HeartBeltSetting(
    @field:DrawableRes val image: Int,
    val smartDevice: SmartDevice,
    val connectionState: BleState,
    val batteryValue: Int,
    val settingItems: ImmutableMap<Int, ImmutableList<SettingItem>>,
)
