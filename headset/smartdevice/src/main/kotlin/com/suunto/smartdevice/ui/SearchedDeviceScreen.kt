package com.suunto.smartdevice.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import com.stt.android.compose.component.SuuntoTopBar
import com.stt.android.compose.newdesign.widgets.PrimaryButton
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.suunto.smartdevice.R
import com.suunto.smartdevice.model.SearchedDevice
import com.suunto.smartdevice.model.SmartDevice
import com.suunto.smartdevice.model.SmartDeviceEvent
import kotlinx.collections.immutable.persistentListOf

@Composable
fun SearchedDeviceScreen(
    searchedDevice: SearchedDevice,
    onHandleEvent: (SmartDeviceEvent) -> Unit,
    onBack: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Scaffold(modifier = modifier, topBar = {
        SuuntoTopBar(
            title = stringResource(searchedDevice.titleRes ?: R.string.pair_device),
            onNavigationClick = onBack
        )
    }) { paddingValues ->
        Column(
            modifier = Modifier
                .background(MaterialTheme.colorScheme.surface)
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth(),
                contentPadding = PaddingValues(horizontal = MaterialTheme.spacing.medium)
            ) {
                item {
                    Box(
                        Modifier
                            .fillMaxWidth()
                            .height(360.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        val composition by rememberLottieComposition(
                            LottieCompositionSpec.Asset(
                                stringResource(R.string.device_found_animation)
                            )
                        )
                        LottieAnimation(
                            composition = composition,
                            iterations = LottieConstants.IterateForever,
                        )
                    }
                }
                item {
                    Text(
                        modifier = Modifier
                            .padding(
                                vertical = MaterialTheme.spacing.medium
                            )
                            .fillMaxWidth(),
                        text = stringResource(
                            id = searchedDevice.subTitleRes ?: R.string.found_device_subtitle
                        ).uppercase(),
                        style = MaterialTheme.typography.bodyLargeBold,
                        color = MaterialTheme.colorScheme.onSurface,
                        textAlign = TextAlign.Center
                    )
                }
                items(searchedDevice.devices, key = { it.macAddress }) { smartDevice ->
                    DeviceItem(
                        device = smartDevice,
                        onPairDevice = { onHandleEvent.invoke(SmartDeviceEvent.PairDevice(it)) }
                    )
                }
                item {
                    Text(
                        text = stringResource(id = R.string.searched_device_notice),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.darkGrey,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
}

@Composable
private fun DeviceItem(
    device: SmartDevice,
    onPairDevice: (SmartDevice) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(top = MaterialTheme.spacing.smaller, bottom = MaterialTheme.spacing.large),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = device.name.uppercase(),
            style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Bold),
            color = MaterialTheme.colorScheme.primary,
        )
        Text(
            modifier = Modifier.padding(top = MaterialTheme.spacing.xsmall),
            text = device.serialNumber,
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface,
        )

        PrimaryButton(
            text = stringResource(R.string.pair_device),
            onClick = { onPairDevice.invoke(device) },
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = MaterialTheme.spacing.small)
        )
    }
}

@Preview
@Composable
private fun SearchedDeviceScreenPreview() {
    M3AppTheme {
        SearchedDeviceScreen(
            searchedDevice = SearchedDevice(
                devices = persistentListOf(
                    SmartDevice(
                        name = "Suunto SHRM 2",
                        serialNumber = "1234567",
                        macAddress = "",
                        connected = false
                    ),
                    SmartDevice(
                        name = "Suunto SHRM 2",
                        serialNumber = "1234567",
                        macAddress = "",
                        connected = false
                    )
                ),
            ),
            onHandleEvent = {},
            onBack = {}
        )
    }
}
