package com.suunto.smartdevice.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.component.SuuntoTextButton
import com.stt.android.compose.component.SuuntoTopBar
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.newdesign.widgets.PrimaryButton
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.suunto.smartdevice.R
import com.suunto.smartdevice.model.SmartDevice
import com.suunto.smartdevice.model.SmartDeviceEvent

@Composable
fun DevicePairFailedScreen(
    device: SmartDevice,
    onHandleEvent: (SmartDeviceEvent) -> Unit,
    onBack: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Scaffold(modifier = modifier, topBar = {
        SuuntoTopBar(
            title = stringResource(R.string.pair_device),
            onNavigationClick = onBack
        )
    }) {
        ContentCenteringColumn(
            modifier = Modifier
                .background(MaterialTheme.colorScheme.surface)
                .fillMaxSize()
                .padding(it)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.spacing.medium)
            ) {
                Box(
                    Modifier
                        .fillMaxWidth()
                        .height(360.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Image(
                        painter = painterResource(R.drawable.icon_failed),
                        contentDescription = ""
                    )
                }
                Text(
                    modifier = Modifier
                        .padding(
                            top = MaterialTheme.spacing.medium,
                            bottom = MaterialTheme.spacing.small
                        )
                        .fillMaxWidth(),
                    text = stringResource(
                        id = R.string.pairing_failed
                    ).uppercase(),
                    style = MaterialTheme.typography.bodyLargeBold,
                    color = MaterialTheme.colorScheme.onSurface,
                    textAlign = TextAlign.Center
                )

                PrimaryButton(
                    text = stringResource(R.string.restart_pairing),
                    onClick = { onHandleEvent.invoke(SmartDeviceEvent.PairDevice(device)) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = MaterialTheme.spacing.small)
                )
                Text(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = MaterialTheme.spacing.medium),
                    text = stringResource(id = R.string.pairing_failed_note),
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurface,
                    textAlign = TextAlign.Center
                )
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(MaterialTheme.spacing.medium),
                    contentAlignment = Alignment.Center
                ) {
                    SuuntoTextButton(
                        text = R.string.need_help,
                        onClick = {
                            onHandleEvent(SmartDeviceEvent.NeedHelp)
                        }
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun DevicePairFailedScreenPreview() {
    M3AppTheme {
        DevicePairFailedScreen(
            device = SmartDevice("", "", "", false),
            onHandleEvent = {},
            onBack = {}
        )
    }
}
