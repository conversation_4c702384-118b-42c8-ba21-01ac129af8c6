package com.suunto.smartdevice.model

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.suunto.smartdevice.R

sealed class SettingItem {
    @get:StringRes
    abstract val titleResId: Int

    @get:StringRes
    abstract val descriptionResId: Int?
    abstract val isDisabled: Boolean
    abstract val settingItemType: SettingItemType

    data class StateItem(
        @field:StringRes override val titleResId: Int,
        @field:StringRes val stateTextResId: Int? = null,
        override val settingItemType: SettingItemType,
        @field:StringRes override val descriptionResId: Int? = null,
        override val isDisabled: Boolean = false,
    ) : SettingItem()

    data class ActionItem(
        @field:StringRes override val titleResId: Int,
        override val settingItemType: SettingItemType,
        @field:StringRes override val descriptionResId: Int? = null,
        override val isDisabled: Boolean = false,
        @field:DrawableRes val rightIconRes: Int = R.drawable.ic_right_arrow,
    ) : SettingItem()
}

interface SettingItemType
