package com.suunto.smartdevice.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.suunto.smartdevice.model.SettingItem
import com.suunto.smartdevice.model.SettingItemType
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.ImmutableMap

@Composable
fun SmartDeviceSettingContent(
    settingItems: ImmutableMap<Int, ImmutableList<SettingItem>>,
    onClickItem: (SettingItemType) -> Unit,
    topContent: @Composable () -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.surface),
    ) {
        item {
            topContent()
            HorizontalDivider(
                color = MaterialTheme.colorScheme.dividerColor,
                thickness = MaterialTheme.spacing.xsmaller
            )
        }
        settingItems.values.forEachIndexed { index, items ->
            items(items, key = { it.settingItemType }) { item ->
                SettingItemContent(
                    item = item,
                    onClickItem = onClickItem,
                )
                HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
            }
            if (index < settingItems.size - 1)
                item {
                    HorizontalDivider(
                        color = MaterialTheme.colorScheme.dividerColor,
                        thickness = MaterialTheme.spacing.xsmaller
                    )
                }
        }
    }
}

@Composable
private fun SettingItemContent(
    item: SettingItem,
    onClickItem: (SettingItemType) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickableThrottleFirst(enabled = !item.isDisabled) {
                onClickItem(item.settingItemType)
            }
            .alpha(if (item.isDisabled) 0.5f else 1f)
            .padding(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = stringResource(id = item.titleResId),
                style = MaterialTheme.typography.bodyLargeBold,
                color = MaterialTheme.colorScheme.onSurface,
            )
            item.descriptionResId?.let {
                Text(
                    modifier = Modifier.padding(top = MaterialTheme.spacing.xsmall),
                    text = stringResource(id = it),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
        }
        when (item) {
            is SettingItem.StateItem -> item.stateTextResId?.let {
                Text(
                    modifier = Modifier.padding(start = MaterialTheme.spacing.medium),
                    text = stringResource(id = it),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.primary
                )
            }

            is SettingItem.ActionItem -> Icon(
                painter = painterResource(id = item.rightIconRes),
                modifier = Modifier
                    .padding(start = MaterialTheme.spacing.medium)
                    .size(MaterialTheme.iconSizes.small),
                contentDescription = null,
            )
        }
    }
}
