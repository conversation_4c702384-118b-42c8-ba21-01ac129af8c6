package com.suunto.smartdevice.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import com.stt.android.compose.component.SuuntoTopBar
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.suunto.smartdevice.R
import com.suunto.smartdevice.model.DeviceSearingData
import kotlinx.collections.immutable.persistentListOf

@Composable
fun DeviceSearchingScreen(
    deviceSearingData: DeviceSearingData,
    onBack: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Scaffold(modifier = modifier, topBar = {
        SuuntoTopBar(
            title = stringResource(deviceSearingData.titleRes ?: R.string.pair_device),
            onNavigationClick = onBack
        )
    }) {
        ContentCenteringColumn(
            modifier = Modifier
                .background(MaterialTheme.colorScheme.surface)
                .fillMaxSize()
                .padding(it)
        ) {
            Column(
                modifier = Modifier
                    .padding(horizontal = MaterialTheme.spacing.medium)
            ) {
                Box(
                    Modifier
                        .fillMaxWidth()
                        .height(360.dp),
                    contentAlignment = Alignment.Center
                ) {
                    val composition by rememberLottieComposition(
                        LottieCompositionSpec.Asset(
                            stringResource(R.string.searching_device_animation)
                        )
                    )
                    LottieAnimation(
                        composition = composition,
                        iterations = LottieConstants.IterateForever,
                    )
                }
                Text(
                    modifier = Modifier
                        .padding(
                            top = MaterialTheme.spacing.medium,
                            bottom = MaterialTheme.spacing.small
                        )
                        .fillMaxWidth(),
                    text = stringResource(
                        id = deviceSearingData.subTitleRes ?: R.string.searching_device
                    ).uppercase(),
                    style = MaterialTheme.typography.bodyLargeBold,
                    color = MaterialTheme.colorScheme.onSurface,
                    textAlign = TextAlign.Center
                )
                deviceSearingData.descriptionsRes.forEach { descriptionRes ->
                    Text(
                        modifier = Modifier.padding(top = MaterialTheme.spacing.xsmall),
                        text = stringResource(id = descriptionRes),
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.darkGrey,
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun DeviceSearchingScreenPreview() {
    M3AppTheme {
        DeviceSearchingScreen(
            deviceSearingData = DeviceSearingData(
                descriptionsRes = persistentListOf(R.string.pair_device_notice)
            ),
            onBack = {}
        )
    }
}
