package com.suunto.smartdevice.model

import androidx.annotation.StringRes
import kotlinx.collections.immutable.ImmutableList

data class SearchedDevice(
    val devices: ImmutableList<SmartDevice>,
    @field:StringRes val subTitleRes: Int? = null,
    @field:StringRes val titleRes: Int? = null,
)

data class SmartDevice(
    val name: String,
    val serialNumber: String,
    val macAddress: String,
    val connected: Boolean = false
)
