package com.stt.android.remote.workout.picture

import com.stt.android.remote.MediaTypes
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File
import javax.inject.Inject

class PictureRemoteApi
@Inject constructor(
    private val pictureRestApi: PictureRestApi
) {
    suspend fun uploadPicture(
        workoutKey: String,
        timestamp: Long,
        latitude: Double?,
        longitude: Double?,
        totaltime: Double,
        md5hash: String,
        pictureFile: File
    ): RemotePicture {
        return pictureRestApi.uploadPicture(
            workoutKey = workoutKey,
            timestamp = timestamp,
            latitude = latitude,
            longitude = longitude,
            totaltime = totaltime,
            md5hash = md5hash,
            pictureFile = pictureFile.asRequestBody(MediaTypes.OCTET_STREAM_CONTENT_TYPE)
        ).payloadOrThrow()
    }
}
