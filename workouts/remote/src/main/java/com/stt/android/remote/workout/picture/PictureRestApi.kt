package com.stt.android.remote.workout.picture

import com.stt.android.remote.response.AskoResponse
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query

interface PictureRestApi {
    @PUT("workouts/{workoutKey}/image")
    suspend fun uploadPicture(
        @Path("workoutKey") workoutKey: String,
        @Query("timestamp") timestamp: Long,
        @Query("lat") latitude: Double?,
        @Query("lon") longitude: Double?,
        @Query("totaltime") totaltime: Double,
        @Query("hash") md5hash: String,
        @Body pictureFile: RequestBody
    ): AskoResponse<RemotePicture>
}
