package com.stt.android.remote.workout.picture

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import com.stt.android.remote.routes.RemotePoint

@JsonClass(generateAdapter = true)
data class RemotePicture(
    @<PERSON><PERSON>(name = "key") val key: String?,
    @<PERSON><PERSON>(name = "location") val location: RemotePoint?,
    @<PERSON><PERSON>(name = "timestamp") val timestamp: Long,
    @<PERSON><PERSON>(name = "totalTime") val totalTime: Double,
    @<PERSON><PERSON>(name = "workoutKey") val workoutKey: String?,
    @<PERSON><PERSON>(name = "description") val description: String?,
    @<PERSON><PERSON>(name = "username") val username: String?,
    @<PERSON><PERSON>(name = "width") val width: Int,
    @<PERSON><PERSON>(name = "height") val height: Int,
    @<PERSON><PERSON>(name = "contentReviewStatus") val reviewState: Int = 1
)
