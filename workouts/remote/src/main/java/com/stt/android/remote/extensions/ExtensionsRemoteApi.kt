package com.stt.android.remote.extensions

import javax.inject.Inject

/**
 * Class responsible of making remote calls for querying extensions data.
 */
class ExtensionsRemoteApi @Inject constructor(
    private val extensionsRestApi: ExtensionsRestApi,
) {
    suspend fun fetchExtensions(
        workoutKey: String,
        extensionTypes: List<String>,
    ): List<RemoteWorkoutExtension> = extensionsRestApi
        .fetchExtensions(workoutKey, extensionTypes)
        .payloadOrThrow()
        .extensions
}
