package com.stt.android.remote.workout

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import com.stt.android.remote.extensions.RemoteWorkoutExtension
import com.stt.android.remote.routes.RemotePoint
import com.stt.android.remote.tags.RemoteUserTag
import com.stt.android.remote.user.RemoteUser
import com.stt.android.remote.workout.video.RemoteVideo
import java.io.File

/**
 * Data class to hold data about a non synced workout
 */
data class RemoteUnsyncedWorkout(
    val workoutFile: File?,
    val remoteWorkoutExtensions: List<RemoteWorkoutExtension>,
    val smlZipFile: File?,
)

@JsonClass(generateAdapter = true)
data class RemoteCompetitionResult(
    val workoutId: String?,
    val username: String?,
    val distance: Double?,
    val result: Int?,
    val finishDuration: Long?,
    val targetDuration: Long?,
    val workoutOwner: String?,
)

/**
 * Data classes to hold information about a synced workout
 */
@JsonClass(generateAdapter = true)
data class RemoteSyncedWorkout(
    @Json(name = "workoutKey") val workoutKey: String,
    @Json(name = "totalDistance") val totalDistance: Double?,
    @Json(name = "maxSpeed") val maxSpeed: Double?,
    @Json(name = "activityId") val activityId: Int,
    @Json(name = "avgSpeed") val avgSpeed: Double?,
    @Json(name = "description") val description: String?,
    @Json(name = "startPosition") val startPosition: RemotePoint?,
    @Json(name = "stopPosition") val stopPosition: RemotePoint?,
    @Json(name = "centerPosition") val centerPosition: RemotePoint?,
    @Json(name = "startTime") val startTime: Long,
    @Json(name = "stopTime") val stopTime: Long,
    @Json(name = "totalTime") val totalTime: Double,
    @Json(name = "energyConsumption") val energyConsumption: Double?,
    @Json(name = "username") val username: String,
    @Json(name = "hrdata") val hrdata: RemoteSyncedWorkoutHrData?,
    @Json(name = "cadence") val cadence: RemoteSyncedWorkoutCadenceData?,
    @Json(name = "viewCount") val viewCount: Int,
    @Json(name = "sharingFlags") val sharingFlags: Int,
    @Json(name = "stepCount") val stepCount: Int?,
    @Json(name = "isManuallyAdded") val manuallyAdded: Boolean?,
    @Json(name = "polyline") val polyline: String?,
    @Json(name = "pictureCount") val pictureCount: Int,
    @Json(name = "totalAscent") val totalAscent: Double?,
    @Json(name = "totalDescent") val totalDescent: Double?,
    @Json(name = "recoveryTime") val recoveryTime: Long?,
    @Json(name = "comments") val comments: List<RemoteSyncedWorkoutComment>?,
    @Json(name = "photos") val photos: List<RemoteSyncedWorkoutImage>?,
    @Json(name = "reactions") val reactions: List<RemoteSyncedWorkoutReaction>?,
    @Json(name = "videos") val videos: List<RemoteVideo>?,
    @Json(name = "extensions") val extensions: List<RemoteWorkoutExtension>?,
    @Json(name = "rankings") val rankings: RemoteSyncedWorkoutRankings?,
    @Json(name = "maxAltitude") val maxAltitude: Double?,
    @Json(name = "minAltitude") val minAltitude: Double?,
    @Json(name = "tss") val tss: RemoteTSS?,
    @Json(name = "tssList") val tssList: List<RemoteTSS>?,
    @Json(name = "suuntoTags") val suuntoTags: List<RemoteSuuntoTag>?,
    @Json(name = "userTagData") val userTags: List<RemoteUserTag>?,
    @Json(name = "zoneSense") val zoneSense: RemoteZoneSense?,
    @Json(name = "avgAscentSpeed") val avgAscentSpeed: Double?,
    @Json(name = "maxAscentSpeed") val maxAscentSpeed: Double?,
    @Json(name = "avgDescentSpeed") val avgDescentSpeed: Double?,
    @Json(name = "maxDescentSpeed") val maxDescentSpeed: Double?,
    @Json(name = "avgDistancePerStroke") val avgDistancePerStroke: Double?,
    @Json(name = "estimatedFloorsClimbed") val estimatedFloorsClimbed: Int?,
)

@JsonClass(generateAdapter = true)
data class RemoteSyncedWorkoutHrData(
    @Json(name = "max") val absoluteMaximum: Int,
    @Json(name = "hrmax") val workoutMaximum: Int,
    @Json(name = "avg") val workoutAverage: Int,
    @Json(name = "userMaxHR") val userMaxHR: Int?,
    @Json(name = "workoutAvgHR") val workoutAvgHR: Int?,
    @Json(name = "workoutMaxHR") val workoutMaxHR: Int?
)

@JsonClass(generateAdapter = true)
data class RemoteSyncedWorkoutCadenceData(
    @Json(name = "avg") val avg: Int,
    @Json(name = "max") val max: Int
)

@JsonClass(generateAdapter = true)
data class RemoteSyncedWorkoutComment(
    @Json(name = "key") val key: String,
    @Json(name = "timestamp") val timestamp: Long,
    @Json(name = "username") val username: String,
    @Json(name = "realname") val realname: String,
    @Json(name = "profilePictureUrl") val profilePictureUrl: String?,
    @Json(name = "comment") val message: String
)

@JsonClass(generateAdapter = true)
data class RemoteSyncedWorkoutImage(
    @Json(name = "key") val key: String,
    @Json(name = "username") val username: String,
    @Json(name = "description") val description: String?,
    @Json(name = "url") val url: String,
    @Json(name = "timestamp") val timestamp: Long,
    @Json(name = "totalTime") val totalTime: Double,
    @Json(name = "width") val width: Int,
    @Json(name = "height") val height: Int,
    @Json(name = "location") val location: RemotePoint?,
    @Json(name = "workoutKey") val workoutKey: String,
    // Fields from combined workout API. See WorkoutRestV2Api.getCombinedWorkout
    @Json(name = "coverImage") val coverImage: Boolean?,
    @Json(name = "sizes") val sizes: List<RemoteSize>?,
    // 0 reviewing; 1 pass; 2 fail
    @Json(name = "contentReviewStatus") val contentReviewStatus: Int = 1
)

@JsonClass(generateAdapter = true)
data class RemoteSyncedWorkoutReaction(
    @Json(name = "utfCode") val utfCode: String,
    @Json(name = "count") val count: Int,
    @Json(name = "userReacted") val userReacted: Boolean
)

@JsonClass(generateAdapter = true)
data class RemoteSyncedWorkoutRankings(
    @Json(name = "totalTimeOnRouteRanking") val totalTimeOnRouteRanking: RemoteSyncedWorkoutRanking
)

@JsonClass(generateAdapter = true)
data class RemoteSyncedWorkoutRanking(
    @Json(name = "originalRanking") val originalRanking: Int,
    @Json(name = "originalNumberOfWorkouts") val originalNumberOfWorkouts: Int
)

@JsonClass(generateAdapter = true)
data class RemoteUpdatedWorkout(
    @Json(name = "workoutKey")
    val workoutKey: String,
    @Json(name = "totalDistance")
    val totalDistance: Double,
    @Json(name = "activityId")
    val activityId: Int,
    @Json(name = "description")
    val description: String?,
    @Json(name = "startTime")
    val startTime: Long,
    @Json(name = "totalTime")
    val totalTime: Double,
    @Json(name = "energyConsumption")
    val energyConsumption: Int,
    @Json(name = "hrMaxValue")
    val hrMaxValue: Int,
    @Json(name = "hrAvgValue")
    val hrAvgValue: Int,
    @Json(name = "sharingFlags")
    val sharingFlags: Int,
    @Json(name = "stepCount")
    val stepCount: Int,
)

@JsonClass(generateAdapter = true)
data class RemotePublicWorkout(
    @Json(name = "user") val user: RemoteUser,
    @Json(name = "workout") val workout: RemoteSyncedWorkout
)

@JsonClass(generateAdapter = true)
data class RemoteWorkoutLocation(
    @Json(name = "lat") val latitude: Double,
    @Json(name = "lon") val longitude: Double
)

@JsonClass(generateAdapter = true)
data class RemoteWorkoutAttributes(
    @Json(name = "startPosition") val startPosition: RemoteWorkoutLocation?,
    @Json(name = "tss") val tss: RemoteTSS?,
    @Json(name = "maxSpeed") val maxSpeed: Double?,
    @Json(name = "ascent") val totalAscent: Double?,
    @Json(name = "descent") val totalDescent: Double?,
    @Json(name = "suuntoTags") val suuntoTags: List<RemoteSuuntoTag>?,
    @Json(name = "userTags") val userTags: List<String>?,
)

@JsonClass(generateAdapter = true)
data class RemoteTSS(
    @Json(name = "trainingStressScore") val trainingStressScore: Float,
    @Json(name = "calculationMethod") val calculationMethod: RemoteTSSCalculationMethod,
    @Json(name = "intensityFactor") val intensityFactor: Float? = null,
    @Json(name = "normalizedPower") val normalizedPower: Float? = null,
    @Json(name = "averageGradeAdjustedPace") val averageGradeAdjustedPace: Float? = null
)

@JsonClass(generateAdapter = true)
data class RemoteZoneSense(
    @Json(name = "aerobicBaseline") val aerobicBaseline: Double?,
)

@JsonClass(generateAdapter = false)
enum class RemoteTSSCalculationMethod {
    POWER,
    PACE,
    HR,
    SWIM_PACE,
    MET,
    MANUAL,
    DYNAMIC_DFA,
}

@JsonClass(generateAdapter = true)
data class RemoteWorkoutStats(
    @Json(name = "totalDistanceSum") val totalDistanceSum: Double,
    @Json(name = "totalTimeSum") val totalTimeSum: Double,
    @Json(name = "totalEnergyConsumptionSum") val totalEnergyConsumptionKCalSum: Double,
    @Json(name = "totalNumberOfWorkoutsSum") val totalNumberOfWorkoutsSum: Int,
    @Json(name = "totalDays") val totalDays: Int?,
    @Json(name = "allStats") val activityTypeStats: List<RemoteActivityTypeStats>
)

@JsonClass(generateAdapter = true)
data class RemoteActivityTypeStats(
    @Json(name = "_id") val activityId: Int,
    @Json(name = "totalDistance") val totalDistance: Double,
    @Json(name = "totalTime") val totalTime: Double,
    @Json(name = "energyConsumption") val energyConsumptionKCal: Double,
    @Json(name = "numberOfWorkouts") val numberOfWorkouts: Double,
)

@JsonClass(generateAdapter = false)
enum class RemoteSuuntoTag {
    COMMUTE,
    MARATHON,
    HALF_MARATHON,
    IMPACT_SPEED_AND_AGILITY,
    IMPACT_SPEED_AND_STRENGTH,
    IMPACT_FLEXIBILITY,
    IMPACT_STRENGTH,
    IMPACT_ABOVE_THRESHOLD_VO2MAX,
    IMPACT_HARD_ANAEROBIC_EFFORT,
    IMPACT_ANAEROBIC_THRESHOLD,
    IMPACT_AEROBIC_TO_ANAEROBIC,
    IMPACT_HARD_LONG_AEROBIC_BASE,
    IMPACT_LONG_AEROBIC_BASE,
    IMPACT_AEROBIC,
    IMPACT_EASY_RECOVERY,
    UNKNOWN,
}

@JsonClass(generateAdapter = true)
data class RemoteCombinedWorkout(
    @Json(name = "achievements") val achievements: List<String>?,
    @Json(name = "activityId") val activityId: Int,
    @Json(name = "availableExtensions") val availableExtensions: List<String>?,
    @Json(name = "avgPace") val avgPace: Double?,
    @Json(name = "avgSpeed") val avgSpeed: Double?,
    @Json(name = "cadence") val cadence: RemoteSyncedWorkoutCadenceData?,
    @Json(name = "centerPosition") val centerPosition: RemotePoint?,
    /**
     * todo add support
     * @Json(name = "clientCalculatedAchievements")
     * val clientCalculatedAchievements: Any,
     */
    @Json(name = "comments") val comments: List<RemoteSyncedWorkoutComment>?,
    /**
     * todo add support
     * @Json(name = "coverPhoto")
     * val coverPhoto: Any,
     */
    @Json(name = "created") val created: Long,
    @Json(name = "cumulativeRecoveryTime") val cumulativeRecoveryTime: Int,
    @Json(name = "description") val description: String?,
    @Json(name = "energyConsumption") val energyConsumption: Double?,
    @Json(name = "extensions") val extensions: List<RemoteWorkoutExtension>?,
    @Json(name = "feedType") val feedType: String?,
    @Json(name = "fullname") val fullName: String?,
    @Json(name = "hrdata") val hrData: RemoteSyncedWorkoutHrData?,
    @Json(name = "key") val key: String,
    @Json(name = "lastModified") val lastModified: Long,
    @Json(name = "maxAltitude") val maxAltitude: Double?,
    @Json(name = "maxSpeed") val maxSpeed: Double?,
    @Json(name = "minAltitude") val minAltitude: Double?,
    @Json(name = "photos") val photos: List<RemoteSyncedWorkoutImage>?,
    @Json(name = "polyline") val polyline: String?,
    @Json(name = "rankings") val rankings: RemoteSyncedWorkoutRankings?,
    @Json(name = "reactionCount") val reactionCount: Int?,
    @Json(name = "recoveryTime") val recoveryTime: Long?,
    @Json(name = "sharingFlags") val sharingFlags: Int,
    @Json(name = "startPosition") val startPosition: RemotePoint?,
    @Json(name = "startTime") val startTime: Long,
    @Json(name = "stepCount") val stepCount: Int?,
    @Json(name = "stopPosition") val stopPosition: RemotePoint?,
    @Json(name = "stopTime") val stopTime: Long,
    @Json(name = "suuntoTags") val suuntoTags: List<RemoteSuuntoTag>?,
    @Json(name = "totalAscent") val totalAscent: Double?,
    @Json(name = "totalDescent") val totalDescent: Double?,
    @Json(name = "totalDistance") val totalDistance: Double?,
    @Json(name = "totalTime") val totalTime: Double,
    @Json(name = "tss") val tss: RemoteTSS?,
    @Json(name = "tssList") val tssList: List<RemoteTSS>?,
    @Json(name = "userPhoto") val userPhoto: String?,
    @Json(name = "userReacted") val userReacted: Boolean?,
    @Json(name = "userTagData") val userTagData: List<RemoteUserTag>?,
    @Json(name = "username") val username: String,
    @Json(name = "videos") val videos: List<RemoteVideo>?,
    @Json(name = "workoutKey") val workoutKey: String,
    @Json(name = "workoutName") val workoutName: String?,
    @Json(name = "estimatedFloorsClimbed") val estimatedFloorsClimbed: Int?
)

@JsonClass(generateAdapter = true)
data class RemoteSize(
    @Json(name = "height")
    val height: Int,
    @Json(name = "size")
    val size: String,
    @Json(name = "url")
    val url: String,
    @Json(name = "width")
    val width: Int
)

@JsonClass(generateAdapter = true)
data class RemoteCompetitionWorkoutDetails(
    @Json(name = "competition")
    val competition: RemoteCompetitionResult?,
    @Json(name = "status")
    val status: String,
    @Json(name = "showUserName")
    val showUserName: String?,
)
