package com.stt.android.remote.reactions

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class RemoteReaction(
    @<PERSON>son(name = "id") val key: String?,
    @<PERSON><PERSON>(name = "utfCode") val reaction: String,
    @<PERSON><PERSON>(name = "username") val userName: String,
    @<PERSON>son(name = "realname") val userRealOrUserName: String,
    @<PERSON>son(name = "profilePictureUrl") val userProfilePictureUrl: String?,
    @<PERSON><PERSON>(name = "timestamp") val timestamp: Long
)
