package com.stt.android.remote.smlzip

import okhttp3.ResponseBody
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.Path
import retrofit2.http.Streaming

private const val WORKOUTS_ENDPOINT = "workouts"

/**
 * RestAPI to fetch sml for a workout in a zipped format disabling automatic decompression in OkHttp.
 *
 * NOTE: Do not add new endpoints unless you want to keep the response compressed. [SmlRestApi] is created without
 * STTPayloadParserInterceptor, see BrandOkHttpConfigFactory.getStNoPayloadParserOkHttpConfig for more details.
 */
interface SmlRestApi {
    @Streaming
    @GET("$WORKOUTS_ENDPOINT/{workoutKey}/sml")
    @Headers("Accept-Encoding: gzip")
    suspend fun fetchSmlZip(
        @Path("workoutKey") workoutKey: String,
    ): Response<ResponseBody>
}
