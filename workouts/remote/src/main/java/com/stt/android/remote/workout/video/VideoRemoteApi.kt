package com.stt.android.remote.workout.video

import com.stt.android.remote.MediaTypes
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File
import javax.inject.Inject

class VideoRemoteApi
@Inject constructor(
    private val videoRestApi: VideoRestApi
) {
    suspend fun uploadVideo(
        workoutKey: String,
        timestamp: Long,
        latitude: Double?,
        longitude: Double?,
        totaltime: Long,
        md5hash: String,
        videoFile: File,
        thumbnail: File,
        width: Int,
        height: Int
    ): RemoteVideo {
        return videoRestApi.uploadVideo(
            workoutKey = workoutKey,
            timestamp = timestamp,
            latitude = latitude,
            longitude = longitude,
            totaltime = totaltime,
            md5hash = md5hash,
            videoFile = MultipartBody.Part.createFormData(
                "video",
                "video",
                videoFile.asRequestBody(MediaTypes.OCTET_STREAM_CONTENT_TYPE)
            ),
            thumbnail = MultipartBody.Part.createFormData(
                "thumbnail",
                "thumbnail",
                thumbnail.asRequestBody(MediaTypes.OCTET_STREAM_CONTENT_TYPE)
            ),
            width = width,
            height = height
        ).payloadOrThrow()
    }
}
