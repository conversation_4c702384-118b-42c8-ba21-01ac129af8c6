package com.stt.android.remote.workout

import com.stt.android.remote.response.AskoResponse
import retrofit2.http.GET
import retrofit2.http.Path
import retrofit2.http.Query

interface WorkoutRestV2Api {

    @GET("workouts/{username}/{workoutKey}/combined")
    suspend fun getCombinedWorkout(
        @Path("username") username: String,
        @Path("workoutKey") workoutKey: String,
        @Query("extensions") extensions: String?,
        @Query("additionalData") additionalData: String?
    ): AskoResponse<RemoteCombinedWorkout>

    @GET("workouts/{username}/public")
    suspend fun searchWorkouts(
        @Path("username") username: String,
        @Query("activityIds") activityIds: String?,
        @Query("suuntoTags") suuntoTags: String?,
        @Query("searchKeywords") searchKeywords: String,
        @Query("offset") offset: Int,
        @Query("limit") limit: Int,
    ): AskoResponse<List<RemoteSyncedWorkout>>
}
