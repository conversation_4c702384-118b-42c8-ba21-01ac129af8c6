package com.stt.android.remote.workout

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class RemoteWeChatWorkoutData(
    @Json(name = "workoutDataDetails")
    val workoutDataDetails: List<WeChatWorkoutDetailData>
)

@JsonClass(generateAdapter = true)
class WeChatWorkoutDetailData(
    @Json(name = "productModel")
    val productModel: String?,
    @Json(name = "sn")
    val sn: String?,
    @<PERSON>son(name = "duration")
    val duration: Double,
    @<PERSON><PERSON>(name = "endTime")
    val endTime: Long,
    @Json(name = "energy")
    val energy: Double,
    @<PERSON><PERSON>(name = "heartRateAvg")
    val heartRateAvg: Double,
    @<PERSON><PERSON>(name = "sportType")
    val sportType: Int,
    @Json(name = "startTime")
    val startTime: Long,
    @<PERSON><PERSON>(name = "totalDistance")
    val totalDistance: Double,
    @Json(name = "number")
    val count: Int?,
)
