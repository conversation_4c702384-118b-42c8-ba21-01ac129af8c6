package com.stt.android.remote.workout

import com.stt.android.remote.extensions.RemoteWorkoutExtension
import org.assertj.core.api.Assertions.assertThat
import org.junit.Test
import readFileContent

class RemoteSyncedWorkoutTest {

    private var moshiAdapter = createMoshiWithWorkoutExtensionAdapter().adapter(RemoteSyncedWorkout::class.java)

    @Test
    fun `Should parse correctly RemoteSyncedWorkout`() {
        // Prepare entities
        val remoteFitnessExtension = moshiAdapter.fromJson("workout/RemoteSyncedWorkout.json".readFileContent())
        // Verify
        assertThat(remoteFitnessExtension?.workoutKey).isEqualTo("5a70a9f7205b9b7290590d05")
        assertThat(remoteFitnessExtension?.totalDistance).isEqualTo(10970.0)
        assertThat(remoteFitnessExtension?.maxSpeed).isEqualTo(4.87)
        assertThat(remoteFitnessExtension?.activityId).isEqualTo(1)
        assertThat(remoteFitnessExtension?.avgSpeed).isEqualTo(2.86)
        assertThat(remoteFitnessExtension?.description).isNotBlank()

        assertThat(remoteFitnessExtension?.startPosition?.longitude).isEqualTo(25.074158333333333)
        assertThat(remoteFitnessExtension?.startPosition?.latitude).isEqualTo(60.224803333333334)
        assertThat(remoteFitnessExtension?.stopPosition?.longitude).isEqualTo(25.074931666666668)
        assertThat(remoteFitnessExtension?.stopPosition?.latitude).isEqualTo(60.22475333333333)
        assertThat(remoteFitnessExtension?.centerPosition?.longitude).isEqualTo(25.040875)
        assertThat(remoteFitnessExtension?.centerPosition?.latitude).isEqualTo(60.22304333333334)

        assertThat(remoteFitnessExtension?.startTime).isEqualTo(1517328718000)
        assertThat(remoteFitnessExtension?.stopTime).isEqualTo(1517332769000)
        assertThat(remoteFitnessExtension?.totalTime).isEqualTo(3837.0)
        assertThat(remoteFitnessExtension?.energyConsumption).isEqualTo(1224.0)
        assertThat(remoteFitnessExtension?.username).isNotBlank()

        assertThat(remoteFitnessExtension?.hrdata?.absoluteMaximum).isEqualTo(200)
        assertThat(remoteFitnessExtension?.hrdata?.workoutMaximum).isEqualTo(186)
        assertThat(remoteFitnessExtension?.hrdata?.workoutAverage).isEqualTo(162)

        assertThat(remoteFitnessExtension?.cadence?.avg).isEqualTo(0)
        assertThat(remoteFitnessExtension?.cadence?.max).isEqualTo(0)

        assertThat(remoteFitnessExtension?.viewCount).isEqualTo(0)
        assertThat(remoteFitnessExtension?.sharingFlags).isEqualTo(16)
        assertThat(remoteFitnessExtension?.stepCount).isEqualTo(0)
        assertThat(remoteFitnessExtension?.manuallyAdded).isNull()
        assertThat(remoteFitnessExtension?.polyline).isNotBlank()
        assertThat(remoteFitnessExtension?.pictureCount).isEqualTo(2)
        assertThat(remoteFitnessExtension?.totalAscent).isEqualTo(129.27)
        assertThat(remoteFitnessExtension?.totalDescent).isEqualTo(98.17)
        assertThat(remoteFitnessExtension?.recoveryTime).isEqualTo(89220)

        assertThat(remoteFitnessExtension?.comments).isNull()

        assertThat(remoteFitnessExtension?.photos).hasSize(2)
        assertThat(remoteFitnessExtension?.photos?.first()?.key).isEqualTo("5a70ad4ec3d639089e5949f5")
        assertThat(remoteFitnessExtension?.photos?.first()?.username).isNotBlank()
        assertThat(remoteFitnessExtension?.photos?.first()?.description).isBlank()
        assertThat(remoteFitnessExtension?.photos?.first()?.url).isNotBlank()
        assertThat(remoteFitnessExtension?.photos?.first()?.timestamp).isEqualTo(1517333838214)
        assertThat(remoteFitnessExtension?.photos?.first()?.totalTime).isEqualTo(2164.0)
        assertThat(remoteFitnessExtension?.photos?.first()?.width).isEqualTo(1920)
        assertThat(remoteFitnessExtension?.photos?.first()?.height).isEqualTo(962)
        assertThat(remoteFitnessExtension?.photos?.first()?.location).isNull()
        assertThat(remoteFitnessExtension?.photos?.first()?.workoutKey).isEqualTo("5a70a9f7205b9b7290590d05")

        assertThat(remoteFitnessExtension?.reactions).hasSize(1)
        assertThat(remoteFitnessExtension?.reactions?.first()?.utfCode).isEqualTo("LIKE")
        assertThat(remoteFitnessExtension?.reactions?.first()?.count).isEqualTo(4)
        assertThat(remoteFitnessExtension?.reactions?.first()?.userReacted).isFalse()

        assertThat(remoteFitnessExtension?.videos).isNull()

        assertThat(remoteFitnessExtension?.extensions?.filter { it is RemoteWorkoutExtension.RemoteSummaryExtension }).hasSize(1)
        // Extension parsing individually tested

        assertThat(remoteFitnessExtension?.rankings?.totalTimeOnRouteRanking?.originalNumberOfWorkouts).isEqualTo(1)
        assertThat(remoteFitnessExtension?.rankings?.totalTimeOnRouteRanking?.originalRanking).isEqualTo(1)
    }
}
