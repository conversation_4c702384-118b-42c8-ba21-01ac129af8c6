package com.stt.android.remote.extensions

import org.assertj.core.api.Assertions.assertThat
import org.junit.Test

class RemoteWorkoutExtensionTypeTest {
    @Test
    fun `textValues should return list of type values`() {
        val textValues = RemoteWorkoutExtension.Type.textValues

        assertThat(textValues[0])
            .isEqualTo(RemoteWorkoutExtension.Type.SUMMARY_EXTENSION.value)
        assertThat(textValues[1])
            .isEqualTo(RemoteWorkoutExtension.Type.FITNESS_EXTENSION.value)
        assertThat(textValues[2])
            .isEqualTo(RemoteWorkoutExtension.Type.SKI_EXTENSION.value)
        assertThat(textValues[3])
            .isEqualTo(RemoteWorkoutExtension.Type.INTENSITY_EXTENSION.value)
        assertThat(textValues[4])
            .isEqualTo(RemoteWorkoutExtension.Type.DIVE_HEADER_EXTENSION.value)
        assertThat(textValues[5])
            .isEqualTo(RemoteWorkoutExtension.Type.SWIMMING_HEADER_EXTENSION.value)
    }

    @Test
    fun `fromValue should return the right type`() {
        assertThat(RemoteWorkoutExtension.Type.fromValue(RemoteWorkoutExtension.Type.SWIMMING_HEADER_EXTENSION.value))
            .isEqualTo(RemoteWorkoutExtension.Type.SWIMMING_HEADER_EXTENSION)
        assertThat(RemoteWorkoutExtension.Type.fromValue(RemoteWorkoutExtension.Type.DIVE_HEADER_EXTENSION.value))
            .isEqualTo(RemoteWorkoutExtension.Type.DIVE_HEADER_EXTENSION)
        assertThat(RemoteWorkoutExtension.Type.fromValue(RemoteWorkoutExtension.Type.INTENSITY_EXTENSION.value))
            .isEqualTo(RemoteWorkoutExtension.Type.INTENSITY_EXTENSION)
        assertThat(RemoteWorkoutExtension.Type.fromValue(RemoteWorkoutExtension.Type.SKI_EXTENSION.value))
            .isEqualTo(RemoteWorkoutExtension.Type.SKI_EXTENSION)
        assertThat(RemoteWorkoutExtension.Type.fromValue(RemoteWorkoutExtension.Type.FITNESS_EXTENSION.value))
            .isEqualTo(RemoteWorkoutExtension.Type.FITNESS_EXTENSION)
        assertThat(RemoteWorkoutExtension.Type.fromValue(RemoteWorkoutExtension.Type.SUMMARY_EXTENSION.value))
            .isEqualTo(RemoteWorkoutExtension.Type.SUMMARY_EXTENSION)
    }

    @Test(expected = NoSuchElementException::class)
    fun `fromValue with unknown value throws exception`() {
        RemoteWorkoutExtension.Type.fromValue("foo")
    }
}
