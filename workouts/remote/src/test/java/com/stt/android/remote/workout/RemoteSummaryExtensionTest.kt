package com.stt.android.remote.workout

import com.stt.android.remote.di.RestApiFactory.createMoshi
import com.stt.android.remote.extensions.RemoteWorkoutExtension
import org.assertj.core.api.Assertions.assertThat
import org.junit.Test
import readFileContent

class RemoteSummaryExtensionTest {

    private var moshiAdapter = createMoshi().adapter(RemoteWorkoutExtension.RemoteSummaryExtension::class.java)

    @Test
    fun `Should parse correct RemoteSummary from json`() {
        // Prepare entities
        val remoteSummaryExtension = moshiAdapter.fromJson("workout/SummaryExtension.json".readFileContent())
        // Verify
        assertThat(remoteSummaryExtension?.type).isEqualTo(RemoteWorkoutExtension.Type.SUMMARY_EXTENSION.value)
        assertThat(remoteSummaryExtension?.pte).isEqualTo(1.0f)
        assertThat(remoteSummaryExtension?.feeling).isEqualTo(5)
        assertThat(remoteSummaryExtension?.avgTemperature).isEqualTo(303.62796f)
        assertThat(remoteSummaryExtension?.peakEpoc).isEqualTo(0.1f)
        assertThat(remoteSummaryExtension?.avgCadence).isEqualTo(0.65087837f)
        assertThat(remoteSummaryExtension?.ascentTime).isNull()
        assertThat(remoteSummaryExtension?.descentTime).isNull()
        assertThat(remoteSummaryExtension?.performanceLevel).isNull()
        assertThat(remoteSummaryExtension?.lacticThHr).isNull()
        assertThat(remoteSummaryExtension?.lacticThPace).isNull()
    }
}
