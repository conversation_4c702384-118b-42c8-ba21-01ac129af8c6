package com.stt.android.remote.workout

import com.stt.android.remote.extensions.RemoteWorkoutExtension
import org.assertj.core.api.Assertions.assertThat
import org.junit.Assert.assertEquals
import org.junit.Test
import readFileContent

class RemoteDiveHeaderExtensionTest {

    private var moshiAdapter = createMoshiWithWorkoutExtensionAdapter().adapter(RemoteWorkoutExtension::class.java)

    @Test
    fun `Should parse correct RemoteDiveHeaderExtension from json`() {
        // Prepare entities
        val remoteDiveHeaderExtension = moshiAdapter.fromJson("workout/DiveHeaderExtension.json".readFileContent())
            as RemoteWorkoutExtension.RemoteDiveHeaderExtension?
        // Verify
        assertThat(remoteDiveHeaderExtension?.type).isEqualTo(RemoteWorkoutExtension.Type.DIVE_HEADER_EXTENSION.value)
        assertThat(remoteDiveHeaderExtension?.maxDepth).isEqualTo(47.4f)
        assertThat(remoteDiveHeaderExtension?.pauseDuration).isEqualTo(20f)
        assertThat(remoteDiveHeaderExtension?.maxDepthTemperature).isEqualTo(276.25f)
        assertThat(remoteDiveHeaderExtension?.gasesUsed).isEqualTo(
            listOf(
                "Tx 22/35",
                "Tx 38/20",
                "Nx 99"
            )
        )
        assertThat(remoteDiveHeaderExtension?.gasQuantities).isEqualTo(
            mapOf(
                "1" to 345.3f,
                "2" to 0.0f,
                "3" to null
            )
        )

        assertEquals(
            remoteDiveHeaderExtension,
            moshiAdapter.fromJson(moshiAdapter.serializeNulls().toJson(remoteDiveHeaderExtension))
        )
    }
}
