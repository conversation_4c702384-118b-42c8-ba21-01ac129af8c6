package com.stt.android.remote.workout

import com.stt.android.remote.di.RestApiFactory.createMoshi
import com.stt.android.remote.extensions.RemoteWorkoutExtension
import org.assertj.core.api.Assertions.assertThat
import org.junit.Test
import readFileContent

class RemoteFitnessExtensionTest {

    private var moshiAdapter = createMoshi().adapter(RemoteWorkoutExtension.RemoteFitnessExtension::class.java)

    @Test
    fun `Should parse correct RemoteFitness from json`() {
        // Prepare entities
        val remoteFitnessExtension = moshiAdapter.fromJson("workout/FitnessExtension.json".readFileContent())
        // Verify
        assertThat(remoteFitnessExtension?.type).isEqualTo(RemoteWorkoutExtension.Type.FITNESS_EXTENSION.value)
        assertThat(remoteFitnessExtension?.maxHeartRate).isEqualTo(1)
        assertThat(remoteFitnessExtension?.vo2Max).isEqualTo(50.4f)
    }
}
