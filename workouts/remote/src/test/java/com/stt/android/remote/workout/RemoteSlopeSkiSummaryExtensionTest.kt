package com.stt.android.remote.workout

import com.stt.android.remote.di.RestApiFactory.createMoshi
import com.stt.android.remote.extensions.RemoteWorkoutExtension
import org.assertj.core.api.Assertions.assertThat
import org.junit.Test
import readFileContent

class RemoteSlopeSkiSummaryExtensionTest {

    private var moshiAdapter = createMoshi().adapter(RemoteWorkoutExtension.RemoteSlopeSkiSummaryExtension::class.java)

    @Test
    fun `Should parse correct RemoteSummary from json`() {
        // Prepare entities
        val remoteSlopeSkiSummaryExtension = moshiAdapter.fromJson("workout/SlopeSkiSummaryExtension.json".readFileContent())
        // Verify
        assertThat(remoteSlopeSkiSummaryExtension?.type).isEqualTo(RemoteWorkoutExtension.Type.SKI_EXTENSION.value)
        assertThat(remoteSlopeSkiSummaryExtension?.statistics?.descentDistanceMeters).isEqualTo(4.0)
        assertThat(remoteSlopeSkiSummaryExtension?.statistics?.descentDurationSeconds).isEqualTo(2)
        assertThat(remoteSlopeSkiSummaryExtension?.statistics?.descentMeters).isEqualTo(3.0)
        assertThat(remoteSlopeSkiSummaryExtension?.statistics?.maxSpeed).isEqualTo(5.0)
        assertThat(remoteSlopeSkiSummaryExtension?.statistics?.numberOfRuns).isEqualTo(1)
        assertThat(remoteSlopeSkiSummaryExtension?.runs?.first()?.descentDistanceMeters).isEqualTo(4.0)
        assertThat(remoteSlopeSkiSummaryExtension?.runs?.first()?.descentDurationSeconds).isEqualTo(2)
        assertThat(remoteSlopeSkiSummaryExtension?.runs?.first()?.descentMeters).isEqualTo(3.0)
        assertThat(remoteSlopeSkiSummaryExtension?.runs?.first()?.startTimestamp).isEqualTo(1)
    }
}
