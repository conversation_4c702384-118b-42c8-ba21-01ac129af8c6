package com.stt.android.remote.workout

import com.stt.android.remote.di.RestApiFactory.createMoshi
import com.stt.android.remote.extensions.RemoteWorkoutExtension
import org.assertj.core.api.Assertions.assertThat
import org.junit.Test
import readFileContent

class RemoteIntensityExtensionTest {

    private var moshiAdapter = createMoshi().adapter(RemoteWorkoutExtension.RemoteIntensityExtension::class.java)

    @Test
    fun `Should parse correct RemoteIntensity from json`() {
        // Prepare entities
        val remoteIntensityExtension = moshiAdapter.fromJson("workout/IntensityExtension.json".readFileContent())
        // Verify
        assertThat(remoteIntensityExtension?.type).isEqualTo(RemoteWorkoutExtension.Type.INTENSITY_EXTENSION.value)
        assertThat(remoteIntensityExtension?.physiologicalThresholds?.aerobic?.heartRate).isEqualTo(1)
        assertThat(remoteIntensityExtension?.physiologicalThresholds?.aerobic?.power).isEqualTo(3f)
        assertThat(remoteIntensityExtension?.physiologicalThresholds?.aerobic?.speed).isEqualTo(2f)
        assertThat(remoteIntensityExtension?.physiologicalThresholds?.anaerobic?.heartRate).isEqualTo(1)
        assertThat(remoteIntensityExtension?.physiologicalThresholds?.anaerobic?.power).isEqualTo(3f)
        assertThat(remoteIntensityExtension?.physiologicalThresholds?.anaerobic?.speed).isEqualTo(2f)
        assertThat(remoteIntensityExtension?.zones?.heartRate?.zone1?.lowerLimit).isEqualTo(2f)
        assertThat(remoteIntensityExtension?.zones?.heartRate?.zone1?.totalTime).isEqualTo(1f)
        assertThat(remoteIntensityExtension?.zones?.heartRate?.zone2?.lowerLimit).isEqualTo(2f)
        assertThat(remoteIntensityExtension?.zones?.heartRate?.zone2?.totalTime).isEqualTo(1f)
        assertThat(remoteIntensityExtension?.zones?.heartRate?.zone3?.lowerLimit).isEqualTo(2f)
        assertThat(remoteIntensityExtension?.zones?.heartRate?.zone3?.totalTime).isEqualTo(1f)
        assertThat(remoteIntensityExtension?.zones?.heartRate?.zone4?.lowerLimit).isEqualTo(2f)
        assertThat(remoteIntensityExtension?.zones?.heartRate?.zone4?.totalTime).isEqualTo(1f)
        assertThat(remoteIntensityExtension?.zones?.heartRate?.zone5?.lowerLimit).isEqualTo(2f)
        assertThat(remoteIntensityExtension?.zones?.heartRate?.zone5?.totalTime).isEqualTo(1f)
        assertThat(remoteIntensityExtension?.zones?.speed?.zone1?.lowerLimit).isEqualTo(2f)
        assertThat(remoteIntensityExtension?.zones?.speed?.zone1?.totalTime).isEqualTo(1f)
        assertThat(remoteIntensityExtension?.zones?.speed?.zone2?.lowerLimit).isEqualTo(2f)
        assertThat(remoteIntensityExtension?.zones?.speed?.zone2?.totalTime).isEqualTo(1f)
        assertThat(remoteIntensityExtension?.zones?.speed?.zone3?.lowerLimit).isEqualTo(2f)
        assertThat(remoteIntensityExtension?.zones?.speed?.zone3?.totalTime).isEqualTo(1f)
        assertThat(remoteIntensityExtension?.zones?.speed?.zone4?.lowerLimit).isEqualTo(2f)
        assertThat(remoteIntensityExtension?.zones?.speed?.zone4?.totalTime).isEqualTo(1f)
        assertThat(remoteIntensityExtension?.zones?.speed?.zone5?.lowerLimit).isEqualTo(2f)
        assertThat(remoteIntensityExtension?.zones?.speed?.zone5?.totalTime).isEqualTo(1f)
        assertThat(remoteIntensityExtension?.zones?.power?.zone1?.lowerLimit).isEqualTo(2f)
        assertThat(remoteIntensityExtension?.zones?.power?.zone1?.totalTime).isEqualTo(1f)
        assertThat(remoteIntensityExtension?.zones?.power?.zone2?.lowerLimit).isEqualTo(2f)
        assertThat(remoteIntensityExtension?.zones?.power?.zone2?.totalTime).isEqualTo(1f)
        assertThat(remoteIntensityExtension?.zones?.power?.zone3?.lowerLimit).isEqualTo(2f)
        assertThat(remoteIntensityExtension?.zones?.power?.zone3?.totalTime).isEqualTo(1f)
        assertThat(remoteIntensityExtension?.zones?.power?.zone4?.lowerLimit).isEqualTo(2f)
        assertThat(remoteIntensityExtension?.zones?.power?.zone4?.totalTime).isEqualTo(1f)
        assertThat(remoteIntensityExtension?.zones?.power?.zone5?.lowerLimit).isEqualTo(2f)
        assertThat(remoteIntensityExtension?.zones?.power?.zone5?.totalTime).isEqualTo(1f)
    }
}
