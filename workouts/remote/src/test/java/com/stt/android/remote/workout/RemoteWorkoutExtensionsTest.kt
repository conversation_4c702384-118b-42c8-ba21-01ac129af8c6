package com.stt.android.remote.workout

import com.stt.android.moshi.parseList
import com.stt.android.moshi.toJson
import com.stt.android.remote.extensions.RemoteWorkoutExtension
import org.assertj.core.api.Assertions.assertThat
import org.junit.Test

class RemoteWorkoutExtensionsTest {

    @Test
    fun `Should create a json correctly from given RemoteWorkoutExtensions and then parse it again`() {
        // Prepare entities
        val remoteWorkoutExtensions = WorkoutEntityFactory.makeWorkoutExtensionEntityList()
        // Convert extensions to json
        val moshi = createMoshiWithWorkoutExtensionAdapter()
        val jsonString = remoteWorkoutExtensions.toJson(moshi)
        // Parse it again
        val list = jsonString.parseList<RemoteWorkoutExtension>(moshi)
        // Verify as set to avoid sorting issues
        assertThat(list?.toSet()).isEqualTo(remoteWorkoutExtensions.toSet())
    }
}
