package com.stt.android.remote.smlzip

import kotlinx.coroutines.test.runTest
import okhttp3.ResponseBody.Companion.toResponseBody
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import retrofit2.Response

@RunWith(MockitoJUnitRunner::class)
class SmlRemoteApiTest {
    @Mock
    private lateinit var smlRestApi: SmlRestApi

    private lateinit var smlRemoteApi: SmlRemoteApi

    @Before
    fun setup() {
        smlRemoteApi = SmlRemoteApi(smlRestApi)
    }

    @Test
    fun `fetchSmlZip should make a call to rest api`() = runTest {
        whenever(smlRestApi.fetchSmlZip("key"))
            .thenReturn(Response.success("".toResponseBody(null)))

        smlRemoteApi.fetchSmlZip("key")
        verify(smlRestApi).fetchSmlZip("key")
    }
}
