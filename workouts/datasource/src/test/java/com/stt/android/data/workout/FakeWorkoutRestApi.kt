package com.stt.android.data.workout

import com.stt.android.remote.response.AskoResponse
import com.stt.android.remote.workout.RemoteCompetitionWorkoutDetails
import com.stt.android.remote.workout.RemotePublicWorkout
import com.stt.android.remote.workout.RemoteSyncedWorkout
import com.stt.android.remote.workout.RemoteUpdatedWorkout
import com.stt.android.remote.workout.RemoteWorkoutAttributes
import com.stt.android.remote.workout.RemoteWorkoutStats
import com.stt.android.remote.workout.WorkoutRestApi
import okhttp3.MultipartBody
import retrofit2.http.Part

/**
 * A fake implementation of [WorkoutRestApi] that always returns success results for testing
 *
 * Spy this class and stub its methods for different results.
 */
class FakeWorkoutRestApi : WorkoutRestApi {
    override suspend fun updateWorkoutAttributes(
        workoutKey: String,
        attributes: RemoteWorkoutAttributes
    ): AskoResponse<RemoteSyncedWorkout> {
        TODO("not implemented") // To change body of created functions use File | Settings | File Templates.
    }

    override suspend fun deleteWorkoutAttributes(
        workoutKey: String,
        attributes: String
    ): AskoResponse<RemoteSyncedWorkout> {
        TODO("not implemented") // To change body of created functions use File | Settings | File Templates.
    }

    override suspend fun fetchFolloweesWorkouts(
        since: Long,
        limit: Int,
        offset: Int
    ): AskoResponse<List<RemoteSyncedWorkout>> {
        TODO("not implemented") // To change body of created functions use File | Settings | File Templates.
    }

    override suspend fun fetchPublicWorkouts(
        lowerlat: Double,
        lowerlng: Double,
        upperlat: Double,
        upperlng: Double,
        limit: Int
    ): AskoResponse<List<RemotePublicWorkout>> {
        TODO("not implemented") // To change body of created functions use File | Settings | File Templates.
    }

    override suspend fun fetchOwnWorkouts(
        since: Long,
        limit: Int,
        offset: Int
    ): AskoResponse<List<RemoteSyncedWorkout>> {
        TODO("not implemented") // To change body of created functions use File | Settings | File Templates.
    }

    override suspend fun saveWorkout(
        @Part filePart: MultipartBody.Part?,
        @Part workoutExtensionsPart: MultipartBody.Part,
        @Part smlZip: MultipartBody.Part?,
    ): AskoResponse<RemoteSyncedWorkout> {
        TODO("not implemented") // To change body of created functions use File | Settings | File Templates.
    }

    override suspend fun getWorkout(workoutKey: String): AskoResponse<RemoteSyncedWorkout> {
        TODO("Not yet implemented")
    }

    override suspend fun deleteWorkout(workoutKey: String): AskoResponse<Boolean> {
        return AskoResponse(null, null, true)
    }

    @Deprecated("This is marked as deprecated on the backend. Replaced by PUT workouts/{workoutIdOrKey}/attributes")
    override suspend fun updateWorkouts(
        shareToFB: Boolean,
        workouts: List<RemoteUpdatedWorkout>
    ): AskoResponse<Map<String, Boolean>> {
        return AskoResponse(null, null, mapOf(workouts.first().workoutKey to true))
    }

    override suspend fun fetchWorkoutStatsForUser(username: String): AskoResponse<RemoteWorkoutStats> {
        return AskoResponse(
            null,
            null,
            RemoteWorkoutStats(
                totalDistanceSum = 0.0,
                totalTimeSum = 0.0,
                totalEnergyConsumptionKCalSum = 0.0,
                totalNumberOfWorkoutsSum = 0,
                activityTypeStats = emptyList(),
                totalDays = 0
            )
        )
    }

    override suspend fun fetchCompetitionWorkoutResult(
        totp: String,
        username: String,
        workoutKey: String
    ): AskoResponse<RemoteCompetitionWorkoutDetails> {
        TODO("not implemented")
    }

    override suspend fun fetchWorkoutsByUsername(
        username: String,
        offset: Int,
        limit: Int,
        withMediaOnly: Boolean
    ): AskoResponse<List<RemoteSyncedWorkout>> {
        TODO("Not yet implemented")
    }
}
