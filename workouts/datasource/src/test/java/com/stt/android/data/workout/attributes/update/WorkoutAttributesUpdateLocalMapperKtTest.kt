package com.stt.android.data.workout.attributes.update

import com.soy.algorithms.tss.TSSCalculationMethod
import com.stt.android.data.source.local.workout.attributes.LocalWorkoutAttributeTSS
import com.stt.android.data.source.local.workout.attributes.LocalWorkoutAttributeTSSCalculationMethod
import com.stt.android.data.source.local.workout.attributes.LocalWorkoutAttributes
import com.stt.android.data.source.local.workout.attributes.LocalWorkoutAttributesUpdate
import com.stt.android.data.source.local.workout.attributes.LocalWorkoutLocation
import com.stt.android.domain.workouts.attributes.DomainWorkoutAttributes
import com.stt.android.domain.workouts.attributes.DomainWorkoutAttributesUpdate
import com.stt.android.domain.workouts.attributes.DomainWorkoutLocation
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.domain.workouts.tss.TSS
import org.assertj.core.api.Assertions.assertThat
import org.junit.Test

class WorkoutAttributesUpdateLocalMapperKtTest {
    @Test
    fun toLocalEntity() {
        val expected = DomainWorkoutAttributesUpdate(
            1,
            "foo",
            DomainWorkoutAttributes(
                DomainWorkoutLocation(1.23, 4.56),
                TSS(123f, TSSCalculationMethod.MET, 2f, 3f, 4f),
                12.7,
                42.0,
                24.0,
                suuntoTags = listOf(SuuntoTag.COMMUTE)
            ),
            listOf("bar"),
            false
        )
        val actual = expected.toLocalEntity()
        assertThat(actual.workoutId).isEqualTo(expected.workoutId)
        assertThat(actual.ownerUsername).isEqualTo(expected.ownerUsername)
        assertThat(actual.attributes?.startPosition).isNotNull
        assertThat(actual.attributes?.startPosition?.latitude).isEqualTo(expected.attributes.startPosition?.latitude)
        assertThat(actual.attributes?.startPosition?.longitude).isEqualTo(expected.attributes.startPosition?.longitude)
        assertThat(actual.attributes?.tss).isNotNull
        assertThat(actual.attributes?.tss?.trainingStressScore).isEqualTo(expected.attributes.tss?.trainingStressScore)
        assertThat(actual.attributes?.tss?.calculationMethod).isEqualTo(LocalWorkoutAttributeTSSCalculationMethod.MET)
        assertThat(actual.attributes?.tss?.intensityFactor).isEqualTo(expected.attributes.tss?.intensityFactor)
        assertThat(actual.attributes?.tss?.normalizedPower).isEqualTo(expected.attributes.tss?.normalizedPower)
        assertThat(actual.attributes?.tss?.averageGradeAdjustedPace).isEqualTo(expected.attributes.tss?.averageGradeAdjustedPace)
        assertThat(actual.attributes?.maxSpeed).isEqualTo(expected.attributes.maxSpeed)
        assertThat(actual.attributes?.ascent).isEqualTo(expected.attributes.totalAscent)
        assertThat(actual.attributes?.descent).isEqualTo(expected.attributes.totalDescent)
        assertThat(actual.attributes?.suuntoTags).isEqualTo(expected.attributes.suuntoTags?.map { it.name })
        assertThat(actual.fields).isEqualTo(expected.fieldsToDelete)
    }

    @Test
    fun toDomainEntity() {
        val expected = LocalWorkoutAttributesUpdate(
            1,
            "foo",
            LocalWorkoutAttributes(
                LocalWorkoutLocation(1.23, 4.56),
                LocalWorkoutAttributeTSS(123f, LocalWorkoutAttributeTSSCalculationMethod.MET, 2f, 3f, 4f),
                12.7,
                42.0,
                24.0,
                listOf(SuuntoTag.COMMUTE.name)
            ),
            listOf("bar"),
            false
        )
        val actual = expected.toDomainEntity()
        assertThat(actual.workoutId).isEqualTo(expected.workoutId)
        assertThat(actual.ownerUsername).isEqualTo(expected.ownerUsername)
        assertThat(actual.attributes).isNotNull
        assertThat(actual.attributes.startPosition).isNotNull
        assertThat(actual.attributes.startPosition?.latitude).isEqualTo(expected.attributes?.startPosition?.latitude)
        assertThat(actual.attributes.startPosition?.longitude).isEqualTo(expected.attributes?.startPosition?.longitude)
        assertThat(actual.attributes.tss).isNotNull
        assertThat(actual.attributes.tss?.trainingStressScore).isEqualTo(expected.attributes?.tss?.trainingStressScore)
        assertThat(actual.attributes.tss?.calculationMethod).isEqualTo(TSSCalculationMethod.MET)
        assertThat(actual.attributes.tss?.intensityFactor).isEqualTo(expected.attributes?.tss?.intensityFactor)
        assertThat(actual.attributes.tss?.normalizedPower).isEqualTo(expected.attributes?.tss?.normalizedPower)
        assertThat(actual.attributes.tss?.averageGradeAdjustedPace).isEqualTo(expected.attributes?.tss?.averageGradeAdjustedPace)
        assertThat(actual.attributes.suuntoTags).isEqualTo(expected.attributes?.suuntoTags?.map { SuuntoTag.valueOf(it) })
        assertThat(actual.fieldsToDelete).isEqualTo(expected.fields)
    }
}
