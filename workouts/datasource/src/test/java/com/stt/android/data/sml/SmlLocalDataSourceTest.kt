package com.stt.android.data.sml

import com.stt.android.data.source.local.smljson.SMLFileStorage
import com.stt.android.domain.workouts.extensions.SMLExtension
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.anyInt
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import kotlin.test.assertContentEquals
import kotlin.test.assertNull

@RunWith(MockitoJUnitRunner::class)
class SmlLocalDataSourceTest {
    @Mock
    private lateinit var smlFileStorage: SMLFileStorage

    private lateinit var smlLocalDataSource: SmlLocalDataSource

    @Before
    fun setup() {
        smlLocalDataSource = SmlLocalDataSource(smlFileStorage)
    }

    @Test
    fun `fetchSmlExtension should access file storage read`() = runTest {
        smlLocalDataSource.fetchSmlExtension(1, "key")

        verify(smlFileStorage).readSmlFromFile(1)
    }

    @Test
    fun `saveSmlExtension should access file storage write`() = runTest {
        val smlExtension = SMLExtension(1, byteArrayOf(1, 2, 3))
        smlLocalDataSource.saveSmlExtension(smlExtension)

        verify(smlFileStorage).writeSmlToFile(anyInt(), any())
    }

    @Test
    fun `fetchSmlExtension has value if file storage returns bytearray`() = runTest {
        whenever(smlFileStorage.readSmlFromFile(anyInt())).thenReturn(byteArrayOf(1, 2, 3))

        val actual = smlLocalDataSource.fetchSmlExtension(1, "key")

        assertContentEquals(byteArrayOf(1, 2, 3), actual?.smlZip)
    }

    @Test
    fun `fetchSmlExtension has value with null zip if file storage returns empty bytearray`() = runTest {
        whenever(smlFileStorage.readSmlFromFile(anyInt())).thenReturn(byteArrayOf())
        assertNull(smlLocalDataSource.fetchSmlExtension(1, "key")?.smlZip)
    }

    @Test
    fun `fetchSmlExtension is empty if file storage returns null`() = runTest {
        whenever(smlFileStorage.readSmlFromFile(anyInt())).thenReturn(null)
        smlLocalDataSource.fetchSmlExtension(1, "key")
    }
}
