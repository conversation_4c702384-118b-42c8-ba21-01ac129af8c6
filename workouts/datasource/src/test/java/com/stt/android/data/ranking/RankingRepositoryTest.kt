package com.stt.android.data.ranking

import com.stt.android.data.source.local.RankingDao
import com.stt.android.data.source.local.ranking.LocalRanking
import com.stt.android.domain.ranking.Ranking
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import kotlin.test.assertEquals

@RunWith(MockitoJUnitRunner::class)
class RankingRepositoryTest {

    private lateinit var repository: RankingRepository
    private lateinit var mapper: RankingMapper

    @Mock
    private lateinit var rankingDao: RankingDao

    @Before
    fun setup() {
        mapper = RankingMapper()

        repository = RankingRepository(rankingDao, mapper)
    }

    @Test
    fun `fetch rankings by workout key`() = runTest {
        val workoutKey = "workout-key"
        val rankings = listOf(
            LocalRanking(key = workoutKey, type = "is nice", ranking = 1, numberOfWorkouts = 1)
        )

        whenever(rankingDao.findAllByWorkoutKey(any())).thenReturn(flowOf(rankings))

        assertEquals(
            rankings.map(mapper.toDomainEntity()),
            repository.fetchRankings(workoutKey).first(),
        )
    }

    @Test
    fun `insert rankings`() = runTest {
        val rankings = listOf(
            Ranking(key = "workoutkey", type = "great", ranking = 1, numberOfWorkouts = 1)
        )

        repository.insertRankings(rankings)
    }

    @Test
    fun `delete rankings by workout key`() = runTest {
        val workoutKey = "workout-key"

        repository.deleteRankings(workoutKey)
    }
}
