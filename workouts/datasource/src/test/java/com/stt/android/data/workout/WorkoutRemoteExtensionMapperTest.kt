package com.stt.android.data.workout

import com.stt.android.domain.user.workoutextension.FitnessExtension
import com.stt.android.domain.user.workoutextension.IntensityExtension
import com.stt.android.domain.user.workoutextension.SlopeSkiSummary
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.remote.extensions.RemoteWorkoutExtension
import com.stt.android.remote.extensions.toRemoteWorkoutExtensionType
import org.junit.Test
import kotlin.test.assertTrue

class WorkoutRemoteExtensionMapperTest {
    private val mapper = WorkoutRemoteExtensionMapper()

    @Test
    fun `map Domain Summary workout extension to remote`() {
        // prepare
        val item = WorkoutEntityFactory.makeDomainExtensionEntity(RemoteWorkoutExtension.Type.SUMMARY_EXTENSION)
        // verify
        assertTrue(item.isEqual(mapper.toDataEntity()(item)))
    }

    @Test
    fun `map Domain Fitness extension to remote`() {
        // prepare
        val item = WorkoutEntityFactory.makeDomainExtensionEntity(RemoteWorkoutExtension.Type.FITNESS_EXTENSION)
        // verify
        assertTrue(item.isEqual(mapper.toDataEntity()(item)))
    }

    @Test
    fun `map Domain Ski extension to remote`() {
        // prepare
        val item = WorkoutEntityFactory.makeDomainExtensionEntity(RemoteWorkoutExtension.Type.SKI_EXTENSION)
        // verify
        assertTrue(item.isEqual(mapper.toDataEntity()(item)))
    }

    @Test
    fun `map Domain Intensity extension to remote`() {
        // prepare
        val item = WorkoutEntityFactory.makeDomainExtensionEntity(RemoteWorkoutExtension.Type.INTENSITY_EXTENSION) as IntensityExtension
        val itemWithBpmHrZones = item.run {
            IntensityExtension(workoutId, intensityZones.copy(hr = intensityZones.hr?.hzToBpm()))
        }
        // verify
        assertTrue(itemWithBpmHrZones.isEqual(mapper.toDataEntity()(item)))
    }

    @Test
    fun `map Remote Summary workout extension to domain`() {
        // prepare
        val item = WorkoutEntityFactory.makeRemoteExtensionEntity(RemoteWorkoutExtension.Type.SUMMARY_EXTENSION)
        // verify
        assertTrue(mapper.toDomainEntity(1)(item)?.isEqual(item) == true)
    }

    @Test
    fun `map Remote Fitness extension to domain`() {
        // prepare
        val item = WorkoutEntityFactory.makeRemoteExtensionEntity(RemoteWorkoutExtension.Type.FITNESS_EXTENSION)
        // verify
        assertTrue(mapper.toDomainEntity(1)(item)?.isEqual(item) == true)
    }

    @Test
    fun `map Remote Ski extension to domain`() {
        // prepare
        val item = WorkoutEntityFactory.makeRemoteExtensionEntity(RemoteWorkoutExtension.Type.SKI_EXTENSION)
        // verify
        assertTrue(mapper.toDomainEntity(1)(item)?.isEqual(item) == true)
    }

    @Test
    fun `map Remote Intensity extension to domain`() {
        // prepare
        val item = WorkoutEntityFactory.makeRemoteExtensionEntity(RemoteWorkoutExtension.Type.INTENSITY_EXTENSION)
        // verify
        assertTrue(mapper.toDomainEntity(1)(item)?.isEqual(item) == true)
    }

    private fun WorkoutExtension.isEqual(remoteWorkout: RemoteWorkoutExtension): Boolean {
        return when (this) {
            is SummaryExtension -> {
                remoteWorkout is RemoteWorkoutExtension.RemoteSummaryExtension &&
                    this.type == remoteWorkout.type.toRemoteWorkoutExtensionType().toDomainType() &&
                    this.ascentTime == remoteWorkout.ascentTime &&
                    this.avgCadence == remoteWorkout.avgCadence &&
                    this.avgPower == remoteWorkout.avgPower &&
                    this.avgSpeed == remoteWorkout.avgSpeed &&
                    this.avgTemperature == remoteWorkout.avgTemperature &&
                    this.descentTime == remoteWorkout.descentTime &&
                    this.feeling == remoteWorkout.feeling &&
                    this.peakEpoc == remoteWorkout.peakEpoc &&
                    this.performanceLevel == remoteWorkout.performanceLevel &&
                    this.pte == remoteWorkout.pte
            }
            is FitnessExtension -> {
                remoteWorkout is RemoteWorkoutExtension.RemoteFitnessExtension &&
                    this.type == remoteWorkout.type.toRemoteWorkoutExtensionType().toDomainType() &&
                    this.maxHeartRate == remoteWorkout.maxHeartRate &&
                    this.vo2Max == remoteWorkout.vo2Max
            }
            is SlopeSkiSummary -> {
                remoteWorkout is RemoteWorkoutExtension.RemoteSlopeSkiSummaryExtension &&
                    this.type == remoteWorkout.type.toRemoteWorkoutExtensionType().toDomainType() &&
                    this.descentDistanceInMeters == remoteWorkout.statistics.descentDistanceMeters &&
                    this.descentDurationInMilliseconds / 1000 == remoteWorkout.statistics.descentDurationSeconds &&
                    this.descentsInMeters == remoteWorkout.statistics.descentMeters &&
                    this.maxSpeedMetersPerSecond == remoteWorkout.statistics.maxSpeed
            }
            is IntensityExtension -> {
                remoteWorkout is RemoteWorkoutExtension.RemoteIntensityExtension &&
                    this.type == remoteWorkout.type.toRemoteWorkoutExtensionType().toDomainType() &&
                    this.intensityZones.hr == remoteWorkout.zones?.heartRate?.toDomain() &&
                    this.intensityZones.power == remoteWorkout.zones?.power?.toDomain() &&
                    this.intensityZones.speed == remoteWorkout.zones?.speed?.toDomain()
            }
            else -> false
        }
    }
}
