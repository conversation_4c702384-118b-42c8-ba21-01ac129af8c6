package com.stt.android.data.workout.videos

import com.stt.android.domain.Point
import com.stt.android.domain.workouts.videos.Video
import com.stt.android.domain.workouts.videos.VideoDataSource

fun createVideo(): Video {
    return Video(
        id = 1,
        key = "key",
        workoutId = 2,
        workoutKey = "workoutkey",
        username = "username",
        totalTime = 3,
        timestamp = 4,
        description = "description",
        location = Point(1.2, 3.4),
        url = "url",
        thumbnailUrl = "thumbUrl",
        width = 5,
        height = 6,
        filename = "filename",
        thumbnailFilename = "thumbnFilename",
        locallyChanged = true
    )
}

class FakeVideoDataSource : VideoDataSource {
    override suspend fun deleteVideo(video: Video) {
        TODO("not implemented") // To change body of created functions use File | Settings | File Templates.
    }

    override suspend fun findByWorkoutId(workoutId: Int): List<Video> {
        return listOf(createVideo())
    }

    override suspend fun findUnsyncedVideos(username: String): List<Video> {
        return listOf(createVideo())
    }

    override suspend fun saveVideo(video: Video) {
    }
}
