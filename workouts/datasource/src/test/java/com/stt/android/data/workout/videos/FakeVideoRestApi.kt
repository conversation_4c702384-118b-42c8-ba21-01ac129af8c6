package com.stt.android.data.workout.videos

import com.stt.android.remote.response.AskoResponse
import com.stt.android.remote.workout.video.RemoteVideo
import com.stt.android.remote.workout.video.VideoRestApi
import okhttp3.MultipartBody

class FakeVideoRestApi : VideoRestApi {
    override suspend fun uploadVideo(
        workoutKey: String,
        timestamp: Long,
        latitude: Double?,
        longitude: Double?,
        totaltime: Long,
        md5hash: String,
        width: Int,
        height: Int,
        videoFile: MultipartBody.Part,
        thumbnail: MultipartBody.Part
    ): AskoResponse<RemoteVideo> {
        val payload = RemoteVideo(
            location = null,
            workoutKey = null,
            timestamp = 123L,
            totalTime = 0,
            description = null,
            username = null,
            url = null,
            height = 0,
            width = 0,
            thumbnailUrl = null,
            key = "serverkey"
        )
        return AskoResponse(null, null, payload)
    }
}
