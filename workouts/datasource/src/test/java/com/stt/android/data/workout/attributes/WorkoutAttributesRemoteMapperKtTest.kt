package com.stt.android.data.workout.attributes

import com.soy.algorithms.tss.TSSCalculationMethod
import com.stt.android.data.workout.toDomain
import com.stt.android.domain.workouts.attributes.DomainWorkoutAttributes
import com.stt.android.domain.workouts.attributes.DomainWorkoutLocation
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.domain.workouts.tss.TSS
import com.stt.android.remote.workout.RemoteSuuntoTag
import com.stt.android.remote.workout.RemoteTSS
import com.stt.android.remote.workout.RemoteTSSCalculationMethod
import com.stt.android.remote.workout.RemoteWorkoutAttributes
import com.stt.android.remote.workout.RemoteWorkoutLocation
import org.assertj.core.api.Assertions.assertThat
import org.junit.Test

class WorkoutAttributesRemoteMapperKtTest {
    @Test
    fun toRemoteEntity() {
        val expected = DomainWorkoutAttributes(
            DomainWorkoutLocation(1.23, 4.56),
            TSS(123f, TSSCalculationMethod.HR, 2f, 3f, 4f),
            12.7,
            42.0,
            24.0,
            suuntoTags = listOf(SuuntoTag.COMMUTE)
        )
        val actual = expected.toRemoteEntity()
        assertThat(actual.startPosition).isNotNull
        assertThat(actual.startPosition?.latitude).isEqualTo(expected.startPosition?.latitude)
        assertThat(actual.startPosition?.longitude).isEqualTo(expected.startPosition?.longitude)
        assertThat(actual.tss).isNotNull
        assertThat(actual.tss?.trainingStressScore).isEqualTo(expected.tss?.trainingStressScore)
        assertThat(actual.tss?.calculationMethod).isEqualTo(RemoteTSSCalculationMethod.HR)
        assertThat(actual.tss?.intensityFactor).isEqualTo(expected.tss?.intensityFactor)
        assertThat(actual.tss?.normalizedPower).isEqualTo(expected.tss?.normalizedPower)
        assertThat(actual.tss?.averageGradeAdjustedPace).isEqualTo(expected.tss?.averageGradeAdjustedPace)
        assertThat(actual.maxSpeed).isEqualTo(expected.maxSpeed)
        assertThat(actual.totalAscent).isEqualTo(expected.totalAscent)
        assertThat(actual.totalDescent).isEqualTo(expected.totalDescent)
        assertThat(actual.suuntoTags).isEqualTo(expected.suuntoTags?.map { it.toRemote() })
    }

    @Test
    fun toDomainEntity() {
        val expected = RemoteWorkoutAttributes(
            RemoteWorkoutLocation(1.23, 4.56),
            RemoteTSS(123f, RemoteTSSCalculationMethod.HR, 2f, 3f, 4f),
            12.7,
            42.0,
            24.0,
            suuntoTags = listOf(RemoteSuuntoTag.COMMUTE),
            userTags = emptyList()
        )
        val actual = expected.toDomainEntity()
        assertThat(actual.startPosition).isNotNull
        assertThat(actual.startPosition?.latitude).isEqualTo(expected.startPosition?.latitude)
        assertThat(actual.startPosition?.longitude).isEqualTo(expected.startPosition?.longitude)
        assertThat(actual.tss).isNotNull
        assertThat(actual.tss?.trainingStressScore).isEqualTo(expected.tss?.trainingStressScore)
        assertThat(actual.tss?.calculationMethod).isEqualTo(TSSCalculationMethod.HR)
        assertThat(actual.tss?.intensityFactor).isEqualTo(expected.tss?.intensityFactor)
        assertThat(actual.tss?.normalizedPower).isEqualTo(expected.tss?.normalizedPower)
        assertThat(actual.tss?.averageGradeAdjustedPace).isEqualTo(expected.tss?.averageGradeAdjustedPace)
        assertThat(actual.maxSpeed).isEqualTo(expected.maxSpeed)
        assertThat(actual.totalAscent).isEqualTo(expected.totalAscent)
        assertThat(actual.totalDescent).isEqualTo(expected.totalDescent)
        assertThat(actual.suuntoTags).isEqualTo(expected.suuntoTags?.map { it.toDomain() })
    }
}
