package com.stt.android.data.workout

import com.stt.android.remote.response.AskoResponse
import com.stt.android.remote.workout.RemoteCombinedWorkout
import com.stt.android.remote.workout.RemoteSyncedWorkout
import com.stt.android.remote.workout.WorkoutRestV2Api

class FakeWorkoutRestV2Api : WorkoutRestV2Api {
    override suspend fun getCombinedWorkout(
        username: String,
        workoutKey: String,
        extensions: String?,
        additionalData: String?
    ): AskoResponse<RemoteCombinedWorkout> {
        TODO("Not yet implemented")
    }

    override suspend fun searchWorkouts(
        username: String,
        activityIds: String?,
        suuntoTags: String?,
        searchKeywords: String,
        offset: Int,
        limit: Int
    ): AskoResponse<List<RemoteSyncedWorkout>> {
        TODO("Not yet implemented")
    }
}
