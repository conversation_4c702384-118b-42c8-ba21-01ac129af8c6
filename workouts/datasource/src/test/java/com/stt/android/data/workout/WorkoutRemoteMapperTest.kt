package com.stt.android.data.workout

import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.remote.workout.RemoteSyncedWorkout
import org.junit.Test
import kotlin.test.assertTrue

class WorkoutRemoteMapperTest {
    private val mapper = WorkoutRemoteExtensionMapper()

    @Test
    fun `map Remote workout to domain`() {
        // prepare
        val item = WorkoutEntityFactory.makeRemoteSyncedWorkout()
        // verify
        assertTrue(item.toDomainWorkout(mapper, 1).header.isEqual(item))
    }

    private fun WorkoutHeader.isEqual(remoteWorkout: RemoteSyncedWorkout): Boolean {
        val remoteAverageHRPercentage =
            if (remoteWorkout.hrdata != null && remoteWorkout.hrdata!!.absoluteMaximum > 0) {
                remoteWorkout.hrdata!!.workoutAverage.toDouble() / remoteWorkout.hrdata!!.absoluteMaximum.toDouble() * 100.0
            } else {
                0.toDouble()
            }
        val remoteMaxHRPercentage =
            if (remoteWorkout.hrdata != null && remoteWorkout.hrdata!!.absoluteMaximum > 0) {
                remoteWorkout.hrdata!!.workoutMaximum.toDouble() / remoteWorkout.hrdata!!.absoluteMaximum.toDouble() * 100.0
            } else {
                0.toDouble()
            }

        return this.manuallyAdded == remoteWorkout.manuallyAdded &&
            this.activityTypeId == remoteWorkout.activityId &&
            this.averageCadence == remoteWorkout.cadence?.avg &&
            this.maxCadence == remoteWorkout.cadence?.max &&
            this.avgSpeed == remoteWorkout.avgSpeed &&
            this.startPosition?.latitude == remoteWorkout.startPosition?.latitude &&
            this.startPosition?.longitude == remoteWorkout.startPosition?.longitude &&
            this.centerPosition?.latitude == remoteWorkout.centerPosition?.latitude &&
            this.centerPosition?.longitude == remoteWorkout.centerPosition?.longitude &&
            this.stopPosition?.latitude == remoteWorkout.stopPosition?.latitude &&
            this.stopPosition?.longitude == remoteWorkout.stopPosition?.longitude &&
            this.commentCount == remoteWorkout.comments?.size ?: 0 &&
            this.description == remoteWorkout.description &&
            this.energyConsumption == remoteWorkout.energyConsumption &&
            this.key == remoteWorkout.workoutKey &&
            this.totalAscent == remoteWorkout.totalAscent &&
            this.totalDescent == remoteWorkout.totalDescent &&
            this.totalDistance == remoteWorkout.totalDistance &&
            this.maxSpeed == remoteWorkout.maxSpeed &&
            this.startTime == remoteWorkout.startTime &&
            this.stopTime == remoteWorkout.stopTime &&
            this.totalTime == remoteWorkout.totalTime &&
            this.heartRateAverage == remoteWorkout.hrdata?.workoutAverage?.toDouble() &&
            this.heartRateAvgPercentage == remoteAverageHRPercentage &&
            this.heartRateMax == remoteWorkout.hrdata?.workoutMaximum?.toDouble() &&
            this.heartRateMaxPercentage == remoteMaxHRPercentage &&
            this.heartRateUserSetMax == remoteWorkout.hrdata?.absoluteMaximum?.toDouble() &&
            this.pictureCount == remoteWorkout.photos?.size ?: remoteWorkout.pictureCount &&
            this.viewCount == remoteWorkout.viewCount &&
            this.commentCount == remoteWorkout.comments?.size ?: 0 &&
            this.sharingFlags == remoteWorkout.sharingFlags &&
            this.stepCount == remoteWorkout.stepCount &&
            this.reactionCount == remoteWorkout.reactions?.size ?: 0 &&
            this.totalAscent == remoteWorkout.totalAscent &&
            this.totalDescent == remoteWorkout.totalDescent &&
            this.recoveryTime == remoteWorkout.recoveryTime &&
            this.seen
    }
}
