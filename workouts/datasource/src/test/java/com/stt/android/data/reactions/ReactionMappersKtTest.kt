package com.stt.android.data.reactions

import com.stt.android.remote.reactions.RemoteReaction
import org.assertj.core.api.Assertions.assertThat
import org.junit.Test

class ReactionMappersKtTest {

    @Test
    fun `map remote reaction to to Domain entity successfully`() {
        val remoteReaction = RemoteReaction(
            key = "key",
            reaction = "foo",
            userName = "bar",
            userRealOrUserName = "barfoo",
            userProfilePictureUrl = "foobar",
            timestamp = 131L
        )

        val workoutKey = "nicekey"
        val actual = remoteReaction.toDomainEntity(workoutKey)

        assertThat(actual.key).isEqualTo(remoteReaction.key)
        assertThat(actual.reaction).isEqualTo(remoteReaction.reaction)
        assertThat(actual.timestamp).isEqualTo(remoteReaction.timestamp)
        assertThat(actual.userName).isEqualTo(remoteReaction.userName)
        assertThat(actual.userRealOrUsername).isEqualTo(remoteReaction.userRealOrUserName)
        assertThat(actual.workoutKey).isEqualTo(workoutKey)
        assertThat(actual.userProfilePictureUrl).isEqualTo(remoteReaction.userProfilePictureUrl)
    }
}
