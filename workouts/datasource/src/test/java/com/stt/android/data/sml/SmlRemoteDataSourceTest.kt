package com.stt.android.data.sml

import com.stt.android.remote.smlzip.SmlRemoteApi
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

@RunWith(MockitoJUnitRunner::class)
class SmlRemoteDataSourceTest {

    @Mock
    private lateinit var smlRemoteApi: SmlRemoteApi

    @Mock
    private lateinit var smlRemoteDataSource: SmlRemoteDataSource

    @Before
    fun setup() {
        smlRemoteDataSource = SmlRemoteDataSource(smlRemoteApi)
    }

    @Test
    fun `should access smlRemoteApi when fetching sml extension`() = runTest {
        whenever(smlRemoteApi.fetchSmlZip("key"))
            .thenReturn(null)

        smlRemoteDataSource.fetchSmlExtension(1, "key")
        verify(smlRemoteApi).fetchSmlZip("key")
    }
}
