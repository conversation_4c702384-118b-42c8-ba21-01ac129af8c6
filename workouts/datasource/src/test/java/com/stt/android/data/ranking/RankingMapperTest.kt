package com.stt.android.data.ranking

import com.stt.android.data.source.local.ranking.LocalRanking
import com.stt.android.domain.ranking.Ranking
import org.assertj.core.api.Assertions.assertThat
import org.junit.Test

class RankingMapperTest {
    private val mapper = RankingMapper()

    @Test
    fun `map local ranking to domain ranking successfully`() {
        val expected = LocalRanking(
            key = "bla",
            type = "isnice",
            ranking = 1,
            numberOfWorkouts = 1
        )

        val actual = mapper.toDomainEntity().invoke(expected)

        assertThat(actual.id).isEqualTo(expected.id)
        assertThat(actual.key).isEqualTo(expected.key)
        assertThat(actual.type).isEqualTo(expected.type)
        assertThat(actual.ranking).isEqualTo(expected.ranking)
        assertThat(actual.numberOfWorkouts).isEqualTo(expected.numberOfWorkouts)
    }

    @Test
    fun `map domain ranking to local ranking successfully`() {
        val expected = Ranking(
            key = "bla",
            type = "isnice",
            ranking = 1,
            numberOfWorkouts = 1
        )

        val actual = mapper.toDataEntity().invoke(expected)

        assertThat(actual.id).isEqualTo(expected.id)
        assertThat(actual.key).isEqualTo(expected.key)
        assertThat(actual.type).isEqualTo(expected.type)
        assertThat(actual.ranking).isEqualTo(expected.ranking)
        assertThat(actual.numberOfWorkouts).isEqualTo(expected.numberOfWorkouts)
    }
}
