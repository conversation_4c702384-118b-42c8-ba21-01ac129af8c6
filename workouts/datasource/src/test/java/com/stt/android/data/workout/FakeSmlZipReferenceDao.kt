package com.stt.android.data.workout

import com.stt.android.data.source.local.smlzip.LocalSMLZipReference
import com.stt.android.data.source.local.smlzip.SMLZipReferenceDao

class FakeSmlZipReferenceDao : SMLZipReferenceDao {
    override suspend fun insert(smlZipReference: LocalSMLZipReference) {
    }

    override suspend fun remove(smlZipReference: LocalSMLZipReference) {
    }

    override suspend fun findById(id: Int): LocalSMLZipReference? {
        return LocalSMLZipReference(1, 1L, "path", "key", 1, "message")
    }
}
