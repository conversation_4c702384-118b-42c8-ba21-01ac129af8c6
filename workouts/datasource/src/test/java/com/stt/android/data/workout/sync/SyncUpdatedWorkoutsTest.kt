package com.stt.android.data.workout.sync

import com.stt.android.data.workout.WorkoutEntityFactory
import com.stt.android.data.workout.toRemoteUpdatedWorkout
import com.stt.android.domain.workouts.WorkoutHeaderDataSource
import com.stt.android.exceptions.remote.AskoError
import com.stt.android.remote.workout.WorkoutRemoteApi
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.whenever

@RunWith(MockitoJUnitRunner::class)
class SyncUpdatedWorkoutsTest {
    @Mock
    private lateinit var workoutHeaderDataSource: WorkoutHeaderDataSource

    @Mock
    private lateinit var workoutRemoteApi: WorkoutRemoteApi

    private lateinit var syncUpdatedWorkouts: SyncUpdatedWorkouts

    @Before
    fun setup() {
        syncUpdatedWorkouts = SyncUpdatedWorkouts(
            workoutHeaderDataSource,
            workoutRemoteApi
        )
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    @Test
    fun `no workouts to update returns 0 to 0`() = runTest {
        whenever(workoutHeaderDataSource.getLocallyModifiedWorkouts()).thenReturn(emptyList())

        assertThat(syncUpdatedWorkouts()).isEqualTo(0 to 0)
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    @Test
    fun `update 4 workouts returns 4 to 0`() = runTest {
        val workouts = (0..3).map { WorkoutEntityFactory.makeDomainWorkout(incrementKey = true) }

        whenever(workoutHeaderDataSource.getLocallyModifiedWorkouts()).thenReturn(workouts)
        whenever(workoutRemoteApi.updateWorkouts(any(), eq(false))).thenReturn(workouts.associate { it.key!! to true })

        assertThat(syncUpdatedWorkouts()).isEqualTo(4 to 0)
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    @Test
    fun `update one workout should return 0 to 1`() = runTest {
        val workouts = listOf(WorkoutEntityFactory.makeDomainWorkout())

        whenever(workoutHeaderDataSource.getLocallyModifiedWorkouts()).thenReturn(workouts)
        whenever(workoutRemoteApi.updateWorkouts(workouts.map { it.toRemoteUpdatedWorkout() }, false))
            .thenReturn(mapOf(workouts.first().key!! to false))

        assertThat(syncUpdatedWorkouts()).isEqualTo(0 to 1)
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    @Test
    fun `update one workout with remote error INVALID_OBJECTID should return 0 to 1`() = runTest {
        val workouts = listOf(WorkoutEntityFactory.makeDomainWorkout())

        whenever(workoutHeaderDataSource.getLocallyModifiedWorkouts()).thenReturn(workouts)
        whenever(workoutRemoteApi.updateWorkouts(workouts.map { it.toRemoteUpdatedWorkout() }, false))
            .thenThrow(AskoError.InvalidObjectID())

        assertThat(syncUpdatedWorkouts()).isEqualTo(0 to 1)
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    @Test
    fun `attempt to update workout that doesnot exist on server should return 0 to 1`() = runTest {
        val workouts = listOf(WorkoutEntityFactory.makeDomainWorkout())

        whenever(workoutHeaderDataSource.getLocallyModifiedWorkouts()).thenReturn(workouts)
        whenever(workoutRemoteApi.updateWorkouts(workouts.map { it.toRemoteUpdatedWorkout() }, false))
            .thenReturn(mapOf("foo" to false))

        assertThat(syncUpdatedWorkouts()).isEqualTo(0 to 1)
    }
}
