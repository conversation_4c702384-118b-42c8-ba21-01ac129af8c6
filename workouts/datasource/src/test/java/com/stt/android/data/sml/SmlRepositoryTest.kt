package com.stt.android.data.sml

import com.stt.android.domain.workouts.extensions.SMLExtension
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.whenever
import kotlin.test.assertEquals

@RunWith(MockitoJUnitRunner::class)
class SmlRepositoryTest {
    @Mock
    private lateinit var smlLocalDataSource: SmlLocalDataSource

    @Mock
    private lateinit var smlRemoteDataSource: SmlRemoteDataSource

    private lateinit var smlRepository: SmlRepository

    @Before
    fun setup() {
        smlRepository = SmlRepository(smlLocalDataSource, smlRemoteDataSource)
    }

    @Test
    fun `fetch should return from local fetch`() = runTest {
        val fromLocal = SMLExtension(1, null)

        whenever(smlLocalDataSource.fetchSmlExtension(1, "key"))
            .thenReturn(fromLocal)

        assertEquals(fromLocal, smlRepository.fetchSmlExtension(1, "key"))
    }

    @Test
    fun `fetch should return from remote fetch`() = runTest {
        val fromRemote = SMLExtension(2, null)
        whenever(smlLocalDataSource.fetchSmlExtension(1, "key"))
            .thenReturn(null)
        whenever(smlRemoteDataSource.fetchSmlExtension(1, "key"))
            .thenReturn(fromRemote)
        whenever(smlLocalDataSource.saveSmlExtension(anyOrNull()))
            .thenReturn(Unit)

        assertEquals(fromRemote, smlRepository.fetchSmlExtension(1, "key"))
    }
}
