package com.stt.android.data.workout.pictures

import com.stt.android.domain.workouts.pictures.Picture
import com.stt.android.domain.workouts.pictures.PicturesDataSource

fun createPicture(): Picture {
    return Picture(
        id = 0,
        key = null,
        location = null,
        timestamp = 0,
        totalTime = 0.0,
        fileName = "path/to/file",
        workoutId = 1,
        workoutKey = "workoutkey",
        md5Hash = "md5",
        locallyChanged = true,
        description = "description",
        username = "user",
        width = 100,
        height = 200,
        indexInWorkoutHeader = 0
    )
}

class FakePicturesDataSource : PicturesDataSource {
    override suspend fun deletePicture(picture: Picture) {
        TODO("not implemented") // To change body of created functions use File | Settings | File Templates.
    }

    override suspend fun findByWorkoutId(workoutId: Int): List<Picture> {
        return listOf(createPicture())
    }

    override suspend fun findUnsyncedPictures(username: String): List<Picture> {
        return listOf(
            createPicture()
        )
    }

    override suspend fun savePicture(picture: Picture) {
    }
}
