package com.stt.android.data.workout

import android.content.Context
import app.cash.turbine.test
import com.google.common.truth.Truth.assertThat
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.data.source.local.tags.UserTagsDao
import com.stt.android.data.source.local.workout.LocalSummaryWorkoutHeader
import com.stt.android.data.source.local.workout.WorkoutHeaderDao
import com.stt.android.data.source.local.workouts.WorkoutsLastFetchTimestampStore
import com.stt.android.remote.workout.WorkoutRemoteApi
import com.stt.android.testutils.NewCoroutinesTestRule
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.whenever

@RunWith(MockitoJUnitRunner::class)
class WorkoutRepositoryTest {
    @get:Rule
    val coroutinesTestRule = NewCoroutinesTestRule()

    @Mock
    lateinit var appContext: Context

    @Mock
    lateinit var workoutRemoteApi: WorkoutRemoteApi

    @Mock
    lateinit var workoutRemoteExtensionMapper: WorkoutRemoteExtensionMapper

    @Mock
    lateinit var workoutsLastFetchTimestampStore: WorkoutsLastFetchTimestampStore

    @Mock
    lateinit var workoutHeaderDao: WorkoutHeaderDao

    @Mock
    lateinit var userTagsDao: UserTagsDao

    @Mock
    lateinit var workoutRepository: WorkoutRepository

    @Mock
    lateinit var uploadWorkoutTasksProvider: UploadWorkoutTasksProvider

    private fun initWorkoutRepository() {
        workoutRepository = WorkoutRepository(
            appContext = appContext,
            workoutRemoteApi = workoutRemoteApi,
            workoutRemoteExtensionMapper = workoutRemoteExtensionMapper,
            workoutsLastFetchTimestampStore = workoutsLastFetchTimestampStore,
            workoutHeaderDao = workoutHeaderDao,
            userTagsDao = userTagsDao,
            uploadWorkoutTasksProvider = uploadWorkoutTasksProvider
        )
    }

    @Test
    fun getAllWorkoutsForSummary_shouldReturnSelectedActivitiesIfMinDistanceIsNotSpecified() =
        runTest {
            whenever(
                workoutHeaderDao.getAllWorkoutsForSummary(
                    any(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                )
            ).thenReturn(
                flowOf(listOf(running1, running2, gym1))
            )

            initWorkoutRepository()

            workoutRepository.getAllWorkoutsForSummary(
                username = "username",
                activityIds = listOf(),
                minStartTimeInclusive = null,
                maxStartTimeInclusive = null,
                minTotalDistanceInclusive = null,
                maxTotalDistanceInclusive = null,
                suuntoTags = null,
                userTags = null
            ).test {
                val workouts = expectMostRecentItem()
                assertThat(workouts.size).isEqualTo(3)
            }
        }

    @Test
    fun getAllWorkoutsForSummary_shouldExcludeActivitiesWithNoDistanceIfMinDistanceIsSpecified() =
        runTest {
            whenever(
                workoutHeaderDao.getAllWorkoutsForSummary(
                    any(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                )
            ).thenReturn(
                flowOf(listOf(running1, running2, gym1))
            )

            initWorkoutRepository()

            workoutRepository.getAllWorkoutsForSummary(
                username = "username",
                activityIds = listOf(),
                minStartTimeInclusive = null,
                maxStartTimeInclusive = null,
                minTotalDistanceInclusive = 2000.0,
                maxTotalDistanceInclusive = null,
                suuntoTags = null,
                userTags = null
            ).test {
                val workouts = expectMostRecentItem()
                assertThat(workouts.size).isEqualTo(2)
                assertThat(workouts[0].id).isEqualTo(running1.id)
                assertThat(workouts[1].id).isEqualTo(running2.id)
            }
        }

    private val running1 = LocalSummaryWorkoutHeader(
        id = 1,
        key = "key1",
        username = "username",
        totalDistance = 5000.0,
        activityId = CoreActivityType.RUNNING.id,
        avgSpeed = 10.0,
        startTime = 10,
        totalTime = 10.0,
        heartRateAvg = 10.0,
        totalAscent = 10.0,
        energyConsumption = 10.0,
        tss = null,
        suuntoTags = null,
        vo2Max = null,
        summaryAvgSpeed = null,
        divePauseDuration = null,
        summaryAvgPower = null,
    )

    private val running2 = LocalSummaryWorkoutHeader(
        id = 2,
        key = "key2",
        username = "username",
        totalDistance = 8000.0,
        activityId = CoreActivityType.RUNNING.id,
        avgSpeed = 20.0,
        startTime = 20,
        totalTime = 20.0,
        heartRateAvg = 20.0,
        totalAscent = 20.0,
        energyConsumption = 20.0,
        tss = null,
        suuntoTags = null,
        vo2Max = null,
        summaryAvgSpeed = null,
        divePauseDuration = null,
        summaryAvgPower = null,
    )

    private val gym1 = LocalSummaryWorkoutHeader(
        id = 3,
        key = "key3",
        username = "username",
        totalDistance = 1000.0,
        activityId = CoreActivityType.GYM.id,
        avgSpeed = 10.0,
        startTime = 10,
        totalTime = 10.0,
        heartRateAvg = 10.0,
        totalAscent = 10.0,
        energyConsumption = 10.0,
        tss = null,
        suuntoTags = null,
        vo2Max = null,
        summaryAvgSpeed = null,
        divePauseDuration = null,
        summaryAvgPower = null,
    )
}
