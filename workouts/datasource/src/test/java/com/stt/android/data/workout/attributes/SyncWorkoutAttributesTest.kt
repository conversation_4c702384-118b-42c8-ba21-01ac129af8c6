package com.stt.android.data.workout.attributes

import com.soy.algorithms.tss.TSSCalculationMethod
import com.stt.android.data.session.CurrentUser
import com.stt.android.data.workout.WorkoutHeaderRepository
import com.stt.android.domain.Point
import com.stt.android.domain.tags.UserTag
import com.stt.android.domain.tags.UserTagsRepository
import com.stt.android.domain.workouts.DomainWorkout
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.attributes.DomainWorkoutAttributes
import com.stt.android.domain.workouts.attributes.DomainWorkoutAttributesUpdate
import com.stt.android.domain.workouts.attributes.DomainWorkoutLocation
import com.stt.android.domain.workouts.attributes.WorkoutAttributesDataSource
import com.stt.android.domain.workouts.attributes.WorkoutAttributesUpdateDataSource
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.domain.workouts.tss.TSS
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.never
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

@RunWith(MockitoJUnitRunner::class)
@OptIn(ExperimentalCoroutinesApi::class)
class SyncWorkoutAttributesTest {

    @Mock
    private lateinit var currentUser: CurrentUser

    @Mock
    private lateinit var workoutAttributesUpdateRepository: WorkoutAttributesUpdateDataSource

    @Mock
    private lateinit var workoutAttributesRepository: WorkoutAttributesDataSource

    @Mock
    private lateinit var workoutHeaderRepository: WorkoutHeaderRepository

    @Mock
    private lateinit var workout: DomainWorkout

    @Mock
    private lateinit var workoutHeader: WorkoutHeader

    @Mock
    private lateinit var tagsRepository: UserTagsRepository

    private lateinit var syncWorkoutAttributes: SyncWorkoutAttributes

    @Before
    fun setup() = runTest {
        whenever(currentUser.getUsername()).thenReturn("foo")
        syncWorkoutAttributes = SyncWorkoutAttributes(
            currentUser,
            workoutAttributesUpdateRepository,
            workoutAttributesRepository,
            workoutHeaderRepository,
            tagsRepository
        )
    }

    @Test
    fun `skip updating workout attributes if fetch to local db fails`() =
        runTest {
            whenever(workoutAttributesUpdateRepository.fetchUnsyncedWorkoutAttributesUpdate(currentUser.getUsername()))
                .thenThrow(RuntimeException::class.java)

            syncWorkoutAttributes()
            verify(workoutHeaderRepository, never()).findById(any())
            verify(workoutHeaderRepository, never()).storeWorkout(any())
        }

    @Test
    fun `skip updating workout attributes if fetch to local db returns nothing`() =
        runTest {
            whenever(workoutAttributesUpdateRepository.fetchUnsyncedWorkoutAttributesUpdate(currentUser.getUsername()))
                .thenReturn(emptyList())

            syncWorkoutAttributes()
            verify(workoutHeaderRepository, never()).findById(any())
            verify(workoutHeaderRepository, never()).storeWorkout(any())
        }

    @Test
    fun `remove and do nothing with updates without data`() =
        runTest {
            val updates = listOf(
                DomainWorkoutAttributesUpdate(1, "foo", DomainWorkoutAttributes(null, null, null, null, null, null), emptyList(), false), // should NOT call storeWorkout
                DomainWorkoutAttributesUpdate(2, "foo", DomainWorkoutAttributes(null, null, null, null, null, null), emptyList(), false) // should NOT call storeWorkout as header null
            )
            whenever(workoutAttributesUpdateRepository.fetchUnsyncedWorkoutAttributesUpdate(currentUser.getUsername()))
                .thenReturn(updates)
            whenever(workoutHeader.key).thenReturn("bar")
            whenever(workoutHeaderRepository.findById(1)).thenReturn(workoutHeader)
            whenever(workoutHeaderRepository.findById(2)).thenReturn(null) // simulate header not found

            val spy = Mockito.spy(syncWorkoutAttributes)
            spy()
            verify(workoutHeaderRepository, never()).storeWorkout(any()) // Skipped with updates without data
            verify(spy, times(updates.size)).onUpdateHandled(any()) // Called for all
        }

    @Test
    fun `verify invalid updates do not affect valid updates`() =
        runTest {
            val updates = listOf(
                DomainWorkoutAttributesUpdate(1, "foo", DomainWorkoutAttributes(null, null, null, null, null, null), emptyList(), false), // should NOT call storeWorkout as no updates or deletions
                DomainWorkoutAttributesUpdate(2, "foo", DomainWorkoutAttributes(null, null, null, null, null, null), emptyList(), false), // should NOT call storeWorkout as header null
                DomainWorkoutAttributesUpdate(3, "foo", DomainWorkoutAttributes(null, null, null, null, null, null), listOf("startPosition"), false), // should call storeWorkout
                DomainWorkoutAttributesUpdate(4, "foo", DomainWorkoutAttributes(DomainWorkoutLocation(0.0, 0.0), null, null, null, null, null), emptyList(), false) // should call storeWorkout
            )
            whenever(workoutAttributesUpdateRepository.fetchUnsyncedWorkoutAttributesUpdate(currentUser.getUsername()))
                .thenReturn(updates)
            whenever(workout.header).thenReturn(workoutHeader)
            whenever(workoutHeader.key).thenReturn("bar")
            whenever(workoutHeaderRepository.findById(1)).thenReturn(workoutHeader)
            whenever(workoutHeaderRepository.findById(2)).thenReturn(null) // simulate header not found
            whenever(workoutHeaderRepository.findById(3)).thenReturn(workoutHeader)
            whenever(workoutHeaderRepository.findById(4)).thenReturn(workoutHeader)
            whenever(workoutAttributesRepository.updateWorkoutAttributes(any(), any(), any())).thenReturn(workout)
            whenever(workoutAttributesRepository.deleteWorkoutAttributes(any(), any(), any())).thenReturn(workout)

            val spy = Mockito.spy(syncWorkoutAttributes)
            spy()
            verify(workoutHeaderRepository, times(2)).storeWorkout(anyOrNull()) // Skipped with updates without data
            verify(spy, times(updates.size)).onUpdateHandled(any()) // Called for all
        }

    @Test
    fun `verify network issues will not remove updates`() =
        runTest {
            val updates = listOf(
                DomainWorkoutAttributesUpdate(1, "foo", DomainWorkoutAttributes(null, null, null, null, null, null), listOf("startPosition"), false), // should call storeWorkout
                DomainWorkoutAttributesUpdate(2, "foo", DomainWorkoutAttributes(DomainWorkoutLocation(0.0, 0.0), null, null, null, null, null), emptyList(), false) // should call storeWorkout
            )
            whenever(workoutAttributesUpdateRepository.fetchUnsyncedWorkoutAttributesUpdate(currentUser.getUsername()))
                .thenReturn(updates)
            whenever(workoutHeader.key).thenReturn("bar")
            whenever(workoutHeaderRepository.findById(1)).thenReturn(workoutHeader)
            whenever(workoutHeaderRepository.findById(2)).thenReturn(workoutHeader)
            whenever(workoutAttributesRepository.updateWorkoutAttributes(any(), any(), any()))
                .thenThrow(RuntimeException::class.java)
            whenever(workoutAttributesRepository.deleteWorkoutAttributes(any(), any(), any()))
                .thenThrow(RuntimeException::class.java)

            val spy = Mockito.spy(syncWorkoutAttributes)
            spy()
            verify(workoutHeaderRepository, never()).storeWorkout(any()) // Skipped with updates without data
            verify(spy, never()).onUpdateHandled(any())
        }

    @Test
    fun `verify network issues on one does not affect the other`() =
        runTest {
            val updates = listOf(
                DomainWorkoutAttributesUpdate(1, "foo", DomainWorkoutAttributes(null, null, null, null, null, null), listOf("startPosition"), false), // should call storeWorkout
                DomainWorkoutAttributesUpdate(2, "foo", DomainWorkoutAttributes(DomainWorkoutLocation(0.0, 0.0), null, null, null, null, null), emptyList(), false) // should call storeWorkout
            )
            whenever(workoutAttributesUpdateRepository.fetchUnsyncedWorkoutAttributesUpdate(currentUser.getUsername()))
                .thenReturn(updates)
            whenever(workout.header).thenReturn(workoutHeader)
            whenever(workoutHeader.key).thenReturn("bar")
            whenever(workoutHeaderRepository.findById(1)).thenReturn(workoutHeader)
            whenever(workoutHeaderRepository.findById(2)).thenReturn(workoutHeader)
            whenever(workoutAttributesRepository.updateWorkoutAttributes(any(), any(), any()))
                .thenThrow(RuntimeException::class.java)
            whenever(workoutAttributesRepository.deleteWorkoutAttributes(any(), any(), any()))
                .thenReturn(workout)

            val spy = Mockito.spy(syncWorkoutAttributes)
            spy()
            verify(workoutHeaderRepository, times(1)).storeWorkout(anyOrNull()) // Skipped with updates without data
            verify(spy, times(1)).onUpdateHandled(any())
        }

    @Test
    fun `syncing attributes does not affect other locally stored WorkoutHeader fields`() = runTest {
        val updates = listOf(
            DomainWorkoutAttributesUpdate(
                1,
                "foo",
                DomainWorkoutAttributes(
                    DomainWorkoutLocation(10.0, 10.0),
                    TSS(50f, TSSCalculationMethod.MANUAL),
                    12.7,
                    42.0,
                    24.0,
                    suuntoTags = listOf(SuuntoTag.COMMUTE)
                ),
                emptyList(),
                false
            )
        )

        val initialStoredWorkoutHeader = WorkoutHeader(
            id = 1,
            key = "key",
            totalDistance = 0.0,
            maxSpeed = 0.0,
            activityTypeId = 63,
            avgSpeed = 0.0,
            description = null,
            startPosition = null,
            stopPosition = null,
            centerPosition = null,
            startTime = 10000,
            stopTime = 20000,
            totalTime = 10000.0,
            energyConsumption = 50.0,
            username = "foo",
            heartRateAverage = 0.0,
            heartRateAvgPercentage = 0.0,
            heartRateMax = 0.0,
            heartRateMaxPercentage = 0.0,
            heartRateUserSetMax = 180.0,
            averageCadence = 0,
            maxCadence = 0,
            pictureCount = 1,
            viewCount = 0,
            commentCount = 0,
            sharingFlags = 0,
            stepCount = 0,
            polyline = null,
            manuallyAdded = true,
            reactionCount = 0,
            totalAscent = 0.0,
            totalDescent = 0.0,
            recoveryTime = 10000,
            locallyChanged = false,
            deleted = false,
            seen = true,
            maxAltitude = 0.0,
            minAltitude = 0.0,
            extensionsFetched = false,
            tss = TSS(321f, TSSCalculationMethod.POWER, 2f, 3f, 4f),
            suuntoTags = listOf(),
            userTags = listOf()
        )

        val expectedWorkoutToBeStored = initialStoredWorkoutHeader.copy(
            startPosition = Point(10.0, 10.0),
            centerPosition = Point(10.0, 10.0),
            stopPosition = Point(10.0, 10.0),
            tss = TSS(50f, TSSCalculationMethod.MANUAL),
            maxSpeed = 12.7,
            totalAscent = 42.0,
            totalDescent = 24.0
        )

        // simulates sync trying to update irrelevant values in local DB
        val workoutHeaderWithSyncUpdates = expectedWorkoutToBeStored.copy(pictureCount = 0)

        whenever(workoutAttributesUpdateRepository.fetchUnsyncedWorkoutAttributesUpdate(currentUser.getUsername()))
            .thenReturn(updates)
        whenever(workout.header).thenReturn(workoutHeaderWithSyncUpdates)
        whenever(workoutHeaderRepository.findById(1)).thenReturn(initialStoredWorkoutHeader)
        whenever(workoutAttributesRepository.updateWorkoutAttributes(any(), any(), any())).thenReturn(workout)

        val spy = Mockito.spy(syncWorkoutAttributes)
        spy()
        verify(workoutHeaderRepository, times(1)).storeWorkout(expectedWorkoutToBeStored)
        verify(spy, times(1)).onUpdateHandled(any())
    }

    @Test
    fun `syncs both updates and deletions`() = runTest {
        val updates = listOf(
            DomainWorkoutAttributesUpdate(
                1,
                "foo",
                DomainWorkoutAttributes(null, TSS(123f, TSSCalculationMethod.MANUAL), null, null, null, emptyList()),
                listOf("startPosition", "maxSpeed", "totalAscent", "totalDescent", "suuntoTags"),
                false
            )
        )

        val initialStoredWorkoutHeader = WorkoutHeader(
            id = 1,
            key = "key",
            totalDistance = 0.0,
            maxSpeed = 12.7,
            activityTypeId = 63,
            avgSpeed = 0.0,
            description = null,
            startPosition = Point(10.0, 10.0),
            centerPosition = Point(10.0, 10.0),
            stopPosition = Point(10.0, 10.0),
            startTime = 10000,
            stopTime = 20000,
            totalTime = 10000.0,
            energyConsumption = 50.0,
            username = "foo",
            heartRateAverage = 0.0,
            heartRateAvgPercentage = 0.0,
            heartRateMax = 0.0,
            heartRateMaxPercentage = 0.0,
            heartRateUserSetMax = 180.0,
            averageCadence = 0,
            maxCadence = 0,
            pictureCount = 1,
            viewCount = 0,
            commentCount = 0,
            sharingFlags = 0,
            stepCount = 0,
            polyline = null,
            manuallyAdded = true,
            reactionCount = 0,
            totalAscent = 42.0,
            totalDescent = 24.0,
            recoveryTime = 10000,
            locallyChanged = false,
            deleted = false,
            seen = true,
            maxAltitude = 0.0,
            minAltitude = 0.0,
            extensionsFetched = false,
            tss = TSS(321f, TSSCalculationMethod.POWER, 2f, 3f, 4f),
            suuntoTags = listOf(SuuntoTag.COMMUTE),
            userTags = listOf()
        )

        val expectedWorkoutToBeStored = initialStoredWorkoutHeader.copy(
            startPosition = null,
            centerPosition = null,
            stopPosition = null,
            tss = TSS(123f, TSSCalculationMethod.MANUAL),
            maxSpeed = 0.0,
            totalAscent = 0.0,
            totalDescent = 0.0,
            suuntoTags = emptyList()
        )

        whenever(workoutAttributesUpdateRepository.fetchUnsyncedWorkoutAttributesUpdate(currentUser.getUsername()))
            .thenReturn(updates)
        whenever(workout.header).thenReturn(expectedWorkoutToBeStored)
        whenever(workoutHeaderRepository.findById(1)).thenReturn(initialStoredWorkoutHeader)
        whenever(workoutAttributesRepository.updateWorkoutAttributes(any(), any(), any())).thenReturn(workout)
        whenever(workoutAttributesRepository.deleteWorkoutAttributes(any(), any(), any())).thenReturn(workout)

        val spy = Mockito.spy(syncWorkoutAttributes)
        spy()
        verify(workoutHeaderRepository, times(1)).storeWorkout(expectedWorkoutToBeStored)
        verify(spy, times(1)).onUpdateHandled(any())
    }

    @Test
    fun `user tags attributes should update correctly`() = runTest {
        whenever(workoutAttributesUpdateRepository.fetchUnsyncedWorkoutAttributesUpdate(currentUser.getUsername()))
            .thenReturn(emptyList())
        val initialUserTag = UserTag(
            id = 1,
            key = "key1",
            name = "name 1"
        )
        val initialButRemovedUserTag = UserTag(
            id = 2,
            key = "key2",
            name = "name 2"
        )

        val initialStoredWorkoutHeader = WorkoutHeader(
            id = 1,
            key = "key",
            totalDistance = 0.0,
            maxSpeed = 12.7,
            activityTypeId = 63,
            avgSpeed = 0.0,
            description = null,
            startPosition = Point(10.0, 10.0),
            centerPosition = Point(10.0, 10.0),
            stopPosition = Point(10.0, 10.0),
            startTime = 10000,
            stopTime = 20000,
            totalTime = 10000.0,
            energyConsumption = 50.0,
            username = "foo",
            heartRateAverage = 0.0,
            heartRateAvgPercentage = 0.0,
            heartRateMax = 0.0,
            heartRateMaxPercentage = 0.0,
            heartRateUserSetMax = 180.0,
            averageCadence = 0,
            maxCadence = 0,
            pictureCount = 1,
            viewCount = 0,
            commentCount = 0,
            sharingFlags = 0,
            stepCount = 0,
            polyline = null,
            manuallyAdded = true,
            reactionCount = 0,
            totalAscent = 42.0,
            totalDescent = 24.0,
            recoveryTime = 10000,
            locallyChanged = false,
            deleted = false,
            seen = true,
            maxAltitude = 0.0,
            minAltitude = 0.0,
            extensionsFetched = false,
            tss = TSS(321f, TSSCalculationMethod.POWER, 2f, 3f, 4f),
            suuntoTags = listOf(),
            userTags = listOf(initialUserTag, initialButRemovedUserTag)
        )
        whenever(workoutHeaderRepository.findWorkoutsWithUnsyncedUserTags()).thenReturn(listOf(initialStoredWorkoutHeader))
        whenever(tagsRepository.findNonDeletedUserTagsForAWorkout(initialStoredWorkoutHeader.id)).thenReturn(listOf(initialUserTag))
        val spy = Mockito.spy(syncWorkoutAttributes)
        spy()

        verify(tagsRepository).markUserTagInWorkoutAsSynced(initialStoredWorkoutHeader.id, initialUserTag.id!!)
        verify(tagsRepository).deleteRemovedUserTagsFromAWorkout(initialStoredWorkoutHeader.id)
    }

    @Test
    fun `mark tags as synced must be skipped if update attribute failed`() = runTest {
        val initialUserTag = UserTag(
            id = 1,
            key = "key1",
            name = "name 1"
        )

        val initialStoredWorkoutHeader = WorkoutHeader(
            id = 1,
            key = "key",
            totalDistance = 0.0,
            maxSpeed = 12.7,
            activityTypeId = 63,
            avgSpeed = 0.0,
            description = null,
            startPosition = Point(10.0, 10.0),
            centerPosition = Point(10.0, 10.0),
            stopPosition = Point(10.0, 10.0),
            startTime = 10000,
            stopTime = 20000,
            totalTime = 10000.0,
            energyConsumption = 50.0,
            username = "foo",
            heartRateAverage = 0.0,
            heartRateAvgPercentage = 0.0,
            heartRateMax = 0.0,
            heartRateMaxPercentage = 0.0,
            heartRateUserSetMax = 180.0,
            averageCadence = 0,
            maxCadence = 0,
            pictureCount = 1,
            viewCount = 0,
            commentCount = 0,
            sharingFlags = 0,
            stepCount = 0,
            polyline = null,
            manuallyAdded = true,
            reactionCount = 0,
            totalAscent = 42.0,
            totalDescent = 24.0,
            recoveryTime = 10000,
            locallyChanged = false,
            deleted = false,
            seen = true,
            maxAltitude = 0.0,
            minAltitude = 0.0,
            extensionsFetched = false,
            tss = TSS(321f, TSSCalculationMethod.POWER, 2f, 3f, 4f),
            suuntoTags = listOf(),
            userTags = listOf(initialUserTag)
        )
        whenever(workoutHeaderRepository.findWorkoutsWithUnsyncedUserTags()).thenReturn(listOf(initialStoredWorkoutHeader))
        whenever(workoutAttributesUpdateRepository.fetchUnsyncedWorkoutAttributesUpdate(currentUser.getUsername()))
            .thenReturn(emptyList())
        whenever(tagsRepository.findNonDeletedUserTagsForAWorkout(initialStoredWorkoutHeader.id))
            .thenReturn(listOf(initialUserTag))
        whenever(workoutAttributesRepository.updateWorkoutAttributes(any(), any(), any()))
            .thenThrow(RuntimeException::class.java)
        val spy = Mockito.spy(syncWorkoutAttributes)
        spy()
        verify(tagsRepository, never()).markUserTagInWorkoutAsSynced(initialStoredWorkoutHeader.id, 1)
        verify(tagsRepository, never()).deleteRemovedUserTagsFromAWorkout(initialStoredWorkoutHeader.id)
    }

    // Uncomment after having support for multiple
//    @Test
//    fun `Delete single tag should delete update workout with multiple tags`() = runTest {
//        val updates = listOf(
//            DomainWorkoutAttributesUpdate(
//                1,
//                "foo",
//                DomainWorkoutAttributes(null, null, null, null, null, listOf(SuuntoTag.COMMUTE)),
//                emptyList(),
//                false
//            )
//        )
//
//        val initialStoredWorkoutHeader = WorkoutHeader(
//            id = 1,
//            key = "key",
//            totalDistance = 0.0,
//            maxSpeed = 0.0,
//            activityTypeId = 63,
//            avgSpeed = 0.0,
//            description = null,
//            startPosition = null,
//            centerPosition = null,
//            stopPosition = null,
//            startTime = 10000,
//            stopTime = 20000,
//            totalTime = 10000.0,
//            energyConsumption = 50.0,
//            username = "foo",
//            heartRateAverage = 0.0,
//            heartRateAvgPercentage = 0.0,
//            heartRateMax = 0.0,
//            heartRateMaxPercentage = 0.0,
//            heartRateUserSetMax = 180.0,
//            averageCadence = 0,
//            maxCadence = 0,
//            pictureCount = 1,
//            viewCount = 0,
//            commentCount = 0,
//            sharingFlags = 0,
//            stepCount = 0,
//            polyline = null,
//            manuallyAdded = true,
//            reactionCount = 0,
//            totalAscent = 0.0,
//            totalDescent = 0.0,
//            recoveryTime = 10000,
//            locallyChanged = false,
//            deleted = false,
//            seen = true,
//            maxAltitude = 0.0,
//            minAltitude = 0.0,
//            extensionsFetched = false,
//            tss = null,
//            suuntoTags = listOf(SuuntoTag.COMMUTE, SuuntoTag.TEST)
//        )
//
//        val expectedWorkoutToBeStored = initialStoredWorkoutHeader.copy(
//            suuntoTags = listOf(SuuntoTag.COMMUTE)
//        )
//
//        whenever(workoutAttributesUpdateRepository.fetchUnsyncedWorkoutAttributesUpdate(currentUser.getUsername()))
//            .thenReturn(updates)
//        whenever(workout.header).thenReturn(expectedWorkoutToBeStored)
//        whenever(workoutHeaderRepository.findById(1)).thenReturn(initialStoredWorkoutHeader)
//        whenever(workoutAttributesRepository.updateWorkoutAttributes(any(), any(), any())).thenReturn(workout)
//        whenever(workoutAttributesRepository.deleteWorkoutAttributes(any(), any(), any())).thenReturn(workout)
//
//        val spy = Mockito.spy(syncWorkoutAttributes)
//        spy()
//        verify(workoutHeaderRepository, times(1)).storeWorkout(expectedWorkoutToBeStored)
//        verify(spy, times(1)).onUpdateHandled(any())
//    }
}
