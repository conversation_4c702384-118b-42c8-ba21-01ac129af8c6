package com.stt.android.data.workout

import com.stt.android.data.source.local.workout.LocalSummaryWorkoutHeader
import com.stt.android.domain.Point
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.remote.routes.RemotePoint
import com.stt.android.remote.workout.RemoteSyncedWorkoutImage
import com.stt.android.remote.workout.RemoteSyncedWorkoutRanking
import com.stt.android.remote.workout.RemoteSyncedWorkoutRankings
import com.stt.android.remote.workout.RemoteSyncedWorkoutReaction
import com.stt.android.remote.workout.video.RemoteVideo
import org.assertj.core.api.Assertions.assertThat
import org.junit.Test
import kotlin.math.roundToInt

class WorkoutMappersKtTest {
    @Test
    fun toPicture() {
        val expected = RemoteSyncedWorkoutImage(
            key = "key",
            username = "user",
            description = "desc",
            url = "url",
            timestamp = 1L,
            totalTime = 2.0,
            width = 3,
            height = 4,
            location = RemotePoint(1.2, 2.3, 4.5),
            workoutKey = "workoutkey",
            coverImage = false,
            sizes = null,
            contentReviewStatus = 1
        )

        val actual = expected.toPicture(1)
        assertThat(actual.description).isEqualTo(expected.description)
        assertThat(actual.height).isEqualTo(expected.height)
        assertThat(actual.width).isEqualTo(expected.width)
        assertThat(actual.key).isEqualTo(expected.key)
        assertThat(actual.workoutKey).isEqualTo(expected.workoutKey)
        assertThat(actual.location?.longitude).isEqualTo(expected.location?.longitude)
        assertThat(actual.location?.latitude).isEqualTo(expected.location?.latitude)
        assertThat(actual.location?.altitude).isEqualTo(expected.location?.altitude)
        assertThat(actual.timestamp).isEqualTo(expected.timestamp)
        assertThat(actual.totalTime).isEqualTo(expected.totalTime)
    }

    @Test
    fun toRanking() {
        val expected = RemoteSyncedWorkoutRankings(
            totalTimeOnRouteRanking = RemoteSyncedWorkoutRanking(
                originalRanking = 3,
                originalNumberOfWorkouts = 5
            )
        )

        val actual = expected.toRanking("workoutkey")

        assertThat(actual.numberOfWorkouts).isEqualTo(expected.totalTimeOnRouteRanking.originalNumberOfWorkouts)
        assertThat(actual.type).isEqualTo(TOTAL_TIME_ON_ROUTE_RANKING)
        assertThat(actual.ranking).isEqualTo(expected.totalTimeOnRouteRanking.originalRanking)
    }

    @Test
    fun toReactionSummary() {
        val expected = RemoteSyncedWorkoutReaction(utfCode = "abc", count = 1, userReacted = true)

        val actual = expected.toReactionSummary("workoutkey")

        assertThat(actual.count).isEqualTo(expected.count)
        assertThat(actual.reaction).isEqualTo(expected.utfCode)
        assertThat(actual.userReacted).isEqualTo(expected.userReacted)
    }

    @Test
    fun toVideo() {
        val expected = RemoteVideo(
            location = RemotePoint(longitude = 1.0, latitude = 2.0, altitude = 3.0),
            workoutKey = "workoutkey",
            timestamp = 1L,
            totalTime = 2,
            description = "desc",
            username = "user",
            url = "url",
            height = 4,
            width = 5,
            thumbnailUrl = "thumburl",
            key = "key"
        )

        val actual = expected.toVideo(1)

        assertThat(actual.location?.altitude).isEqualTo(expected.location?.altitude)
        assertThat(actual.location?.latitude).isEqualTo(expected.location?.latitude)
        assertThat(actual.location?.longitude).isEqualTo(expected.location?.longitude)
        assertThat(actual.description).isEqualTo(expected.description)
        assertThat(actual.height).isEqualTo(expected.height)
        assertThat(actual.width).isEqualTo(expected.width)
        assertThat(actual.timestamp).isEqualTo(expected.timestamp)
        assertThat(actual.totalTime).isEqualTo(expected.totalTime)
        assertThat(actual.username).isEqualTo(expected.username)
        assertThat(actual.url).isEqualTo(expected.url)
        assertThat(actual.thumbnailUrl).isEqualTo(expected.thumbnailUrl)
        assertThat(actual.key).isEqualTo(expected.key)
        assertThat(actual.workoutKey).isEqualTo(expected.workoutKey)
    }

    @Test
    fun `map RemotePoint to Point`() {
        val expected = RemotePoint(1.0, 2.0, 3.0)
        val actual = expected.toPoint()
        assertThat(actual.altitude).isEqualTo(expected.altitude)
        assertThat(actual.latitude).isEqualTo(expected.latitude)
        assertThat(actual.longitude).isEqualTo(expected.longitude)
    }

    @Test
    fun `map DomainWorkout to RemoteUpdatedWorkout succeeds`() {
        val expected = WorkoutHeader(
            id = 0,
            key = "key",
            totalDistance = 1.0,
            maxSpeed = 2.0,
            activityTypeId = 3,
            avgSpeed = 4.0,
            description = "desc",
            startPosition = Point(
                longitude = 5.0,
                latitude = 6.0,
                altitude = 7.0,
                relativeDistance = 8.0
            ),
            stopPosition = Point(
                longitude = 9.0,
                latitude = 10.0,
                altitude = 11.0,
                relativeDistance = 12.0
            ),
            centerPosition = Point(
                longitude = 13.0,
                latitude = 14.0,
                altitude = 15.0,
                relativeDistance = 16.0
            ),
            startTime = 17,
            stopTime = 18,
            totalTime = 19.0,
            energyConsumption = 20.0,
            username = "user",
            heartRateAverage = 21.0,
            heartRateAvgPercentage = 22.0,
            heartRateMax = 23.0,
            heartRateMaxPercentage = 24.0,
            heartRateUserSetMax = 25.0,
            averageCadence = 26,
            maxCadence = 27,
            pictureCount = 28,
            viewCount = 29,
            commentCount = 30,
            sharingFlags = 31,
            stepCount = 32,
            polyline = "asf",
            manuallyAdded = true,
            reactionCount = 33,
            totalAscent = 34.0,
            totalDescent = 35.0,
            recoveryTime = 36,
            maxAltitude = 37.0,
            minAltitude = 38.0,
            suuntoTags = listOf(),
            userTags = listOf()
        )

        val actual = expected.toRemoteUpdatedWorkout()
        assertThat(actual.workoutKey).isEqualTo(expected.key)
        assertThat(actual.activityId).isEqualTo(expected.activityTypeId)
        assertThat(actual.description).isEqualTo(expected.description)
        assertThat(actual.energyConsumption).isEqualTo(expected.energyConsumption.roundToInt())
        assertThat(actual.hrAvgValue).isEqualTo(expected.heartRateAverage.roundToInt())
        assertThat(actual.hrMaxValue).isEqualTo(expected.heartRateMax.roundToInt())
        assertThat(actual.sharingFlags).isEqualTo(expected.sharingFlags)
        assertThat(actual.startTime).isEqualTo(expected.startTime)
        assertThat(actual.stepCount).isEqualTo(expected.stepCount)
        assertThat(actual.totalDistance).isEqualTo(expected.totalDistance)
        assertThat(actual.totalTime).isEqualTo(expected.totalTime)
    }

    @Test(expected = IllegalStateException::class)
    fun `map DomainWorkout to RemoteUpdatedWorkout fails due to null workout key`() {
        val expected = WorkoutHeader(
            id = 0,
            key = null,
            totalDistance = 1.0,
            maxSpeed = 2.0,
            activityTypeId = 3,
            avgSpeed = 4.0,
            description = "desc",
            startPosition = Point(
                longitude = 5.0,
                latitude = 6.0,
                altitude = 7.0,
                relativeDistance = 8.0
            ),
            stopPosition = Point(
                longitude = 9.0,
                latitude = 10.0,
                altitude = 11.0,
                relativeDistance = 12.0
            ),
            centerPosition = Point(
                longitude = 13.0,
                latitude = 14.0,
                altitude = 15.0,
                relativeDistance = 16.0
            ),
            startTime = 17,
            stopTime = 18,
            totalTime = 19.0,
            energyConsumption = 20.0,
            username = "user",
            heartRateAverage = 21.0,
            heartRateAvgPercentage = 22.0,
            heartRateMax = 23.0,
            heartRateMaxPercentage = 24.0,
            heartRateUserSetMax = 25.0,
            averageCadence = 26,
            maxCadence = 27,
            pictureCount = 28,
            viewCount = 29,
            commentCount = 30,
            sharingFlags = 31,
            stepCount = 32,
            polyline = "asf",
            manuallyAdded = true,
            reactionCount = 33,
            totalAscent = 34.0,
            totalDescent = 35.0,
            recoveryTime = 36,
            maxAltitude = 37.0,
            minAltitude = 38.0,
            suuntoTags = listOf(),
            userTags = listOf()
        )

        expected.toRemoteUpdatedWorkout()
    }

    @Test
    fun `summary avg speed should be used for indoor swimming workouts in summary`() {
        val summaryWorkoutHeader = LocalSummaryWorkoutHeader(
            id = 1,
            key = "key1",
            username = "username",
            totalDistance = 0.0,
            activityId = ActivityType.SWIMMING.id,
            avgSpeed = 50.0,
            startTime = 0,
            totalTime = 0.0,
            heartRateAvg = 0.0,
            totalAscent = 0.0,
            energyConsumption = 0.0,
            tss = null,
            suuntoTags = null,
            vo2Max = null,
            summaryAvgSpeed = 70f,
            divePauseDuration = null,
            summaryAvgPower = null,
        ).toDomain(summaryItems = emptyList(), supportsDistance = false, supportsAscent = false)

        assertThat(summaryWorkoutHeader.avgSpeed).isEqualTo(70.0)
    }

    @Test
    fun `header avg speed should be used for indoor swimming workouts in summary`() {
        val summaryWorkoutHeader = LocalSummaryWorkoutHeader(
            id = 1,
            key = "key1",
            username = "username",
            totalDistance = 0.0,
            activityId = ActivityType.RUNNING.id,
            avgSpeed = 50.0,
            startTime = 0,
            totalTime = 0.0,
            heartRateAvg = 0.0,
            totalAscent = 0.0,
            energyConsumption = 0.0,
            tss = null,
            suuntoTags = null,
            vo2Max = null,
            summaryAvgSpeed = 70f,
            divePauseDuration = null,
            summaryAvgPower = null,
        ).toDomain(summaryItems = emptyList(), supportsDistance = false, supportsAscent = false)

        assertThat(summaryWorkoutHeader.avgSpeed).isEqualTo(50.0)
    }

    @Test
    fun `dive pause should be subtracted from total time if available for diving`() {
        val summaryWorkoutHeader = LocalSummaryWorkoutHeader(
            id = 1,
            key = "key1",
            username = "username",
            totalDistance = 0.0,
            activityId = ActivityType.SCUBADIVING.id,
            avgSpeed = 50.0,
            startTime = 0,
            totalTime = 100.0,
            heartRateAvg = 0.0,
            totalAscent = 0.0,
            energyConsumption = 0.0,
            tss = null,
            suuntoTags = null,
            vo2Max = null,
            summaryAvgSpeed = 70f,
            divePauseDuration = 20f,
            summaryAvgPower = null,
        ).toDomain(summaryItems = emptyList(), supportsDistance = false, supportsAscent = false)

        assertThat(summaryWorkoutHeader.totalTime).isEqualTo(80.0)
    }
}
