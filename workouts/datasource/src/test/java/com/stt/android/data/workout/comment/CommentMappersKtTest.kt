package com.stt.android.data.workout.comment

import com.stt.android.remote.workout.RemoteSyncedWorkoutComment
import org.assertj.core.api.Assertions.assertThat
import org.junit.Test

class CommentMappersKtTest {
    @Test
    fun `map RemoteSyncedWorkoutComment to DomainWorkoutComment`() {
        val expected = RemoteSyncedWorkoutComment(
            key = "key",
            timestamp = 123L,
            username = "username",
            realname = "realname",
            profilePictureUrl = "url",
            message = "message"
        )
        val actual = expected.toDomainWorkoutComment()

        assertThat(actual.key).isEqualTo(expected.key)
        assertThat(actual.message).isEqualTo(expected.message)
        assertThat(actual.profilePictureUrl).isEqualTo(expected.profilePictureUrl)
        assertThat(actual.realname).isEqualTo(expected.realname)
        assertThat(actual.timestamp).isEqualTo(expected.timestamp)
        assertThat(actual.username).isEqualTo(expected.username)
    }
}
