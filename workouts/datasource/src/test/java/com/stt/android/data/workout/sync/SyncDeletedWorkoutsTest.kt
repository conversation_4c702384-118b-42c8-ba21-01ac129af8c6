package com.stt.android.data.workout.sync

import com.stt.android.data.workout.FakeWorkoutRestApi
import com.stt.android.data.workout.FakeWorkoutRestV2Api
import com.stt.android.domain.sync.AggregatedSyncException
import com.stt.android.domain.workouts.WorkoutHeaderDataSource
import com.stt.android.exceptions.remote.AskoError
import com.stt.android.exceptions.remote.ClientError
import com.stt.android.remote.di.RestApiFactory.createMoshi
import com.stt.android.remote.otp.GenerateOTPUseCase
import com.stt.android.remote.workout.WorkoutRemoteApi
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.spy
import org.mockito.kotlin.whenever

@OptIn(ExperimentalCoroutinesApi::class)
@RunWith(MockitoJUnitRunner::class)
class SyncDeletedWorkoutsTest {
    private lateinit var workoutRemoteApi: WorkoutRemoteApi

    @Mock
    private lateinit var workoutHeaderDataSource: WorkoutHeaderDataSource

    @Mock
    private lateinit var generateOTPUseCase: GenerateOTPUseCase

    private lateinit var syncDeletedWorkouts: SyncDeletedWorkouts

    @Before
    fun setup() {
        workoutRemoteApi = spy(WorkoutRemoteApi(FakeWorkoutRestApi(), FakeWorkoutRestV2Api(), createMoshi(), generateOTPUseCase))
        syncDeletedWorkouts = SyncDeletedWorkouts(workoutHeaderDataSource, workoutRemoteApi)
    }

    @Test
    fun `delete 2 workouts should return 2`() = runTest {
        val keys = listOf("foo", "bar")

        whenever(workoutHeaderDataSource.getDeletedWorkoutsKeys()).thenReturn(keys)
        whenever(workoutRemoteApi.deleteWorkout(keys.first())).thenReturn(true)
        whenever(workoutRemoteApi.deleteWorkout(keys.last())).thenReturn(true)

        assertThat(syncDeletedWorkouts()).isEqualTo(2)
    }

    @Test(expected = AggregatedSyncException::class)
    fun `delete workouts throws AggregatedSyncException due to datasource error`() = runTest {
        whenever(workoutHeaderDataSource.getDeletedWorkoutsKeys()).thenThrow(RuntimeException::class.java)

        syncDeletedWorkouts()
    }

    @Test
    fun `delete 2 workouts with remote api error WORKOUT_NOT_FOUND should returns 2`() =
        runTest {
            val keys = listOf("foo", "bar")

            whenever(workoutHeaderDataSource.getDeletedWorkoutsKeys()).thenReturn(keys)
            whenever(workoutRemoteApi.deleteWorkout(keys.first())).thenThrow(AskoError.WorkoutNotFound())
            whenever(workoutRemoteApi.deleteWorkout(keys.last())).thenReturn(true)

            assertThat(syncDeletedWorkouts()).isEqualTo(2)
        }

    @Test
    fun `delete 2 workouts with remote api error NOT_OWN_WORKOUT should returns 1`() =
        runTest {
            val keys = listOf("foo", "bar")

            whenever(workoutHeaderDataSource.getDeletedWorkoutsKeys()).thenReturn(keys)
            whenever(workoutRemoteApi.deleteWorkout(keys.first())).thenThrow(AskoError.NotOwnWorkout())
            whenever(workoutRemoteApi.deleteWorkout(keys.last())).thenReturn(true)

            assertThat(syncDeletedWorkouts()).isEqualTo(2)
        }

    @Test
    fun `delete 2 workouts with remote error INVALID_OBJECTID returns 2`() = runTest {
        val keys = listOf("foo", "bar")

        whenever(workoutHeaderDataSource.getDeletedWorkoutsKeys()).thenReturn(keys)
        whenever(workoutRemoteApi.deleteWorkout(keys.first())).thenThrow(AskoError.InvalidObjectID())
        whenever(workoutRemoteApi.deleteWorkout(keys.last())).thenReturn(true)

        assertThat(syncDeletedWorkouts()).isEqualTo(2)
    }

    @Test(expected = AggregatedSyncException::class)
    fun `delete 2 workouts returns 0 due to unsupported asko error`() = runTest {
        val keys = listOf("foo", "bar")

        whenever(workoutHeaderDataSource.getDeletedWorkoutsKeys()).thenReturn(keys)
        whenever(workoutRemoteApi.deleteWorkout(keys.first())).thenThrow(ClientError.BadRequest(""))

        syncDeletedWorkouts()
    }
}
