package com.stt.android.data.workout.pictures

import com.stt.android.remote.response.AskoResponse
import com.stt.android.remote.workout.picture.PictureRestApi
import com.stt.android.remote.workout.picture.RemotePicture
import okhttp3.RequestBody

class FakePictureRestApi : PictureRestApi {
    override suspend fun uploadPicture(
        workoutKey: String,
        timestamp: Long,
        latitude: Double?,
        longitude: Double?,
        totaltime: Double,
        md5hash: String,
        pictureFile: RequestBody
    ): AskoResponse<RemotePicture> {
        val payload = RemotePicture(
            key = "key",
            location = null,
            timestamp = 0,
            totalTime = 0.0,
            workoutKey = "workoutkey",
            description = null,
            username = null,
            width = 0,
            height = 0,
            1
        )
        return AskoResponse(null, null, payload)
    }
}
