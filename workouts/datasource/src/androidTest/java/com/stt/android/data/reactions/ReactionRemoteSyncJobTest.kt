package com.stt.android.data.reactions

import android.app.Application
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.work.ListenableWorker
import androidx.work.testing.TestListenableWorkerBuilder
import com.google.common.truth.Truth.assertThat
import com.stt.android.data.TestWorkerFactory
import com.stt.android.data.session.CurrentUser
import com.stt.android.domain.workouts.reactions.Reaction
import com.stt.android.domain.workouts.reactions.ReactionDataSource
import com.stt.android.exceptions.remote.AskoError
import com.stt.android.remote.otp.GenerateOTPUseCase
import com.stt.android.remote.reactions.ReactionRestApi
import com.stt.android.remote.response.AskoErrorWrapper
import com.stt.android.remote.response.AskoResponse
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

@RunWith(AndroidJUnit4::class)
class ReactionRemoteSyncJobTest {

    private val reactionDataSource = mock<ReactionDataSource>()
    private val reactionRestApi = mock<ReactionRestApi>()
    private val currentUser = mock<CurrentUser>()
    private val generateOTPUseCase = mock<GenerateOTPUseCase>()

    private lateinit var job: ReactionRemoteSyncJob

    private val context = ApplicationProvider.getApplicationContext<Application>()

    @Before
    fun setup() {
        job = TestListenableWorkerBuilder<ReactionRemoteSyncJob>(context)
            .setWorkerFactory(
                TestWorkerFactory(
                    ReactionRemoteSyncJob.Factory(
                        reactionDataSource,
                        reactionRestApi,
                        currentUser,
                        generateOTPUseCase
                    )
                )
            ).build()
    }

    @Test
    fun pushNewReactionsWithNoDuplicatesSetsResultSuccess() = runTest {
        val reactions = listOf(
            Reaction(
                key = "",
                workoutKey = "",
                reaction = "",
                userName = "",
                userRealOrUsername = "",
                userProfilePictureUrl = "",
                timestamp = 0
            )
        )
        whenever(currentUser.getUsername()).thenReturn("user")
        whenever(reactionDataSource.findUnsyncedNotDeletedReactionsByUserName(any())).thenReturn(reactions)
        whenever(reactionDataSource.findDeletedReactions(any())).thenReturn(emptyList())
        whenever(reactionRestApi.saveReaction(any(), any())).thenReturn(
            AskoResponse(
                null,
                null,
                "key"
            )
        )

        val result = job.doWork()

        verify(reactionDataSource, never()).removeDuplicateReactions(any())
        assertThat(result).isEqualTo(ListenableWorker.Result.success())
    }

    @Test
    fun pushNewReactionToWorkoutFailureShouldRemoveDupsAndSetResultSuccess() = runTest {
        val reactions = listOf(
            Reaction(
                key = "",
                workoutKey = "",
                reaction = "",
                userName = "",
                userRealOrUsername = "",
                userProfilePictureUrl = "",
                timestamp = 0
            )
        )

        whenever(reactionDataSource.findDeletedReactions(any())).thenReturn(emptyList())
        whenever(currentUser.getUsername()).thenReturn("user")
        whenever(reactionDataSource.findUnsyncedNotDeletedReactionsByUserName(any())).thenReturn(reactions)
        whenever(reactionRestApi.saveReaction(any(), any()))
            .thenReturn(
                AskoResponse(AskoErrorWrapper.of(AskoError.ReactionToWorkoutFailed()), null, null)
            )

        val result = job.doWork()

        verify(reactionDataSource, never()).storeReactions(any())
        verify(reactionDataSource).removeDuplicateReactions(reactions)
        assertThat(result).isEqualTo(ListenableWorker.Result.success())
    }

    @Test
    fun pushNewReactionToWorkoutExceptionShouldRemoveDupsAndSetResultSuccess() = runTest {
        val reactions = listOf(
            Reaction(
                key = "",
                workoutKey = "",
                reaction = "",
                userName = "",
                userRealOrUsername = "",
                userProfilePictureUrl = "",
                timestamp = 0
            )
        )
        whenever(currentUser.getUsername()).thenReturn("user")
        whenever(reactionDataSource.findUnsyncedNotDeletedReactionsByUserName(any())).thenReturn(reactions)
        whenever(reactionDataSource.findDeletedReactions(any())).thenReturn(emptyList())
        whenever(reactionRestApi.saveReaction(any(), any())).thenThrow(AskoError.ReactionToWorkoutFailed())

        val result = job.doWork()

        verify(reactionDataSource, never()).storeReactions(any())
        verify(reactionDataSource).removeDuplicateReactions(reactions)
        assertThat(result).isEqualTo(ListenableWorker.Result.success())
    }

    @Test
    fun pushNewReactionsDatabaseErrorShouldSetResultToFailure() = runTest {
        val reactions = listOf(
            Reaction(
                key = "",
                workoutKey = "",
                reaction = "",
                userName = "",
                userRealOrUsername = "",
                userProfilePictureUrl = "",
                timestamp = 0
            )
        )
        whenever(currentUser.getUsername()).thenReturn("user")
        whenever(reactionDataSource.findUnsyncedNotDeletedReactionsByUserName(any())).thenReturn(reactions)
        whenever(reactionRestApi.saveReaction(any(), any())).thenThrow(AskoError.DatabaseError())

        val result = job.doWork()

        assertThat(result).isEqualTo(ListenableWorker.Result.failure())
    }

    @Test
    fun pushNewReactionsOtherExceptionShouldSetResultToFailure() = runTest {
        val reactions = listOf(
            Reaction(
                key = "",
                workoutKey = "",
                reaction = "",
                userName = "",
                userRealOrUsername = "",
                userProfilePictureUrl = "",
                timestamp = 0
            )
        )
        whenever(currentUser.getUsername()).thenReturn("user")
        whenever(reactionDataSource.findUnsyncedNotDeletedReactionsByUserName(any())).thenReturn(reactions)
        whenever(reactionRestApi.saveReaction(any(), any())).thenThrow(RuntimeException::class.java)

        val result = job.doWork()

        assertThat(result).isEqualTo(ListenableWorker.Result.failure())
    }

    @Test
    fun pushNewReactionsProceedsRegardlessOfExceptionAndFinallySetsResultToFailure() = runTest {
        val reactions = listOf(
            Reaction(
                key = "",
                workoutKey = "",
                reaction = "",
                userName = "",
                userRealOrUsername = "",
                userProfilePictureUrl = "",
                timestamp = 0
            ),
            Reaction(
                key = "",
                workoutKey = "",
                reaction = "",
                userName = "",
                userRealOrUsername = "",
                userProfilePictureUrl = "",
                timestamp = 0
            )
        )
        whenever(currentUser.getUsername()).thenReturn("user")
        whenever(reactionDataSource.findUnsyncedNotDeletedReactionsByUserName(any())).thenReturn(reactions)
        whenever(reactionRestApi.saveReaction(any(), any()))
            .thenThrow(RuntimeException::class.java)
            .thenReturn(AskoResponse(null, null, null))

        val result = job.doWork()

        assertThat(result).isEqualTo(ListenableWorker.Result.failure())
        verify(reactionRestApi, times(2)).saveReaction(any(), any())
    }

    @Test
    fun noReadtionsToSyncShouldSetResultSuccess() = runTest {
        whenever(currentUser.getUsername()).thenReturn("user")
        whenever(reactionDataSource.findUnsyncedNotDeletedReactionsByUserName(any()))
            .thenReturn(emptyList())
        whenever(reactionDataSource.findDeletedReactions(any())).thenReturn(emptyList())

        val result = job.doWork()

        assertThat(result).isEqualTo(ListenableWorker.Result.success())
    }

    @Test
    fun pushDeletedReactionsShouldRemoveLocalReactionsAndSetResultSuccess() = runTest {
        val reactions = listOf(
            Reaction(
                key = "",
                workoutKey = "",
                reaction = "",
                userName = "",
                userRealOrUsername = "",
                userProfilePictureUrl = "",
                timestamp = 0
            )
        )

        whenever(reactionDataSource.findUnsyncedNotDeletedReactionsByUserName(any()))
            .thenReturn(emptyList())
        whenever(currentUser.getUsername()).thenReturn("user")
        whenever(reactionDataSource.findDeletedReactions(any())).thenReturn(reactions)

        val result = job.doWork()

        verify(reactionDataSource).removeReactions(reactions)
        assertThat(result).isEqualTo(ListenableWorker.Result.success())
    }

    @Test
    fun pushDeletedReactionsFailsFirstTimeShouldRecoverAndRemoveSyncReactionsAndSetResultFailure() = runTest {
        val reactions = listOf(
            Reaction(
                key = "",
                workoutKey = "",
                reaction = "",
                userName = "",
                userRealOrUsername = "",
                userProfilePictureUrl = "",
                timestamp = 0
            ),
            Reaction(
                key = "",
                workoutKey = "",
                reaction = "",
                userName = "",
                userRealOrUsername = "",
                userProfilePictureUrl = "",
                timestamp = 0
            )
        )

        whenever(reactionDataSource.findUnsyncedNotDeletedReactionsByUserName(any()))
            .thenReturn(emptyList())
        whenever(currentUser.getUsername()).thenReturn("user")
        whenever(reactionDataSource.findDeletedReactions(any())).thenReturn(reactions)
        whenever(reactionRestApi.deleteReaction(any()))
            .thenReturn(Unit)
            .thenThrow(RuntimeException::class.java)

        val result = job.doWork()

        verify(reactionDataSource).removeReactions(listOf(reactions.last()))
        assertThat(result).isEqualTo(ListenableWorker.Result.failure())
    }
}
