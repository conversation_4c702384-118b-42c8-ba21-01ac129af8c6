package com.stt.android.data.ranking

import com.stt.android.data.EntityMapper
import com.stt.android.data.source.local.ranking.LocalRanking
import com.stt.android.domain.ranking.Ranking
import javax.inject.Inject

class RankingMapper
@Inject constructor() : EntityMapper<LocalRanking, Ranking> {
    override fun toDomainEntity(): (LocalRanking) -> Ranking {
        return {
            Ranking(
                id = it.id,
                key = it.key,
                type = it.type,
                ranking = it.ranking,
                numberOfWorkouts = it.numberOfWorkouts
            )
        }
    }

    override fun toDataEntity(): (Ranking) -> LocalRanking {
        return {
            LocalRanking(
                id = it.id,
                key = it.key,
                type = it.type,
                ranking = it.ranking,
                numberOfWorkouts = it.numberOfWorkouts
            )
        }
    }
}
