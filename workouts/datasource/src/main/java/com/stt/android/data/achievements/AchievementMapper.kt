package com.stt.android.data.achievements

import com.stt.android.data.EntityMapper
import com.stt.android.data.source.local.achievements.LocalAchievement
import com.stt.android.data.source.local.achievements.LocalActivityCounts
import com.stt.android.data.source.local.achievements.LocalCumulativeAchievement
import com.stt.android.data.source.local.achievements.LocalPersonalBestAchievement
import com.stt.android.domain.achievements.Achievement
import com.stt.android.domain.achievements.ActivityCounts
import com.stt.android.domain.achievements.CumulativeAchievement
import com.stt.android.domain.achievements.PersonalBestAchievement
import javax.inject.Inject

class AchievementMapper
@Inject constructor() : EntityMapper<LocalAchievement, Achievement> {
    override fun toDomainEntity(): (LocalAchievement) -> Achievement {
        return {
            Achievement(
                id = it.id,
                workoutKey = it.workoutKey,
                activityType = it.activityType,
                timestamp = it.timestamp,
                cumulativeAchievements = it.cumulativeAchievements.map { localCumulativeAchievement ->
                    CumulativeAchievement(
                        localCumulativeAchievement.description,
                        localCumulativeAchievement.activityCounts.let { localActivityCounts ->
                            ActivityCounts(
                                timeCategory = localActivityCounts.timeCategory,
                                currentCount = localActivityCounts.currentCount,
                                previousCount = localActivityCounts.previousCount,
                                firstInCount = localActivityCounts.firstInCount,
                                activityCount = localActivityCounts.activityCount,
                                activityTypeCount = localActivityCounts.activityTypeCount
                            )
                        }
                    )
                },
                personalBestAchievements = it.personalBestAchievements.map { localPersonalAchievement ->
                    PersonalBestAchievement(
                        timeCategory = localPersonalAchievement.timeCategory,
                        valueCategory = localPersonalAchievement.valueCategory,
                        since = localPersonalAchievement.since
                    )
                }
            )
        }
    }

    override fun toDataEntity(): (Achievement) -> LocalAchievement {
        return {
            LocalAchievement(
                id = it.id,
                workoutKey = it.workoutKey,
                activityType = it.activityType,
                timestamp = it.timestamp,
                cumulativeAchievements = it.cumulativeAchievements.map { cumulativeAchievement ->
                    LocalCumulativeAchievement(
                        cumulativeAchievement.description,
                        cumulativeAchievement.activityCounts.let { activityCounts ->
                            LocalActivityCounts(
                                timeCategory = activityCounts.timeCategory,
                                currentCount = activityCounts.currentCount,
                                previousCount = activityCounts.previousCount,
                                firstInCount = activityCounts.firstInCount,
                                activityCount = activityCounts.activityCount,
                                activityTypeCount = activityCounts.activityTypeCount
                            )
                        }
                    )
                },
                personalBestAchievements = it.personalBestAchievements.map { personalBestAchievement ->
                    LocalPersonalBestAchievement(
                        timeCategory = personalBestAchievement.timeCategory,
                        valueCategory = personalBestAchievement.valueCategory,
                        since = personalBestAchievement.since
                    )
                }
            )
        }
    }
}
