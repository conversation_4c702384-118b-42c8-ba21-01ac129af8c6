package com.stt.android.data.sml

import com.stt.android.data.source.local.smljson.SMLFileStorage
import com.stt.android.domain.sml.SmlDataSource
import com.stt.android.domain.workouts.extensions.SMLExtension
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * Uses files to store SMLExtension data.
 * To differentiate when no data for workout is saved at all and when empty / null data is
 * saved, empty dummy files are saved to mark that the data is null.
 */
class SmlLocalDataSource @Inject constructor(
    private val smlFileStorage: SMLFileStorage,
) : SmlDataSource {
    override suspend fun fetchSmlExtension(
        workoutId: Int,
        workoutKey: String?,
    ): SMLExtension? = withContext(Dispatchers.IO) {
        smlFileStorage.readSmlFromFile(workoutId)
            ?.let { zip ->
                val zipIfReal = if (zip.isNotEmpty()) zip else null
                SMLExtension(workoutId, zipIfReal)
            }
    }

    override suspend fun saveSmlExtension(smlExtension: SMLExtension) = withContext(Dispatchers.IO) {
        smlFileStorage.writeSmlToFile(smlExtension.workoutId, smlExtension.smlZip ?: byteArrayOf())
    }
}
