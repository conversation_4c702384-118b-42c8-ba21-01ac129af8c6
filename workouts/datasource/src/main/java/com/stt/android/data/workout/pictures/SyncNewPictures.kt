package com.stt.android.data.workout.pictures

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.session.CurrentUser
import com.stt.android.data.workout.toPicture
import com.stt.android.domain.sync.aggregateAndThrowCapturedErrors
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.pictures.Picture
import com.stt.android.domain.workouts.pictures.PicturesDataSource
import com.stt.android.remote.workout.picture.PictureRemoteApi
import com.stt.android.remote.workout.picture.RemotePicture
import timber.log.Timber
import javax.inject.Inject

/**
 * Uploads unsynced pictures to the server and returns the total count of successful uploads
 */
class SyncNewPictures
@Inject constructor(
    private val picturesDataSource: PicturesDataSource,
    private val currentUser: CurrentUser,
    private val pictureRemoteApi: PictureRemoteApi,
    private val pictureFileRepository: PictureFileRepository,
) {

    suspend operator fun invoke(): Int = sync()

    /**
     * can sync pictures from certain workout instead of all pictures
     */
    suspend fun sync(workoutHeader: WorkoutHeader? = null): Int = aggregateAndThrowCapturedErrors { errors ->
        val newWorkoutPictures = runSuspendCatching {
            workoutHeader?.let {
                picturesDataSource.findByWorkoutId(it.id)
                    .filter { picture -> picture.workoutId != null && !picture.workoutKey.isNullOrEmpty() && picture.locallyChanged }
            } ?: picturesDataSource.findUnsyncedPictures(currentUser.getUsername())
        }.getOrElse { e ->
            Timber.w(e, "Unable to get new workout pictures from local DB, skipping...")
            return@aggregateAndThrowCapturedErrors 0
        }

        var uploadedSuccessfully = 0
        newWorkoutPictures.forEach { newWorkoutPicture ->
            runSuspendCatching {
                val pictureFileName = newWorkoutPicture.fileName
                    ?: throw IllegalStateException("Picture filename cannot be null")
                Timber.v("Uploading image %s", pictureFileName)

                val response = uploadPicture(newWorkoutPicture, pictureFileName)
                val updatedPicture = getUpdatedPicture(response, newWorkoutPicture)
                store(updatedPicture)

                uploadedSuccessfully++
            }.onFailure { e ->
                // Report the error and continue to the next picture
                Timber.w(e, "Unable to upload workout picture")
                errors += e
            }
        }
        uploadedSuccessfully
    }

    private suspend fun uploadPicture(
        newWorkoutPicture: Picture,
        pictureFileName: String
    ): RemotePicture {
        return pictureRemoteApi.uploadPicture(
            workoutKey = newWorkoutPicture.workoutKey
                ?: throw IllegalStateException("Workout key cannot be null"),
            timestamp = newWorkoutPicture.timestamp,
            latitude = newWorkoutPicture.location?.latitude,
            longitude = newWorkoutPicture.location?.longitude,
            totaltime = newWorkoutPicture.totalTime,
            md5hash = newWorkoutPicture.md5Hash
                ?: throw IllegalStateException("MD5 hash cannot be null"),
            pictureFile = pictureFileRepository.getPictureFile(pictureFileName)
        )
    }

    private fun getUpdatedPicture(
        response: RemotePicture,
        newWorkoutPicture: Picture
    ): Picture {
        // Keep workoutId, Picture ID and timestamp from the local content. Backend returns a
        // different timestamp, but we keep using the local one so that the ordering of images
        // is stable.
        return response.toPicture(
            newWorkoutPicture.workoutId,
            newWorkoutPicture.indexInWorkoutHeader
        ).copy(id = newWorkoutPicture.id, timestamp = newWorkoutPicture.timestamp)
    }

    private suspend fun store(
        updatedPicture: Picture,
    ) {
        // Store the picture. Will trigger PICTURE_OR_VIDEO_STORED broadcast.
        picturesDataSource.savePicture(updatedPicture)
    }
}
