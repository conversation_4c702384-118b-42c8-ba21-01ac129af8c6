package com.stt.android.data.workout.sync

import com.stt.android.data.TimeUtils
import com.stt.android.domain.sync.aggregateAndThrowCapturedErrors
import com.stt.android.domain.workouts.WeChatWorkoutData
import com.stt.android.domain.workouts.WeChatWorkoutDataSource
import com.stt.android.remote.workout.RemoteWeChatWorkoutData
import com.stt.android.remote.workout.WeChatWorkoutDetailData
import com.stt.android.remote.workout.WeChatWorkoutRestAPI
import timber.log.Timber
import java.util.Locale
import javax.inject.Inject

class WeChatSyncWorkouts
@Inject constructor(
    private val weChatWorkoutDataSource: WeChatWorkoutDataSource,
    private val weChatWorkoutRestAPI: WeChatWorkoutRestAPI,
) {
    private val pageSize = 100
    suspend operator fun invoke() = aggregateAndThrowCapturedErrors { errors ->
        pushToRemote()
        deleteSyncedData()
    }

    private suspend fun deleteSyncedData() {
        try {
            weChatWorkoutDataSource.deleteSynced()
        } catch (e: Exception) {
            Timber.w(e, "delete synced data fail:${e.message}")
        }
    }

    private suspend fun pushToRemote() {
        val weChatWorkoutData: List<WeChatWorkoutData>
        try {
            weChatWorkoutData = weChatWorkoutDataSource.findUnSyncData()
        } catch (e: Exception) {
            Timber.w(e, "Unable to get unSynced newWorkouts from local DB")
            throw e
        }
        // syc data to remote
        val pagesWorkoutHeader = weChatWorkoutData.chunked(pageSize)
        pagesWorkoutHeader.forEach {
            try {
                val remoteWorkoutData = getRemoteWorkoutData(it)
                weChatWorkoutRestAPI.saveWorkoutData(remoteWorkoutData)
                Timber.d("save second workout data: $remoteWorkoutData")
                it.forEach { item ->
                    weChatWorkoutDataSource.updateStatus(item.id, true)
                }
            } catch (e: Exception) {
                Timber.w(e, "unable sync data to remote:${e.message}")
                throw e
            }
        }
    }

    private fun getRemoteWorkoutData(workouts: List<WeChatWorkoutData>): RemoteWeChatWorkoutData {
        val workoutDetailDataList = arrayListOf<WeChatWorkoutDetailData>()
        workouts.forEach { item ->
            val productModel = item.deviceName?.let { name ->
                val temp = name.replaceFirstChar {
                    if (it.isLowerCase()) it.titlecase(Locale.CHINA) else it.toString()
                }
                if (temp.endsWith("C")) {
                    temp.substring(0, temp.length - 1)
                } else {
                    temp
                }
            }
            val workoutDetailData = WeChatWorkoutDetailData(
                productModel = productModel,
                sn = item.deviceSerial,
                duration = item.totalTime,
                endTime = TimeUtils.millisecondToSecond(item.stopTime),
                // Kcal to Cal
                energy = item.energyConsumption * 1000,
                heartRateAvg = item.heartRateAverage,
                sportType = item.activityTypeId,
                startTime = TimeUtils.millisecondToSecond(item.startTime),
                totalDistance = item.totalDistance,
                // Int type field, such as the number of jump rope,it will be set when our watch supports it
                count = item.skipsCount,
            )
            workoutDetailDataList.add(workoutDetailData)
        }
        return RemoteWeChatWorkoutData(workoutDetailDataList)
    }
}
