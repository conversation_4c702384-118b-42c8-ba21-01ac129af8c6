package com.stt.android.data.workout.extensions

import com.stt.android.data.source.local.fitnessextension.FitnessExtensionDao
import com.stt.android.domain.user.workoutextension.FitnessExtension
import com.stt.android.domain.workouts.extensions.intensity.FitnessExtensionDataSource
import javax.inject.Inject

class FitnessExtensionRepository @Inject constructor(
    private val fitnessExtensionDao: FitnessExtensionDao,
    private val fitnessExtensionLocalMapper: FitnessExtensionLocalMapper,
) : FitnessExtensionDataSource {

    override suspend fun findById(id: Int): FitnessExtension? {
        return fitnessExtensionDao.findById(id)?.let {
            fitnessExtensionLocalMapper.toDomainEntity()(it)
        }
    }

    override suspend fun findLatestFitnessAge(username: String): Int? {
        return fitnessExtensionDao.findLatestFitnessAge(username)
    }

    override suspend fun findLatestVo2MaxInRangeByTime(
        username: String,
        vo2MaxRange: ClosedRange<Float>,
        untilMillis: Long,
    ): FitnessExtension? = fitnessExtensionDao.findLatestVo2MaxInRangeByTime(
        username = username,
        minVo2Max = vo2MaxRange.start,
        maxVo2Max = vo2MaxRange.endInclusive,
        untilMillis = untilMillis,
    )?.let {
        fitnessExtensionLocalMapper.toDomainEntity()(it)
    }
}
