package com.stt.android.data.workout.sync

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.workout.toRemoteUpdatedWorkout
import com.stt.android.domain.sync.aggregateAndThrowCapturedErrors
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.WorkoutHeaderDataSource
import com.stt.android.exceptions.remote.AskoError
import com.stt.android.remote.workout.WorkoutRemoteApi
import kotlinx.coroutines.runBlocking
import timber.log.Timber
import javax.inject.Inject

/**
 * Syncs modified workouts to the server and returns a pair of amount of updated/deleted workouts.
 *
 * Backend will update SummaryExtension's ascent & descent if the WorkoutHeader values get updated
 * for backwards compatibility with app versions that preferred the descent & ascent from extension
 */
class SyncUpdatedWorkouts
@Inject constructor(
    private val workoutHeaderDataSource: WorkoutHeaderDataSource,
    private val workoutRemoteApi: WorkoutRemoteApi
) {

    suspend operator fun invoke(): Pair<Int, Int> = sync()

    /**
     * can update a workout from param instead of all workout everytime
     */
    suspend fun sync(workoutHeader: WorkoutHeader? = null): Pair<Int, Int> {
        return aggregateAndThrowCapturedErrors { errors ->
            val remoteUpdatedWorkouts = workoutHeader?.let {
                listOf(listOf(it.toRemoteUpdatedWorkout()))
            } ?: run {
                workoutHeaderDataSource.getLocallyModifiedWorkouts()
                    .map { it.toRemoteUpdatedWorkout() }
                    // Chunk each update into separate list so that we can request each update separately.
                    // This is necessary due to the server failing to update the workouts when any
                    // of them doesn't exist on the server.
                    .chunked(1)
            }
            val syncedWorkouts = mutableSetOf<String>()
            val workoutsToDelete = mutableSetOf<String>()
            if (remoteUpdatedWorkouts.isNotEmpty()) {
                Timber.v("Updating remote workouts")
                remoteUpdatedWorkouts.forEach {
                    val workoutKey = it.first().workoutKey
                    Timber.v("Updating remote workout: $workoutKey")
                    runSuspendCatching {
                        val response = workoutRemoteApi.updateWorkouts(it)
                        if (response.getValue(workoutKey)) {
                            // Workout updated successfully
                            syncedWorkouts += workoutKey
                        } else {
                            // Workout update unsuccessful. This can happen if the workout has been
                            // deleted from the server by another device.
                            workoutsToDelete += workoutKey
                        }
                    }.onFailure { e ->
                        when (e) {
                            is AskoError.InvalidObjectID -> {
                                Timber.w(e, "Workout [$workoutKey] does not exist on the server")
                                workoutsToDelete += workoutKey
                            }

                            is NoSuchElementException -> {
                                // Should never happen, log anyways
                                Timber.w(
                                    e,
                                    "Tried to update workout $workoutKey, but the response did not include this key"
                                )
                                workoutsToDelete += workoutKey
                            }

                            else -> errors += e
                        }
                    }
                }
                if (syncedWorkouts.isNotEmpty()) {
                    workoutHeaderDataSource.markWorkoutsAsSynced(syncedWorkouts)
                }
                workoutsToDelete.forEach { workoutHeaderDataSource.remove(it) }
            }
            syncedWorkouts.size to workoutsToDelete.size
        }
    }

    companion object {
        @JvmStatic
        fun start(syncUpdatedWorkouts: SyncUpdatedWorkouts) {
            runBlocking {
                syncUpdatedWorkouts()
            }
        }
    }
}
