package com.stt.android.data.workout.extensions

import androidx.work.WorkManager
import com.stt.android.data.source.local.summaryextension.SummaryExtensionDao
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.domain.workouts.extensions.SummaryExtensionDataSource
import javax.inject.Inject

class SummaryExtensionRepository
@Inject constructor(
    private val summaryExtensionDao: SummaryExtensionDao,
    private val summaryExtensionLocalMapper: SummaryExtensionLocalMapper,
    private val workManager: dagger.Lazy<WorkManager>
) : SummaryExtensionDataSource {

    override suspend fun insert(summaryExtension: SummaryExtension) =
        summaryExtensionDao.insertAwait(
            summaryExtensionLocalMapper.toDataEntity()(summaryExtension)
        )

    override suspend fun fetchAll(): List<SummaryExtension> =
        summaryExtensionDao.fetchAllAwait()
            .run(summaryExtensionLocalMapper.toDomainEntityList())

    override suspend fun findByWorkoutId(workoutId: Int): SummaryExtension? =
        summaryExtensionDao.findByIdAwait(workoutId)
            ?.run(summaryExtensionLocalMapper.toDomainEntity())

    override fun scheduleSummaryExtensionUpdateWithZapps() {
        SummaryExtensionUpdateWithZappsWorker.enqueue(workManager.get())
    }

    override suspend fun findLatestLacticThHr(username: String): Float? {
        return summaryExtensionDao.findLatestLacticThHr(username)
    }

    override suspend fun findLatestLacticThPace(username: String): Float? {
        return summaryExtensionDao.findLatestLacticThPace(username)
    }
}
