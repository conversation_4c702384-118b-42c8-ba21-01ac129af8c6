package com.stt.android.data.workout.extensions

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.ExistingWorkPolicy
import androidx.work.ListenableWorker
import androidx.work.OneTimeWorkRequest
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.domain.sml.SmlExtensionUseCase
import com.stt.android.domain.workouts.extensions.SMLExtension
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.logbook.SmlParser
import com.stt.android.logbook.SuuntoLogbookSummaryContent
import timber.log.Timber
import java.io.ByteArrayInputStream
import java.util.zip.GZIPInputStream
import javax.inject.Inject

class SummaryExtensionUpdateWithZappsWorker(
    private val summaryExtensionRepository: SummaryExtensionRepository,
    private val smlExtensionUseCase: SmlExtensionUseCase,
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {

    private val parser = SmlParser()

    override suspend fun doWork(): Result {
        summaryExtensionRepository.fetchAll()
            .mapNotNull { summaryExtension ->
                kotlin.runCatching { summaryExtension.updateWithZapps() }
                    .onFailure {
                        Timber.w(it, "Error updating zapps for SummaryExtension: $summaryExtension")
                    }
                    .getOrNull()
            }
            .forEach { updatedSummaryExtension ->
                kotlin.runCatching { summaryExtensionRepository.insert(updatedSummaryExtension) }
                    .onFailure {
                        Timber.w(
                            it,
                            "Error inserting updated SummaryExtension: $updatedSummaryExtension"
                        )
                    }
            }
        return Result.success()
    }

    private suspend fun SummaryExtension.updateWithZapps(): SummaryExtension? {
        if (this.zapps.isNotEmpty()) return null
        val sml = smlExtensionUseCase.fetchSmlExtension(workoutId, null) ?: return null
        val zapps = parseSummaries(sml)?.zapps ?: return null
        if (zapps.isEmpty()) return null
        return this.copy(zapps = zapps)
    }

    private fun parseSummaries(sml: SMLExtension): SuuntoLogbookSummaryContent? {
        return try {
            parser.parseSmlSummary(
                GZIPInputStream(ByteArrayInputStream(sml.smlZip))
            )
        } catch (e: Exception) {
            Timber.w(e, "Error in reading or parsing Summary from SML")
            null
        }
    }

    class Factory
    @Inject constructor(
        private val summaryExtensionRepository: SummaryExtensionRepository,
        private val smlExtensionUseCase: SmlExtensionUseCase
    ) : CoroutineWorkerAssistedFactory {
        override fun create(context: Context, params: WorkerParameters): ListenableWorker {
            return SummaryExtensionUpdateWithZappsWorker(
                summaryExtensionRepository,
                smlExtensionUseCase,
                context,
                params
            )
        }
    }

    companion object {
        private const val WORK_NAME = "SummaryExtensionUpdateWithZappsWorker"

        @JvmStatic
        fun enqueue(
            workManager: WorkManager
        ) {
            Timber.d("Enqueuing $WORK_NAME")

            workManager.enqueueUniqueWork(
                WORK_NAME,
                ExistingWorkPolicy.REPLACE,
                OneTimeWorkRequest.from(SummaryExtensionUpdateWithZappsWorker::class.java)
            )
        }
    }
}
