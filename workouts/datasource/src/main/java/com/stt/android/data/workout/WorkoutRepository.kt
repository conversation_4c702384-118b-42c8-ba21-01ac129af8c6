package com.stt.android.data.workout

import android.content.Context
import com.stt.android.data.source.local.tags.UserTagsDao
import com.stt.android.data.source.local.workout.LocalSummaryWorkoutHeader
import com.stt.android.data.source.local.workout.WorkoutHeaderDao
import com.stt.android.data.source.local.workouts.WorkoutsLastFetchTimestampStore
import com.stt.android.data.tags.toLocal
import com.stt.android.data.user.toDomainUser
import com.stt.android.data.workout.attributes.toRemote
import com.stt.android.domain.tags.UserTag
import com.stt.android.domain.user.User
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.AdditionalData
import com.stt.android.domain.workouts.BasicWorkoutHeader
import com.stt.android.domain.workouts.DomainWorkout
import com.stt.android.domain.workouts.Extension
import com.stt.android.domain.workouts.SummaryWorkoutHeader
import com.stt.android.domain.workouts.WorkoutDataSource
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.ZoneSenseSyncWorkoutHeader
import com.stt.android.domain.workouts.competition.CompetitionWorkoutSummary
import com.stt.android.domain.workouts.stats.WorkoutStats
import com.stt.android.domain.workouts.summary.UserWorkoutSummaryByActivity
import com.stt.android.domain.workouts.summary.WorkoutFeeling
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.exceptions.InternalDataException
import com.stt.android.infomodel.ActivityMapping
import com.stt.android.infomodel.getActivitySummaryForActivity
import com.stt.android.remote.workout.RemoteSuuntoTag
import com.stt.android.remote.workout.RemoteSyncedWorkout
import com.stt.android.remote.workout.WorkoutRemoteApi
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers.Default
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.EnumSet
import javax.inject.Inject

class WorkoutRepository
@Inject constructor(
    @ApplicationContext private val appContext: Context,
    private val workoutRemoteApi: WorkoutRemoteApi,
    private val workoutRemoteExtensionMapper: WorkoutRemoteExtensionMapper,
    private val workoutsLastFetchTimestampStore: WorkoutsLastFetchTimestampStore,
    private val workoutHeaderDao: WorkoutHeaderDao,
    private val userTagsDao: UserTagsDao,
    private val uploadWorkoutTasksProvider: UploadWorkoutTasksProvider
) : WorkoutDataSource {
    override suspend fun fetchWorkout(workoutKey: String): DomainWorkout = workoutRemoteApi
        .getWorkout(
            workoutKey = workoutKey,
        )
        .toDomainWorkout(workoutRemoteExtensionMapper)

    override suspend fun fetchCombinedWorkout(
        username: String,
        workoutKey: String,
        extensions: EnumSet<Extension>?,
        additionalData: EnumSet<AdditionalData>?
    ): DomainWorkout {
        return workoutRemoteApi.getCombinedWorkout(
            username,
            workoutKey,
            extensions?.joinToString(separator = ",") { it.value },
            additionalData?.joinToString(separator = ",") { it.value }
        )
            .toDomainWorkout(workoutRemoteExtensionMapper)
    }

    override suspend fun store(workoutHeader: WorkoutHeader): WorkoutHeader {
        val key = workoutHeader.key
        workoutHeaderDao.upsert(workoutHeader.toLocal())
        return if (key != null) {
            workoutHeaderDao.findByKeySync(key)!!.toDomain()
        } else {
            workoutHeaderDao.findById(workoutHeader.id)!!.toDomain()
        }
    }

    override suspend fun fetchWorkoutHeader(id: Int): Flow<WorkoutHeader?> {
        return workoutHeaderDao.fetchById(id).map { header ->
            val userTags = header?.run { userTagsDao.getAllUserTagsForWorkoutId(this.id) }
            header?.toDomain(userTags)
        }
    }

    override suspend fun fetchFolloweesWorkoutsPaged(): Flow<List<DomainWorkout>> {
        val previousTimestamp =
            workoutsLastFetchTimestampStore.getLastFetchTimestampForFolloweesWorkouts()

        return fetchWorkoutsPaged(
            previousTimestamp,
            { timestamp -> workoutRemoteApi.fetchFolloweesWorkoutsPaged(since = timestamp) },
            { fetchedTimestamp ->
                workoutsLastFetchTimestampStore.setLastFetchTimestampForFolloweesWorkouts(
                    fetchedTimestamp
                )
            }
        )
    }

    override suspend fun fetchOwnWorkoutsPaged(): Flow<List<DomainWorkout>> {
        val previousTimestamp =
            workoutsLastFetchTimestampStore.getLastFetchTimestampForOwnWorkouts()

        return fetchWorkoutsPaged(
            previousTimestamp,
            { timestamp -> workoutRemoteApi.fetchOwnWorkoutsPaged(since = timestamp) },
            { fetchedTimestamp ->
                workoutsLastFetchTimestampStore.setLastFetchTimestampForOwnWorkouts(
                    fetchedTimestamp
                )
            }
        )
    }

    private suspend fun fetchWorkoutsPaged(
        previousTimestamp: Long,
        flowGetter: suspend (timestamp: Long) -> Flow<Pair<Long, List<RemoteSyncedWorkout>>>,
        storeLatestFetchTimestamp: suspend (timestamp: Long) -> Unit
    ): Flow<List<DomainWorkout>> {
        // If a workout created before previousTimestamp is edited after it, we might not get the updates.
        // One example of this is that if followee changes their private workout to be shared, we might
        // never download it if we did a sync between that workout's creation and modification.
        // To limit the effects of this issue, we download two days worth of workouts again,
        // as newer workouts are more likely to have been edited.
        val previousTimestampWithModificationBuffer =
            (previousTimestamp - TWO_DAYS_IN_MILLISECONDS).coerceAtLeast(0)
        var latestFetchTimestamp: Long? = null
        return flowGetter(previousTimestampWithModificationBuffer)
            .onEach {
                latestFetchTimestamp = it.first
            }
            .onCompletion {
                latestFetchTimestamp?.let { storeLatestFetchTimestamp(it) }
            }
            .map {
                it.second.map { remote ->
                    remote.toDomainWorkout(workoutRemoteExtensionMapper)
                }
            }
    }

    override suspend fun fetchPublicWorkouts(
        lowerlat: Double,
        lowerlng: Double,
        upperlat: Double,
        upperlng: Double,
        limit: Int
    ): List<Pair<User, DomainWorkout>> = withContext(Default) {
        workoutRemoteApi.fetchPublicWorkouts(
            lowerlat = lowerlat,
            lowerlng = lowerlng,
            upperlat = upperlat,
            upperlng = upperlng,
            limit = limit
        ).map {
            it.user.toDomainUser() to it.workout.toDomainWorkout(workoutRemoteExtensionMapper)
        }
    }

    override suspend fun fetchWorkoutStatsForUser(username: String): WorkoutStats =
        withContext(Default) {
            workoutRemoteApi.fetchWorkoutStatsForUser(username).toDomain()
        }

    override suspend fun uploadLocalWorkoutData(): List<Result<Pair<Any, String>>> {
        Timber.d("Uploading local workout data to the server")
        return uploadWorkoutTasksProvider.provideTasks()
    }

    override fun clearOwnWorkoutsFetchedTimestamp() {
        workoutsLastFetchTimestampStore.clearOwnWorkoutsFetchedTimestamp()
    }

    override fun clearFolloweesWorkoutsFetchedTimestamp() {
        workoutsLastFetchTimestampStore.clearFolloweesWorkoutsFetchedTimestamp()
    }

    override suspend fun clearOldFolloweesWorkoutsAboveMaxCount(currentUser: User) {
        workoutHeaderDao.deleteAllNotFromUserAboveMaxCount(
            currentUser.username,
            MAX_FOLLOWEES_WORKOUTS
        )
    }

    override suspend fun clearOldFolloweesWorkouts(currentUser: User) {
        workoutHeaderDao.deleteAllNotFromUser(currentUser.username)
    }

    override suspend fun fetchUserWorkoutSummaryByActivity(
        username: String,
        minStartTime: Long,
        maxStartTime: Long,
    ): Map<Int, UserWorkoutSummaryByActivity> {
        return workoutHeaderDao.loadUserWorkoutSummaryByActivity(
            username = username,
            since = minStartTime,
            until = maxStartTime
        ).associateBy { it.activityId }
            .mapValues { (_, value) -> value.toDomain() }
    }

    override suspend fun fetchUserWorkoutFeelings(
        username: String,
        minStartTime: Long,
        maxStartTime: Long
    ): List<WorkoutFeeling> {
        return workoutHeaderDao.loadUserWorkoutFeelings(
            username = username,
            since = minStartTime,
            until = maxStartTime
        ).map { it.toDomain() }
    }

    override suspend fun fetchUserWorkoutsBasic(
        username: String,
        minStartTime: Long,
        maxStartTime: Long
    ): List<BasicWorkoutHeader> {
        return workoutHeaderDao.loadUserBasicWorkoutHeaders(
            username = username,
            since = minStartTime,
            until = maxStartTime
        ).map { it.toDomain() }
    }

    override fun loadLatestWorkoutsOfActivityTypes(
        username: String,
        activityTypes: List<Int>?,
        maxCount: Int,
        minDurationSeconds: Long,
        fromTimestampMillis: Long
    ): List<WorkoutHeader> {
        try {
            return workoutHeaderDao.loadLatestWorkoutsOfActivityTypes(
                username = username,
                activityTypes = activityTypes,
                maxCount = maxCount,
                minDurationSeconds = minDurationSeconds,
                since = fromTimestampMillis,
                until = null
            ).map { it.toDomain() }
        } catch (e: Exception) {
            throw InternalDataException(
                "Unable to fetch latest workout of activity types $activityTypes from local database: ${e.message}",
                e
            )
        }
    }

    override fun getAllCommuteWorkouts(
        username: String,
        minimumStartTime: Long?,
        maximumStartTime: Long?
    ): Flow<List<WorkoutHeader>> {
        return workoutHeaderDao.getAllCommuteWorkouts(username, minimumStartTime, maximumStartTime)
            .map {
                it.map { workouts -> workouts.toDomain() }
            }
    }

    override fun getAllWorkoutsForSummary(
        username: String,
        activityIds: List<Int>?,
        minStartTimeInclusive: Long?,
        maxStartTimeInclusive: Long?,
        minTotalDistanceInclusive: Double?,
        maxTotalDistanceInclusive: Double?,
        suuntoTags: List<SuuntoTag>?,
        userTags: List<UserTag>?
    ): Flow<List<SummaryWorkoutHeader>> {
        val excludeWorkoutsWithDistance =
            minTotalDistanceInclusive != null && minTotalDistanceInclusive > 0

        return workoutHeaderDao.getAllWorkoutsForSummary(
            username = username,
            activityIds = activityIds.takeUnless { it?.isEmpty() == true },
            minStartTimeInclusive = minStartTimeInclusive,
            maxStartTimeInclusive = maxStartTimeInclusive,
            minTotalDistanceInclusive = minTotalDistanceInclusive,
            maxTotalDistanceInclusive = maxTotalDistanceInclusive,
            suuntoTags = suuntoTags?.map { it.toLocalSuuntoTag() },
            userTags = userTags?.map { it.toLocal() }
        ).map { headers: List<LocalSummaryWorkoutHeader> ->
            headers.map { localSummaryWorkoutHeader ->
                getActivitySummaryForActivity(
                    ActivityMapping.entries
                        .firstOrNull { mapping ->
                            mapping.stId == localSummaryWorkoutHeader.activityId
                        }
                ).let {
                    localSummaryWorkoutHeader.toDomain(
                        summaryItems = it.items,
                        supportsDistance = it.hasDistance,
                        supportsAscent = it.hasAscent
                    )
                }
            }.filter { !excludeWorkoutsWithDistance || it.supportsDistance }
        }
    }

    override suspend fun getAllWorkoutsForZoneSenseSync(username: String): List<ZoneSenseSyncWorkoutHeader> =
        workoutHeaderDao.getAllWorkoutsForZoneSenseSync(username).map { it.toDomain() }

    override suspend fun getWorkoutsForZoneSense(
        username: String,
        activityTypeIds: Set<Int>,
        untilInMillsSinceEpoch: Long,
    ): List<ZoneSenseSyncWorkoutHeader> = workoutHeaderDao.getWorkoutsForZoneSense(
        username = username,
        activityTypeIds = activityTypeIds,
        untilInMillsSinceEpoch = untilInMillsSinceEpoch,
    ).map { it.toDomain() }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun hasWorkouts(username: String): Flow<Boolean> =
        workoutHeaderDao.workoutsCount(username = username)
            .mapLatest { it > 0 }
            .distinctUntilChanged()

    override suspend fun loadAllDoneActivityTypes(username: String): List<Int> =
        workoutHeaderDao.loadAllDoneActivityTypes(username)

    override suspend fun loadRecentActivityTypesIds(username: String, limit: Int): List<Int> =
        workoutHeaderDao.loadRecentActivityTypesIds(username, limit)

    override suspend fun updateSharingFlagsByUsername(username: String, sharingFlags: Int) {
        workoutHeaderDao.updateSharingFlagsByUsername(username, sharingFlags)
    }

    override suspend fun fetchCompetitionWorkoutResult(
        username: String,
        workoutKey: String
    ): CompetitionWorkoutSummary =
        withContext(Default) {
            workoutRemoteApi.fetchCompetitionWorkoutResult(username, workoutKey).toDomain()
        }

    override suspend fun fetchWorkoutsPage(
        username: String,
        page: Int,
        pageSize: Int,
        withMediaOnly: Boolean,
    ): List<DomainWorkout> {
        // TODO Compare with local & save
        return withContext(IO) {
            val list = workoutRemoteApi.fetchWorkoutByUsername(
                username,
                (page - 1) * pageSize,
                pageSize,
                withMediaOnly
            )
                .map {
                    it.toDomainWorkout(workoutRemoteExtensionMapper)
                }
            list
        }
    }

    override suspend fun searchWorkouts(
        username: String,
        query: String,
        page: Int,
        pageSize: Int,
    ): List<DomainWorkout> {
        val activityIdAndNames = ActivityType.values().map {
            it.id to ActivityType.getLocalizedNameByActivityId(appContext.resources, it.id)
        }
        val suuntoTagAndNames =
            SuuntoTag.entries.map { it.toRemote() to appContext.getString(it.nameRes) }
        val (activityIds, suuntoTags) = findMatchingActivityIdsAndSuuntoTags(
            query,
            activityIdAndNames,
            suuntoTagAndNames
        )
        return withContext(IO) {
            workoutRemoteApi.searchWorkouts(
                username,
                activityIds,
                suuntoTags,
                query,
                (page - 1) * pageSize,
                pageSize
            )
                .map { it.toDomainWorkout(workoutRemoteExtensionMapper) }
        }
    }

    private fun findMatchingActivityIdsAndSuuntoTags(
        query: String,
        activityIdAndNames: List<Pair<Int, String>>,
        suuntoTagAndNames: List<Pair<RemoteSuuntoTag, String>>
    ): Pair<List<Int>, List<RemoteSuuntoTag>> {
        val normalizedQuery = query.trim().lowercase()
        if (normalizedQuery.isEmpty()) return emptyList<Int>() to emptyList()

        fun matches(name: String): Boolean {
            val lowerName = name.lowercase()
            if (normalizedQuery.contains(lowerName)) return true
            if (lowerName.contains(normalizedQuery)) return true
            return normalizedQuery.any { ch -> lowerName.contains(ch) }
        }

        val matchedActivityIds = activityIdAndNames
            .filter { (_, name) -> matches(name) }
            .map { (id, _) -> id }

        val matchedSuuntoTags = suuntoTagAndNames
            .filter { (_, name) -> matches(name) }
            .map { (tag, _) -> tag }

        return matchedActivityIds to matchedSuuntoTags
    }

    companion object {
        private const val TWO_DAYS_IN_MILLISECONDS = 1000 * 60 * 60 * 48
        const val MAX_FOLLOWEES_WORKOUTS = 200
    }
}
