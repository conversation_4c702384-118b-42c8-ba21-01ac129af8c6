package com.stt.android.data.workout.attributes.update

import com.soy.algorithms.tss.TSSCalculationMethod
import com.stt.android.data.source.local.workout.attributes.LocalWorkoutAttributeTSS
import com.stt.android.data.source.local.workout.attributes.LocalWorkoutAttributeTSSCalculationMethod
import com.stt.android.data.source.local.workout.attributes.LocalWorkoutAttributes
import com.stt.android.data.source.local.workout.attributes.LocalWorkoutAttributesUpdate
import com.stt.android.data.source.local.workout.attributes.LocalWorkoutLocation
import com.stt.android.domain.workouts.attributes.DomainWorkoutAttributes
import com.stt.android.domain.workouts.attributes.DomainWorkoutAttributesUpdate
import com.stt.android.domain.workouts.attributes.DomainWorkoutLocation
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.domain.workouts.tss.TSS

fun DomainWorkoutAttributesUpdate.toLocalEntity() =
    LocalWorkoutAttributesUpdate(
        workoutId,
        ownerUsername,
        attributes.toLocalEntity(),
        fieldsToDelete,
        requiresUserConfirmation
    )

fun DomainWorkoutAttributes.toLocalEntity() =
    LocalWorkoutAttributes(
        startPosition?.toLocalEntity(),
        tss?.toLocalEntity(),
        maxSpeed,
        totalAscent,
        totalDescent,
        suuntoTags?.map { it.name }
    )

fun DomainWorkoutLocation.toLocalEntity(): LocalWorkoutLocation =
    LocalWorkoutLocation(latitude, longitude)

fun TSS.toLocalEntity(): LocalWorkoutAttributeTSS = LocalWorkoutAttributeTSS(
    trainingStressScore,
    calculationMethod.toLocal(),
    intensityFactor,
    normalizedPower,
    averageGradeAdjustedPace
)

fun TSSCalculationMethod.toLocal(): LocalWorkoutAttributeTSSCalculationMethod = when (this) {
    TSSCalculationMethod.POWER -> LocalWorkoutAttributeTSSCalculationMethod.POWER
    TSSCalculationMethod.PACE -> LocalWorkoutAttributeTSSCalculationMethod.PACE
    TSSCalculationMethod.HR -> LocalWorkoutAttributeTSSCalculationMethod.HR
    TSSCalculationMethod.SWIM_PACE -> LocalWorkoutAttributeTSSCalculationMethod.SWIM_PACE
    TSSCalculationMethod.MET -> LocalWorkoutAttributeTSSCalculationMethod.MET
    TSSCalculationMethod.MANUAL -> LocalWorkoutAttributeTSSCalculationMethod.MANUAL
    TSSCalculationMethod.DYNAMIC_DFA -> LocalWorkoutAttributeTSSCalculationMethod.DYNAMIC_DFA
}

fun LocalWorkoutAttributesUpdate.toDomainEntity() =
    DomainWorkoutAttributesUpdate(
        workoutId,
        ownerUsername,
        attributes?.toDomainEntity() ?: DomainWorkoutAttributes(null, null, null, null, null, null),
        fields,
        requiresUserConfirmation
    )

fun LocalWorkoutAttributes.toDomainEntity(): DomainWorkoutAttributes =
    DomainWorkoutAttributes(
        startPosition?.toDomainEntity(),
        tss?.toDomainEntity(),
        maxSpeed,
        ascent,
        descent,
        suuntoTags?.map { SuuntoTag.valueOf(it) }
    )

fun LocalWorkoutLocation.toDomainEntity(): DomainWorkoutLocation =
    DomainWorkoutLocation(latitude, longitude)

fun LocalWorkoutAttributeTSS.toDomainEntity(): TSS = TSS(
    trainingStressScore,
    calculationMethod.toDomain(),
    intensityFactor,
    normalizedPower,
    averageGradeAdjustedPace
)

fun LocalWorkoutAttributeTSSCalculationMethod.toDomain(): TSSCalculationMethod = when (this) {
    LocalWorkoutAttributeTSSCalculationMethod.POWER -> TSSCalculationMethod.POWER
    LocalWorkoutAttributeTSSCalculationMethod.PACE -> TSSCalculationMethod.PACE
    LocalWorkoutAttributeTSSCalculationMethod.HR -> TSSCalculationMethod.HR
    LocalWorkoutAttributeTSSCalculationMethod.SWIM_PACE -> TSSCalculationMethod.SWIM_PACE
    LocalWorkoutAttributeTSSCalculationMethod.MET -> TSSCalculationMethod.MET
    LocalWorkoutAttributeTSSCalculationMethod.MANUAL -> TSSCalculationMethod.MANUAL
    LocalWorkoutAttributeTSSCalculationMethod.DYNAMIC_DFA -> TSSCalculationMethod.DYNAMIC_DFA
}
