package com.stt.android.data.reactions

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.ExistingWorkPolicy
import androidx.work.ListenableWorker
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.OneTimeWorkRequestWithNetwork
import com.stt.android.data.session.CurrentUser
import com.stt.android.data.shouldRescheduleOrFailJob
import com.stt.android.domain.workouts.reactions.Reaction
import com.stt.android.domain.workouts.reactions.ReactionDataSource
import com.stt.android.exceptions.remote.AskoError
import com.stt.android.remote.otp.GenerateOTPUseCase
import com.stt.android.remote.reactions.ReactionRestApi
import kotlinx.coroutines.async
import kotlinx.coroutines.supervisorScope
import timber.log.Timber
import javax.inject.Inject

class ReactionRemoteSyncJob(
    context: Context,
    params: WorkerParameters,
    private val reactionDataSource: ReactionDataSource,
    private val reactionRestApi: ReactionRestApi,
    private val currentUser: CurrentUser,
    private val generateOTPUseCase: GenerateOTPUseCase
) : CoroutineWorker(context, params) {

    private val results = mutableListOf<Result>()

    override suspend fun doWork(): Result {
        Timber.d("Reaction sync started")
        runSuspendCatching {
            pushNewReactions()
            pushDeletedReactions()
        }.onFailure { e ->
            results += shouldRescheduleOrFailJob(e)
        }

        val retry = results.any { it == Result.retry() }
        val failure = results.any { it == Result.failure() }

        return when {
            retry -> Result.retry()
            failure -> Result.failure()
            else -> Result.success()
        }
    }

    private suspend fun pushNewReactions() = supervisorScope {
        val reactionsToSync =
            reactionDataSource.findUnsyncedNotDeletedReactionsByUserName(currentUser.getUsername())
        if (reactionsToSync.isNotEmpty()) {
            Timber.d("Syncing new reactions")
            val successReactions = mutableListOf<Reaction>()
            val failedReactions = mutableListOf<Reaction>()

            reactionsToSync.map {
                it to async {
                    val totp = runCatching { generateOTPUseCase.generateTOTP() }
                        .onFailure { e -> Timber.w(e, "Error generating TOTP in pushNewReactions()") }
                        .getOrNull() ?: ""
                    reactionRestApi.saveReaction(totp, it.workoutKey)
                }
            }.forEach { (reaction, request) ->
                Timber.v("Syncing new reaction to workout key: %s", reaction.workoutKey)
                runSuspendCatching {
                    val key = request.await().payloadOrThrow()
                    successReactions += reaction.copy(key = key)
                }.onFailure { e ->
                    if (e is AskoError.ReactionToWorkoutFailed) {
                        Timber.d(e, "Failed to sync new reaction, possible duplicate")
                        failedReactions += reaction
                    } else {
                        Timber.w(e, "Failed to sync new reaction")
                        results += shouldRescheduleOrFailJob(e)
                    }
                }
            }

            if (successReactions.isNotEmpty()) {
                // Update reaction server keys
                Timber.v("Updating reaction server keys")
                reactionDataSource.storeReactions(successReactions)
            }

            if (failedReactions.isNotEmpty()) {
                // Remove from local db if the backend response was REACTION_TO_WORKOUT_FAILED
                // and the reactions are duplicate
                reactionDataSource.removeDuplicateReactions(failedReactions)
            }
        }
    }

    private suspend fun pushDeletedReactions() = supervisorScope {
        val reactionsToSync = reactionDataSource.findDeletedReactions(currentUser.getUsername())
        if (reactionsToSync.isNotEmpty()) {
            Timber.d("Syncing deleted reactions")
            val successfullySynced = reactionsToSync.map { it to async { reactionRestApi.deleteReaction(it.workoutKey) } }
                .mapNotNull { (reaction, request) ->
                    Timber.v("Syncing deleted reaction to workout key: %s", reaction.workoutKey)
                    runSuspendCatching {
                        request.await()
                        reaction
                    }.getOrElse { e ->
                        Timber.w(e, "Failed to sync deleted reaction")
                        results += shouldRescheduleOrFailJob(e)
                        null
                    }
                }
            reactionDataSource.removeReactions(successfullySynced)
        }
    }

    class Factory
    @Inject constructor(
        private val reactionDataSource: ReactionDataSource,
        private val reactionRestApi: ReactionRestApi,
        private val currentUser: CurrentUser,
        private val generateOTPUseCase: GenerateOTPUseCase
    ) : CoroutineWorkerAssistedFactory {
        override fun create(context: Context, params: WorkerParameters): ListenableWorker {
            return ReactionRemoteSyncJob(
                context,
                params,
                reactionDataSource,
                reactionRestApi,
                currentUser,
                generateOTPUseCase
            )
        }
    }

    companion object {
        @JvmStatic
        fun enqueue(workManager: WorkManager) {
            Timber.d("Enqueuing")
            workManager.enqueueUniqueWork(
                "ReactionRemoteSyncJob",
                ExistingWorkPolicy.REPLACE,
                OneTimeWorkRequestWithNetwork<ReactionRemoteSyncJob>()
            )
        }
    }
}
