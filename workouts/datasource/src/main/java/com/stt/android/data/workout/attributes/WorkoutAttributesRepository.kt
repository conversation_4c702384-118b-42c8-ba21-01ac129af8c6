package com.stt.android.data.workout.attributes

import com.stt.android.data.workout.WorkoutRemoteExtensionMapper
import com.stt.android.data.workout.toDomainWorkout
import com.stt.android.domain.workouts.DomainWorkout
import com.stt.android.domain.workouts.attributes.DomainWorkoutAttributes
import com.stt.android.domain.workouts.attributes.WorkoutAttributesDataSource
import com.stt.android.remote.workout.WorkoutRemoteApi
import javax.inject.Inject

class WorkoutAttributesRepository
@Inject constructor(
    private val workoutRemoteExtensionMapper: WorkoutRemoteExtensionMapper,
    private val workoutRemoteApi: WorkoutRemoteApi
) : WorkoutAttributesDataSource {
    override suspend fun updateWorkoutAttributes(
        workoutId: Int,
        workoutKey: String,
        attributes: DomainWorkoutAttributes
    ): DomainWorkout = workoutRemoteApi.updateWorkoutAttributes(
        workoutKey,
        attributes.toRemoteEntity()
    ).toDomainWorkout(workoutRemoteExtensionMapper, workoutId)

    override suspend fun deleteWorkoutAttributes(
        workoutId: Int,
        workoutKey: String,
        fields: String
    ): DomainWorkout = workoutRemoteApi.deleteWorkoutAttributes(workoutKey, fields)
        .toDomainWorkout(workoutRemoteExtensionMapper, workoutId)
}
