package com.stt.android.data.workout.extensions

import com.stt.android.data.EntityMapper
import com.stt.android.data.source.local.summaryextension.LocalSummaryExtension
import com.stt.android.data.source.local.summaryextension.LocalZapp
import com.stt.android.data.source.local.summaryextension.LocalZappChannel
import com.stt.android.data.source.local.summaryextension.LocalZappSummaryOutput
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.logbook.SuuntoLogbookZapp
import javax.inject.Inject

class SummaryExtensionLocalMapper
@Inject constructor() : EntityMapper<LocalSummaryExtension, SummaryExtension> {

    override fun toDomainEntity(): Function1<LocalSummaryExtension, SummaryExtension> = {
        SummaryExtension(
            workoutId = it.workoutId,
            pte = it.pte,
            feeling = it.feeling,
            avgTemperature = it.avgTemperature,
            peakEpoc = it.peakEpoc,
            avgPower = it.avgPower,
            avgCadence = it.avgCadence,
            avgSpeed = it.avgSpeed,
            ascentTime = it.ascentTime,
            descentTime = it.descentTime,
            performanceLevel = it.performanceLevel,
            recoveryTime = it.recoveryTime,
            ascent = it.ascent,
            descent = it.descent,
            deviceHardwareVersion = it.deviceHardwareVersion,
            deviceSoftwareVersion = it.deviceSoftwareVersion,
            productType = it.productType,
            displayName = it.displayName,
            deviceName = it.deviceName,
            deviceSerialNumber = it.deviceSerialNumber,
            deviceManufacturer = it.deviceManufacturer,
            exerciseId = it.exerciseId,
            zapps = it.zapps.map { zapp -> zapp.toDomain() },
            repetitionCount = it.repetitionCount,
            maxCadence = it.maxCadence,
            avgStrideLength = it.avgStrideLength,
            fatConsumption = it.fatConsumption,
            carbohydrateConsumption = it.carbohydrateConsumption,
            avgGroundContactTime = it.avgGroundContactTime,
            avgVerticalOscillation = it.avgVerticalOscillation,
            avgLeftGroundContactBalance = it.avgLeftGroundContactBalance,
            avgRightGroundContactBalance = it.avgRightGroundContactBalance,
            lacticThHr = it.lacticThHr,
            lacticThPace = it.lacticThPace,
            avgAscentSpeed = it.avgAscentSpeed,
            maxAscentSpeed = it.maxAscentSpeed,
            avgDescentSpeed = it.avgDescentSpeed,
            maxDescentSpeed = it.maxDescentSpeed,
            avgDistancePerStroke = it.avgDistancePerStroke,
        )
    }

    override fun toDataEntity(): Function1<SummaryExtension, LocalSummaryExtension> = {
        LocalSummaryExtension(
            workoutId = it.workoutId,
            pte = it.pte,
            feeling = it.feeling,
            avgTemperature = it.avgTemperature,
            peakEpoc = it.peakEpoc,
            avgPower = it.avgPower,
            avgCadence = it.avgCadence,
            avgSpeed = it.avgSpeed,
            ascentTime = it.ascentTime,
            descentTime = it.descentTime,
            performanceLevel = it.performanceLevel,
            recoveryTime = it.recoveryTime,
            ascent = it.ascent,
            descent = it.descent,
            deviceHardwareVersion = it.deviceHardwareVersion,
            deviceSoftwareVersion = it.deviceSoftwareVersion,
            productType = it.productType,
            displayName = it.displayName,
            deviceName = it.deviceName,
            deviceSerialNumber = it.deviceSerialNumber,
            deviceManufacturer = it.deviceManufacturer,
            exerciseId = it.exerciseId,
            zapps = it.zapps.map { zapp -> zapp.toData() },
            repetitionCount = it.repetitionCount,
            maxCadence = it.maxCadence,
            avgStrideLength = it.avgStrideLength,
            fatConsumption = it.fatConsumption,
            carbohydrateConsumption = it.carbohydrateConsumption,
            avgGroundContactTime = it.avgGroundContactTime,
            avgVerticalOscillation = it.avgVerticalOscillation,
            avgLeftGroundContactBalance = it.avgLeftGroundContactBalance,
            avgRightGroundContactBalance = it.avgRightGroundContactBalance,
            lacticThHr = it.lacticThHr,
            lacticThPace = it.lacticThPace,
            avgAscentSpeed = it.avgAscentSpeed,
            maxAscentSpeed = it.maxAscentSpeed,
            avgDescentSpeed = it.avgDescentSpeed,
            maxDescentSpeed = it.maxDescentSpeed,
            avgDistancePerStroke = it.avgDistancePerStroke,
        )
    }

    private fun LocalZapp.toDomain() = SuuntoLogbookZapp(
        id = id,
        authorId = authorId,
        externalId = externalId,
        name = name,
        summaryOutputs = summaryOutputs?.map { it.toDomain() },
        channels = channels?.map { it.toDomain() }
    )

    private fun LocalZappSummaryOutput.toDomain() = SuuntoLogbookZapp.SummaryOutput(
        format = format,
        id = id,
        name = name,
        postfix = postfix,
        summaryValue = summaryValue
    )

    private fun LocalZappChannel.toDomain() = SuuntoLogbookZapp.ZappChannel(
        channelId = channelId,
        format = format,
        inverted = inverted,
        name = name,
        variableId = variableId,
    )

    private fun SuuntoLogbookZapp.toData() = LocalZapp(
        id = id,
        authorId = authorId,
        externalId = externalId,
        name = name,
        summaryOutputs = summaryOutputs?.mapNotNull { it.toData() },
        channels = channels?.map { it.toData() }
    )

    private fun SuuntoLogbookZapp.SummaryOutput.toData(): LocalZappSummaryOutput? =
        if (summaryValue != null) {
            LocalZappSummaryOutput(
                format = format,
                id = id,
                name = name,
                postfix = postfix,
                summaryValue = summaryValue!!
            )
        } else {
            null
        }

    private fun SuuntoLogbookZapp.ZappChannel.toData() = LocalZappChannel(
        channelId = channelId,
        format = format,
        inverted = inverted,
        name = name,
        variableId = variableId,
    )
}
