package com.stt.android.data.workout.sync

import com.stt.android.coroutines.forEachAsync
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.sync.aggregateAndThrowCapturedErrors
import com.stt.android.domain.workouts.WorkoutHeaderDataSource
import com.stt.android.exceptions.remote.AskoError
import com.stt.android.remote.workout.WorkoutRemoteApi
import timber.log.Timber
import javax.inject.Inject

/**
 * Syncs deleted workouts to the server and returns the amount of deleted workouts
 */
class SyncDeletedWorkouts
@Inject constructor(
    private val workoutHeaderDataSource: WorkoutHeaderDataSource,
    private val workoutRemoteApi: WorkoutRemoteApi
) {
    suspend operator fun invoke(): Int = aggregateAndThrowCapturedErrors { errors ->
        var deleted = 0
        workoutHeaderDataSource.getDeletedWorkoutsKeys().forEachAsync { key ->
            Timber.v("Deleting remote workout with key: $key")
            runSuspendCatching {
                if (workoutRemoteApi.deleteWorkout(key)) {
                    workoutHeaderDataSource.remove(key).also { deleted++ }
                }
            }.onFailure { e ->
                when (e) {
                    is AskoError.InvalidObjectID,
                    is AskoError.NotOwnWorkout,
                    is AskoError.WorkoutNotFound -> {
                        workoutHeaderDataSource.remove(key).also { deleted++ }
                    }
                    else -> {
                        Timber.w(e, "Unable to delete remote workout")
                        errors += e
                    }
                }
            }
        }
        deleted
    }
}
