package com.stt.android.data.workout.extensions

import com.stt.android.data.source.local.diveextension.DiveExtensionDao
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.domain.workouts.extensions.DiveExtensionDataSource
import javax.inject.Inject

class DiveExtensionRepository
@Inject constructor(
    private val diveExtensionDao: DiveExtensionDao,
    private val diveExtensionLocalMapper: DiveExtensionLocalMapper
) : DiveExtensionDataSource {
    override suspend fun findById(id: Int): DiveExtension? {
        return diveExtensionDao.findByIdSuspendable(id)?.run(
            diveExtensionLocalMapper.toDomainEntity()
        )
    }
}
