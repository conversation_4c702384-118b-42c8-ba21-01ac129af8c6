package com.stt.android.data.sml

import com.stt.android.data.Local
import com.stt.android.data.Remote
import com.stt.android.domain.sml.SmlDataSource
import com.stt.android.domain.workouts.extensions.SMLExtension
import javax.inject.Inject

class SmlRepository @Inject constructor(
    @Local private val smlLocalDataSource: SmlDataSource,
    @Remote private val smlRemoteDataSource: SmlDataSource
) : SmlDataSource {
    override suspend fun fetchSmlExtension(workoutId: Int, workoutKey: String?): SMLExtension? {
        val local = smlLocalDataSource.fetchSmlExtension(workoutId, workoutKey)
        if (local != null) {
            return local
        }

        return smlRemoteDataSource.fetchSmlExtension(workoutId, workoutKey)
            ?.also { fetched ->
                saveSmlExtension(fetched)
            }
    }

    override suspend fun saveSmlExtension(smlExtension: SMLExtension) {
        smlLocalDataSource.saveSmlExtension(smlExtension)
    }
}
