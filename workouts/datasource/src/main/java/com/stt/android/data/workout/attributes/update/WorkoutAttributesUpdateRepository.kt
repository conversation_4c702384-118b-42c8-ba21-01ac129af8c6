package com.stt.android.data.workout.attributes.update

import com.stt.android.data.source.local.workout.attributes.WorkoutAttributesUpdateDao
import com.stt.android.domain.workouts.attributes.DomainWorkoutAttributesUpdate
import com.stt.android.domain.workouts.attributes.WorkoutAttributesUpdateDataSource
import javax.inject.Inject

class WorkoutAttributesUpdateRepository
@Inject constructor(
    private val workoutAttributesUpdateDao: WorkoutAttributesUpdateDao
) : WorkoutAttributesUpdateDataSource {
    override suspend fun addWorkoutAttributesUpdate(update: DomainWorkoutAttributesUpdate) =
        workoutAttributesUpdateDao.insert(update.toLocalEntity())

    override suspend fun removeWorkoutAttributesUpdate(update: DomainWorkoutAttributesUpdate) =
        workoutAttributesUpdateDao.remove(update.toLocalEntity())

    override suspend fun fetchUnsyncedWorkoutAttributesUpdate(ownerUsername: String): List<DomainWorkoutAttributesUpdate> =
        workoutAttributesUpdateDao.fetchUnsynced(ownerUsername)
            .map { it.toDomainEntity() }

    override suspend fun fetchUnconfirmedWorkoutAttributeUpdate(
        workoutId: Int,
        ownerUsername: String
    ): DomainWorkoutAttributesUpdate? =
        workoutAttributesUpdateDao.fetchUnconfirmed(workoutId, ownerUsername)?.toDomainEntity()

    override suspend fun fetchUnsyncedWorkoutAttributesUpdate(
        workoutId: Int,
        ownerUsername: String
    ): DomainWorkoutAttributesUpdate? =
        workoutAttributesUpdateDao.fetchUnsynced(workoutId, ownerUsername)?.toDomainEntity()
}
