package com.stt.android.data.workout.pictures

import com.stt.android.remote.workout.picture.PictureRemoteApi
import com.stt.android.remote.workout.picture.RemotePicture
import java.io.File
import javax.inject.Inject

class WorkoutPictureUploadRepository @Inject constructor(
    private val pictureRemoteApi: PictureRemoteApi
) {
    suspend fun uploadPictureToRemote(
        workoutKey: String,
        timestamp: Long,
        latitude: Double?,
        longitude: Double?,
        totaltime: Double,
        md5hash: String,
        pictureFile: File
    ): RemotePicture {
        return pictureRemoteApi.uploadPicture(
            workoutKey,
            timestamp,
            latitude,
            longitude,
            totaltime,
            md5hash,
            pictureFile
        )
    }
}
