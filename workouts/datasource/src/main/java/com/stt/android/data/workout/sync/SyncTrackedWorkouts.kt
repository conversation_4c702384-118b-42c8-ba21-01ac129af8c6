package com.stt.android.data.workout.sync

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.source.local.smlzip.SMLZipReferenceDao
import com.stt.android.data.workout.binary.BinaryFileRepository
import com.stt.android.domain.sync.aggregateAndThrowCapturedErrors
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.WorkoutHeaderDataSource
import timber.log.Timber
import java.io.File
import javax.inject.Inject

/**
 * Syncs newly tracked workouts to the server
 */
class SyncTrackedWorkouts @Inject constructor(
    private val binaryFileRepository: BinaryFileRepository,
    private val workoutHeaderDataSource: WorkoutHeaderDataSource,
    private val smlZipReferenceDao: SMLZipReferenceDao,
    private val syncNewWorkout: SyncNewWorkout,
) {

    suspend operator fun invoke() = sync()

    /**
     * can sync a workout from param instead of all workout
     */
    suspend fun sync(workoutHeader: WorkoutHeader? = null) =
        aggregateAndThrowCapturedErrors { errors ->
            val workoutHeaders = workoutHeader?.let {
                it.key?.let { emptyList() } ?: listOf(workoutHeader)
            } ?: run {
                runSuspendCatching {
                    workoutHeaderDataSource.findNewUnsyncedWorkouts()
                }.getOrElse { e ->
                    Timber.w(e, "Unable to get unsynced workouts from local DB")
                    throw e
                }
            }

            workoutHeaders.forEach { workoutHeader ->
                runSuspendCatching {
                    Timber.v("Updating binary file for workout ${workoutHeader.id}")
                    binaryFileRepository.update(workoutHeader)
                }.onFailure { e ->
                    val smlSize = workoutHeader.getSmlSize()
                    val hasSml = (smlSize ?: -1L) > 0L
                    Timber.w(
                        e,
                        "Failed to update binary file for new tracked workout %d, SML size %d",
                        workoutHeader.id,
                        smlSize,
                    )

                    // For workouts synced from watch (i.e. has SML), we only upload the SML file, so
                    // continue the syncing process.
                    if (!hasSml) {
                        errors += e
                        return@forEach
                    }
                }

                runSuspendCatching {
                    Timber.v("Syncing newly tracked workout ${workoutHeader.id}")
                    syncNewWorkout(workoutHeader)
                }.onFailure { e ->
                    Timber.w(
                        e,
                        "Failed to sync new tracked workout ${workoutHeader.id}, SML size ${workoutHeader.getSmlSize()}"
                    )
                    errors += e
                }
            }
        }

    private suspend fun WorkoutHeader.getSmlSize(): Long? = runSuspendCatching {
        smlZipReferenceDao.findById(id)
            ?.zipPath
            ?.let(::File)
            ?.length()
    }.getOrNull()
}
