package com.stt.android.data.ranking

import com.stt.android.data.source.local.RankingDao
import com.stt.android.domain.ranking.Ranking
import com.stt.android.domain.ranking.RankingDataSource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class RankingRepository @Inject constructor(
    private val rankingDao: RankingDao,
    private val rankingMapper: RankingMapper,
) : RankingDataSource {
    override fun fetchRankings(workoutKey: String): Flow<List<Ranking>> {
        return rankingDao.findAllByWorkoutKey(workoutKey)
            .map { it.map(rankingMapper.toDomainEntity()) }
    }

    override suspend fun insertRankings(rankings: List<Ranking>) {
        val localRankings = rankings.map { ranking ->
            rankingMapper.toDataEntity()(ranking)
        }
        rankingDao.insert(localRankings)
    }

    override suspend fun deleteRankings(workoutKey: String) {
        rankingDao.deleteBy<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(workoutKey)
    }
}
