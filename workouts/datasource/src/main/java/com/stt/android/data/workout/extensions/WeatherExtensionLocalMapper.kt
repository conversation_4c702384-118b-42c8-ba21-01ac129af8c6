package com.stt.android.data.workout.extensions

import com.stt.android.data.EntityMapper
import com.stt.android.data.source.local.weatherextension.LocalWeatherExtension
import com.stt.android.domain.workouts.extensions.WeatherExtension
import javax.inject.Inject

class WeatherExtensionLocalMapper
@Inject constructor() : EntityMapper<LocalWeatherExtension, WeatherExtension> {

    override fun toDomainEntity(): Function1<LocalWeatherExtension, WeatherExtension> = {
        WeatherExtension(
            workoutId = it.workoutId,
            airPressure = it.airPressure,
            cloudiness = it.cloudiness,
            groundLevelAirPressure = it.groundLevelAirPressure,
            humidity = it.humidity,
            rainVolume1h = it.rainVolume1h,
            rainVolume3h = it.rainVolume3h,
            seaLevelAirPressure = it.seaLevelAirPressure,
            snowVolume1h = it.snowVolume1h,
            snowVolume3h = it.snowVolume3h,
            temperature = it.temperature,
            weatherIcon = it.weatherIcon,
            windDirection = it.windDirection,
            windSpeed = it.windSpeed
        )
    }

    override fun toDataEntity(): Function1<WeatherExtension, LocalWeatherExtension> = {
        LocalWeatherExtension(
            workoutId = it.workoutId ?: throw IllegalStateException("Workout ID cannot be null"),
            airPressure = it.airPressure,
            cloudiness = it.cloudiness,
            groundLevelAirPressure = it.groundLevelAirPressure,
            humidity = it.humidity,
            rainVolume1h = it.rainVolume1h,
            rainVolume3h = it.rainVolume3h,
            seaLevelAirPressure = it.seaLevelAirPressure,
            snowVolume1h = it.snowVolume1h,
            snowVolume3h = it.snowVolume3h,
            temperature = it.temperature,
            weatherIcon = it.weatherIcon,
            windDirection = it.windDirection,
            windSpeed = it.windSpeed
        )
    }
}
