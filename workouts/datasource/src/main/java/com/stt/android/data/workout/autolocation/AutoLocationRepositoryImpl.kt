package com.stt.android.data.workout.autolocation

import com.stt.android.data.source.local.workouts.AutoLocationSettingStore
import com.stt.android.domain.workouts.autolocation.AutoLocationRepository
import javax.inject.Inject

class AutoLocationRepositoryImpl
@Inject constructor(
    private val autoLocationSettingStore: AutoLocationSettingStore
) : AutoLocationRepository {
    override suspend fun fetchIsAutoLocationEnabled(): <PERSON><PERSON><PERSON> =
        autoLocationSettingStore.getAutoLocationSetting()

    override fun setAutoLocationSetting(value: Boolean) =
        autoLocationSettingStore.setAutoLocationSetting(value)
}
