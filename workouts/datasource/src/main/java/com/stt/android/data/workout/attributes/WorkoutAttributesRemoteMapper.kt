package com.stt.android.data.workout.attributes

import com.soy.algorithms.tss.TSSCalculationMethod
import com.stt.android.data.workout.toDomain
import com.stt.android.domain.workouts.attributes.DomainWorkoutAttributes
import com.stt.android.domain.workouts.attributes.DomainWorkoutLocation
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.domain.workouts.tss.TSS
import com.stt.android.remote.workout.RemoteSuuntoTag
import com.stt.android.remote.workout.RemoteTSS
import com.stt.android.remote.workout.RemoteTSSCalculationMethod
import com.stt.android.remote.workout.RemoteWorkoutAttributes
import com.stt.android.remote.workout.RemoteWorkoutLocation

fun DomainWorkoutAttributes.toRemoteEntity() =
    RemoteWorkoutAttributes(
        startPosition?.toRemoteEntity(),
        tss?.toRemoteEntity(),
        maxSpeed,
        totalAscent,
        totalDescent,
        suuntoTags?.map { it.toRemote() },
        userTags?.mapNotNull { it.key }
    )

fun DomainWorkoutLocation.toRemoteEntity(): RemoteWorkoutLocation =
    RemoteWorkoutLocation(latitude, longitude)

fun TSS.toRemoteEntity(): RemoteTSS = RemoteTSS(
    trainingStressScore,
    calculationMethod.toRemote(),
    intensityFactor,
    normalizedPower,
    averageGradeAdjustedPace
)

fun TSSCalculationMethod.toRemote(): RemoteTSSCalculationMethod = when (this) {
    TSSCalculationMethod.POWER -> RemoteTSSCalculationMethod.POWER
    TSSCalculationMethod.PACE -> RemoteTSSCalculationMethod.PACE
    TSSCalculationMethod.HR -> RemoteTSSCalculationMethod.HR
    TSSCalculationMethod.SWIM_PACE -> RemoteTSSCalculationMethod.SWIM_PACE
    TSSCalculationMethod.MET -> RemoteTSSCalculationMethod.MET
    TSSCalculationMethod.MANUAL -> RemoteTSSCalculationMethod.MANUAL
    TSSCalculationMethod.DYNAMIC_DFA -> RemoteTSSCalculationMethod.DYNAMIC_DFA
}

fun RemoteWorkoutAttributes.toDomainEntity(): DomainWorkoutAttributes =
    DomainWorkoutAttributes(
        startPosition?.toDomainEntity(),
        tss?.toDomain(),
        maxSpeed,
        totalAscent,
        totalDescent,
        suuntoTags?.mapNotNull { it.toDomain() }
    )

fun RemoteWorkoutLocation.toDomainEntity(): DomainWorkoutLocation =
    DomainWorkoutLocation(latitude, longitude)

fun SuuntoTag.toRemote(): RemoteSuuntoTag = when (this) {
    SuuntoTag.COMMUTE -> RemoteSuuntoTag.COMMUTE
    SuuntoTag.MARATHON -> RemoteSuuntoTag.MARATHON
    SuuntoTag.HALF_MARATHON -> RemoteSuuntoTag.HALF_MARATHON
    SuuntoTag.IMPACT_SPEED_AND_AGILITY -> RemoteSuuntoTag.IMPACT_SPEED_AND_AGILITY
    SuuntoTag.IMPACT_SPEED_AND_STRENGTH -> RemoteSuuntoTag.IMPACT_SPEED_AND_STRENGTH
    SuuntoTag.IMPACT_FLEXIBILITY -> RemoteSuuntoTag.IMPACT_FLEXIBILITY
    SuuntoTag.IMPACT_STRENGTH -> RemoteSuuntoTag.IMPACT_STRENGTH
    SuuntoTag.IMPACT_ABOVE_THRESHOLD_VO2MAX -> RemoteSuuntoTag.IMPACT_ABOVE_THRESHOLD_VO2MAX
    SuuntoTag.IMPACT_HARD_ANAEROBIC_EFFORT -> RemoteSuuntoTag.IMPACT_HARD_ANAEROBIC_EFFORT
    SuuntoTag.IMPACT_ANAEROBIC_THRESHOLD -> RemoteSuuntoTag.IMPACT_ANAEROBIC_THRESHOLD
    SuuntoTag.IMPACT_AEROBIC_TO_ANAEROBIC -> RemoteSuuntoTag.IMPACT_AEROBIC_TO_ANAEROBIC
    SuuntoTag.IMPACT_HARD_LONG_AEROBIC_BASE -> RemoteSuuntoTag.IMPACT_HARD_LONG_AEROBIC_BASE
    SuuntoTag.IMPACT_LONG_AEROBIC_BASE -> RemoteSuuntoTag.IMPACT_LONG_AEROBIC_BASE
    SuuntoTag.IMPACT_AEROBIC -> RemoteSuuntoTag.IMPACT_AEROBIC
    SuuntoTag.IMPACT_EASY_RECOVERY -> RemoteSuuntoTag.IMPACT_EASY_RECOVERY
}
