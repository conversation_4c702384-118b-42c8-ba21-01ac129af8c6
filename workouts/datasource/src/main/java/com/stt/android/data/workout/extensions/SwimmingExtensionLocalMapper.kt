package com.stt.android.data.workout.extensions

import com.stt.android.data.EntityMapper
import com.stt.android.data.source.local.swimmingextension.LocalSwimmingExtension
import com.stt.android.domain.workouts.extensions.SwimmingExtension
import javax.inject.Inject

class SwimmingExtensionLocalMapper
@Inject constructor() : EntityMapper<LocalSwimmingExtension, SwimmingExtension> {

    override fun toDomainEntity(): Function1<LocalSwimmingExtension, SwimmingExtension> = {
        SwimmingExtension(
            it.workoutId,
            it.avgSwolf,
            it.avgStrokeRate,
            it.breathingRate,
            it.breaststrokeDuration,
            it.breaststrokePercent,
            it.breaststrokeGlideTime,
            it.breaststrokeMaxBreathAngle,
            it.breaststrokeAvgBreathAngle,
            it.freestyleDuration,
            it.freestylePercent,
            it.freestyleMaxBreathAngle,
            it.freestyleAvgBreathAngle,
            it.freestylePitchAngle,
            it.breaststrokeHeadAngle,
        )
    }

    override fun toDataEntity(): Function1<SwimmingExtension, LocalSwimmingExtension> = {
        LocalSwimmingExtension(
            it.workoutId ?: throw IllegalStateException("Workout ID cannot be null"),
            it.avg<PERSON>wolf,
            it.avgStrokeRate,
            it.breathingRate,
            it.breaststrokeDuration,
            it.breaststrokePercentage,
            it.breaststrokeGlideTime,
            it.breaststrokeMaxBreathAngle,
            it.breaststrokeAvgBreathAngle,
            it.freestyleDuration,
            it.freestylePercentage,
            it.freestyleMaxBreathAngle,
            it.freestyleAvgBreathAngle,
            it.freestylePitchAngle,
            it.breaststrokeHeadAngle,
        )
    }
}
