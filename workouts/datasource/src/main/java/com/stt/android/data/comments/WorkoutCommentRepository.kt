package com.stt.android.data.comments

import com.stt.android.data.Local
import com.stt.android.domain.comments.WorkoutCommentDataSource
import com.stt.android.domain.workouts.comment.DomainWorkoutComment
import com.stt.android.remote.comments.WorkoutCommentsRemoteApi
import javax.inject.Inject

/**
 * Repository definition to save and load workout comments.
 */
class WorkoutCommentRepository
@Inject constructor(
    @Local private val workoutCommentDataSource: WorkoutCommentDataSource, // TODO replace with Room DAO interface once migrated TP #87987
    private val workoutCommentsRemoteApi: WorkoutCommentsRemoteApi
) : WorkoutCommentDataSource {
    override suspend fun removeByWorkoutKey(workoutKey: String) {
        workoutCommentDataSource.removeByWorkoutKey(workoutKey)
    }

    override suspend fun saveComment(workoutKey: String?, comments: List<DomainWorkoutComment>) {
        workoutCommentDataSource.saveComment(workoutKey, comments)
    }

    override suspend fun removeComment(commentKey: String) {
        workoutCommentsRemoteApi.deleteComment(commentKey)
        workoutCommentDataSource.removeComment(commentKey)
    }
}
