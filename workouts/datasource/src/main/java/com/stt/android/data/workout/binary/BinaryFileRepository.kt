package com.stt.android.data.workout.binary

import com.stt.android.domain.workouts.WorkoutHeader
import java.io.File

interface BinaryFileRepository {
    /**
     * Creates a dummy workout binary
     */
    suspend fun create(workoutHeader: WorkoutHeader)

    /**
     * Updates workout binary file
     */
    suspend fun update(workoutHeader: WorkoutHeader)

    /**
     * Gets the workout's binary file
     */
    suspend fun get(workoutHeader: WorkoutHeader): File
}
