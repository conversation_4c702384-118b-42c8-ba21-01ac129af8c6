package com.stt.android.data.workout.attributes

import androidx.annotation.VisibleForTesting
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.session.CurrentUser
import com.stt.android.data.workout.WorkoutHeaderRepository
import com.stt.android.domain.tags.UserTagsRepository
import com.stt.android.domain.workouts.DomainWorkout
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.attributes.DomainWorkoutAttributes
import com.stt.android.domain.workouts.attributes.DomainWorkoutAttributesUpdate
import com.stt.android.domain.workouts.attributes.WorkoutAttributesDataSource
import com.stt.android.domain.workouts.attributes.WorkoutAttributesUpdateDataSource
import timber.log.Timber
import javax.inject.Inject

/**
 * Sends pending workout attributes updates to the backend
 */
class SyncWorkoutAttributes
@Inject constructor(
    private val currentUser: CurrentUser,
    private val workoutAttributesUpdateRepository: WorkoutAttributesUpdateDataSource,
    private val workoutAttributesRepository: WorkoutAttributesDataSource,
    private val workoutHeaderRepository: WorkoutHeaderRepository,
    private val tagsRepository: UserTagsRepository
) {
    suspend operator fun invoke() {
        syncNonNullableAttributes()
        syncUserTagsAttribute()
    }

    private suspend fun syncNonNullableAttributes() {
        val unsyncedAttributes = runSuspendCatching {
            workoutAttributesUpdateRepository.fetchUnsyncedWorkoutAttributesUpdate(currentUser.getUsername())
        }.getOrElse { e ->
            Timber.w(e, "Unable to get pending workout attributes updates from local DB, skipping...")
            return
        }

        unsyncedAttributes.forEach { unsyncedUpdate ->
            runSuspendCatching {
                val workoutId = unsyncedUpdate.workoutId
                val header = workoutHeaderRepository.findById(workoutId)
                if (header == null) {
                    onUpdateHandled(unsyncedUpdate)
                } else {
                    val workoutKey =
                        header.key ?: return@runSuspendCatching // workout not yet synced to backend
                    val attributes = unsyncedUpdate.attributes
                    val fieldsToDelete = unsyncedUpdate.fieldsToDelete

                    val hasUpdates = !attributes.isEmpty
                    val hasDeletions = fieldsToDelete.isNotEmpty()

                    if (!hasUpdates && !hasDeletions) {
                        onUpdateHandled(unsyncedUpdate)
                        return@runSuspendCatching
                    }

                    val headerWithUpdates = if (hasUpdates) {
                        updateAttributes(workoutId, workoutKey, attributes).header
                    } else {
                        header
                    }

                    val headerWithDeletions = if (hasDeletions) {
                        deleteAttributes(workoutId, workoutKey, fieldsToDelete).header
                    } else {
                        header
                    }

                    fun getHeaderWithValueFor(updatedValue: Any?, fieldKey: String) = when {
                        updatedValue != null -> headerWithUpdates
                        fieldsToDelete.contains(fieldKey) -> headerWithDeletions
                        else -> header
                    }

                    val copyLocationFrom = getHeaderWithValueFor(attributes.startPosition, DomainWorkoutAttributesUpdate.FIELD_START_POSITION)
                    val copyTssFrom = getHeaderWithValueFor(attributes.tss, DomainWorkoutAttributesUpdate.FIELD_TSS)
                    val copyMaxSpeedFrom = getHeaderWithValueFor(attributes.maxSpeed, DomainWorkoutAttributesUpdate.FIELD_MAX_SPEED)
                    val copyAscentFrom = getHeaderWithValueFor(attributes.totalAscent, DomainWorkoutAttributesUpdate.FIELD_TOTAL_ASCENT)
                    val copyDescentFrom = getHeaderWithValueFor(attributes.totalDescent, DomainWorkoutAttributesUpdate.FIELD_TOTAL_DESCENT)
                    val copySuuntoTagsFrom = getHeaderWithValueFor(attributes.suuntoTags, DomainWorkoutAttributesUpdate.FIELD_SUUNTO_TAGS)

                    val mergedHeader = header.copy(
                        startPosition = copyLocationFrom.startPosition,
                        centerPosition = copyLocationFrom.centerPosition,
                        stopPosition = copyLocationFrom.stopPosition,
                        polyline = copyLocationFrom.polyline,
                        tss = copyTssFrom.tss,
                        maxSpeed = copyMaxSpeedFrom.maxSpeed,
                        totalAscent = copyAscentFrom.totalAscent,
                        totalDescent = copyDescentFrom.totalDescent,
                        suuntoTags = copySuuntoTagsFrom.suuntoTags
                    )

                    workoutHeaderRepository.storeWorkout(mergedHeader)
                    onUpdateHandled(unsyncedUpdate)
                }
            }.onFailure { e ->
                Timber.w(e, "Unable to sync workout attributes")
            }
        }
    }

    private suspend fun syncUserTagsAttribute() {
        runSuspendCatching {
            val workoutsWithUnsyncedUserTags: List<WorkoutHeader> = workoutHeaderRepository.findWorkoutsWithUnsyncedUserTags()

            for (workoutHeader in workoutsWithUnsyncedUserTags) {
                val workoutKey = workoutHeader.key ?: continue // findWorkoutsWithUnsyncedUserTags() returns only workout headers with non nullable keys anyway

                val currentUserTags = tagsRepository
                    .findNonDeletedUserTagsForAWorkout(workoutId = workoutHeader.id)
                    .filter { !it.key.isNullOrBlank() } // We send to backend only synced user tags that actually has a remote key

                runSuspendCatching {
                    updateAttributes(
                        workoutId = workoutHeader.id,
                        workoutKey = workoutKey,
                        DomainWorkoutAttributes(
                            userTags = currentUserTags
                        )
                    )
                    for (userTag in currentUserTags) {
                        tagsRepository.markUserTagInWorkoutAsSynced(workoutHeader.id, userTag.id!!)
                    }
                    tagsRepository.deleteRemovedUserTagsFromAWorkout(workoutHeader.id)
                }.onFailure { e ->
                    Timber.d(e)
                }
            }
        }.onFailure { e ->
            Timber.w(e, "Unable to get unsynced user tags from local DB")
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    suspend fun onUpdateHandled(unsyncedUpdate: DomainWorkoutAttributesUpdate) {
        workoutAttributesUpdateRepository.removeWorkoutAttributesUpdate(unsyncedUpdate)
    }

    private suspend fun updateAttributes(
        workoutId: Int,
        workoutKey: String,
        attributes: DomainWorkoutAttributes
    ): DomainWorkout =
        workoutAttributesRepository.updateWorkoutAttributes(
            workoutId,
            workoutKey,
            attributes
        )

    private suspend fun deleteAttributes(
        workoutId: Int,
        workoutKey: String,
        fields: List<String>
    ): DomainWorkout =
        workoutAttributesRepository.deleteWorkoutAttributes(
            workoutId,
            workoutKey,
            fields.joinToString(",")
        )
}
