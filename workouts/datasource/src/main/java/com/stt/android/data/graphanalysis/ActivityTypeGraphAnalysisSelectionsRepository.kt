package com.stt.android.data.graphanalysis

import com.stt.android.core.domain.GraphType
import com.stt.android.core.domain.SuuntoPlusChannel
import com.stt.android.data.source.local.graphanalysis.ActivityTypeGraphAnalysisSelectionsSharedPrefsStorage
import com.stt.android.data.source.local.graphanalysis.LocalGraphType
import com.stt.android.data.source.local.graphanalysis.LocalSuuntoPlusChannel
import com.stt.android.domain.graphanalysis.ActivityTypeGraphAnalysisSelectionsDataSource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class ActivityTypeGraphAnalysisSelectionsRepository @Inject constructor(
    private val sharedPrefsStorage: ActivityTypeGraphAnalysisSelectionsSharedPrefsStorage
) : ActivityTypeGraphAnalysisSelectionsDataSource {
    override fun observeSelectedGraphsForActivityType(activityTypeId: Int): Flow<List<GraphType>?> =
        sharedPrefsStorage.observeSelectedGraphsForActivityType(activityTypeId)
            .map { list -> list?.map { it.toDomain() } }

    override fun saveSelectedGraphTypesForActivityType(
        activityTypeId: Int,
        mainGraphType: GraphType,
        comparisonGraphType: GraphType,
        backgroundGraphType: GraphType,
    ) = sharedPrefsStorage.saveSelectedGraphTypesForActivityType(
        activityTypeId,
        mainGraphType.toLocal(),
        comparisonGraphType.toLocal(),
        backgroundGraphType.toLocal()
    )
}

private fun GraphType.toLocal(): LocalGraphType = when (this) {
    is GraphType.Summary -> LocalGraphType.Summary(summaryGraph)
    is GraphType.SuuntoPlus -> LocalGraphType.SuuntoPlus(suuntoPlusChannel.toLocal())
}

private fun LocalGraphType.toDomain() = when (this) {
    is LocalGraphType.Summary -> GraphType.Summary(summaryGraph)
    is LocalGraphType.SuuntoPlus -> GraphType.SuuntoPlus(suuntoPlusChannel.toDomain())
}

fun LocalSuuntoPlusChannel.toDomain() = SuuntoPlusChannel(
    zappId = zappId,
    channelId = channelId,
    format = format,
    inverted = inverted,
    name = name,
    variableId = variableId,
)

fun SuuntoPlusChannel.toLocal() = LocalSuuntoPlusChannel(
    zappId = zappId,
    channelId = channelId,
    format = format,
    inverted = inverted,
    name = name,
    variableId = variableId,
)
