package com.stt.android.data.workout.attributes.update;

import com.stt.android.data.source.local.DaoFactory;
import com.stt.android.data.source.local.workout.attributes.WorkoutAttributesUpdateDao;
import com.stt.android.domain.workouts.attributes.WorkoutAttributesUpdateDataSource;
import dagger.Binds;
import dagger.Module;
import dagger.Provides;

@Module
public abstract class WorkoutAttributesUpdateModule {
    @Binds
    abstract WorkoutAttributesUpdateDataSource bindWorkoutAttributesUpdateRepository(
        WorkoutAttributesUpdateRepository repository
    );

    @Provides
    public static WorkoutAttributesUpdateDao provideRouteDao(DaoFactory daoFactory) {
        return daoFactory.getWorkoutAttributesUpdateDao();
    }
}
