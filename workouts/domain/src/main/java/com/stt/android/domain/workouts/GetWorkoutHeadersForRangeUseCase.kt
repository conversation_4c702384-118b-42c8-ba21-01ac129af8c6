package com.stt.android.domain.workouts

import com.stt.android.domain.CoroutineUseCase
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.withContext
import javax.inject.Inject

class GetWorkoutHeadersForRangeUseCase
@Inject constructor(
    private val workoutHeaderDataSource: WorkoutHeaderDataSource
) : CoroutineUseCase<List<WorkoutHeader>, GetWorkoutHeadersForRangeUseCase.Params> {

    override suspend fun run(params: Params): List<WorkoutHeader> = withContext(IO) {
        workoutHeaderDataSource.findNotDeletedByRange(
            params.username,
            params.activityTypeId,
            params.sinceMs,
            params.untilMs
        )
    }

    data class Params(
        val username: String,
        val activityTypeId: Int?,
        val sinceMs: Long,
        val untilMs: Long
    )
}
