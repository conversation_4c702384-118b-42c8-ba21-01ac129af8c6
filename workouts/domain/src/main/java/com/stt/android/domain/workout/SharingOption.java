package com.stt.android.domain.workout;

import com.stt.android.analytics.AnalyticsPropertyValue;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public enum SharingOption {
    NOT_SHARED(0),
    FOLLOWERS(1 << 4),
    EVERYONE(1 << 1),
    FACEBOOK(1 << 2),
    TWITTER(1 << 3),
    LINK(1);

    public static final SharingOption DEFAULT = NOT_SHARED;
    public static final int SHARED_MASK = 1;
    public static final int PUBLIC_MASK = 2;

    private final int backendId;

    SharingOption(int backendId) {
        this.backendId = backendId;
    }

    public static SharingOption valueOf(int position) {
        for (SharingOption sharingOption : values()) {
            if (position == sharingOption.ordinal()) {
                return sharingOption;
            }
        }
        throw new IllegalArgumentException("Invalid SharingOption ordinal value");
    }

    public static List<SharingOption> valuesOf(int sharingFlags) {
        List<SharingOption> result = new ArrayList<>();
        if ((sharingFlags & SharingOption.FOLLOWERS.getBackendId()) != 0) {
            result.add(SharingOption.FOLLOWERS);
        }
        if ((sharingFlags & SharingOption.FACEBOOK.getBackendId()) != 0) {
            result.add(SharingOption.FACEBOOK);
        }
        if ((sharingFlags & SharingOption.TWITTER.getBackendId()) != 0) {
            result.add(SharingOption.TWITTER);
        }
        if ((sharingFlags & SharingOption.EVERYONE.getBackendId()) != 0) {
            result.add(SharingOption.EVERYONE);
        }
        if (result.isEmpty()) {
            result.add(SharingOption.NOT_SHARED);
        }
        return Collections.unmodifiableList(result);
    }

    public static int flagOf(List<SharingOption> sharingOptions) {
        int flag = 0;
        for (int i = 0; i < sharingOptions.size(); i++) {
            SharingOption sharingOption = sharingOptions.get(i);

            if (sharingOption != NOT_SHARED) {
                flag |= sharingOption.getBackendId();
            }
        }
        if (flag != 0) {
            flag |= SHARED_MASK;
        }
        return flag;
    }

    public int getBackendId() {
        return backendId;
    }

    public static String getVisibility(List<SharingOption> sharingOptions, boolean isShared) {
        if (sharingOptions.contains(SharingOption.EVERYONE)) {
            return AnalyticsPropertyValue.SharingOptionVisibilityProperty.PUBLIC;
        } else if (sharingOptions.contains(SharingOption.FOLLOWERS)) {
            return AnalyticsPropertyValue.SharingOptionVisibilityProperty.FOLLOWERS;
        } else if (isShared) {
            return AnalyticsPropertyValue.SharingOptionVisibilityProperty.SHARED_BY_LINK;
        } else {
            return AnalyticsPropertyValue.SharingOptionVisibilityProperty.PRIVATE;
        }
    }
}
