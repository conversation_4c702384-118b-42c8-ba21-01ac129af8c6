package com.stt.android.domain.workouts

import android.content.SharedPreferences
import com.stt.android.domain.comments.WorkoutCommentDataSource
import com.stt.android.domain.ranking.RankingDataSource
import com.stt.android.domain.tags.UserTagsRepository
import com.stt.android.domain.user.CurrentUserDataSource
import com.stt.android.domain.workouts.extensions.ExtensionsDataSource
import com.stt.android.domain.workouts.pictures.PicturesDataSource
import com.stt.android.domain.workouts.reactions.ReactionSummaryDataSource
import com.stt.android.domain.workouts.videos.VideoDataSource
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

/**
 * Use case for fetching followees workouts from the server and storing them to local store
 */
class FetchAndStoreFolloweesWorkoutsUseCase
@Inject constructor(
    private val workoutDataSource: WorkoutDataSource,
    private val currentUserDataSource: CurrentUserDataSource,
    picturesDataSource: PicturesDataSource,
    videoDataSource: VideoDataSource,
    reactionDataSource: ReactionSummaryDataSource,
    rankingDataSource: RankingDataSource,
    workoutHeaderDataSource: WorkoutHeaderDataSource,
    workoutCommentDataSource: WorkoutCommentDataSource,
    extensionsDataSource: ExtensionsDataSource,
    sharedPreferences: SharedPreferences,
    userTagsRepository: UserTagsRepository
) : AbstractFetchAndStoreWorkouts(
    picturesDataSource,
    videoDataSource,
    reactionDataSource,
    rankingDataSource,
    workoutHeaderDataSource,
    workoutCommentDataSource,
    extensionsDataSource,
    sharedPreferences,
    userTagsRepository
) {
    suspend operator fun invoke() {
        Timber.d("Fetching followees workouts")
        val workouts = withContext(Dispatchers.IO) {
            workoutDataSource.fetchFolloweesWorkoutsPaged()
                .firstOrNull()
                ?: emptyList()
        }
        store(workouts, ownWorkouts = false)
        // https://suunto.tpondemand.com/entity/174263-the-followers-can-see-my-activities
        // After the follower makes the workout private, the cloud will not return the exercise,
        // but our local still have the workout, so the bug still exists, let's fix it later.
        workoutDataSource.clearOldFolloweesWorkoutsAboveMaxCount(
            currentUserDataSource.getCurrentUser()
        )
    }
}
