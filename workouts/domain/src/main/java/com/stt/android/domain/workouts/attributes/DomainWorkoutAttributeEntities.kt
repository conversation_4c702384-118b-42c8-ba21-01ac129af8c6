package com.stt.android.domain.workouts.attributes

import com.stt.android.domain.tags.UserTag
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.domain.workouts.tss.TSS

data class DomainWorkoutAttributesUpdate(
    val workoutId: Int,
    val ownerUsername: String,
    val attributes: DomainWorkoutAttributes = DomainWorkoutAttributes(
        null,
        null,
        null,
        null,
        null,
        null,
        null
    ),
    val fieldsToDelete: List<String>,
    val requiresUserConfirmation: Boolean
) {
    fun updateWith(other: DomainWorkoutAttributesUpdate): DomainWorkoutAttributesUpdate {
        val updatedFieldsToDelete = fieldsToDelete.toMutableSet()
        var mergedStartPosition: DomainWorkoutLocation? = workoutLocation
        var mergedTSS: TSS? = tss
        var mergedMaxSpeed: Double? = maxSpeed
        var mergedAscent: Double? = totalAscent
        var mergedDescent: Double? = totalDescent
        var mergedSuuntoTags: List<SuuntoTag>? = suuntoTags

        if (other.fieldsToDelete.contains(FIELD_START_POSITION)) {
            mergedStartPosition = null
            updatedFieldsToDelete.add(FIELD_START_POSITION)
        } else if (other.workoutLocation != null) {
            mergedStartPosition = other.workoutLocation
            updatedFieldsToDelete.remove(FIELD_START_POSITION)
        }

        if (other.fieldsToDelete.contains(FIELD_TSS)) {
            mergedTSS = null
            updatedFieldsToDelete.add(FIELD_TSS)
        } else if (other.tss != null) {
            mergedTSS = other.tss
            updatedFieldsToDelete.remove(FIELD_TSS)
        }

        if (other.fieldsToDelete.contains(FIELD_MAX_SPEED)) {
            mergedMaxSpeed = null
            updatedFieldsToDelete.add(FIELD_MAX_SPEED)
        } else if (other.maxSpeed != null) {
            mergedMaxSpeed = other.maxSpeed
            updatedFieldsToDelete.remove(FIELD_MAX_SPEED)
        }

        if (other.fieldsToDelete.contains(FIELD_TOTAL_ASCENT)) {
            mergedAscent = null
            updatedFieldsToDelete.add(FIELD_TOTAL_ASCENT)
        } else if (other.totalAscent != null) {
            mergedAscent = other.totalAscent
            updatedFieldsToDelete.remove(FIELD_TOTAL_ASCENT)
        }

        if (other.fieldsToDelete.contains(FIELD_TOTAL_DESCENT)) {
            mergedDescent = null
            updatedFieldsToDelete.add(FIELD_TOTAL_DESCENT)
        } else if (other.totalDescent != null) {
            mergedDescent = other.totalDescent
            updatedFieldsToDelete.remove(FIELD_TOTAL_DESCENT)
        }

        if (other.fieldsToDelete.contains(FIELD_SUUNTO_TAGS)) {
            mergedSuuntoTags = null
            updatedFieldsToDelete.add(FIELD_SUUNTO_TAGS)
        } else if (other.suuntoTags != null) {
            mergedSuuntoTags = other.suuntoTags
            updatedFieldsToDelete.remove(FIELD_SUUNTO_TAGS)
        }

        return DomainWorkoutAttributesUpdate(
            workoutId,
            ownerUsername,
            DomainWorkoutAttributes(
                mergedStartPosition,
                mergedTSS,
                mergedMaxSpeed,
                mergedAscent,
                mergedDescent,
                mergedSuuntoTags,
                null // userTags are synced separately, because we don't always have the remote key for a tag, custom user tags needs to be synced first
            ),
            updatedFieldsToDelete.toList(),
            other.requiresUserConfirmation
        )
    }

    companion object {
        const val FIELD_START_POSITION = "startPosition"
        const val FIELD_TSS = "tss"
        const val FIELD_MAX_SPEED = "maxSpeed"
        const val FIELD_TOTAL_ASCENT = "totalAscent"
        const val FIELD_TOTAL_DESCENT = "totalDescent"
        const val FIELD_SUUNTO_TAGS = "suuntoTags"
    }
}

data class DomainWorkoutAttributes(
    val startPosition: DomainWorkoutLocation? = null,
    val tss: TSS? = null,
    val maxSpeed: Double? = null,
    val totalAscent: Double? = null,
    val totalDescent: Double? = null,
    val suuntoTags: List<SuuntoTag>? = null,
    val userTags: List<UserTag>? = null
) {
    val isEmpty: Boolean
        get() = startPosition == null &&
            tss == null &&
            maxSpeed == null &&
            totalAscent == null &&
            totalDescent == null &&
            suuntoTags == null &&
            userTags == null
}

data class DomainWorkoutLocation(
    val latitude: Double,
    val longitude: Double
)

val DomainWorkoutAttributesUpdate?.workoutLocation: DomainWorkoutLocation?
    get() = if (this == null) null else attributes.startPosition

val DomainWorkoutAttributesUpdate?.tss: TSS?
    get() = if (this == null) null else attributes.tss

val DomainWorkoutAttributesUpdate?.maxSpeed: Double?
    get() = if (this == null) null else attributes.maxSpeed

val DomainWorkoutAttributesUpdate?.totalAscent: Double?
    get() = if (this == null) null else attributes.totalAscent

val DomainWorkoutAttributesUpdate?.totalDescent: Double?
    get() = if (this == null) null else attributes.totalDescent

val DomainWorkoutAttributesUpdate?.suuntoTags: List<SuuntoTag>?
    get() = if (this == null) null else attributes.suuntoTags
