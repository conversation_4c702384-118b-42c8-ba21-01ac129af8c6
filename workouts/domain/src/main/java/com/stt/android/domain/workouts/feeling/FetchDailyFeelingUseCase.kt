package com.stt.android.domain.workouts.feeling

import com.stt.android.domain.user.GetCurrentUserUseCase
import com.stt.android.domain.workouts.WorkoutDataSource
import com.stt.android.utils.atEndOfDay
import com.stt.android.utils.toEpochMilli
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import javax.inject.Inject

class FetchDailyFeelingUseCase @Inject constructor(
    private val currentUserUseCase: GetCurrentUserUseCase,
    private val workoutDataSource: WorkoutDataSource,
) {
    fun fetchDailyFeelingForDateRange(
        fromDate: LocalDate,
        toDate: LocalDate,
    ): Flow<List<DailyFeeling>> = flow {
        val username = currentUserUseCase.getCurrentUser().username
        val minMillis = fromDate.atStartOfDay().toEpochMilli()
        val maxMillis = toDate.atEndOfDay().toEpochMilli() - 1

        val feelingList = workoutDataSource.fetchUserWorkoutFeelings(
            username = username,
            minStartTime = minMillis,
            maxStartTime = maxMillis
        )

        val feelingMap = feelingList
            .groupBy { it.timestamp.toLocalDate() }
            .mapValues { entry ->
                entry.value.mapNotNull { workoutFeeling ->
                    workoutFeeling.feeling.takeIf { it > 0 }
                }
            }

        val feelingsList = feelingMap.map { DailyFeeling(it.key, it.value, it.value.average()) }
        emit(feelingsList)
    }.catch {
        emit(emptyList())
    }

    private fun Long.toLocalDate(): LocalDate = Instant
        .ofEpochMilli(this)
        .atZone(ZoneId.systemDefault())
        .toLocalDate()
}
