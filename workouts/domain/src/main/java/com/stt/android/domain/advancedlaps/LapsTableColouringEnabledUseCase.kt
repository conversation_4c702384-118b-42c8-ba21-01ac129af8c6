package com.stt.android.domain.advancedlaps

import javax.inject.Inject

class LapsTableColouringEnabledUseCase
@Inject constructor(
    private val dataSource: LapsTableStateDataSource,
) {
    fun saveIsLapsTableColouringEnabled(enabled: Boolean) {
        dataSource.saveIsLapsTableColouringEnabled(enabled)
    }

    fun fetchIsLapsTableColouringEnabled(): <PERSON>olean {
        return dataSource.fetchIsLapsTableColouringEnabled()
    }
}
