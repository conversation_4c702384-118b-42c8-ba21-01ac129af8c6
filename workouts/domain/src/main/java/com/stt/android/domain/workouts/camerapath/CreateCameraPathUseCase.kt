package com.stt.android.domain.workouts.camerapath

import com.soy.algorithms.camerapath.CameraPathAlgorithm
import com.soy.algorithms.camerapath.CameraPathConfig
import com.soy.algorithms.camerapath.MmlBounds
import com.soy.algorithms.camerapath.entities.CameraPath
import com.soy.algorithms.camerapath.entities.LonLatAltTimestamp
import com.soy.algorithms.camerapath.entities.Tile
import com.soy.algorithms.camerapath.latToTileY
import com.soy.algorithms.camerapath.lonToTileX
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.CoroutineUseCase
import com.stt.android.elevationdata.ElevationData
import kotlinx.coroutines.Dispatchers.Default
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

class CreateCameraPathUseCase @Inject constructor(
    private val mmlBounds: MmlBounds,
    private val elevationData: ElevationData
) : CoroutineUseCase<CameraPath?, CreateCameraPathUseCase.Params> {
    data class Params(
        val points: List<LonLatAltTimestamp>,
        val exaggeration: Double
    )

    override suspend fun run(params: Params): CameraPath? = withContext(Default) {
        runSuspendCatching {
            if (params.points.isEmpty()) return@withContext null
            val config = CameraPathConfig(
                exaggeration = params.exaggeration,
                trackProgressFactor = TRACK_PROGRESS_FACTOR
            )
            createCameraPath(params, config, mmlBounds, elevationData)
        }
            .onFailure { Timber.w(it, "Creating camera path failed.") }
            .getOrNull()
    }

    private suspend fun createCameraPath(
        params: Params,
        config: CameraPathConfig,
        mmlBounds: MmlBounds,
        elevationProvider: ElevationData
    ): CameraPath? {
        val points = createRouteWithTerrainElevation(params.points, config)
        return if (points.isNotEmpty()) {
            with(CameraPathAlgorithm.create(points, config)) {
                val tiles = getTilesForElevationCheck(mmlBounds)
                val zoomLevel = tiles.firstOrNull()?.zoom ?: config.zoomLevel
                Timber.d("Terrain RGB tile zoom level: $zoomLevel")
                withContext(IO) {
                    elevationProvider.fetchTiles(
                        tiles.toList(),
                        zoomLevel
                    )
                }
                evaluateCandidates(elevationProvider)
            }
        } else {
            null
        }
    }

    private suspend fun createRouteWithTerrainElevation(
        points: List<LonLatAltTimestamp>,
        config: CameraPathConfig
    ): List<LonLatAltTimestamp> = withContext(Default) {
        val tiles = points.map {
            latLonToTile(
                it.lat,
                it.lon,
                config.zoomLevel,
                mmlBounds.withinMmlBounds(it.lat, it.lon)
            )
        }.toSet()

        withContext(IO) {
            elevationData.fetchTiles(
                tiles.toList(),
                config.zoomLevel
            )
        }
        points.map {
            it.copy(alt = elevationData.getElevation(it.lat, it.lon))
        }
    }

    private fun latLonToTile(
        latitude: Double,
        longitude: Double,
        zoom: Int,
        withinMmlBounds: Boolean
    ): Tile = Tile(zoom, lonToTileX(longitude, zoom), latToTileY(latitude, zoom), withinMmlBounds)

    companion object {
        private const val TRACK_PROGRESS_FACTOR = 1.1
    }
}
