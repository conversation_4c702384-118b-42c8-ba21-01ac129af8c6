package com.stt.android.domain.advancedlaps

import com.stt.android.core.domain.LapsTableDataType
import javax.inject.Inject

class SaveLapsTableColumnsStatesUseCase @Inject constructor(
    private val dataSource: LapsTableStateDataSource,
) {
    operator fun invoke(stId: Int, lapsTableType: LapsTableType, columns: List<LapsTableDataType>) {
        val key = stId.toString() + lapsTableType.toString()
        dataSource.saveColumnsState(key, columns)
    }
}
