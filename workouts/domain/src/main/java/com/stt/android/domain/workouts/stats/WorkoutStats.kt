package com.stt.android.domain.workouts.stats

data class WorkoutStats(
    val totalDistanceSum: Double,
    val totalTimeSum: Double,
    val totalEnergyConsumptionKCalSum: Double,
    val totalNumberOfWorkoutsSum: Int,
    val totalDays: Int,
    val activityTypeStats: List<ActivityTypeStats>
)

data class ActivityTypeStats(
    val activityId: Int,
    val totalDistance: Double,
    val totalTime: Double,
    val energyConsumptionKCal: Double,
    val numberOfWorkouts: Double,
)
