package com.stt.android.domain.workouts

import android.os.Parcelable
import com.stt.android.domain.advancedlaps.WindowType
import com.stt.android.domain.workout.ActivityType
import com.stt.android.logbook.SuuntoLogbookMinMax
import com.stt.android.logbook.SuuntoLogbookWindow
import kotlinx.parcelize.Parcelize

/**
 * @param temperature is not always reliable, consider using SML samples whenever possible,
 * We know that the issue that causes the unreliability affects the whole workout's Activity windows but the Move windows seems to be unaffected
 * Lap / multisport windows haven't been thoroughly investigated;
 */
data class DomainWindow constructor(
    val activityId: Int,
    val type: String,
    val altitudeRange: AvgMinMax?,
    val ascent: Double?,
    val ascentTime: Double?,
    val cadence: AvgMinMax?,
    val descent: Double?,
    val descentTime: Double?,
    val descentMax: Double?,
    val distance: Double?,
    val distanceMax: Double?,
    val downhillGrade: AvgMinMax?,
    val duration: Double?,
    val energy: Double?,
    val hr: AvgMinMax?,
    val power: AvgMinMax?,
    val recoveryTime: Long?,
    val speed: AvgMinMax?,
    val strokeRate: AvgMinMax?,
    val strokes: AvgMinMax?,
    val swimStyle: String?,
    val swimmingStyle: String?,
    val swolf: AvgMinMax?,
    /**
     * Unreliable, avoid using. See class documentation for details
     */
    val temperature: AvgMinMax?,
    val verticalSpeed: AvgMinMax?,
    val depth: AvgMinMax?,
    val diveTime: Double?,
    val diveRecoveryTime: Double?,
    val diveTimeMax: Double?,
    val diveInWorkout: Int?,
    val repetitionCount: Int?,
    val depthAverage: Double?, // Reliable avg depth for Seal(Scuba diving) in activity window
    val maxDepthAverage: Double?, // Reliable avg depth for Seal(Free diving) in move window
    val stride: AvgMinMax?,
    val fatConsumption: Int?,
    val carbohydrateConsumption: Int?,
    val groundContactTime: AvgMinMax?,
    val verticalOscillation: AvgMinMax?,
    val leftGroundContactBalance: AvgMinMax?,
    val rightGroundContactBalance: AvgMinMax?,
    val ascentSpeed: AvgMinMax?,
    val descentSpeed: AvgMinMax?,
    val distancePerStroke: AvgMinMax?,
)

@Parcelize
data class AvgMinMax(
    val avg: Double?,
    val min: Double?,
    val max: Double?
) : Parcelable

fun SuuntoLogbookMinMax.toAvgMinMax(): AvgMinMax {
    return AvgMinMax(
        avg = this.avg?.toDouble(),
        min = this.min?.toDouble(),
        max = this.max?.toDouble()
    )
}

fun SuuntoLogbookWindow?.toDomainWindow(): DomainWindow? {
    // If updated with more conditions that might cause returning null,
    // make sure to update toDomainWindowWithFallback below
    if (this == null) return null
    val activityId = this.activityId ?: return null
    val type = this.type ?: return null
    return DomainWindow(
        activityId = activityId,
        type = type,
        altitudeRange = this.altitude?.firstOrNull()?.toAvgMinMax(),
        ascent = this.ascent?.toDouble(),
        ascentTime = this.ascentTime?.toDouble(),
        cadence = this.cadence?.firstOrNull()?.toAvgMinMax(),
        descent = this.descent?.toDouble(),
        descentTime = this.descentTime?.toDouble(),
        descentMax = this.descentMax?.toDouble(),
        distance = this.distance?.toDouble(),
        distanceMax = this.distanceMax?.toDouble(),
        downhillGrade = this.downhillGrade?.firstOrNull()?.toAvgMinMax(),
        duration = this.duration?.toDouble(),
        energy = this.energy?.toDouble(),
        hr = this.hr?.firstOrNull()?.toAvgMinMax(),
        power = this.power?.firstOrNull()?.toAvgMinMax(),
        recoveryTime = this.recoveryTime?.toLong(),
        speed = this.speed?.firstOrNull()?.toAvgMinMax(),
        strokeRate = this.strokeRate?.firstOrNull()?.toAvgMinMax(),
        strokes = this.strokes?.firstOrNull()?.toAvgMinMax(),
        swimStyle = this.swimStyle,
        swimmingStyle = this.swimmingStyle,
        swolf = this.swolf?.firstOrNull()?.toAvgMinMax(),
        temperature = this.temperature?.firstOrNull()?.toAvgMinMax(),
        verticalSpeed = this.verticalSpeed?.firstOrNull()?.toAvgMinMax(),
        depth = this.depth?.firstOrNull()?.toAvgMinMax(),
        diveTime = this.diveTime?.toDouble(),
        diveRecoveryTime = this.diveRecoveryTime?.toDouble(),
        diveTimeMax = this.diveTimeMax?.toDouble(),
        diveInWorkout = this.diveInWorkout,
        repetitionCount = this.repetitionCount,
        depthAverage = this.depthAverage?.toDouble(),
        maxDepthAverage = this.maxDepthAverage?.toDouble(),
        stride = this.stride?.firstOrNull()?.toAvgMinMax(),
        fatConsumption = fatConsumption,
        carbohydrateConsumption = carbohydrateConsumption,
        groundContactTime = this.groundContactTime?.firstOrNull()?.toAvgMinMax(),
        verticalOscillation = this.verticalOscillation?.firstOrNull()?.toAvgMinMax(),
        leftGroundContactBalance = this.leftGroundContactBalance?.firstOrNull()?.toAvgMinMax(),
        rightGroundContactBalance = this.rightGroundContactBalance?.firstOrNull()?.toAvgMinMax(),
        ascentSpeed = this.ascentSpeed?.firstOrNull()?.toAvgMinMax(),
        descentSpeed = this.descentSpeed?.firstOrNull()?.toAvgMinMax(),
        distancePerStroke = this.distancePerStroke?.firstOrNull()?.toAvgMinMax()
    )
}

fun SuuntoLogbookWindow.toDomainWindowWithFallback(
    fallbackActivityId: Int = ActivityType.OTHER_6.id,
    fallbackType: String = WindowType.UNKNOWN.type
): DomainWindow = toDomainWindow() ?: copy(activityId = fallbackActivityId, type = fallbackType).toDomainWindow()!!
