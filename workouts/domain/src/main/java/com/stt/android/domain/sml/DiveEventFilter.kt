package com.stt.android.domain.sml

/**
 * Used for filtering out dive events before showing them in the UI.
 *
 * @return Returns true if the event should be shown in the app, otherwise false
 */
val DiveEvent.isSupported: Boolean
    get() = when (val event = this) {
        is NotifyEvent -> doIsSupported(event.type, event.active, SUPPORTED_NOTIFY_EVENTS)
        is StateEvent -> doIsSupported(event.type, event.active, SUPPORTED_STATE_EVENTS)
        is WarningEvent -> doIsSupported(event.type, event.active, SUPPORTED_WARNING_EVENTS)
        is AlarmEvent -> doIsSupported(event.type, event.active, SUPPORTED_ALARM_EVENTS)
        is ErrorEvent -> SUPPORTED_ERROR_EVENTS.contains(event.type) // no active field in errors
        is GasSwitchEvent, // no active field in gas switch
        is BookmarkEvent, // no active field in bookmarks
        is SetPointEvent, // no active field in setpoint events
        is OoamEvent,
        is DiveTimerEvent -> true // always show dive timer events
        else -> false
    }

private val SUPPORTED_NOTIFY_EVENTS = listOf(
    SupportedEvent(NotifyMarkType.DEPTH, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(NotifyMarkType.SURFACE_TIME, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(NotifyMarkType.DECO, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(NotifyMarkType.DECO_WINDOW, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(NotifyMarkType.SET_POINT_SWITCH, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(NotifyMarkType.SAFETY_STOP_BROKEN, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(NotifyMarkType.SAFETY_STOP, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(NotifyMarkType.DEEP_STOP, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(NotifyMarkType.DEEP_STOP_AHEAD, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(NotifyMarkType.DILUENT_HYPOXIA, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(NotifyMarkType.AIR_TIME, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(NotifyMarkType.TANK_PRESSURE, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(NotifyMarkType.CCR_O2_TANK_PRESSURE, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(NotifyMarkType.DECO_BROKEN_ACKNOWLEDGED, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(NotifyMarkType.DIVE_TIME, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(NotifyMarkType.USER_NDL, SupportedEventActiveStatus.ACTIVE),
)

private val SUPPORTED_STATE_EVENTS = listOf(
    SupportedEvent(StateMarkType.DIVE_ACTIVE, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(StateMarkType.CLOSED_CIRCUIT_MODE, SupportedEventActiveStatus.ANY),
    SupportedEvent(StateMarkType.DECO, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(StateMarkType.DECO_WINDOW, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(StateMarkType.SAFETY_STOP, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(StateMarkType.DEEP_STOP_AHEAD, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(StateMarkType.DEEP_STOP, SupportedEventActiveStatus.ACTIVE),
)

private val SUPPORTED_WARNING_EVENTS = listOf(
    SupportedEvent(WarningMarkType.ICD_PENALTY, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(WarningMarkType.DEEP_STOP_PENALTY, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(WarningMarkType.MANDATORY_SAFETY_STOP, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(WarningMarkType.CNS80, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(WarningMarkType.CNS100, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(WarningMarkType.OTU250, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(WarningMarkType.OTU300, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(WarningMarkType.AIR_TIME, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(WarningMarkType.MAX_DEPTH, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(WarningMarkType.TANK_PRESSURE, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(WarningMarkType.CCR_O2_TANK_PRESSURE, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(WarningMarkType.PO2_HIGH, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(WarningMarkType.DECO_BROKEN, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(WarningMarkType.MINI_LOCK, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(WarningMarkType.DEPTH, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(WarningMarkType.DIVE_TIME, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(WarningMarkType.USER_TANK_PRESSURE, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(WarningMarkType.USER_NDL, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(WarningMarkType.NO_DECO_TIME, SupportedEventActiveStatus.ACTIVE),
)

private val SUPPORTED_ALARM_EVENTS = listOf(
    SupportedEvent(AlarmMarkType.MANDATORY_SAFETY_STOP_BROKEN, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(AlarmMarkType.ASCENT_SPEED, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(AlarmMarkType.DILUENT_HYPEROXIA, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(AlarmMarkType.CEILING_BROKEN, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(AlarmMarkType.PO2_HIGH, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(AlarmMarkType.PO2_LOW, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(AlarmMarkType.OTU300, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(AlarmMarkType.CNS100, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(AlarmMarkType.TANK_PRESSURE, SupportedEventActiveStatus.ACTIVE),
    SupportedEvent(AlarmMarkType.DECO_BROKEN, SupportedEventActiveStatus.ACTIVE)
)

private val SUPPORTED_ERROR_EVENTS = listOf(
    ErrorMarkType.CEILING_BROKEN
)

/**
 * An instance of this class defines a supported event type. The value of [activeStatusToShow] defines
 * if the even will be shown in relation to its status property
 */
private data class SupportedEvent(
    val type: Any,
    val activeStatusToShow: SupportedEventActiveStatus
)

private enum class SupportedEventActiveStatus {
    ACTIVE,
    INACTIVE,
    ANY
}

private fun doIsSupported(
    type: Any,
    active: Boolean?,
    supportedEvents: List<SupportedEvent>
): Boolean {
    return supportedEvents.firstOrNull { type == it.type }
        ?.run {
            when (this.activeStatusToShow) {
                SupportedEventActiveStatus.ACTIVE -> active == true
                SupportedEventActiveStatus.INACTIVE -> active == false
                SupportedEventActiveStatus.ANY -> true
            }
        }
        ?: false
}
