package com.stt.android.domain.workouts

import com.stt.android.domain.user.User
import timber.log.Timber
import javax.inject.Inject

/**
 * Use case for fetching public workouts from the server
 */
class FetchPublicWorkoutsUseCase
@Inject constructor(
    private val workoutDataSource: WorkoutDataSource
) {
    suspend operator fun invoke(
        lowerlat: Double,
        lowerlng: Double,
        upperlat: Double,
        upperlng: Double,
        limit: Int = DEFAULT_PUBLIC_WORKOUTS_FETCH_LIMIT
    ): List<Pair<User, DomainWorkout>> {
        Timber.d("Fetching public workouts")
        return workoutDataSource.fetchPublicWorkouts(lowerlat, lowerlng, upperlat, upperlng, limit)
    }
}
