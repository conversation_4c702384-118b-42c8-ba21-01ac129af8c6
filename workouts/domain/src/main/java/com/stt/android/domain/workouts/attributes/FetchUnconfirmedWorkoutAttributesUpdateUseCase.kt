package com.stt.android.domain.workouts.attributes

import com.stt.android.domain.CoroutineUseCase
import javax.inject.Inject

/**
 * Use case for storing a pending workout attributes update
 */
class FetchUnconfirmedWorkoutAttributesUpdateUseCase
@Inject constructor(
    private val workoutAttributesUpdateRepository: WorkoutAttributesUpdateDataSource
) : CoroutineUseCase<DomainWorkoutAttributesUpdate?, FetchUnconfirmedWorkoutAttributesUpdateUseCase.Params> {
    fun getParams(
        workoutId: Int,
        username: String
    ) = Params(workoutId, username)

    override suspend fun run(params: Params): DomainWorkoutAttributesUpdate? {
        return workoutAttributesUpdateRepository.fetchUnconfirmedWorkoutAttributeUpdate(
            params.workoutId,
            params.username
        )
    }

    data class Params(
        val workoutId: Int,
        val username: String
    )
}
