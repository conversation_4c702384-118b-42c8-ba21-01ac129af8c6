package com.stt.android.domain.workouts.tss

import java.time.LocalDate
import java.time.ZoneId
import java.time.ZonedDateTime

interface WorkoutTSSSummaryRepository {
    /**
     * Load all non-deleted workouts with TSS data for the user
     */
    suspend fun loadWorkoutTSSSummaries(username: String): List<WorkoutTSSSummary>

    companion object {
        val START_OF_TSS_SUPPORT: ZonedDateTime = LocalDate
            .of(2021, 1, 31)
            .atStartOfDay(ZoneId.systemDefault())
    }
}
