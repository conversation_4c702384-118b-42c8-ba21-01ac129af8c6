package com.stt.android.domain.workouts;

import android.content.res.Resources;

/**
 * Implement this interface to provide search capabilities.
 */
public interface Searchable {
    /**
     * Creates a string representation of this object which can be used for filtering/searching purposes.
     *
     * @param resources required for localization purposes
     * @return a string representation of the main characterizes this object.
     */
    String getSearchString(Resources resources);
}
