package com.stt.android.domain.advancedlaps

import android.os.Parcelable
import com.stt.android.core.domain.SuuntoPlusChannel
import com.stt.android.domain.workouts.AvgMinMax
import kotlinx.parcelize.Parcelize

/*
    Data from the window object in sml. Types of the variables are same than in the sml.
 */
@Parcelize
data class LapsTableRow(
    val rowid: Int,
    val lapNumber: Int,
    val minAltitude: Float?,
    val maxAltitude: Float?,
    val avgAltitude: Float?,
    val ascent: Float?,
    val ascentTime: Float?,
    val descent: Float?,
    val descentTime: Float?,
    val maxDescent: Float?,
    val minCadence: Float?,
    val maxCadence: Float?,
    val avgCadence: Float?,
    val distance: Float?,
    val distanceMax: Float?,
    val minDownhillGrade: Float?,
    val maxDownhillGrade: Float?,
    val avgDownhillGrade: Float?,
    val duration: Float?,
    val energy: Float?,
    val minHR: Float?,
    val maxHR: Float?,
    val avgHR: Float?,
    val minPower: Float?,
    val maxPower: Float?,
    val avgPower: Float?,
    val recoveryTime: Int?,
    val minSpeed: Float?,
    val maxSpeed: Float?,
    val avgSpeed: Float?,
    val minStrokeRate: Float?,
    val maxStrokeRate: Float?,
    val avgStrokeRate: Float?,
    val minStrokes: Float?,
    val maxStrokes: Float?,
    val avgStrokes: Float?,
    val swimStyle: String?, // "Free", "Breast", etc
    val minSwolf: Float?,
    val maxSwolf: Float?,
    val avgSwolf: Float?,
    val minTemperature: Float?,
    val maxTemperature: Float?,
    val avgTemperature: Float?,
    val type: WindowType,
    val minVerticalSpeed: Float?,
    val maxVerticalSpeed: Float?,
    val avgVerticalSpeed: Float?,
    val minDepth: Float?,
    val maxDepth: Float?,
    val avgDepth: Float?,
    val diveTime: Float?,
    val diveRecoveryTime: Float?,
    val diveTimeMax: Float?,
    val diveInWorkout: Int?,
    val suuntoPlusData: Map<SuuntoPlusChannel, AvgMinMax>?,
    val cumulatedDistance: Float?,
    /**
     * Time from workout start to end of this lap, without pauses
     *
     * For lap types other than downhill this is sum of previous lap durations
     * plus this lap's duration. For downhill laps the time spent in lifts and other
     * downtime between laps is included.
     */
    val cumulatedDuration: Float?,
    val repetitionCount: Int?,
    val isIntervalRecoveryLap: Boolean?,

    val aerobicHrThreshold: Float?,
    val anaerobicHrThreshold: Float?,
    val aerobicPaceThreshold: Float?,
    val anaerobicPaceThreshold: Float?,
    val aerobicPowerThreshold: Float?,
    val anaerobicPowerThreshold: Float?,
    val aerobicDuration: Float?,
    val anaerobicDuration: Float?,
    val vo2MaxDuration: Float?,
    val avgStrideLength: Float?,
    val minStrideLength: Float?,
    val maxStrideLength: Float?,
    val fatConsumption: Int?,
    val carbohydrateConsumption: Int?,
    val avgGroundContactTime: Float?,
    val minGroundContactTime: Float?,
    val maxGroundContactTime: Float?,
    val avgVerticalOscillation: Float?,
    val minVerticalOscillation: Float?,
    val maxVerticalOscillation: Float?,
    val avgLeftGroundContactBalance: Float?,
    val minLeftGroundContactBalance: Float?,
    val maxLeftGroundContactBalance: Float?,
    val avgRightGroundContactBalance: Float?,
    val minRightGroundContactBalance: Float?,
    val maxRightGroundContactBalance: Float?,
    val avgAscentSpeed: Float?,
    val minAscentSpeed: Float?,
    val maxAscentSpeed: Float?,
    val avgDescentSpeed: Float?,
    val minDescentSpeed: Float?,
    val maxDescentSpeed: Float?,
    val avgDistancePerStroke: Float?,
    val minDistancePerStroke: Float?,
    val maxDistancePerStroke: Float?,
    val breaststrokeGlideTime: Int? = null,
    val breaststrokeAvgBreathAngle: Int? = null,
    val freestyleAvgBreathAngle: Int? = null,
    val breathingRate: Int? = null,
    // set value according swimming style
    val headAngle: Int? = null,
) : Parcelable
