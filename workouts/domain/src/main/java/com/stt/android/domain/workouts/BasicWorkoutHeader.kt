package com.stt.android.domain.workouts

import com.stt.android.domain.workouts.tss.TSS

/**
 * Basic version of [WorkoutHeader] loading only necessary data for heavier computations
 */
data class BasicWorkoutHeader(
    val id: Int,
    val key: String?,
    val totalDistance: Double,
    val activityTypeId: Int,
    val startTime: Long, // ms UTC
    val totalTime: Double, // seconds
    val username: String,
    val totalAscent: Double,
    val totalDescent: Double,
    val extensionsFetched: Boolean,
    val tss: TSS?,
    val energyConsumption: Double,
)

fun WorkoutHeader.toBasic() = BasicWorkoutHeader(
    id = id,
    key = key,
    totalDistance = totalDistance,
    activityTypeId = activityTypeId,
    startTime = startTime,
    totalTime = totalTime,
    username = username,
    totalAscent = totalAscent,
    totalDescent = totalDescent,
    extensionsFetched = extensionsFetched,
    tss = tss,
    energyConsumption = energyConsumption,
)
