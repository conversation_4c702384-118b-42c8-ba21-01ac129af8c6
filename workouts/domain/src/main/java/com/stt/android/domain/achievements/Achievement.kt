package com.stt.android.domain.achievements

import com.stt.android.data.generateId

data class Achievement(
    val id: String = generateId(),
    val workoutKey: String,
    val activityType: Int,
    val timestamp: Long,
    val cumulativeAchievements: List<CumulativeAchievement> = emptyList(),
    val personalBestAchievements: List<PersonalBestAchievement> = emptyList()
) {
    val count: Int
        get() = cumulativeAchievements.size + personalBestAchievements.size
}
