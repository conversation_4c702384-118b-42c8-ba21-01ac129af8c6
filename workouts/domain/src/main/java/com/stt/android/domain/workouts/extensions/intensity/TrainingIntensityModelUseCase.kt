package com.stt.android.domain.workouts.extensions.intensity

import com.soy.algorithms.intensity.WorkoutIntensity
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.domain.workouts.BasicWorkoutHeader
import com.stt.android.utils.distributionBy
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.withContext
import javax.inject.Inject

class TrainingIntensityModelUseCase @Inject constructor(
    private val workoutIntensityUseCase: WorkoutIntensityUseCase,
) {
    suspend fun calculate(workouts: List<BasicWorkoutHeader>): TrainingIntensityModelWithDistribution =
        withContext(IO) {
            val intensities = workouts
                .filterNot { CoreActivityType.valueOf(it.activityTypeId).hasUnclassifiedCardioImpact }
                .mapNotNull {
                    workoutIntensityUseCase.calculateWorkoutIntensity(it)
                }
            TrainingIntensityModelWithDistribution(
                trainingIntensityModel = computeTrainingIntensityModel(intensities),
                distribution = intensities.distributionBy { it }
            )
        }

    private fun computeTrainingIntensityModel(intensities: List<WorkoutIntensity>): TrainingIntensityModel {
        if (intensities.isEmpty()) return TrainingIntensityModel.UNIDENTIFIED
        val intensityDistribution =
            intensities.distributionBy { it }.mapValues { (_, v) -> v.toFloat() }
        val totalCount = intensityDistribution.values.sum()
        val zone1Count = intensityDistribution.getOrElse(WorkoutIntensity.ZONE_1) { 0f }
        val zone2Count = intensityDistribution.getOrElse(WorkoutIntensity.ZONE_2) { 0f }
        val zone3Count = intensityDistribution.getOrElse(WorkoutIntensity.ZONE_3) { 0f }
        val zone4Count = intensityDistribution.getOrElse(WorkoutIntensity.ZONE_4) { 0f }
        val zone5Count = intensityDistribution.getOrElse(WorkoutIntensity.ZONE_5) { 0f }
        return when {
            zone1Count + zone2Count > 0.8 * totalCount &&
                zone4Count + zone5Count < 0.1 * totalCount -> TrainingIntensityModel.BASE_ENDURANCE

            zone4Count + zone5Count >= 0.5 * totalCount &&
                zone5Count > 0.1 * totalCount -> TrainingIntensityModel.HIGH_INTENSITY

            zone3Count + zone4Count >= 0.4 * totalCount &&
                zone5Count < 0.1 * totalCount -> TrainingIntensityModel.SWEETSPOT

            zone1Count + zone2Count > zone3Count &&
                zone4Count + zone5Count > zone3Count &&
                zone1Count + zone2Count > zone4Count + zone5Count &&
                zone1Count + zone2Count > 0.6 * totalCount &&
                zone4Count + zone5Count > 0.1 * totalCount -> TrainingIntensityModel.POLARIZED

            zone1Count + zone2Count <= 0.8 * totalCount &&
                zone1Count + zone2Count >= zone3Count &&
                zone3Count >= zone4Count + zone5Count -> TrainingIntensityModel.PYRAMID

            else -> TrainingIntensityModel.UNIDENTIFIED
        }
    }
}
