package com.stt.android.domain.user.workoutextension

import com.soy.algorithms.intensity.IntensityZones
import com.stt.android.domain.workouts.extensions.WorkoutExtension

/**
 * @param workoutId Id of the workout
 * @param intensityZones intensity zones of the workout
 */
data class IntensityExtension(
    override val workoutId: Int,
    val intensityZones: IntensityZones,
) : WorkoutExtension(TYPE_WORKOUT_INTENSITY, workoutId) {
    val isEmpty: Boolean
        get() = intensityZones.isEmpty

    override fun copyWithNewWorkoutId(newWorkoutId: Int): WorkoutExtension =
        copy(workoutId = newWorkoutId)
}
