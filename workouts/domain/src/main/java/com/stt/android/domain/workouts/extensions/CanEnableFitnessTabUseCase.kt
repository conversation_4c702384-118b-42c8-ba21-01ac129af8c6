package com.stt.android.domain.workouts.extensions

import com.stt.android.domain.user.workoutextension.FitnessExtension
import javax.inject.Inject

/**
 * A use case for checking if fitness tab can be enabled
 */
class CanEnableFitnessTabUseCase
@Inject constructor(
    private val extensionsDataSource: ExtensionsDataSource
) {
    suspend operator fun invoke(): Boolean {
        return extensionsDataSource.findExtensionsByType(FitnessExtension::class)
            .isNotEmpty()
    }
}
