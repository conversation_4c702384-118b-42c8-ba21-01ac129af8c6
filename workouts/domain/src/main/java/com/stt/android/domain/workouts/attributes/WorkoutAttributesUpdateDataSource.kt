package com.stt.android.domain.workouts.attributes

interface WorkoutAttributesUpdateDataSource {
    suspend fun addWorkoutAttributesUpdate(update: DomainWorkoutAttributesUpdate)

    suspend fun removeWorkoutAttributesUpdate(update: DomainWorkoutAttributesUpdate)

    suspend fun fetchUnsyncedWorkoutAttributesUpdate(
        ownerUsername: String
    ): List<DomainWorkoutAttributesUpdate>

    suspend fun fetchUnconfirmedWorkoutAttributeUpdate(
        workoutId: Int,
        ownerUsername: String
    ): DomainWorkoutAttributesUpdate?

    suspend fun fetchUnsyncedWorkoutAttributesUpdate(
        workoutId: Int,
        ownerUsername: String
    ): DomainWorkoutAttributesUpdate?
}
