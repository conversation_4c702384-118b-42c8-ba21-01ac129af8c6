package com.stt.android.domain.workouts.extensions

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class JumpRopeExtension(
    override val workoutId: Int,
    val rounds: Int?,
    val avgSkipsPerRound: Int?,
    val maxConsecutiveSkips: Int?,
) : WorkoutExtension(TYPE_JUMP_ROPE, workoutId), Parcelable {
    override fun copyWithNewWorkoutId(newWorkoutId: Int): WorkoutExtension {
        return copy(workoutId = newWorkoutId)
    }
}
