package com.stt.android.domain.workouts.extensions

import android.os.Parcelable
import com.stt.android.domain.weather.WeatherConditions
import kotlinx.parcelize.Parcelize

@Parcelize
data class WeatherExtension(
    override val workoutId: Int,
    val airPressure: Float?, // kPa
    val cloudiness: Int?,
    val groundLevelAirPressure: Float?, // kPa
    val humidity: Int?,
    val rainVolume1h: Float?,
    val rainVolume3h: Float?,
    val seaLevelAirPressure: Float?, // kPa
    val snowVolume1h: Float?,
    val snowVolume3h: Float?,
    val temperature: Float?,
    val weatherIcon: String?,
    val windDirection: Float?,
    val windSpeed: Float?
) : WorkoutExtension(TYPE_WEATHER, workoutId), Parcelable {
    override fun copyWithNewWorkoutId(newWorkoutId: Int): WorkoutExtension {
        return copy(workoutId = newWorkoutId)
    }

    fun toDomainEntity(): WeatherConditions = WeatherConditions(
        airPressure = airPressure,
        cloudiness = cloudiness,
        groundLevelAirPressure = groundLevelAirPressure,
        humidity = humidity,
        rainVolume1h = rainVolume1h,
        rainVolume3h = rainVolume3h,
        seaLevelAirPressure = seaLevelAirPressure,
        snowVolume1h = snowVolume1h,
        snowVolume3h = snowVolume3h,
        temperature = temperature,
        weatherIcon = weatherIcon,
        windDirection = windDirection,
        windSpeed = windSpeed
    )

    constructor(workoutId: Int, domainEntity: WeatherConditions) : this(
        workoutId = workoutId,
        airPressure = domainEntity.airPressure,
        cloudiness = domainEntity.cloudiness,
        groundLevelAirPressure = domainEntity.groundLevelAirPressure,
        humidity = domainEntity.humidity,
        rainVolume1h = domainEntity.rainVolume1h,
        rainVolume3h = domainEntity.rainVolume3h,
        seaLevelAirPressure = domainEntity.seaLevelAirPressure,
        snowVolume1h = domainEntity.snowVolume1h,
        snowVolume3h = domainEntity.snowVolume3h,
        temperature = domainEntity.temperature,
        weatherIcon = domainEntity.weatherIcon,
        windDirection = domainEntity.windDirection,
        windSpeed = domainEntity.windSpeed
    )

    companion object {
        val workoutIdFieldName: String
            get() = "workoutId"
    }
}
