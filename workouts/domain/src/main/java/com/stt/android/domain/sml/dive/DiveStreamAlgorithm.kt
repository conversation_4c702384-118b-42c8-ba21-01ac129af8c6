package com.stt.android.domain.sml.dive

import com.stt.android.logbook.LogbookGasData
import com.stt.android.logbook.SuuntoLogbookSummary
import com.stt.android.utils.DiveGasNameHelper

object DiveStreamAlgorithm {

    fun extractGases(suuntoLogbookSummary: SuuntoLogbookSummary): List<LogbookGasData> {
        return suuntoLogbookSummary.diving?.gases?.mapIndexed { index, gas ->
            val helium = gas.helium ?: 0f
            val oxygen = gas.oxygen ?: 0f
            val gasName = DiveGasNameHelper.extractNameFrom(oxygen, helium, gas.state)
            LogbookGasData(index + 1, gasName, gas.startPressureKPa)
        } ?: listOf<LogbookGasData>().filter { it.gasName.isNotEmpty() }
    }
}
