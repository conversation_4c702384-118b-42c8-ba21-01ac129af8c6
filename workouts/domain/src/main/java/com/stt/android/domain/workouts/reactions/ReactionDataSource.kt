package com.stt.android.domain.workouts.reactions

interface ReactionDataSource {
    suspend fun findUnsyncedNotDeletedReactionsByUserName(username: String): List<Reaction>
    suspend fun findDeletedReactions(username: String): List<Reaction>
    suspend fun storeReactions(reactions: List<Reaction>)
    suspend fun removeDuplicateReactions(reactions: List<Reaction>)
    suspend fun removeReactions(reactions: List<Reaction>)
    fun syncReactions()
}
