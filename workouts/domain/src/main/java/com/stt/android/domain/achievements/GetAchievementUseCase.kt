package com.stt.android.domain.achievements

import com.stt.android.domain.CoroutineUseCase
import javax.inject.Inject

class GetAchievementUseCase @Inject constructor(
    private val achievementRepository: AchievementDataSource,
) : CoroutineUseCase<Achievement?, String> {
    override suspend fun run(params: String): Achievement? {
        return achievementRepository.getAchievementFromLocalStore(params)
    }

    suspend operator fun invoke(workoutKeys: List<String>): Map<String, Achievement> =
        achievementRepository.getAchievementsFromLocalStore(workoutKeys)
}
