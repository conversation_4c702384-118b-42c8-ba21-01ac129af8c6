package com.stt.android.domain.comments

import com.stt.android.domain.workouts.comment.DomainWorkoutComment

interface WorkoutCommentDataSource {
    /**
     * Permanently removes a workout comment from local database
     * @param commentKey comment key
     */
    suspend fun removeComment(commentKey: String)

    /**
     * Saves the comment to local data store
     */
    suspend fun saveComment(workoutKey: String?, comments: List<DomainWorkoutComment>)

    /**
     * Deletes all comments by workout key
     */
    suspend fun removeByWorkoutKey(workoutKey: String)
}
