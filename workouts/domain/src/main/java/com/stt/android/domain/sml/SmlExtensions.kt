package com.stt.android.domain.sml

import com.stt.android.domain.advancedlaps.WindowType

val Sml.hasSettings get() = this.summary.header?.settings != null

val Sml.hasManualLaps get() = this.summary.windows.any { it.type.equals(WindowType.LAP.type) }

val Sml.hasAutolaps get() = this.summary.windows.any { it.type.equals(WindowType.AUTOLAP.type) }

val Sml.hasIntervals get() = this.summary.windows.any { it.type.equals(WindowType.INTERVAL.type) }

val Sml.hasDownhillLaps get() = this.summary.windows.any { it.type.equals(WindowType.DOWNHILL.type) }

val Sml.hasDiveLaps get() = this.summary.windows.any { it.type.equals(WindowType.DIVE.type) }

val Sml.hasAnyLaps get() = hasManualLaps || hasAutolaps || hasIntervals || hasDownhillLaps || hasDiveLaps
