package com.stt.android.domain.user.workoutextension

import com.j256.ormlite.field.DatabaseField
import com.j256.ormlite.table.DatabaseTable
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.ski.SlopeSki
import kotlin.math.roundToLong

@DatabaseTable(tableName = SlopeSkiSummary.TABLE_NAME)
data class SlopeSkiSummary(
    @field:DatabaseField(columnName = DbFields.WORKOUT_ID, id = true)
    override val workoutId: Int,

    @field:DatabaseField(columnName = DbFields.TOTAL_RUNS)
    val totalRuns: Int,

    @field:DatabaseField(columnName = DbFields.DESCENT_DURATION)
    val descentDurationInMilliseconds: Long,

    @field:DatabaseField(columnName = DbFields.DESCENTS)
    val descentsInMeters: Double,

    @field:DatabaseField(columnName = DbFields.DESCENT_DISTANCE)
    val descentDistanceInMeters: Double,

    @field:DatabaseField(columnName = DbFields.MAX_SPEED)
    val maxSpeedMetersPerSecond: Double
) : WorkoutExtension(TYPE_SLOPE_SKI, workoutId) {
    override fun copyWithNewWorkoutId(newWorkoutId: Int): WorkoutExtension {
        return SlopeSkiSummary(
            workoutId = newWorkoutId,
            totalRuns = totalRuns,
            descentDurationInMilliseconds = descentDurationInMilliseconds,
            descentsInMeters = descentsInMeters,
            descentDistanceInMeters = descentDistanceInMeters,
            maxSpeedMetersPerSecond = maxSpeedMetersPerSecond
        )
    }

    object DbFields {
        const val WORKOUT_ID = "workoutId"
        const val TOTAL_RUNS = "totalRuns"
        const val DESCENT_DURATION = "descentDuration"
        const val DESCENTS = "descents"
        const val DESCENT_DISTANCE = "descentDistance"
        const val MAX_SPEED = "maxSpeedMetersPerSecond"
    }

    // OrmLite constructor
    internal constructor() : this(0, 0, 0L, 0.0, 0.0, 0.0)

    constructor(workoutId: Int, slopeSki: SlopeSki) : this(
        workoutId,
        slopeSki.totalRuns,
        (slopeSki.totalDescentTimeInSeconds * 1000.0).roundToLong(),
        slopeSki.totalDescents,
        slopeSki.totalDescentDistance,
        slopeSki.maxSkiRunSpeedMetersPerSecond
    )

    companion object {
        const val TABLE_NAME = "slopeSkiSummary"
    }
}
