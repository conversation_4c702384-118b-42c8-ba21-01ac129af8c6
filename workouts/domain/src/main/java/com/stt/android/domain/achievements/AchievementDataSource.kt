package com.stt.android.domain.achievements

import com.stt.android.domain.workouts.WorkoutHeader

interface AchievementDataSource {

    suspend fun calculateAchievements(workout: WorkoutHeader)

    suspend fun getAchievementFromLocalStore(workoutKey: String): Achievement?

    /**
     * Returns a map from workout keys to the achievement of that workout.
     */
    suspend fun getAchievementsFromLocalStore(workoutKeys: List<String>): Map<String, Achievement>

    suspend fun storeAchievementToLocalStore(achievement: Achievement)

    suspend fun calculateRecordsForTrailRunning(): List<RecordItem>

    suspend fun calculateRecordsForCycling(): List<RecordItem>

    suspend fun calculateRecordsForRunning(): List<RecordItem>
}
