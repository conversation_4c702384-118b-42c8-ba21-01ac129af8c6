package com.stt.android.domain.advancedlaps;

import com.google.gson.annotations.SerializedName;

public class Statistics {
    @SerializedName("lastValue")
    private double lastValue;
    @SerializedName("count")
    private int count;
    @SerializedName("sum")
    private double sum;
    @SerializedName("min")
    private double min;
    @SerializedName("avg")
    private double avg;
    @SerializedName("max")
    private double max;

    @SerializedName("countZero")
    private int countZero;
    @SerializedName("countPositivie")
    private int countPositive;
    @SerializedName("countNegative")
    private int countNegative;

    @SerializedName("deltaUp")
    private double deltaUp;
    @SerializedName("deltaDown")
    private double deltaDown;

    @SerializedName("indexMax")
    private int indexMax;
    @SerializedName("indexMin")
    private int indexMin;

    public void addSample(double sample) {

        if (count > 0) {
            double delta = sample - lastValue;

            if (delta < 0)
                deltaDown += delta;
            else
                deltaUp += delta;
        }

        count++;
        lastValue = sample;

        if (sample < 0)
            countNegative++;
        else if (sample > 0)
            countPositive++;
        else
            countZero++;

        sum += sample;
        avg = sum / count;

        if (count == 1) {
            min = sample;
            max = sample;
        }

        if (sample < min) {
            min = sample;
            indexMin = count;
        }

        if (sample > max) {
            max = sample;
            indexMax = count;
        }
    }

    public double getLastValue() {
        return lastValue;
    }

    public void setLastValue(double lastValue) {
        this.lastValue = lastValue;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public double getSum() {
        return sum;
    }

    public void setSum(double sum) {
        this.sum = sum;
    }

    public double getMin() {
        return min;
    }

    public void setMin(double min) {
        this.min = min;
    }

    public double getAvg() {
        return avg;
    }

    public void setAvg(double avg) {
        this.avg = avg;
    }

    public double getMax() {
        return max;
    }

    public void setMax(double max) {
        this.max = max;
    }

    public int getCountZero() {
        return countZero;
    }

    public void setCountZero(int countZero) {
        this.countZero = countZero;
    }

    public int getCountPositive() {
        return countPositive;
    }

    public void setCountPositive(int countPositive) {
        this.countPositive = countPositive;
    }

    public int getCountNegative() {
        return countNegative;
    }

    public void setCountNegative(int countNegative) {
        this.countNegative = countNegative;
    }

    public double getDeltaUp() {
        return deltaUp;
    }

    public void setDeltaUp(double deltaUp) {
        this.deltaUp = deltaUp;
    }

    public double getDeltaDown() {
        return deltaDown;
    }

    public void setDeltaDown(double deltaDown) {
        this.deltaDown = deltaDown;
    }

    public int getIndexMax() {
        return indexMax;
    }

    public void setIndexMax(int indexMax) {
        this.indexMax = indexMax;
    }

    public int getIndexMin() {
        return indexMin;
    }

    public void setIndexMin(int indexMin) {
        this.indexMin = indexMin;
    }

}
