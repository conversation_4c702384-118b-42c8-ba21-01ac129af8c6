package com.stt.android.domain.tss

import com.stt.android.domain.user.GetCurrentUserUseCase
import com.stt.android.domain.workouts.WorkoutDataSource
import com.stt.android.domain.workouts.extensions.SummaryExtensionDataSource
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import javax.inject.Inject

class CalculateRefSwimSpeedUseCase @Inject constructor(
    private val currentUserUseCase: GetCurrentUserUseCase,
    private val workoutDataSource: WorkoutDataSource,
    private val summaryExtensionDataSource: SummaryExtensionDataSource
) {
    suspend operator fun invoke(
        activityId: Int,
        referenceInstant: Instant? = null
    ): Float? = withContext(Dispatchers.IO) {
        kotlin.runCatching {
            val username = currentUserUseCase.getCurrentUser().username
            val oneYearBeforeMillis = (referenceInstant ?: Instant.now())
                .minus(365L, ChronoUnit.DAYS).toEpochMilli()
            val latestSwims = workoutDataSource.loadLatestWorkoutsOfActivityTypes(
                username = username,
                activityTypes = listOf(activityId),
                maxCount = 30,
                minDurationSeconds = Duration.ofMinutes(10).seconds,
                fromTimestampMillis = oneYearBeforeMillis
            )
            if (latestSwims.isEmpty()) return@runCatching null

            val latestSwimsAverage: MutableList<Float> = latestSwims.mapNotNull {
                summaryExtensionDataSource.findByWorkoutId(it.id)?.avgSpeed
            }.take(20).toMutableList()

            if (latestSwimsAverage.size > 10) {
                latestSwimsAverage.sort()
                latestSwimsAverage.removeAt(0)
                latestSwimsAverage.removeAt(latestSwimsAverage.lastIndex)
            }
            latestSwimsAverage.average().takeIf { !it.isNaN() }?.toFloat()
        }.onFailure { Timber.w(it, "Error calculating refSwimSpeed") }.getOrNull()
    }
}
