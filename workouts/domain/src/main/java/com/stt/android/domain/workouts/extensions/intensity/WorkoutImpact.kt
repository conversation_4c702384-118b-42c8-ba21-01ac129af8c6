package com.stt.android.domain.workouts.extensions.intensity

import androidx.annotation.StringRes
import com.soy.algorithms.impact.WorkoutImpact
import com.soy.algorithms.impact.WorkoutImpactType
import com.stt.android.workouts.domain.R
import com.stt.android.core.R as BaseR

val WorkoutImpactType.titleRes: Int? get() = when (this) {
    WorkoutImpactType.CARDIO -> R.string.cardio
    WorkoutImpactType.MUSCULAR -> R.string.muscular
    WorkoutImpactType.NONE -> null
}

@StringRes
fun WorkoutImpact.nameRes(): Int? = when (this) {
    WorkoutImpact.UNCLASSIFIED -> null
    WorkoutImpact.EASY_RECOVERY -> BaseR.string.training_hub_impact_recovery
    WorkoutImpact.AEROBIC -> BaseR.string.training_hub_impact_aerobic
    WorkoutImpact.LONG_AEROBIC_BASE -> BaseR.string.training_hub_impact_cardio_long_aerobic
    WorkoutImpact.ABOVE_THRESHOLD_VO2MAX -> BaseR.string.training_hub_impact_cardio_vo2_max
    WorkoutImpact.HARD_ANAEROBIC_EFFORT -> BaseR.string.training_hub_impact_cardio_anaerobic_hard
    WorkoutImpact.AEROBIC_TO_ANAEROBIC -> BaseR.string.training_hub_impact_cardio_aerobic_anaerobic
    WorkoutImpact.HARD_LONG_AEROBIC_BASE -> BaseR.string.training_hub_impact_cardio_heavy_aerobic
    WorkoutImpact.ANAEROBIC_THRESHOLD -> BaseR.string.training_hub_impact_cardio_anaerobic
    WorkoutImpact.SPEED_AND_AGILITY -> BaseR.string.training_hub_impact_muscular_speed_and_agility
    WorkoutImpact.SPEED_AND_STRENGTH -> BaseR.string.training_hub_impact_muscular_speed_and_strength
    WorkoutImpact.FLEXIBILITY -> BaseR.string.training_hub_impact_muscular_flexibility
    WorkoutImpact.STRENGTH -> BaseR.string.training_hub_impact_muscular_strength
}
