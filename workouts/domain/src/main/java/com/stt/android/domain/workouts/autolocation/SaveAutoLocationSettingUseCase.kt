package com.stt.android.domain.workouts.autolocation

import com.stt.android.domain.CoroutineUseCase
import javax.inject.Inject

class SaveAutoLocationSettingUseCase
@Inject constructor(
    private val autoLocationRepository: AutoLocationRepository
) : CoroutineUseCase<Unit, Boolean> {

    override suspend fun run(params: <PERSON><PERSON>an) =
        autoLocationRepository.setAutoLocationSetting(params)
}
