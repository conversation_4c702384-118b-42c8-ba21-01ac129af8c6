package com.stt.android.domain.workouts

import com.stt.android.core.domain.workouts.CoreActivityType
import javax.inject.Inject

class GetHasOnlyDiveWorkoutsUseCase @Inject constructor(
    private val workoutHeaderDataSource: WorkoutHeaderDataSource
) {
    suspend operator fun invoke(): <PERSON><PERSON>an {
        val totalCount = workoutHeaderDataSource.loadTotalActivityCount()
        if (totalCount == 0L) return false

        val diveCount =
            workoutHeaderDataSource.loadActivityTypeCount(CoreActivityType.FREEDIVING.id) +
                workoutHeaderDataSource.loadActivityTypeCount(CoreActivityType.SCUBADIVING.id)

        return diveCount == totalCount
    }
}
