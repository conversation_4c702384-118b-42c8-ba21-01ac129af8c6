package com.stt.android.domain.workouts.pictures

import com.stt.android.domain.Point
import com.stt.android.domain.review.ReviewState

data class Picture(
    val id: Int? = null,
    val key: String?,
    val location: Point?,
    val timestamp: Long,
    val totalTime: Double,
    val fileName: String?,
    val workoutId: Int?,
    val workoutKey: String?,
    val md5Hash: String?,
    val locallyChanged: Boolean,
    val description: String?,
    val username: String?,
    val width: Int,
    val height: Int,
    val indexInWorkoutHeader: Int,
    val reviewState: ReviewState = ReviewState.PASS
)
