package com.stt.android.domain.workouts.extensions

import javax.inject.Inject

/**
 * This job scans for all the SummaryExtensions on the local DB and their corresponding SML.
 * It tries to parse SML and extract Zapps to add to SummaryExtensions.
 * IMPORTANT: It is meant to run only once during DB migration. It only works with local data.
 */
class SummaryExtensionUpdateWithZappsUseCase
@Inject constructor(
    private val summaryExtensionDataSource: SummaryExtensionDataSource
) {
    fun schedule() = summaryExtensionDataSource.scheduleSummaryExtensionUpdateWithZapps()
}
