package com.stt.android.domain.advancedlaps

import com.stt.android.core.domain.LapsTableDataType
import javax.inject.Inject

class FetchLapsTableColumnStatesUseCase @Inject constructor(
    private val dataSource: LapsTableStateDataSource,
) {
    operator fun invoke(stId: Int, lapsTableType: LapsTableType): List<LapsTableDataType>? {
        val key = stId.toString() + lapsTableType.toString()
        return dataSource.fetchColumnsState(key)
    }
}
