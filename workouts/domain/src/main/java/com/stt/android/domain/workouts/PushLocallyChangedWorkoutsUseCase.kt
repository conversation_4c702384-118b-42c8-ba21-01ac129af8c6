package com.stt.android.domain.workouts

import timber.log.Timber
import javax.inject.Inject

/**
 * Pushes local workout changes to the server
 */
class PushLocallyChangedWorkoutsUseCase
@Inject constructor(
    private val workoutDataSource: WorkoutDataSource
) {
    suspend operator fun invoke(): List<Result<Pair<Any, String>>> =
        workoutDataSource.uploadLocalWorkoutData().also {
            it.forEach { result ->
                val throwable = result.exceptionOrNull()
                if (throwable != null) {
                    val errorMessage = result.getOrNull()?.second
                    Timber.w(throwable, "Failed to push own workouts. $errorMessage")
                }
            }
        }
}
