package com.stt.android.domain.workouts.extensions.intensity

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.soy.algorithms.intensity.WorkoutIntensity
import com.stt.android.workouts.domain.R

enum class TrainingIntensityModel(
    @DrawableRes val iconRes: Int? = null,
    @StringRes val titleRes: Int? = null
) {
    BASE_ENDURANCE(R.drawable.base_model_outline, R.string.training_intensity_model_base_endurance),
    HIGH_INTENSITY(
        R.drawable.high_intensity_outline,
        R.string.training_intensity_model_high_intensity
    ),
    SWEETSPOT(R.drawable.sweetspot_outline, R.string.training_intensity_model_sweetspot),
    POLARIZED(R.drawable.polarized_outline, R.string.training_intensity_model_polarized),
    PYRAMID(R.drawable.pyramid_outline, R.string.training_intensity_model_pyramid),
    UNIDENTIFIED
}

data class TrainingIntensityModelWithDistribution(
    val trainingIntensityModel: TrainingIntensityModel,
    val distribution: Map<WorkoutIntensity, Int>
)
