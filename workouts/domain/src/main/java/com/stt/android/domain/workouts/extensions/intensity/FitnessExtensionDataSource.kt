package com.stt.android.domain.workouts.extensions.intensity

import com.stt.android.domain.user.workoutextension.FitnessExtension

interface FitnessExtensionDataSource {
    suspend fun findById(id: Int): FitnessExtension?
    suspend fun findLatestFitnessAge(username: String): Int?
    suspend fun findLatestVo2MaxInRangeByTime(
        username: String,
        vo2MaxRange: ClosedRange<Float>,
        untilMillis: Long,
    ): FitnessExtension?
}
