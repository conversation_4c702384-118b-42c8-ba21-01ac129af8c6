package com.stt.android.domain.workouts.extensions

interface SummaryExtensionDataSource {
    suspend fun findByWorkoutId(workoutId: Int): SummaryExtension?
    fun scheduleSummaryExtensionUpdateWithZapps()
    suspend fun fetchAll(): List<SummaryExtension>
    suspend fun insert(summaryExtension: SummaryExtension)
    suspend fun findLatestLacticThHr(username: String): Float?
    suspend fun findLatestLacticThPace(username: String): Float?
}
