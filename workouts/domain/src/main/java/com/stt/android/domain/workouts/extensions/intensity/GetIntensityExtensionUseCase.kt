package com.stt.android.domain.workouts.extensions.intensity

import com.stt.android.domain.user.workoutextension.IntensityExtension
import com.stt.android.domain.workouts.extensions.ExtensionsDataSource
import javax.inject.Inject

/**
 * Gets workout extensions either from local or remote
 */
class GetIntensityExtensionUseCase @Inject constructor(
    private val extensionsDataSource: ExtensionsDataSource,
) {
    suspend fun loadLocalExtension(
        workoutId: Int,
    ): IntensityExtension? = extensionsDataSource.loadIntensityExtension(workoutId)
}
