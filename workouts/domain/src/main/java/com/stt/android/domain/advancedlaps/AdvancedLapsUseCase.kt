package com.stt.android.domain.advancedlaps

import com.stt.android.core.domain.LapsTableDataType
import com.stt.android.core.domain.MeasurementUnit
import com.stt.android.core.domain.SuuntoPlusChannel
import com.stt.android.data.toEpochMilli
import com.stt.android.domain.advancedlaps.AdvancedLapsUtils.calculateAscentDescentThreshold
import com.stt.android.domain.sml.IntervalEvent
import com.stt.android.domain.sml.IntervalEventType
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.RecordingStatusEvent
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.sml.SmlStreamData
import com.stt.android.domain.sml.SmlStreamSamplePoint
import com.stt.android.domain.sml.SmlTimedStreamSamplePoint
import com.stt.android.domain.sml.VerticalLapEvent
import com.stt.android.domain.sml.VerticalLapEventType
import com.stt.android.domain.sml.avgMinMaxOrNull
import com.stt.android.domain.sml.dataPointsWithoutPauses
import com.stt.android.domain.sml.filterSamplePoint
import com.stt.android.domain.sml.filterStreamPoint
import com.stt.android.domain.workouts.AvgMinMax
import com.stt.android.domain.workouts.toAvgMinMax
import com.stt.android.infomodel.SummaryItem
import com.stt.android.logbook.Settings
import com.stt.android.logbook.SuuntoLogbookMinMax
import com.stt.android.logbook.SuuntoLogbookSummary
import com.stt.android.logbook.SuuntoLogbookWindow
import com.stt.android.utils.filterRepeated
import com.stt.android.utils.sumByFloat
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.roundToInt

class AdvancedLapsUseCase @Inject constructor() {
    internal val autoGeneratedLapDistances = mapOf(
        LapsTableType.ONE_KM_AUTO_LAP to 1000.0f,
        LapsTableType.FIVE_KM_AUTO_LAP to 5000.0f,
        LapsTableType.TEN_KM_AUTO_LAP to 10000.0f,
        LapsTableType.ONE_MILE_AUTO_LAP to 1609.34f,
        LapsTableType.FIVE_MILE_AUTO_LAP to 8046.72f,
        LapsTableType.TEN_MILE_AUTO_LAP to 16093.4f
    )

    fun getLapsTableData(sml: Sml, measurementUnit: MeasurementUnit): List<LapsTable> {
        return getLapsTableData(
            sml.summary.windows,
            sml.streamData,
            multisportPartActivity = null,
            sml.summary.header,
            measurementUnit
        )
    }

    fun getLapsTableData(
        windows: List<SuuntoLogbookWindow>,
        streamData: SmlStreamData,
        multisportPartActivity: MultisportPartActivity?,
        header: SuuntoLogbookSummary?,
        measurementUnit: MeasurementUnit,
        supportsDistanceAutoLaps: Boolean = true,
        supportsDurationAutoLaps: Boolean = true
    ): List<LapsTable> {
        val autoLapsRows = mutableListOf<LapsTableRow>()
        val manualLapsRows = mutableListOf<LapsTableRow>()
        val intervalLapsRows = mutableListOf<LapsTableRow>()
        val downhillLapsRows = mutableListOf<LapsTableRow>()
        val diveLapsRows = mutableListOf<LapsTableRow>()
        var intervalLapsNumber = 1
        var manualLapsNumber = 1
        var autoLapsNumber = 1
        var downhillLapsNumber = 1
        var diveAutoLapsNumber = 1

        val downhillEvents = streamData.events
            .filterIsInstance<VerticalLapEvent>()
            .filter { it.type == VerticalLapEventType.Downhill }
            .toMutableList()

        windows.forEachIndexed { rowId, window ->
            when (window.type) {
                WindowType.AUTOLAP.type -> {
                    autoLapsRows.apply {
                        val previousLapsCumulatedDistance = this.sumByFloat { it.distance ?: 0.0f }
                        val previousLapsCumulatedDuration = this.sumByFloat { it.duration ?: 0.0f }
                        add(
                            createLapTableRow(
                                window = window,
                                rowId = rowId,
                                lapNumber = autoLapsNumber,
                                lapDuration = window.duration,
                                previousLapsCumulatedDistance = previousLapsCumulatedDistance,
                                previousLapsCumulatedDuration = previousLapsCumulatedDuration,
                                streamData = streamData
                            )
                        )
                    }
                    autoLapsNumber++
                }
                WindowType.LAP.type -> {
                    manualLapsRows.apply {
                        val previousLapsCumulatedDistance = this.sumByFloat { it.distance ?: 0.0f }
                        val previousLapsCumulatedDuration = this.sumByFloat { it.duration ?: 0.0f }
                        add(
                            createLapTableRow(
                                window = window,
                                rowId = rowId,
                                lapNumber = manualLapsNumber,
                                lapDuration = window.duration,
                                previousLapsCumulatedDistance = previousLapsCumulatedDistance,
                                previousLapsCumulatedDuration = previousLapsCumulatedDuration,
                                streamData = streamData
                            )
                        )
                    }
                    manualLapsNumber++
                }
                WindowType.INTERVAL.type -> {
                    intervalLapsRows.apply {
                        val intervalRows = this.filter { row -> row.type == WindowType.INTERVAL }
                        val previousLapsCumulatedDistance =
                            intervalRows.sumByFloat { it.distance ?: 0.0f }
                        val previousLapsCumulatedDuration =
                            intervalRows.sumByFloat { it.duration ?: 0.0f }
                        add(
                            createLapTableRow(
                                window = window,
                                rowId = rowId,
                                lapNumber = intervalLapsNumber,
                                lapDuration = window.duration,
                                previousLapsCumulatedDistance = previousLapsCumulatedDistance,
                                previousLapsCumulatedDuration = previousLapsCumulatedDuration,
                                streamData = streamData
                            )
                        )
                    }
                    intervalLapsNumber++
                }
                WindowType.POOLLENGTH.type -> {
                    intervalLapsRows.apply {
                        val poolLengthRows =
                            this.filter { row -> row.type == WindowType.POOLLENGTH }
                        val previousLapsCumulatedDistance =
                            poolLengthRows.sumByFloat { it.distance ?: 0.0f }
                        val previousLapsCumulatedDuration =
                            poolLengthRows.sumByFloat { it.duration ?: 0.0f }
                        add(
                            createLapTableRow(
                                window = window,
                                rowId = rowId,
                                lapNumber = intervalLapsNumber,
                                lapDuration = window.duration,
                                previousLapsCumulatedDistance = previousLapsCumulatedDistance,
                                previousLapsCumulatedDuration = previousLapsCumulatedDuration,
                                streamData = streamData
                            )
                        )
                    }
                }
                WindowType.DOWNHILL.type -> {
                    downhillLapsRows.apply {
                        val previousLapsCumulatedDistance = this.sumByFloat { it.distance ?: 0.0f }
                        val previousLapsCumulatedDuration = this.sumByFloat { it.duration ?: 0.0f }

                        // Finding the correct time when the downhill laps happened is complicated
                        // as unlike other workout types, the first lap doesn't start with the workout
                        // and there's downtime between laps. That means we can't use the cumulated
                        // duration, and need to look up the end time from the SML.
                        //
                        // The SML has VerticalLap events that should have the actual lap end times,
                        // but in some SMLs the amount of events and windows don't match. If there's
                        // not enough events, we use the window's end time as an estimate, and ignore excess events.
                        //
                        // The event timestamps have been wrong in at least one SML that had larger
                        // inconsistency between amount of windows (19) and events (14) (key 622a17a3f2d364661f94dd80)
                        val lapEndSeconds = downhillEvents
                            .removeFirstOrNull()
                            ?.timestamp?.let {
                                streamData.getSecondsInWorkoutForTimestamp(it)
                            }
                            ?: streamData.getSecondsInWorkoutForTimestamp(window.timestamp?.toEpochMilli())

                        add(
                            createLapTableRow(
                                window = window,
                                rowId = rowId,
                                lapNumber = downhillLapsNumber,
                                lapDuration = window.duration,
                                previousLapsCumulatedDistance = previousLapsCumulatedDistance,
                                previousLapsCumulatedDuration = previousLapsCumulatedDuration,
                                streamData = streamData
                            ).let {
                                it.copy(cumulatedDuration = lapEndSeconds ?: it.cumulatedDuration)
                            }
                        )
                    }
                    downhillLapsNumber++
                }
                WindowType.DIVE.type -> {
                    diveLapsRows.apply {
                        val previousLapsCumulatedDistance = this.sumByFloat { it.distance ?: 0.0f }
                        val previousLapsCumulatedDuration = this.sumByFloat { it.duration ?: 0.0f }
                        add(
                            createLapTableRow(
                                window = window,
                                rowId = rowId,
                                lapNumber = diveAutoLapsNumber,
                                lapDuration = window.duration,
                                previousLapsCumulatedDistance = previousLapsCumulatedDistance,
                                previousLapsCumulatedDuration = previousLapsCumulatedDuration,
                                streamData = streamData
                            )
                        )
                    }
                    diveAutoLapsNumber++
                }
            }
        }

        val lapsTotalDuration = manualLapsRows.sumByFloat { it.duration ?: 0f }
        val workoutDuration = header?.duration ?: 0f
        val adjustedManualLapRows = if (lapsTotalDuration > workoutDuration) {
            getManualLapRowsWithAdjustedLastLap(workoutDuration, manualLapsRows)
        } else {
            manualLapsRows
        }

        // If there is only one dive lap with 0 dive time, that is an empty lap so avoid showing that
        if (diveLapsRows.size == 1 && diveLapsRows.first().diveTime == 0f) {
            diveLapsRows.clear()
        }

        return mutableListOf(
            LapsTable(
                LapsTableType.MANUAL,
                adjustedManualLapRows,
                getAvailableDataTypes(adjustedManualLapRows)
            ),
            LapsTable(
                LapsTableType.DOWNHILL,
                downhillLapsRows,
                getAvailableDataTypes(downhillLapsRows)
            ),
            LapsTable(
                LapsTableType.INTERVAL,
                intervalLapsRows,
                getAvailableDataTypes(intervalLapsRows)
            ),
            LapsTable(
                LapsTableType.DIVE,
                diveLapsRows,
                getAvailableDataTypes(diveLapsRows)
            )
        ).apply {
            addAll(
                getAutoLaps(
                    autoLapsRows,
                    streamData,
                    multisportPartActivity,
                    header,
                    measurementUnit,
                    supportsDistanceAutoLaps,
                    supportsDurationAutoLaps
                )
            )
        }
    }

    /**
     * Last lap from a legacy watch (manual lap) contains the pause time so we need to adjust its duration
     * */
    private fun getManualLapRowsWithAdjustedLastLap(
        workoutTotalDuration: Float,
        originalLapTableRows: List<LapsTableRow>
    ): List<LapsTableRow> {
        val adjustedList = originalLapTableRows.dropLast(1)
        val durationWithoutLastItem = adjustedList.sumByFloat { it.duration ?: 0.0f }
        val lastLapDuration = workoutTotalDuration - durationWithoutLastItem
        return adjustedList + originalLapTableRows.last().copy(duration = lastLapDuration)
    }

    private fun getAutoLaps(
        autoLapsRows: List<LapsTableRow>,
        streamData: SmlStreamData,
        multisportPartActivity: MultisportPartActivity?,
        header: SuuntoLogbookSummary?,
        measurementUnit: MeasurementUnit,
        supportsDistanceAutoLaps: Boolean,
        supportsDurationAutoLaps: Boolean
    ): List<LapsTable> {
        val autoLapType = if (header?.settings?.autoLap?.duration != null && supportsDurationAutoLaps) {
            LapsTableType.DURATION_AUTO_LAP
        } else if (supportsDistanceAutoLaps) {
            LapsTableType.DISTANCE_AUTO_LAP
        } else {
            null
        }

        if (autoLapType == null) {
            return emptyList()
        }

        val autoLapLength = getAutoLapLength(header?.settings, autoLapType)

        return buildList {
            val allAutoGeneratedLaps = getAllAutoGeneratedLaps(
                streamData,
                multisportPartActivity,
                header,
                measurementUnit
            )
            if (autoLapLength != null && autoLapsRows.isNotEmpty()) {
                add(
                    LapsTable(
                        autoLapType,
                        autoLapsRows,
                        getAvailableDataTypes(autoLapsRows),
                        autoLapLength
                    )
                )
                val lengthRounded = autoLapLength.roundToInt()
                // Drop autogenerated lap if it matches with the autolap generated by the watch.
                // At least some watches reports 5MI autolap interval to be 8047 so we need to round the values
                // before comparison. See:
                addAll(allAutoGeneratedLaps.filter { it.autoLapLength.roundToInt() != lengthRounded })
            } else {
                addAll(allAutoGeneratedLaps)
            }
        }.sortedBy { it.autoLapLength }
    }

    private fun getAutoLapLength(
        settings: Settings?,
        autoLapType: LapsTableType
    ): Float? {
        return when (autoLapType) {
            LapsTableType.DISTANCE_AUTO_LAP -> settings?.autoLap?.distance
            LapsTableType.DURATION_AUTO_LAP -> settings?.autoLap?.duration
            else -> null
        }
    }

    private fun getAvailableDataTypes(lapRows: List<LapsTableRow>): Set<LapsTableDataType> {
        val summaryTypes = SummaryItem.entries.filter { item ->
            val type = LapsTableDataType.Summary(item)
            when (item) {
                SummaryItem.AVGHEARTRATE,
                SummaryItem.MAXHEARTRATE,
                SummaryItem.MINHEARTRATE,
                SummaryItem.AVGPOWER,
                SummaryItem.AVGCADENCE,
                SummaryItem.MAXPOWER -> lapRows.any { row ->
                    type.rowValue(row).let { it != null && it != 0.0f }
                }
                SummaryItem.REVOLUTIONCOUNT,
                SummaryItem.ROWINGSTROKECOUNT,
                SummaryItem.SKIPCOUNT -> lapRows.any { row ->
                    type.rowValue(row).let { it != null && it != 0 }
                }

                else -> lapRows.any { row ->
                    type.rowValue(row) != null
                }
            }
        }.map { LapsTableDataType.Summary(it) }
            .toSet()

        val suuntoPlusTypes = lapRows
            .flatMap {
                it.suuntoPlusData?.filterValues { avgMinMax ->
                    avgMinMax.avg != null
                }?.keys ?: listOf()
            }.distinct()
            .map { LapsTableDataType.SuuntoPlus(it) }
            .toSet()

        return summaryTypes + suuntoPlusTypes
    }

    private fun createLapTableRow(
        window: SuuntoLogbookWindow,
        rowId: Int,
        lapNumber: Int,
        lapDuration: Float?,
        previousLapsCumulatedDistance: Float,
        previousLapsCumulatedDuration: Float,
        streamData: SmlStreamData?
    ): LapsTableRow {
        val lapsCumulatedDistance = previousLapsCumulatedDistance + (window.distance ?: 0.0f)
        val lapsCumulatedDuration = previousLapsCumulatedDuration + (lapDuration ?: 0.0f)
        val lapStartTimestamp = window.timestamp
        val lapEndTimestamp = window.timestamp?.plusSeconds(window.duration?.toLong() ?: 0)
        val altitude: SuuntoLogbookMinMax? = window.altitude?.firstOrNull()
        val cadence: SuuntoLogbookMinMax? = window.cadence?.firstOrNull()
        val downhillGrade: SuuntoLogbookMinMax? = window.downhillGrade?.firstOrNull()
        val heartrate: SuuntoLogbookMinMax? = window.hr?.firstOrNull()
        val power: SuuntoLogbookMinMax? = window.power?.firstOrNull()
        val speed: SuuntoLogbookMinMax? = window.speed?.firstOrNull()
        val strokeRate: SuuntoLogbookMinMax? = window.strokeRate?.firstOrNull()
        val strokes: SuuntoLogbookMinMax? = window.strokes?.firstOrNull()
        val swolf: SuuntoLogbookMinMax? = window.swolf?.firstOrNull()
        val avgMinMaxTemperature =
            streamData?.temperature?.filterStreamPoint(
                lapStartTimestamp?.toEpochMilli(),
                lapEndTimestamp?.toEpochMilli()
            )?.avgMinMaxOrNull()
                ?: window.temperature?.firstOrNull()?.toAvgMinMax()
        val verticalSpeed: SuuntoLogbookMinMax? = window.verticalSpeed?.firstOrNull()
        val depth: SuuntoLogbookMinMax? = window.depth?.firstOrNull()
        val diveTime = window.diveTime
        val diveRecoveryTime = window.diveRecoveryTime
        val diveTimeMax = window.diveTimeMax
        val diveInWorkout = window.diveInWorkout
        val repetitionCount = window.repetitionCount
        val stride: SuuntoLogbookMinMax? = window.stride?.firstOrNull()
        val fatConsumption: Int? = window.fatConsumption
        val carbohydrateConsumption: Int? = window.carbohydrateConsumption
        val groundContactTime: SuuntoLogbookMinMax? = window.groundContactTime?.firstOrNull()
        val verticalOscillation: SuuntoLogbookMinMax? = window.verticalOscillation?.firstOrNull()
        val leftGroundContactBalance: SuuntoLogbookMinMax? = window.leftGroundContactBalance?.firstOrNull()
        val rightGroundContactBalance: SuuntoLogbookMinMax? = window.rightGroundContactBalance?.firstOrNull()
        val ascentSpeed: SuuntoLogbookMinMax? = window.ascentSpeed?.firstOrNull()
        val descentSpeed: SuuntoLogbookMinMax? = window.descentSpeed?.firstOrNull()
        val distancePerStroke: SuuntoLogbookMinMax? = window.distancePerStroke?.firstOrNull()
        val lapsType =
            WindowType.entries.firstOrNull { it.type == window.type } ?: WindowType.UNKNOWN
        val swimmingStyle = window.swimStyle ?: window.swimmingStyle
        val headAngle =  when (swimmingStyle) {
            SwimStyle.FREE.value -> window.freestyleHeadAngle
            SwimStyle.BREAST.value -> window.breaststrokeHeadAngle
            else -> null
        }

        @Suppress("UNCHECKED_CAST")
        val suuntoPlusData: Map<SuuntoPlusChannel, AvgMinMax>? = streamData?.suuntoPlusSamplePoints
            ?.mapValues { (_, points) ->
                points.filterStreamPoint(
                    lapStartTimestamp?.toEpochMilli(),
                    lapEndTimestamp?.toEpochMilli()
                ).avgMinMaxOrNull()
            }?.filterValues { it != null } as? Map<SuuntoPlusChannel, AvgMinMax>
        return LapsTableRow(
            rowid = rowId,
            lapNumber = lapNumber,
            minAltitude = altitude?.min,
            maxAltitude = altitude?.max,
            avgAltitude = altitude?.avg,
            ascent = window.ascent,
            ascentTime = window.ascentTime,
            descent = window.descent,
            descentTime = window.descentTime,
            maxDescent = window.descentMax,
            minCadence = cadence?.min,
            maxCadence = cadence?.max,
            avgCadence = cadence?.avg,
            distance = window.distance,
            distanceMax = window.distanceMax,
            minDownhillGrade = downhillGrade?.min,
            maxDownhillGrade = downhillGrade?.max,
            avgDownhillGrade = downhillGrade?.avg,
            duration = lapDuration,
            energy = window.energy,
            minHR = heartrate?.min,
            maxHR = heartrate?.max,
            avgHR = heartrate?.avg,
            minPower = power?.min,
            maxPower = power?.max,
            avgPower = power?.avg,
            recoveryTime = window.recoveryTime?.roundToInt(),
            minSpeed = speed?.min,
            maxSpeed = speed?.max,
            avgSpeed = speed?.avg,
            minStrokeRate = strokeRate?.min,
            maxStrokeRate = strokeRate?.max,
            avgStrokeRate = strokeRate?.avg,
            minStrokes = strokes?.min,
            maxStrokes = strokes?.max,
            avgStrokes = strokes?.avg,
            swimStyle = swimmingStyle,
            minSwolf = swolf?.min,
            maxSwolf = swolf?.max,
            avgSwolf = swolf?.avg,
            minTemperature = avgMinMaxTemperature?.min?.toFloat(),
            maxTemperature = avgMinMaxTemperature?.max?.toFloat(),
            avgTemperature = avgMinMaxTemperature?.avg?.toFloat(),
            type = lapsType,
            minVerticalSpeed = verticalSpeed?.min,
            maxVerticalSpeed = verticalSpeed?.max,
            avgVerticalSpeed = verticalSpeed?.avg,
            minDepth = depth?.min,
            maxDepth = depth?.max,
            avgDepth = depth?.avg,
            diveTime = diveTime,
            diveRecoveryTime = diveRecoveryTime,
            diveTimeMax = diveTimeMax,
            diveInWorkout = diveInWorkout,
            suuntoPlusData = suuntoPlusData,
            cumulatedDistance = lapsCumulatedDistance,
            cumulatedDuration = lapsCumulatedDuration,
            repetitionCount = repetitionCount,
            isIntervalRecoveryLap = isIntervalRecoveryLap(streamData, window),
            aerobicHrThreshold = null,
            anaerobicHrThreshold = null,
            aerobicPaceThreshold = null,
            anaerobicPaceThreshold = null,
            aerobicPowerThreshold = null,
            anaerobicPowerThreshold = null,
            aerobicDuration = null,
            anaerobicDuration = null,
            vo2MaxDuration = null,
            avgStrideLength = stride?.avg,
            minStrideLength = stride?.min,
            maxStrideLength = stride?.max,
            fatConsumption = fatConsumption,
            carbohydrateConsumption = carbohydrateConsumption,
            avgGroundContactTime = groundContactTime?.avg,
            minGroundContactTime = groundContactTime?.min,
            maxGroundContactTime = groundContactTime?.max,
            avgVerticalOscillation = verticalOscillation?.avg,
            minVerticalOscillation = verticalOscillation?.min,
            maxVerticalOscillation = verticalOscillation?.max,
            avgLeftGroundContactBalance = leftGroundContactBalance?.avg,
            minLeftGroundContactBalance = leftGroundContactBalance?.min,
            maxLeftGroundContactBalance = leftGroundContactBalance?.max,
            avgRightGroundContactBalance = rightGroundContactBalance?.avg,
            minRightGroundContactBalance = rightGroundContactBalance?.min,
            maxRightGroundContactBalance = rightGroundContactBalance?.max,
            avgAscentSpeed = ascentSpeed?.avg,
            minAscentSpeed = ascentSpeed?.min,
            maxAscentSpeed = ascentSpeed?.max,
            avgDescentSpeed = descentSpeed?.avg,
            minDescentSpeed = descentSpeed?.min,
            maxDescentSpeed = descentSpeed?.max,
            avgDistancePerStroke = distancePerStroke?.avg,
            minDistancePerStroke = distancePerStroke?.min,
            maxDistancePerStroke = distancePerStroke?.max,
            breaststrokeGlideTime = window.breaststrokeGlideTime,
            breaststrokeAvgBreathAngle = window.breaststrokeAvgBreathAngle,
            freestyleAvgBreathAngle = window.freestyleAvgBreathAngle,
            breathingRate = window.breathingRate,
            headAngle = headAngle,
        )
    }

    private fun isIntervalRecoveryLap(
        streamData: SmlStreamData?,
        window: SuuntoLogbookWindow
    ): Boolean {
        val windowTimestamp = window.timestamp?.toEpochMilli() ?: return false
        // To find the interval event matching the window we find the closest one by comparing the
        // timestamps
        return streamData?.events
            ?.filterIsInstance<IntervalEvent>()
            ?.minByOrNull { event -> kotlin.math.abs(windowTimestamp - event.timestamp) }
            ?.type == IntervalEventType.Recovery
    }

    internal fun getAllAutoGeneratedLaps(
        streamData: SmlStreamData,
        multisportPartActivity: MultisportPartActivity?,
        header: SuuntoLogbookSummary?,
        measurementUnit: MeasurementUnit
    ): List<LapsTable> {
        // Autogenerated laps are created only for workouts which have distance data in samples
        if (streamData.samplePoint.all { p -> p.cumulativeDistance == null }) return emptyList()

        val timestampedDataPoints = streamData.samplePoint.filterSamplePoint(multisportPartActivity)
        val distanceCorrection =
            getDistanceCorrection(multisportPartActivity, timestampedDataPoints)

        // Distance samples and heart rate samples are usually in different objects, but may
        // also be in the same object. Or there maybe HR samples and distance samples in separate
        // objects but with an identical timestamp. This is common in workouts imported from FIT
        // files since both HR data and distance data come from the same FIT records.
        val samplesWithoutPauses =
            streamData.dataPointsWithoutPauses(
                timestampedDataPoints = timestampedDataPoints,
                startTimestamp = multisportPartActivity?.startTime,
                startDistance = distanceCorrection
            )
                .asSequence()
                .filterRepeated() // Some SML files may have repeated samples with identical timestamp
                .filter { sample -> sample.samplePoint.cumulativeDistance != null || sample.samplePoint.heartrate != null }

        return if (measurementUnit == MeasurementUnit.METRIC) {
            listOfNotNull(
                getAutoGeneratedLaps(samplesWithoutPauses, header, LapsTableType.ONE_KM_AUTO_LAP),
                getAutoGeneratedLaps(samplesWithoutPauses, header, LapsTableType.FIVE_KM_AUTO_LAP),
                getAutoGeneratedLaps(samplesWithoutPauses, header, LapsTableType.TEN_KM_AUTO_LAP)
            )
        } else {
            listOfNotNull(
                getAutoGeneratedLaps(samplesWithoutPauses, header, LapsTableType.ONE_MILE_AUTO_LAP),
                getAutoGeneratedLaps(
                    samplesWithoutPauses,
                    header,
                    LapsTableType.FIVE_MILE_AUTO_LAP
                ),
                getAutoGeneratedLaps(samplesWithoutPauses, header, LapsTableType.TEN_MILE_AUTO_LAP)
            )
        }
    }

    private fun getAutoGeneratedLaps(
        samplesWithoutPauses: Sequence<SmlTimedStreamSamplePoint>,
        header: SuuntoLogbookSummary?,
        type: LapsTableType
    ): LapsTable? {
        val lapLengthInMeters = autoGeneratedLapDistances.getOrElse(type) { null } ?: return null

        val firstSample = samplesWithoutPauses.firstOrNull()
        val lastSample = samplesWithoutPauses.lastOrNull()
        val autoGeneratedRows = mutableListOf<LapsTableRow>()
        val ascentDescentThreshold = calculateAscentDescentThreshold(header)
        val ongoingLap = AdvancedOngoingLap(
            workoutDurationOnStart = firstSample?.time ?: 0,
            workoutDistanceOnStart = firstSample?.samplePoint?.cumulativeDistance ?: 0.0f,
            ascentDescentThreshold = ascentDescentThreshold
        )
        var lapNumber = 1

        samplesWithoutPauses.forEach { sample ->
            try {
                lapNumber = sampleToLap(
                    autoGeneratedRows,
                    ongoingLap,
                    lapNumber,
                    lapLengthInMeters,
                    sample,
                    lastSample
                )
            } catch (t: Throwable) {
                Timber.w(t, "AdvancedLapsUseCase.sampleToLap threw an error.")
            }
        }
        return LapsTable(
            type,
            autoGeneratedRows,
            getAvailableDataTypes(autoGeneratedRows),
            lapLengthInMeters
        )
    }

    private fun getDistanceCorrection(
        multisportPartActivity: MultisportPartActivity?,
        timestampedDataPoints: List<SmlStreamSamplePoint>
    ) = if (multisportPartActivity != null) {
        timestampedDataPoints.firstOrNull {
            it.cumulativeDistance != null
        }?.cumulativeDistance ?: 0f
    } else {
        null
    }

    private fun sampleToLap(
        autoGeneratedRows: MutableList<LapsTableRow>,
        ongoingLap: AdvancedOngoingLap,
        lapNumber: Int,
        lapLengthInMeters: Float,
        sample: SmlTimedStreamSamplePoint,
        lastSample: SmlTimedStreamSamplePoint?
    ): Int {
        val currentDistance = sample.samplePoint.cumulativeDistance ?: 0.0f
        var currentLapNumber = lapNumber
        val nextLapLimit = lapLengthInMeters * currentLapNumber
        val nextNextLapLimit = lapLengthInMeters * currentLapNumber + lapLengthInMeters

        if (currentDistance >= nextLapLimit) {
            // New lap completed
            ongoingLap.updateOngoingLap(sample)
            autoGeneratedRows.add(createRow(ongoingLap, currentLapNumber, lapLengthInMeters))
            ongoingLap.lapFinished()
            currentLapNumber++
        } else if (sample == lastSample) {
            ongoingLap.updateOngoingLap(sample)
            autoGeneratedRows.add(createRow(ongoingLap, currentLapNumber, ongoingLap.distance))
        } else if (currentDistance < nextNextLapLimit) {
            ongoingLap.updateOngoingLap(sample)
        } else {
            // To end up here means that there is something wrong with the data like in this case:
            // https://suunto.tpondemand.com/entity/91907-not-all-laps-are-shown-in
            // where consecutive samples with 1 second difference in timestamps had 8km difference
            // in distance for some reason. We create empty lap rows to close up the gap
            Timber.w("Unexpected data in the SML.")
            autoGeneratedRows.add(createRow(ongoingLap, currentLapNumber, 0f))
            ongoingLap.lapFinished()
            currentLapNumber++
            return sampleToLap(
                autoGeneratedRows,
                ongoingLap,
                currentLapNumber,
                lapLengthInMeters,
                sample,
                lastSample
            )
        }
        return currentLapNumber
    }

    private fun createRow(
        lap: AdvancedOngoingLap,
        lapNumber: Int,
        lapDistance: Float
    ): LapsTableRow {
        val ascent = lap.ascentDescentStatistics.deltaUp.toFloat()
        val descent = -lap.ascentDescentStatistics.deltaDown.toFloat()
        return LapsTableRow(
            rowid = lapNumber, // rowid is the same as lapnro is autogenerated laps
            lapNumber = lapNumber,
            minAltitude = lap.altitudeStatistics.min.toFloat(),
            maxAltitude = lap.altitudeStatistics.max.toFloat(),
            avgAltitude = lap.altitudeStatistics.avg.toFloat(),
            ascent = ascent,
            ascentTime = null,
            descent = descent,
            descentTime = null,
            maxDescent = null,
            minCadence = lap.cadenceStatistics.min.toFloat(),
            maxCadence = lap.cadenceStatistics.max.toFloat(),
            avgCadence = lap.cadenceStatistics.avg.toFloat(),
            distance = lapDistance,
            distanceMax = null,
            minDownhillGrade = null,
            maxDownhillGrade = null,
            avgDownhillGrade = null,
            duration = lap.duration.toFloat() / 1000, // ms to s
            energy = null,
            minHR = lap.heartRateStatistics.min.toFloat(),
            maxHR = lap.heartRateStatistics.max.toFloat(),
            avgHR = lap.heartRateStatistics.avg.toFloat(),
            minPower = lap.powerStatistics.min.toFloat(),
            maxPower = lap.powerStatistics.max.toFloat(),
            avgPower = lap.powerStatistics.avg.toFloat(),
            recoveryTime = null,
            minSpeed = lap.speedStatistics.min.toFloat(),
            maxSpeed = lap.speedStatistics.max.toFloat(),
            avgSpeed = lapDistance / lap.duration * 1000f,
            minStrokeRate = null,
            maxStrokeRate = null,
            avgStrokeRate = null,
            minStrokes = null,
            maxStrokes = null,
            avgStrokes = null,
            swimStyle = null,
            minSwolf = null,
            maxSwolf = null,
            avgSwolf = null,
            minTemperature = lap.temperatureStatistics?.min?.toFloat(),
            maxTemperature = lap.temperatureStatistics?.max?.toFloat(),
            avgTemperature = lap.temperatureStatistics?.avg?.toFloat(),
            type = WindowType.UNKNOWN,
            minVerticalSpeed = lap.verticalSpeedStatistics.min.toFloat(),
            maxVerticalSpeed = lap.verticalSpeedStatistics.max.toFloat(),
            avgVerticalSpeed = lap.verticalSpeedStatistics.avg.toFloat(),
            minDepth = null,
            maxDepth = null,
            avgDepth = null,
            diveTime = null,
            diveRecoveryTime = null,
            diveTimeMax = null,
            diveInWorkout = null,
            suuntoPlusData = lap.getSuuntoPlusData(),
            cumulatedDistance = lap.cumulativeDistance,
            cumulatedDuration = lap.cumulativeDuration.toFloat() / 1000, // ms to s
            repetitionCount = lap.repetitionCount,
            isIntervalRecoveryLap = false,
            aerobicHrThreshold = null,
            anaerobicHrThreshold = null,
            aerobicPaceThreshold = null,
            anaerobicPaceThreshold = null,
            aerobicPowerThreshold = null,
            anaerobicPowerThreshold = null,
            aerobicDuration = null,
            anaerobicDuration = null,
            vo2MaxDuration = null,
            avgStrideLength = lap.strideStatistics.avg.toFloat(),
            minStrideLength = lap.strideStatistics.min.toFloat(),
            maxStrideLength = lap.strideStatistics.max.toFloat(),
            fatConsumption = lap.fatConsumption,
            carbohydrateConsumption = lap.carbohydrateConsumption,
            avgGroundContactTime = lap.groundContactTimeStatistics.avg.toFloat(),
            minGroundContactTime = lap.groundContactTimeStatistics.min.toFloat(),
            maxGroundContactTime = lap.groundContactTimeStatistics.max.toFloat(),
            avgVerticalOscillation = lap.verticalOscillationStatistics.avg.toFloat(),
            minVerticalOscillation = lap.verticalOscillationStatistics.min.toFloat(),
            maxVerticalOscillation = lap.verticalOscillationStatistics.max.toFloat(),
            avgLeftGroundContactBalance = lap.leftGroundContactBalanceStatistics.avg.toFloat(),
            minLeftGroundContactBalance = lap.leftGroundContactBalanceStatistics.min.toFloat(),
            maxLeftGroundContactBalance = lap.leftGroundContactBalanceStatistics.max.toFloat(),
            avgRightGroundContactBalance = lap.rightGroundContactBalanceStatistics.avg.toFloat(),
            minRightGroundContactBalance = lap.rightGroundContactBalanceStatistics.min.toFloat(),
            maxRightGroundContactBalance = lap.rightGroundContactBalanceStatistics.max.toFloat(),
            avgAscentSpeed = lap.ascentSpeed.avg.toFloat(),
            minAscentSpeed = lap.ascentSpeed.min.toFloat(),
            maxAscentSpeed = lap.ascentSpeed.max.toFloat(),
            avgDescentSpeed = lap.descentSpeed.avg.toFloat(),
            minDescentSpeed = lap.descentSpeed.min.toFloat(),
            maxDescentSpeed = lap.descentSpeed.max.toFloat(),
            avgDistancePerStroke = lap.distancePerStroke.avg.toFloat(),
            minDistancePerStroke = lap.distancePerStroke.min.toFloat(),
            maxDistancePerStroke = lap.distancePerStroke.max.toFloat(),
            breaststrokeGlideTime = lap.breaststrokeGlideTimeStatistics?.sum?.toInt(),
            breaststrokeAvgBreathAngle = lap.avgBreaststrokeBreathAngleStatistics?.avg?.roundToInt(),
            freestyleAvgBreathAngle = lap.avgFreestyleBreathAngleStatistics?.avg?.roundToInt(),
            breathingRate = lap.breatheRateStatistics?.avg?.roundToInt(),
            headAngle = if (
                (lap.breaststrokeHeadAngleStatistics?.count
                    ?: 0) > (lap.freestyleHeadAngleStatistics?.count ?: 0)
            ) {
                lap.breaststrokeHeadAngleStatistics?.avg?.roundToInt()
            } else {
                lap.freestyleHeadAngleStatistics?.avg?.roundToInt()
            },
        )
    }

    private fun SmlStreamData.getSecondsInWorkoutForTimestamp(timestamp: Long?): Float? {
        if (timestamp == null) return null

        val lastEventBeforeTargetTimestamp = events
            .filterIsInstance<RecordingStatusEvent>()
            .lastOrNull { it.data.duration != null && it.data.timestamp <= timestamp }

        val millisInWorkout = when {
            lastEventBeforeTargetTimestamp != null -> {
                timestamp - lastEventBeforeTargetTimestamp.timestamp + lastEventBeforeTargetTimestamp.duration!!
            }
            events.isNotEmpty() -> {
                timestamp - events.first().data.timestamp
            }
            else -> {
                null
            }
        }

        return millisInWorkout?.div(1000f)
    }
}
