package com.stt.android.domain.workout

import android.content.Context
import android.content.res.Resources
import android.graphics.drawable.Drawable
import android.os.Parcelable
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.util.Pair
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.domain.workouts.Searchable
import com.stt.android.workouts.domain.R
import kotlinx.parcelize.Parcelize
import java.util.Locale

@Parcelize
data class ActivityType internal constructor(
    val id: Int,
    val simpleName: String,
    @StringRes
    val localizedStringId: Int,
    @DrawableRes
    val iconId: Int,
    @ColorRes
    val colorId: Int,
    @DrawableRes
    val placeholderImageId: Int,
    val zeroSpeedThreshold: Double,
    val isByFoot: <PERSON><PERSON><PERSON>,
    val isSlopeSki: <PERSON><PERSON>an,
    val isDiving: <PERSON><PERSON><PERSON>,
    val isIndoor: <PERSON><PERSON><PERSON>,
    val isOther: <PERSON><PERSON><PERSON>,
    val isUnknown: <PERSON><PERSON><PERSON>,
    val isSwimming: <PERSON><PERSON><PERSON>,
    val usesNauticalUnits: Boolean,
    val supportsDiveProfile: Boolean,
    val supportsDiveEvents: Boolean,
) : Searchable, Parcelable {

    fun getLocalizedName(resources: Resources): String = resources.getString(localizedStringId)

    override fun getSearchString(resources: Resources): String {
        // We use the strings' locale to lower case the activity type instead of the device
        // current locale
        val stringsLocale = Locale(resources.getString(R.string.language_code))
        return getLocalizedName(resources).lowercase(stringsLocale)
    }

    fun getIcon(context: Context): Drawable? = AppCompatResources.getDrawable(context, iconId)

    fun ordinal(): Int = ALL.indexOfFirst { it == this }.takeIf { it >= 0 }
        ?: throw IllegalArgumentException("Unknown activity type for ID $id")

    fun isSnorkelingOrMermaid() = this == SNORKELING || this == MERMAIDING

    @Suppress("LongParameterList")
    internal class Builder internal constructor(
        private var id: Int = 0,
        private var simpleName: String? = null,
        private var localizedStringId: Int = 0,
        private var placeholderImageId: Int = 0,
        private var iconId: Int = 0,
        private var colorId: Int = 0,
        private var zeroSpeedThreshold: Double = 0.0,
        private var isByFoot: Boolean = false,
        private var isSlopeSki: Boolean = false,
        private var isDiving: Boolean = false,
        private var isIndoor: Boolean = false,
        private var isOther: Boolean = false,
        private var isUnknown: Boolean = false,
        private var isSwimming: Boolean = false,
        private var usesNauticalUnits: Boolean = false,
        private var supportsDiveProfile: Boolean = false,
        private var supportsDiveEvents: Boolean = false,
    ) {
        internal fun id(id: Int): Builder = apply { this.id = id }

        internal fun simpleName(simpleName: String): Builder =
            apply { this.simpleName = simpleName }

        internal fun localizedStringId(localizedStringId: Int): Builder =
            apply { this.localizedStringId = localizedStringId }

        internal fun placeholderImageId(placeholderImageId: Int): Builder =
            apply { this.placeholderImageId = placeholderImageId }

        internal fun iconId(iconId: Int): Builder = apply { this.iconId = iconId }

        internal fun colorId(colorId: Int): Builder = apply { this.colorId = colorId }

        internal fun zeroSpeedThreshold(zeroSpeedThreshold: Double): Builder =
            apply { this.zeroSpeedThreshold = zeroSpeedThreshold }

        internal fun isByFoot(isByFoot: Boolean): Builder = apply { this.isByFoot = isByFoot }

        internal fun isSlopeSki(isSlopeSki: Boolean): Builder =
            apply { this.isSlopeSki = isSlopeSki }

        internal fun isDiving(isDiving: Boolean): Builder = apply { this.isDiving = isDiving }

        internal fun isIndoor(isIndoor: Boolean): Builder = apply { this.isIndoor = isIndoor }

        internal fun isOther(isOther: Boolean): Builder = apply { this.isOther = isOther }

        internal fun isUnknown(isUnknown: Boolean): Builder = apply { this.isUnknown = isUnknown }

        internal fun isSwimming(isSwimming: Boolean): Builder =
            apply { this.isSwimming = isSwimming }

        internal fun usesNauticalUnits(usesNauticalUnits: Boolean): Builder =
            apply { this.usesNauticalUnits = usesNauticalUnits }

        internal fun supportsDiveProfile(supportsDiveProfile: Boolean): Builder =
            apply { this.supportsDiveProfile = supportsDiveProfile }

        internal fun supportsDiveEvents(supportsDiveEvents: Boolean): Builder =
            apply { this.supportsDiveEvents = supportsDiveEvents }

        internal fun build(): ActivityType =
            ActivityType(
                id = id,
                simpleName = simpleName ?: error("simpleName == null"),
                localizedStringId = localizedStringId,
                placeholderImageId = placeholderImageId,
                iconId = iconId,
                colorId = colorId,
                zeroSpeedThreshold = zeroSpeedThreshold,
                isByFoot = isByFoot,
                isSlopeSki = isSlopeSki,
                isDiving = isDiving,
                isIndoor = isIndoor,
                isOther = isOther,
                isUnknown = isUnknown,
                isSwimming = isSwimming,
                usesNauticalUnits = usesNauticalUnits,
                supportsDiveProfile = supportsDiveProfile,
                supportsDiveEvents = supportsDiveEvents
            )
    }

    companion object {

        private const val CYCLING_SPEED_THRESHOLD: Double = 1.0

        private const val RUN_WALK_HIKE_SPEED_THRESHOLD: Double = 0.55

        private const val OTHER_SPEED_THRESHOLD = 0.55

        private val CREATE_UNKNOWN: ActivityType = internalCreate(
            coreActivityType = CoreActivityType.OTHER_6,
            placeholderImageId = R.drawable.activity_placeholder_default,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "UnspecifiedSport",
            byFoot = false,
            slopeSki = false,
            isDiving = false,
            isIndoor = false,
            isOther = false,
            isUnknown = true,
            isSwimming = false,
            usesNauticalUnits = false,
            supportsDiveProfile = false,
            supportsDiveEvents = false,
        )

        @JvmField
        val WALKING: ActivityType = create(
            coreActivityType = CoreActivityType.WALKING,
            zeroSpeedThreshold = RUN_WALK_HIKE_SPEED_THRESHOLD,
            simpleName = "Walking",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_walking
        )

        @JvmField
        val RUNNING: ActivityType = create(
            coreActivityType = CoreActivityType.RUNNING,
            zeroSpeedThreshold = RUN_WALK_HIKE_SPEED_THRESHOLD,
            simpleName = "Running",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_running
        )

        @JvmField
        val CYCLING: ActivityType = create(
            coreActivityType = CoreActivityType.CYCLING,
            zeroSpeedThreshold = CYCLING_SPEED_THRESHOLD,
            simpleName = "Cycling",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_cycling
        )

        @JvmField
        val CROSS_COUNTRY_SKIING: ActivityType = create(
            coreActivityType = CoreActivityType.CROSS_COUNTRY_SKIING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "NordicSkiing",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_cross_country_skiing
        )

        @JvmField
        val OTHER_1: ActivityType = createOther(CoreActivityType.OTHER_1, "Other1")

        @JvmField
        val OTHER_2: ActivityType = createOther(CoreActivityType.OTHER_2, "Other2")

        @JvmField
        val OTHER_3: ActivityType = createOther(CoreActivityType.OTHER_3, "Other3")

        @JvmField
        val OTHER_4: ActivityType = createOther(CoreActivityType.OTHER_4, "Other4")

        @JvmField
        val OTHER_5: ActivityType = createOther(CoreActivityType.OTHER_5, "Other5")

        @JvmField
        val OTHER_6: ActivityType = createOther(CoreActivityType.OTHER_6, "Other6")

        @JvmField
        val MOUNTAIN_BIKING: ActivityType = create(
            coreActivityType = CoreActivityType.MOUNTAIN_BIKING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "MountainBiking",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_mountain_biking
        )

        @JvmField
        val HIKING: ActivityType = create(
            coreActivityType = CoreActivityType.HIKING,
            zeroSpeedThreshold = RUN_WALK_HIKE_SPEED_THRESHOLD,
            simpleName = "Hiking",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_hiking
        )

        @JvmField
        val ROLLER_SKATING: ActivityType = create(
            coreActivityType = CoreActivityType.ROLLER_SKATING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "RollerSkating",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_rollerskating
        )

        @JvmField
        val DOWNHILL_SKIING: ActivityType = createSlopeSki(
            coreActivityType = CoreActivityType.DOWNHILL_SKIING,
            simpleName = "DownhillSkiing",
            placeholderImageId = R.drawable.activity_placeholder_downhill_skiing
        )

        @JvmField
        val PADDLING: ActivityType = create(
            coreActivityType = CoreActivityType.PADDLING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Paddling",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_paddling
        )

        @JvmField
        val ROWING: ActivityType = create(
            coreActivityType = CoreActivityType.ROWING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Rowing",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_rowing
        )

        @JvmField
        val GOLF: ActivityType = create(
            coreActivityType = CoreActivityType.GOLF,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Golf",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_golf
        )

        @JvmField
        val INDOOR: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.INDOOR,
            simpleName = "Indoor",
            placeholderImageId = R.drawable.activity_placeholder_indoor
        )

        @JvmField
        val PARKOUR: ActivityType = create(
            coreActivityType = CoreActivityType.PARKOUR,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Parkour",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_parkour
        )

        @JvmField
        val BALLGAMES: ActivityType = create(
            coreActivityType = CoreActivityType.BALLGAMES,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Ballgames",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_ballgames
        )

        @JvmField
        val OUTDOOR_GYM: ActivityType = create(
            coreActivityType = CoreActivityType.OUTDOOR_GYM,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "OutdoorGym",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_outdoor_gym
        )

        @JvmField
        val SWIMMING: ActivityType = createSwimming(
            coreActivityType = CoreActivityType.SWIMMING,
            simpleName = "Swimming",
            placeholderImageId = R.drawable.activity_placeholder_swimming,
            isIndoor = true
        )

        @JvmField
        val TRAIL_RUNNING: ActivityType = create(
            coreActivityType = CoreActivityType.TRAIL_RUNNING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "TrailRunning",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_trail_running
        )

        @JvmField
        val GYM: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.GYM,
            simpleName = "Gym",
            placeholderImageId = R.drawable.activity_placeholder_gym
        )

        @JvmField
        val NORDIC_WALKING: ActivityType = create(
            coreActivityType = CoreActivityType.NORDIC_WALKING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "NordicWalking",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_nordic_walking
        )

        @JvmField
        val HORSEBACK_RIDING: ActivityType = create(
            coreActivityType = CoreActivityType.HORSEBACK_RIDING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "HorsebackRiding",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_horseback_riding
        )

        @JvmField
        val MOTOR_SPORTS: ActivityType = create(
            coreActivityType = CoreActivityType.MOTOR_SPORTS,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Motorsports",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_motor_sports
        )

        @JvmField
        val SKATEBOARDING: ActivityType = create(
            coreActivityType = CoreActivityType.SKATEBOARDING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Skateboarding",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_skateboarding
        )

        @JvmField
        val WATER_SPORTS: ActivityType = create(
            coreActivityType = CoreActivityType.WATER_SPORTS,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "WaterSports",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_watersports
        )

        @JvmField
        val CLIMBING: ActivityType = create(
            coreActivityType = CoreActivityType.CLIMBING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Climbing",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_climbing
        )

        @JvmField
        val SNOWBOARDING: ActivityType = createSlopeSki(
            coreActivityType = CoreActivityType.SNOWBOARDING,
            simpleName = "Snowboarding",
            placeholderImageId = R.drawable.activity_placeholder_snowboarding
        )

        @JvmField
        val SKI_TOURING: ActivityType = create(
            coreActivityType = CoreActivityType.SKI_TOURING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "SkiTouring",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_ski_touring
        )

        @JvmField
        val FITNESS_CLASS: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.FITNESS_CLASS,
            simpleName = "FitnessClass",
            placeholderImageId = R.drawable.activity_placeholder_fitness_class
        )

        @JvmField
        val SOCCER: ActivityType = create(
            coreActivityType = CoreActivityType.SOCCER,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Soccer",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_soccer
        )

        @JvmField
        val TENNIS: ActivityType = create(
            coreActivityType = CoreActivityType.TENNIS,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Tennis",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_tennis
        )

        @JvmField
        val BASKETBALL: ActivityType = create(
            coreActivityType = CoreActivityType.BASKETBALL,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Basketball",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_ballgames
        )

        @JvmField
        val BADMINTON: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.BADMINTON,
            simpleName = "Badminton",
            placeholderImageId = R.drawable.activity_placeholder_badminton
        )

        @JvmField
        val BASEBALL: ActivityType = create(
            coreActivityType = CoreActivityType.BASEBALL,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Baseball",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_baseball
        )

        @JvmField
        val VOLLEYBALL: ActivityType = create(
            coreActivityType = CoreActivityType.VOLLEYBALL,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Volleyball",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_volleyball
        )

        @JvmField
        val AMERICAN_FOOTBALL: ActivityType = create(
            coreActivityType = CoreActivityType.AMERICAN_FOOTBALL,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "AmericanFootball",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_americanfootball
        )

        @JvmField
        val TABLE_TENNIS: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.TABLE_TENNIS,
            simpleName = "TableTennis",
            placeholderImageId = R.drawable.activity_placeholder_tabletennis
        )

        @JvmField
        val RACQUETBALL: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.RACQUETBALL,
            simpleName = "RacquetBall",
            placeholderImageId = R.drawable.activity_placeholder_racquetball
        )

        @JvmField
        val SQUASH: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.SQUASH,
            simpleName = "Squash",
            placeholderImageId = R.drawable.activity_placeholder_squash
        )

        @JvmField
        val FLOORBALL: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.FLOORBALL,
            simpleName = "Floorball",
            placeholderImageId = R.drawable.activity_placeholder_floorball
        )

        @JvmField
        val HANDBALL: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.HANDBALL,
            simpleName = "Handball",
            placeholderImageId = R.drawable.activity_placeholder_handball
        )

        @JvmField
        val SOFTBALL: ActivityType = create(
            coreActivityType = CoreActivityType.SOFTBALL,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Softball",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_softball
        )

        @JvmField
        val BOWLING: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.BOWLING,
            simpleName = "Bowling",
            placeholderImageId = R.drawable.activity_placeholder_bowling
        )

        @JvmField
        val CRICKET: ActivityType = create(
            coreActivityType = CoreActivityType.CRICKET,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Cricket",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_cricket
        )

        @JvmField
        val RUGBY: ActivityType = create(
            coreActivityType = CoreActivityType.RUGBY,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Rugby",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_rugby
        )

        @JvmField
        val ICE_SKATING: ActivityType = create(
            coreActivityType = CoreActivityType.ICE_SKATING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "IceSkating",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_iceskating
        )

        @JvmField
        val ICE_HOCKEY: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.ICE_HOCKEY,
            simpleName = "IceHockey",
            placeholderImageId = R.drawable.activity_placeholder_icehockey
        )

        @JvmField
        val YOGA: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.YOGA,
            simpleName = "Yoga",
            placeholderImageId = R.drawable.activity_placeholder_yoga
        )

        @JvmField
        val INDOOR_CYCLING: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.INDOOR_CYCLING,
            simpleName = "IndoorCycling",
            placeholderImageId = R.drawable.activity_placeholder_indoorcycling,
            byFoot = false
        )

        @JvmField
        val TREADMILL: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.TREADMILL,
            simpleName = "Treadmill",
            placeholderImageId = R.drawable.activity_placeholder_treadmill
        )

        @JvmField
        val CROSSFIT: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.CROSSFIT,
            simpleName = "CrossTraining",
            placeholderImageId = R.drawable.activity_placeholder_crossfit
        )

        @JvmField
        val CROSSTRAINER: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.CROSSTRAINER,
            simpleName = "Crosstrainer",
            placeholderImageId = R.drawable.activity_placeholder_crosstrainer
        )

        @JvmField
        val ROLLER_SKIING: ActivityType = create(
            coreActivityType = CoreActivityType.ROLLER_SKIING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "RollerSkiing",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_rollerskiing
        )

        @JvmField
        val INDOOR_ROWING: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.INDOOR_ROWING,
            simpleName = "IndoorRowing",
            placeholderImageId = R.drawable.activity_placeholder_indoorrowing
        )

        @JvmField
        val STRETCHING: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.STRETCHING,
            simpleName = "Stretching",
            placeholderImageId = R.drawable.activity_placeholder_stretching
        )

        @JvmField
        val TRACK_AND_FIELD: ActivityType = create(
            coreActivityType = CoreActivityType.TRACK_AND_FIELD,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "TrackAndField",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_trackandfield
        )

        @JvmField
        val ORIENTEERING: ActivityType = create(
            coreActivityType = CoreActivityType.ORIENTEERING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Orienteering",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_orienteering
        )

        @JvmField
        val SUP: ActivityType = create(
            coreActivityType = CoreActivityType.SUP,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "StandupPaddling",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_sup
        )

        @JvmField
        val COMBAT_SPORTS: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.COMBAT_SPORTS,
            simpleName = "CombatSport",
            placeholderImageId = R.drawable.activity_placeholder_combatsports
        )

        @JvmField
        val KETTLEBELL: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.KETTLEBELL,
            simpleName = "Kettlebell",
            placeholderImageId = R.drawable.activity_placeholder_kettlebell
        )

        @JvmField
        val DANCING: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.DANCING,
            simpleName = "Dancing",
            placeholderImageId = R.drawable.activity_placeholder_dancing
        )

        @JvmField
        val SNOWSHOEING: ActivityType = create(
            coreActivityType = CoreActivityType.SNOWSHOEING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "SnowShoeing",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_snowshoeing
        )

        @JvmField
        val FRISBEE_GOLF: ActivityType = create(
            coreActivityType = CoreActivityType.FRISBEE_GOLF,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Frisbee",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_frisbeegolf
        )

        @JvmField
        val FUTSAL: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.FUTSAL,
            simpleName = "Futsal",
            placeholderImageId = R.drawable.activity_placeholder_futsal
        )

        @JvmField
        val MULTISPORT: ActivityType = create(
            coreActivityType = CoreActivityType.MULTISPORT,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Multisport",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_default
        )

        @JvmField
        val AEROBICS: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.AEROBICS,
            simpleName = "Aerobics",
            placeholderImageId = R.drawable.activity_placeholder_aerobics
        )

        @JvmField
        val TREKKING: ActivityType = create(
            coreActivityType = CoreActivityType.TREKKING,
            zeroSpeedThreshold = RUN_WALK_HIKE_SPEED_THRESHOLD,
            simpleName = "Trekking",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_trekking
        )

        @JvmField
        val SAILING: ActivityType = createSailing(
            placeholderImageId = R.drawable.activity_placeholder_sailing
        )

        @JvmField
        val KAYAKING: ActivityType = create(
            coreActivityType = CoreActivityType.KAYAKING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Kayaking",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_kayaking
        )

        @JvmField
        val CIRCUIT_TRAINING: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.CIRCUIT_TRAINING,
            simpleName = "CircuitTraining",
            placeholderImageId = R.drawable.activity_placeholder_circuittraining
        )

        @JvmField
        val TRIATHLON: ActivityType = create(
            coreActivityType = CoreActivityType.TRIATHLON,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Triathlon",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_triathlon
        )

        @JvmField
        val PADEL: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.PADEL,
            simpleName = "Padel",
            placeholderImageId = R.drawable.activity_placeholder_padel
        )

        @JvmField
        val CHEERLEADING: ActivityType = create(
            coreActivityType = CoreActivityType.CHEERLEADING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Cheerleading",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_cheerleading
        )

        @JvmField
        val BOXING: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.BOXING,
            simpleName = "Boxing",
            placeholderImageId = R.drawable.activity_placeholder_boxing
        )

        @JvmField
        val SCUBADIVING: ActivityType = createDive(
            coreActivityType = CoreActivityType.SCUBADIVING,
            simpleName = "ScubaDiving",
            placeholderImageId = R.drawable.activity_placeholder_scubadiving
        )

        @JvmField
        val FREEDIVING: ActivityType = createDive(
            coreActivityType = CoreActivityType.FREEDIVING,
            simpleName = "FreeDiving",
            placeholderImageId = R.drawable.activity_placeholder_freediving
        )

        @JvmField
        val ADVENTURE_RACING: ActivityType = create(
            coreActivityType = CoreActivityType.ADVENTURE_RACING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "AdventureRacing",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_adventureracing
        )

        @JvmField
        val GYMNASTICS: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.GYMNASTICS,
            simpleName = "Gymnastics",
            placeholderImageId = R.drawable.activity_placeholder_gymnastics
        )

        @JvmField
        val CANOEING: ActivityType = create(
            coreActivityType = CoreActivityType.CANOEING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Canoeing",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_canoeing
        )

        @JvmField
        val MOUNTAINEERING: ActivityType = create(
            coreActivityType = CoreActivityType.MOUNTAINEERING,
            zeroSpeedThreshold = RUN_WALK_HIKE_SPEED_THRESHOLD,
            simpleName = "Mountaineering",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_mountaineering
        )

        @JvmField
        val TELEMARKSKIING: ActivityType = createSlopeSki(
            coreActivityType = CoreActivityType.TELEMARKSKIING,
            simpleName = "TelemarkSkiing",
            placeholderImageId = R.drawable.activity_placeholder_telemarkskiing
        )

        @JvmField
        val OPENWATER_SWIMMING: ActivityType = createSwimming(
            coreActivityType = CoreActivityType.OPENWATER_SWIMMING,
            simpleName = "OpenwaterSwimming",
            placeholderImageId = R.drawable.activity_placeholder_openwaterswimming,
            isIndoor = false
        )

        @JvmField
        val WINDSURFING: ActivityType = create(
            coreActivityType = CoreActivityType.WINDSURFING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Windsurfing",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_windsurfing,
            usesNauticalUnits = false
        )

        @JvmField
        val KITESURFING_KITING: ActivityType = create(
            coreActivityType = CoreActivityType.KITESURFING_KITING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "KitesurfingKiting",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_kitesurfing,
            usesNauticalUnits = false
        )

        @JvmField
        val PARAGLIDING: ActivityType = create(
            coreActivityType = CoreActivityType.PARAGLIDING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Paragliding",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_paragliding
        )

        @JvmField
        val SNORKELING: ActivityType = createSwimDive(
            coreActivityType = CoreActivityType.SNORKELING,
            simpleName = "Snorkeling",
            isIndoor = false,
            placeholderImageId = R.drawable.activity_placeholder_snorkeling
        )

        @JvmField
        val SURFING: ActivityType = create(
            coreActivityType = CoreActivityType.SURFING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Surfing",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_surfing
        )

        @JvmField
        val SWIMRUN: ActivityType = create(
            coreActivityType = CoreActivityType.SWIMRUN,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "SwimRun",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_swimrun
        )

        @JvmField
        val DUATHLON: ActivityType = create(
            coreActivityType = CoreActivityType.DUATHLON,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Duathlon",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_duathlon
        )

        @JvmField
        val AQUATHLON: ActivityType = create(
            coreActivityType = CoreActivityType.AQUATHLON,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Aquathlon",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_aquathlon
        )

        @JvmField
        val OBSTACLE_RACING: ActivityType = create(
            coreActivityType = CoreActivityType.OBSTACLE_RACING,
            zeroSpeedThreshold = RUN_WALK_HIKE_SPEED_THRESHOLD,
            simpleName = "ObstacleRacing",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_obstaclecourse
        )

        @JvmField
        val FISHING: ActivityType = create(
            coreActivityType = CoreActivityType.FISHING,
            zeroSpeedThreshold = RUN_WALK_HIKE_SPEED_THRESHOLD,
            simpleName = "Fishing",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_fishing
        )

        @JvmField
        val HUNTING: ActivityType = create(
            coreActivityType = CoreActivityType.HUNTING,
            zeroSpeedThreshold = RUN_WALK_HIKE_SPEED_THRESHOLD,
            simpleName = "Hunting",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_hunting
        )

        @JvmField
        val GRAVEL_CYCLING: ActivityType = create(
            coreActivityType = CoreActivityType.GRAVEL_CYCLING,
            zeroSpeedThreshold = CYCLING_SPEED_THRESHOLD,
            simpleName = "GravelCycling",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_gravelcycling
        )

        @JvmField
        val MERMAIDING: ActivityType = createSwimDive(
            coreActivityType = CoreActivityType.MERMAIDING,
            simpleName = "Mermaiding",
            placeholderImageId = R.drawable.activity_placeholder_mermaiding,
            isIndoor = true
        )

        // TODO support SPEARFISHING
//        @JvmField val SPEARFISHING: ActivityType = create(
//            coreActivityType = CoreActivityType.SPEARFISHING,
//            zeroSpeedThreshold = RUN_WALK_HIKE_SPEED_THRESHOLD,
//            simpleName = "Spearfishing",
//            byFoot = true,
//            placeholderImageId = R.drawable.activity_placeholder_fishing
//        )

        @JvmField
        val JUMP_ROPE: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.JUMP_ROPE,
            simpleName = "JumpRope",
            placeholderImageId = R.drawable.activity_placeholder_jumprope,
        )

        @JvmField
        val TRACK_RUNNING: ActivityType = create(
            coreActivityType = CoreActivityType.TRACK_RUNNING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "TrackRunning",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_track_running
        )

        @JvmField
        val CALISTHENICS: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.CALISTHENICS,
            simpleName = "Calisthenics",
            placeholderImageId = R.drawable.activity_placeholder_default
        )

        @JvmField
        val E_BIKING: ActivityType = create(
            coreActivityType = CoreActivityType.E_BIKING,
            zeroSpeedThreshold = CYCLING_SPEED_THRESHOLD,
            simpleName = "E-Biking",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_default
        )

        @JvmField
        val E_MTB: ActivityType = create(
            coreActivityType = CoreActivityType.E_MTB,
            zeroSpeedThreshold = CYCLING_SPEED_THRESHOLD,
            simpleName = "E-MTB",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_default
        )

        @JvmField
        val BACKCOUNTRY_SKIING: ActivityType = createSlopeSki(
            coreActivityType = CoreActivityType.BACKCOUNTRY_SKIING,
            simpleName = "DownhillSkiing",
            placeholderImageId = R.drawable.activity_placeholder_default
        )

        @JvmField
        val WHEELCHAIRING: ActivityType = create(
            coreActivityType = CoreActivityType.WHEELCHAIRING,
            zeroSpeedThreshold = CYCLING_SPEED_THRESHOLD,
            simpleName = "Wheelchairing",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_default
        )

        @JvmField
        val HANDCYCLING: ActivityType = create(
            coreActivityType = CoreActivityType.HANDCYCLING,
            zeroSpeedThreshold = CYCLING_SPEED_THRESHOLD,
            simpleName = "HandCycling",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_default
        )

        @JvmField
        val SPLITBOARDING: ActivityType = createSlopeSki(
            coreActivityType = CoreActivityType.SPLITBOARDING,
            simpleName = "SplitBoarding",
            placeholderImageId = R.drawable.activity_placeholder_default
        )

        @JvmField
        val BIATHLON: ActivityType = create(
            coreActivityType = CoreActivityType.BIATHLON,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Biathlon",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_default
        )

        @JvmField
        val MEDITATION: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.MEDITATION,
            simpleName = "Meditation",
            placeholderImageId = R.drawable.activity_placeholder_default
        )

        @JvmField
        val FIELD_HOCKEY: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.FIELD_HOCKEY,
            simpleName = "FieldHockey",
            placeholderImageId = R.drawable.activity_placeholder_default
        )

        @JvmField
        val CYCLOCROSS: ActivityType = create(
            coreActivityType = CoreActivityType.CYCLOCROSS,
            zeroSpeedThreshold = CYCLING_SPEED_THRESHOLD,
            simpleName = "Cyclocross",
            byFoot = false,
            placeholderImageId = R.drawable.activity_placeholder_default
        )

        @JvmField
        val VERTICAL_RUN: ActivityType = create(
            coreActivityType = CoreActivityType.VERTICAL_RUN,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "VerticalRun",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_vertical_running
        )

        @JvmField
        val SKI_MOUNTAINEERING: ActivityType = createSlopeSki(
            coreActivityType = CoreActivityType.SKI_MOUNTAINEERING,
            simpleName = "SkiMountaineering",
            placeholderImageId = R.drawable.activity_placeholder_default
        )

        @JvmField
        val SKATE_SKIING: ActivityType = create(
            coreActivityType = CoreActivityType.SKATE_SKIING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "SkateSkiing",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_default
        )

        @JvmField
        val CLASSIC_SKIING: ActivityType = create(
            coreActivityType = CoreActivityType.CLASSIC_SKIING,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "ClassicSkiing",
            byFoot = true,
            placeholderImageId = R.drawable.activity_placeholder_default
        )

        @JvmField
        val PILATES: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.PILATES,
            simpleName = "Pilates",
            placeholderImageId = R.drawable.activity_placeholder_default
        )

        @JvmField
        val CHORES: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.CHORES,
            simpleName = "Chores",
            placeholderImageId = R.drawable.activity_placeholder_default
        )

        @JvmField
        val NEW_YOGA: ActivityType = createIndoor(
            coreActivityType = CoreActivityType.NEW_YOGA,
            simpleName = "Yoga",
            placeholderImageId = R.drawable.activity_placeholder_default
        )

        @JvmField
        val DEFAULT = RUNNING

        private val POPULAR: Array<ActivityType> = arrayOf(
            CYCLING,
            GYM,
            HIKING,
            MOUNTAIN_BIKING,
            RUNNING,
            TRAIL_RUNNING,
            WALKING
        )

        private val ALL_BUT_OTHERS: Array<ActivityType> = arrayOf(
            AMERICAN_FOOTBALL,
            BADMINTON,
            BALLGAMES,
            BASEBALL,
            BASKETBALL,
            BOWLING,
            CLIMBING,
            COMBAT_SPORTS,
            CRICKET,
            CROSS_COUNTRY_SKIING,
            CROSSFIT,
            CROSSTRAINER,
            CYCLING,
            DANCING,
            DOWNHILL_SKIING,
            FITNESS_CLASS,
            FLOORBALL,
            FRISBEE_GOLF,
            FUTSAL,
            GOLF,
            GYM,
            HANDBALL,
            HIKING,
            HORSEBACK_RIDING,
            ICE_HOCKEY,
            ICE_SKATING,
            INDOOR,
            INDOOR_CYCLING,
            INDOOR_ROWING,
            KETTLEBELL,
            MOTOR_SPORTS,
            MOUNTAIN_BIKING,
            NORDIC_WALKING,
            ORIENTEERING,
            OUTDOOR_GYM,
            PADDLING,
            PARKOUR,
            RACQUETBALL,
            ROLLER_SKATING,
            ROLLER_SKIING,
            ROWING,
            RUGBY,
            RUNNING,
            SKATEBOARDING,
            SKI_TOURING,
            SNOWBOARDING,
            SNOWSHOEING,
            SOCCER,
            TENNIS,
            SOFTBALL,
            SQUASH,
            STRETCHING,
            SUP,
            SWIMMING,
            TABLE_TENNIS,
            TRACK_AND_FIELD,
            TRAIL_RUNNING,
            TREADMILL,
            VOLLEYBALL,
            WALKING,
            WATER_SPORTS,
            YOGA,
            MULTISPORT,
            AEROBICS,
            TREKKING,
            SAILING,
            KAYAKING,
            CIRCUIT_TRAINING,
            TRIATHLON,
            CHEERLEADING,
            BOXING,
            SCUBADIVING,
            FREEDIVING,
            ADVENTURE_RACING,
            GYMNASTICS,
            CANOEING,
            MOUNTAINEERING,
            TELEMARKSKIING,
            OPENWATER_SWIMMING,
            WINDSURFING,
            KITESURFING_KITING,
            PARAGLIDING,
            SNORKELING,
            SURFING,
            SWIMRUN,
            DUATHLON,
            AQUATHLON,
            OBSTACLE_RACING,
            FISHING,
            HUNTING,
            PADEL,
            GRAVEL_CYCLING,
            MERMAIDING,
            JUMP_ROPE,
            TRACK_RUNNING,
            CALISTHENICS,
            E_BIKING,
            E_MTB,
            BACKCOUNTRY_SKIING,
            WHEELCHAIRING,
            HANDCYCLING,
            SPLITBOARDING,
            BIATHLON,
            MEDITATION,
            FIELD_HOCKEY,
            CYCLOCROSS,
            VERTICAL_RUN,
            SKI_MOUNTAINEERING,
            SKATE_SKIING,
            CLASSIC_SKIING,
            PILATES,
            CHORES,
            NEW_YOGA
        )

        private val OTHERS: Array<ActivityType> = arrayOf(
            OTHER_1,
            OTHER_2,
            OTHER_3,
            OTHER_4,
            OTHER_5,
            OTHER_6,
        )

        private val ALL: Array<ActivityType> = ALL_BUT_OTHERS + OTHERS

        private val EXCLUDE_FOR_ROUTE_PLANNER: Set<ActivityType> = setOf(
            TRACK_RUNNING,
            CALISTHENICS,
            MEDITATION,
            FIELD_HOCKEY,
            PILATES,
            YOGA,
            CHORES,
            JUMP_ROPE,
            PARKOUR,
            SKATEBOARDING,
            FUTSAL,
            NEW_YOGA
        )

        private val activityTypeMap: Map<Int, ActivityType> = ALL.associateBy { it.id }

        @JvmStatic
        fun values(): Array<ActivityType> = ALL

        @JvmStatic
        fun getTotalNumberOfActivityTypes(): Int = ALL.size

        @JvmStatic
        @Suppress("LongParameterList")
        private fun internalCreate(
            coreActivityType: CoreActivityType,
            placeholderImageId: Int,
            zeroSpeedThreshold: Double,
            simpleName: String,
            byFoot: Boolean,
            slopeSki: Boolean,
            isDiving: Boolean,
            isIndoor: Boolean,
            isOther: Boolean,
            isUnknown: Boolean,
            isSwimming: Boolean,
            usesNauticalUnits: Boolean,
            supportsDiveProfile: Boolean,
            supportsDiveEvents: Boolean
        ): ActivityType = Builder().id(coreActivityType.id)
            .simpleName(simpleName)
            .localizedStringId(coreActivityType.nameRes)
            .placeholderImageId(placeholderImageId)
            .iconId(coreActivityType.icon)
            .colorId(coreActivityType.color)
            .zeroSpeedThreshold(zeroSpeedThreshold)
            .isByFoot(byFoot)
            .isSlopeSki(slopeSki)
            .isDiving(isDiving)
            .isIndoor(isIndoor)
            .isOther(isOther)
            .isUnknown(isUnknown)
            .isSwimming(isSwimming)
            .usesNauticalUnits(usesNauticalUnits)
            .supportsDiveProfile(supportsDiveProfile)
            .supportsDiveEvents(supportsDiveEvents)
            .build()

        @JvmStatic
        private fun create(
            coreActivityType: CoreActivityType,
            zeroSpeedThreshold: Double,
            simpleName: String,
            byFoot: Boolean,
            placeholderImageId: Int,
            usesNauticalUnits: Boolean = false
        ): ActivityType = internalCreate(
            coreActivityType = coreActivityType,
            placeholderImageId = placeholderImageId,
            zeroSpeedThreshold = zeroSpeedThreshold,
            simpleName = simpleName,
            byFoot = byFoot,
            slopeSki = false,
            isDiving = false,
            isIndoor = false,
            isOther = false,
            isUnknown = false,
            isSwimming = false,
            usesNauticalUnits = usesNauticalUnits,
            supportsDiveProfile = false,
            supportsDiveEvents = false,
        )

        @JvmStatic
        private fun createSwimming(
            coreActivityType: CoreActivityType,
            simpleName: String,
            placeholderImageId: Int,
            isIndoor: Boolean
        ): ActivityType = internalCreate(
            coreActivityType = coreActivityType,
            placeholderImageId = placeholderImageId,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = simpleName,
            byFoot = false,
            slopeSki = false,
            isDiving = false,
            isIndoor = isIndoor,
            isOther = false,
            isUnknown = false,
            isSwimming = true,
            usesNauticalUnits = false,
            supportsDiveProfile = false,
            supportsDiveEvents = false
        )

        @JvmStatic
        private fun createSailing(
            placeholderImageId: Int
        ): ActivityType = internalCreate(
            coreActivityType = CoreActivityType.SAILING,
            placeholderImageId = placeholderImageId,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = "Sailing",
            byFoot = false,
            slopeSki = false,
            isDiving = false,
            isIndoor = false,
            isOther = false,
            isUnknown = false,
            isSwimming = false,
            usesNauticalUnits = true,
            supportsDiveProfile = false,
            supportsDiveEvents = false,
        )

        @JvmStatic
        private fun createIndoor(
            coreActivityType: CoreActivityType,
            simpleName: String,
            placeholderImageId: Int,
            byFoot: Boolean = true
        ): ActivityType = internalCreate(
            coreActivityType = coreActivityType,
            placeholderImageId = placeholderImageId,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = simpleName,
            byFoot = byFoot,
            slopeSki = false,
            isDiving = false,
            isIndoor = true,
            isOther = false,
            isUnknown = false,
            isSwimming = false,
            usesNauticalUnits = false,
            supportsDiveProfile = false,
            supportsDiveEvents = false,
        )

        @JvmStatic
        private fun createSlopeSki(
            coreActivityType: CoreActivityType,
            simpleName: String,
            placeholderImageId: Int
        ): ActivityType = internalCreate(
            coreActivityType = coreActivityType,
            placeholderImageId = placeholderImageId,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = simpleName,
            byFoot = false,
            slopeSki = true,
            isDiving = false,
            isIndoor = false,
            isOther = false,
            isUnknown = false,
            isSwimming = false,
            usesNauticalUnits = false,
            supportsDiveProfile = false,
            supportsDiveEvents = false,
        )

        @JvmStatic
        private fun createDive(
            coreActivityType: CoreActivityType,
            simpleName: String,
            placeholderImageId: Int
        ): ActivityType = internalCreate(
            coreActivityType = coreActivityType,
            placeholderImageId = placeholderImageId,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = simpleName,
            byFoot = false,
            slopeSki = false,
            isDiving = true,
            isIndoor = false,
            isOther = false,
            isUnknown = false,
            isSwimming = false,
            usesNauticalUnits = false,
            supportsDiveProfile = true,
            supportsDiveEvents = true,
        )

        @JvmStatic
        private fun createSwimDive(
            coreActivityType: CoreActivityType,
            simpleName: String,
            placeholderImageId: Int,
            isIndoor: Boolean,
        ): ActivityType = internalCreate(
            coreActivityType = coreActivityType,
            placeholderImageId = placeholderImageId,
            zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
            simpleName = simpleName,
            byFoot = false,
            slopeSki = false,
            isDiving = false, // TODO debate about this when Seal work comes in
            isIndoor = isIndoor,
            isOther = false,
            isUnknown = false,
            isSwimming = true,
            usesNauticalUnits = false,
            supportsDiveProfile = true,
            supportsDiveEvents = false,
        )

        @JvmStatic
        private fun createOther(
            coreActivityType: CoreActivityType,
            simpleName: String
        ): ActivityType =
            internalCreate(
                coreActivityType = coreActivityType,
                placeholderImageId = R.drawable.activity_placeholder_default,
                zeroSpeedThreshold = OTHER_SPEED_THRESHOLD,
                simpleName = simpleName,
                byFoot = true,
                slopeSki = false,
                isDiving = false,
                isIndoor = false,
                isOther = true,
                isUnknown = false,
                isSwimming = false,
                usesNauticalUnits = false,
                supportsDiveProfile = false,
                supportsDiveEvents = false,
            )

        /**
         * Get all activities sorted by local names activity type. Note that
         * [ActivityType.ALL_BUT_OTHERS] are sorted and [ActivityType.OTHERS]
         * concatenated to the end
         */
        @JvmStatic
        fun getAllActivitiesSortedByLocalNames(resources: Resources): Array<ActivityType> =
            ALL.sortedBy { it.getLocalizedName(resources).lowercase(Locale.getDefault()) }
                .toTypedArray()

        @JvmStatic
        fun getRoutePlannerActivitiesSortedByLocalName(resources: Resources): Array<ActivityType> =
            ALL.asSequence()
                .filterNot { it in EXCLUDE_FOR_ROUTE_PLANNER }
                .sortedBy { it.getLocalizedName(resources).lowercase(Locale.getDefault()) }
                .toList()
                .toTypedArray()

        /**
         * Get popular activities sorted by local names activity type.
         */
        @JvmStatic
        fun getPopularActivitiesSortedByLocalNames(resources: Resources): Array<ActivityType> =
            POPULAR.sortedBy { it.getLocalizedName(resources).lowercase(Locale.getDefault()) }
                .toTypedArray()

        /**
         * @param activities list of activities whose localized names and icons will be returned
         * @return A pair where [Pair.first] is activities list of localized activity names, the
         * [Pair.second] is activities list of activity icon resource id sorted according
         * to
         * local
         * names.
         */
        @JvmStatic
        fun getListOfActivityNamesAndIcons(
            resources: Resources,
            activities: Array<ActivityType>
        ): Pair<Array<String>, IntArray> = Pair(
            activities.map { it.getLocalizedName(resources) }.toTypedArray(),
            activities.map { it.iconId }.toIntArray()
        )

        @JvmStatic
        fun getLocalizedNameByActivityId(resources: Resources, activityId: Int): String =
            ALL.firstOrNull { it.id == activityId }?.getLocalizedName(resources)
                ?: throw IllegalArgumentException("Unknown activity type for ID $activityId")

        /**
         * @return the Activity type based on the id
         */
        @JvmStatic
        fun valueOf(id: Int): ActivityType {
            val activityType = activityTypeMap[id]
            return activityType ?: CREATE_UNKNOWN
        }
    }
}
