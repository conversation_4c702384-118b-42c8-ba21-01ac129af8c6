package com.stt.android.domain.graphanalysis

import com.stt.android.core.domain.GraphType
import com.stt.android.infomodel.ActivityMapping
import com.stt.android.infomodel.getActivitySummaryForActivity
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class ObserveActivityTypeGraphAnalysisInfoUseCase @Inject constructor(
    private val selectionsDataSource: ActivityTypeGraphAnalysisSelectionsDataSource
) {
    fun observeGraphAnalysisInfoForActivityType(activityTypeId: Int): Flow<ActivityTypeGraphAnalysisInfo> {
        return selectionsDataSource.observeSelectedGraphsForActivityType(activityTypeId)
            .map { userSelectedGraphTypes ->
                val supportedGraphTypes = getSupportedGraphTypesForActivityType(activityTypeId)
                val selectedGraphTypes = userSelectedGraphTypes ?: supportedGraphTypes
                ActivityTypeGraphAnalysisInfo(
                    activityTypeId,
                    supportedGraphTypes = supportedGraphTypes,
                    mainGraphType = selectedGraphTypes.getOrNull(0) ?: GraphType.NONE,
                    comparisonGraphType = selectedGraphTypes.getOrNull(1) ?: GraphType.NONE,
                    backgroundGraphType = selectedGraphTypes.getOrNull(2) ?: GraphType.NONE,
                )
            }
    }

    private fun getSupportedGraphTypesForActivityType(activityTypeId: Int): List<GraphType.Summary> {
        val activityMapping: ActivityMapping? = ActivityMapping.entries
            .firstOrNull { it.stId == activityTypeId }
        return getActivitySummaryForActivity(activityMapping).analysisGraphs
            .map { GraphType.Summary(it) }
    }
}
