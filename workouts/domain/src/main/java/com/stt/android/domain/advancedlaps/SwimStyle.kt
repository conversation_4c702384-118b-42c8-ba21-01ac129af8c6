package com.stt.android.domain.advancedlaps

enum class SwimStyle(val value: String) {
    OTHER("Other"),
    FRE<PERSON>("Free"),
    FREE_LEGACY("Freestyle"), // legacy watches
    BUTTERFLY("Butterfly"),
    BREAST("Breast"),
    BREAST_LEGACY("Breaststroke"), // legacy watches
    BACK("Back"),
    BACK_LEGACY("Backstroke"), // legacy watches
    DRILL("Drill");

    companion object {
        private val map = values().associateBy(SwimStyle::value)
        fun fromValue(value: String) = map.getOrElse(value) { OTHER }
    }
}
