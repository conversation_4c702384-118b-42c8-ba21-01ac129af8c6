package com.stt.android.domain.workouts

import com.stt.android.domain.ranking.Ranking
import com.stt.android.domain.workouts.comment.DomainWorkoutComment
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.domain.workouts.pictures.Picture
import com.stt.android.domain.workouts.reactions.DomainReactionSummary
import com.stt.android.domain.workouts.videos.Video
import kotlin.reflect.KClass

data class DomainWorkout(
    val header: WorkoutHeader,
    val comments: List<DomainWorkoutComment>? = null,
    val pictures: List<Picture>? = null,
    val reactions: List<DomainReactionSummary>? = null,
    val videos: List<Video>? = null,
    val extensions: Map<KClass<out WorkoutExtension>, WorkoutExtension>? = null,
    val rankings: Ranking? = null
)
