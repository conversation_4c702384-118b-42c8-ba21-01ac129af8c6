package com.stt.android.domain.workouts.tag

import android.content.res.Resources
import com.soy.algorithms.impact.WorkoutImpact
import com.stt.android.workouts.domain.R

enum class SuuntoTag(
    val nameRes: Int,
    val analyticsName: String,
    val workoutImpact: WorkoutImpact?,
    val editable: <PERSON><PERSON><PERSON>
) {
    COMMUTE(
        nameRes = R.string.tag_commute,
        analyticsName = "Commute",
        workoutImpact = null,
        editable = true
    ),
    MARATHON(
        nameRes = R.string.tag_marathon,
        analyticsName = "Marathon",
        workoutImpact = null,
        editable = true
    ),
    HALF_MARATHON(
        nameRes = R.string.tag_half_marathon,
        analyticsName = "Half Marathon",
        workoutImpact = null,
        editable = true
    ),
    IMPACT_SPEED_AND_AGILITY(
        nameRes = R.string.tag_impact_muscular_speed_and_agility,
        analyticsName = "Speed and Agility",
        workoutImpact = WorkoutImpact.SPEED_AND_AGILITY,
        editable = false
    ),
    IMPACT_SPEED_AND_STRENGTH(
        nameRes = R.string.tag_impact_muscular_speed_and_strength,
        analyticsName = "Speed and Strength",
        workoutImpact = WorkoutImpact.SPEED_AND_STRENGTH,
        editable = false
    ),
    IMPACT_FLEXIBILITY(
        nameRes = R.string.tag_impact_muscular_flexibility,
        analyticsName = "Flexibility",
        workoutImpact = WorkoutImpact.FLEXIBILITY,
        editable = false
    ),
    IMPACT_STRENGTH(
        nameRes = R.string.tag_impact_muscular_strength,
        analyticsName = "Strength",
        workoutImpact = WorkoutImpact.STRENGTH,
        editable = false
    ),
    IMPACT_ABOVE_THRESHOLD_VO2MAX(
        nameRes = R.string.tag_impact_cardio_vo2_max,
        analyticsName = "VO₂max",
        workoutImpact = WorkoutImpact.ABOVE_THRESHOLD_VO2MAX,
        editable = false
    ),
    IMPACT_HARD_ANAEROBIC_EFFORT(
        nameRes = R.string.tag_impact_cardio_anaerobic_hard,
        analyticsName = "Anaerobic - hard",
        workoutImpact = WorkoutImpact.HARD_ANAEROBIC_EFFORT,
        editable = false
    ),
    IMPACT_ANAEROBIC_THRESHOLD(
        nameRes = R.string.tag_impact_cardio_anaerobic,
        analyticsName = "Anaerobic",
        workoutImpact = WorkoutImpact.ANAEROBIC_THRESHOLD,
        editable = false
    ),
    IMPACT_AEROBIC_TO_ANAEROBIC(
        nameRes = R.string.tag_impact_cardio_aerobic_anaerobic,
        analyticsName = "Aerobic/Anaerobic",
        workoutImpact = WorkoutImpact.AEROBIC_TO_ANAEROBIC,
        editable = false
    ),
    IMPACT_HARD_LONG_AEROBIC_BASE(
        nameRes = R.string.tag_impact_cardio_heavy_aerobic,
        analyticsName = "Heavy Aerobic",
        workoutImpact = WorkoutImpact.HARD_LONG_AEROBIC_BASE,
        editable = false
    ),
    IMPACT_LONG_AEROBIC_BASE(
        nameRes = R.string.tag_impact_cardio_long_aerobic,
        analyticsName = "Long Aerobic",
        workoutImpact = WorkoutImpact.LONG_AEROBIC_BASE,
        editable = false
    ),
    IMPACT_AEROBIC(
        nameRes = R.string.tag_impact_aerobic,
        analyticsName = "Aerobic",
        workoutImpact = WorkoutImpact.AEROBIC,
        editable = false
    ),
    IMPACT_EASY_RECOVERY(
        nameRes = R.string.tag_impact_recovery,
        analyticsName = "Recovery",
        workoutImpact = WorkoutImpact.EASY_RECOVERY,
        editable = false
    );

    fun getSearchString(resources: Resources): String {
        return resources.getString(nameRes).lowercase()
    }

    companion object {
        fun fromWorkoutImpact(workoutImpact: WorkoutImpact): SuuntoTag? {
            return entries.find { it.workoutImpact == workoutImpact }
        }
    }
}
