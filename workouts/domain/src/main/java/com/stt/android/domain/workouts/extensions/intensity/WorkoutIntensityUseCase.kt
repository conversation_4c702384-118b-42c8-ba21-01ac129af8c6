package com.stt.android.domain.workouts.extensions.intensity

import com.soy.algorithms.intensity.WorkoutIntensity
import com.soy.algorithms.intensity.WorkoutIntensityCalculator
import com.stt.android.domain.workouts.BasicWorkoutHeader
import javax.inject.Inject

class WorkoutIntensityUseCase @Inject constructor(
    private val intensityExtensionUseCase: GetIntensityExtensionUseCase,
) {
    suspend fun calculateWorkoutIntensity(workoutHeader: BasicWorkoutHeader): WorkoutIntensity? =
        intensityExtensionUseCase
            .loadLocalExtension(workoutId = workoutHeader.id)
            ?.intensityZones
            .let { intensityZones ->
                WorkoutIntensityCalculator.calculateIntensity(
                    intensityZones = intensityZones,
                    dynamicDFAZones = null, // TODO https://suunto.tpondemand.com/entity/183478-move-zonesense-calculations-to-workout-sync
                )
            }
}
