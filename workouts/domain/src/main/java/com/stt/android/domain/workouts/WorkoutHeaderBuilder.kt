package com.stt.android.domain.workouts

import com.stt.android.domain.Point
import com.stt.android.domain.tags.UserTag
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.domain.workouts.tss.TSS
import com.stt.android.domain.workouts.zonesense.ZoneSense
import kotlin.math.roundToInt

/**
 * TODO eliminate once all code using WorkoutHeader is in kotlin and can use named arguments
 */
data class WorkoutHeaderBuilder(
    var id: Int = 0,
    var key: String? = "",
    var totalDistance: Double = 0.0,
    var maxSpeed: Double = 0.0,
    var activityTypeId: Int = 0,
    var avgSpeed: Double = 0.0,
    var description: String? = "",
    var startPosition: Point? = null,
    var stopPosition: Point? = null,
    var centerPosition: Point? = null,
    var startTime: Long = 0,
    var stopTime: Long = 0,
    var totalTime: Double = 0.0,
    var energyConsumption: Double = 0.0,
    var username: String = "",
    var heartRateAverage: Double = 0.0,
    var heartRateAvgPercentage: Double = 0.0,
    var heartRateMax: Double = 0.0,
    var heartRateMaxPercentage: Double = 0.0,
    var heartRateUserSetMax: Double = 0.0,
    var averageCadence: Int = 0,
    var maxCadence: Int = 0,
    var pictureCount: Int = 0,
    var viewCount: Int = 0,
    var commentCount: Int = 0,
    var sharingFlags: Int = 0,
    var stepCount: Int = 0,
    var polyline: String? = "",
    var manuallyAdded: Boolean = false,
    var reactionCount: Int = 0,
    var totalAscent: Double = 0.0,
    var totalDescent: Double = 0.0,
    var recoveryTime: Long = 0,
    var locallyChanged: Boolean = false,
    var deleted: Boolean = false,
    var seen: Boolean = false,
    var maxAltitude: Double? = 0.0,
    var minAltitude: Double? = 0.0,
    var extensionsFetched: Boolean = false,
    var tss: TSS? = null,
    var tssList: List<TSS> = listOf(),
    var suuntoTags: List<SuuntoTag> = listOf(),
    var userTags: List<UserTag> = listOf(),
    var zoneSense: ZoneSense? = null,
    var estimatedFloorsClimbed: Int? = null
) {

    constructor(workoutHeader: WorkoutHeader) : this(
        workoutHeader.id,
        workoutHeader.key,
        workoutHeader.totalDistance,
        workoutHeader.maxSpeed,
        workoutHeader.activityTypeId,
        workoutHeader.avgSpeed,
        workoutHeader.description,
        workoutHeader.startPosition,
        workoutHeader.stopPosition,
        workoutHeader.centerPosition,
        workoutHeader.startTime,
        workoutHeader.stopTime,
        workoutHeader.totalTime,
        workoutHeader.energyConsumption,
        workoutHeader.username,
        workoutHeader.heartRateAverage,
        workoutHeader.heartRateAvgPercentage,
        workoutHeader.heartRateMax,
        workoutHeader.heartRateMaxPercentage,
        workoutHeader.heartRateUserSetMax,
        workoutHeader.averageCadence,
        workoutHeader.maxCadence,
        workoutHeader.pictureCount,
        workoutHeader.viewCount,
        workoutHeader.commentCount,
        workoutHeader.sharingFlags,
        workoutHeader.stepCount,
        workoutHeader.polyline,
        workoutHeader.manuallyAdded,
        workoutHeader.reactionCount,
        workoutHeader.totalAscent,
        workoutHeader.totalDescent,
        workoutHeader.recoveryTime,
        workoutHeader.locallyChanged,
        workoutHeader.deleted,
        workoutHeader.seen,
        workoutHeader.maxAltitude,
        workoutHeader.minAltitude,
        workoutHeader.extensionsFetched,
        workoutHeader.tss,
        workoutHeader.tssList,
        workoutHeader.suuntoTags,
        workoutHeader.userTags,
        workoutHeader.zoneSense,
        workoutHeader.estimatedFloorsClimbed
    )

    fun build(): WorkoutHeader {
        check(username.isNotBlank()) { "Missing workout owner user name" }
        return WorkoutHeader(
            id = id,
            key = key,
            totalDistance = totalDistance,
            maxSpeed = maxSpeed,
            activityTypeId = activityTypeId,
            avgSpeed = avgSpeed,
            description = description,
            startPosition = startPosition,
            stopPosition = stopPosition,
            centerPosition = centerPosition,
            startTime = startTime,
            stopTime = stopTime,
            totalTime = totalTime,
            energyConsumption = energyConsumption,
            username = username,
            heartRateAverage = heartRateAverage,
            heartRateAvgPercentage = heartRateAvgPercentage,
            heartRateMax = heartRateMax,
            heartRateMaxPercentage = heartRateMaxPercentage,
            heartRateUserSetMax = heartRateUserSetMax,
            averageCadence = averageCadence,
            maxCadence = maxCadence,
            pictureCount = pictureCount,
            viewCount = viewCount,
            commentCount = commentCount,
            sharingFlags = sharingFlags,
            stepCount = stepCount,
            polyline = polyline,
            manuallyAdded = manuallyAdded,
            reactionCount = reactionCount,
            totalAscent = totalAscent,
            totalDescent = totalDescent,
            recoveryTime = recoveryTime,
            locallyChanged = locallyChanged,
            deleted = deleted,
            seen = seen,
            maxAltitude = maxAltitude,
            minAltitude = minAltitude,
            extensionsFetched = extensionsFetched,
            tss = tss,
            tssList = tssList,
            suuntoTags = suuntoTags,
            userTags = userTags,
            zoneSense = zoneSense,
            estimatedFloorsClimbed = estimatedFloorsClimbed
        )
    }

    fun id(id: Int): WorkoutHeaderBuilder {
        this.id = id
        return this
    }

    fun key(key: String?): WorkoutHeaderBuilder {
        this.key = key
        return this
    }

    fun userName(username: String): WorkoutHeaderBuilder {
        this.username = username
        return this
    }

    fun activityId(activityId: Int): WorkoutHeaderBuilder {
        this.activityTypeId = activityId
        return this
    }

    fun description(description: String?): WorkoutHeaderBuilder {
        this.description = description
        return this
    }

    fun startTime(
        startTime: Long,
        updateStopTime: Boolean
    ): WorkoutHeaderBuilder {
        if (updateStopTime) {
            // We need to keep the stop time in sync
            val timeDelta = startTime - this.startTime
            stopTime += timeDelta
        }
        this.startTime = startTime
        return this
    }

    fun stopTime(stopTime: Long): WorkoutHeaderBuilder {
        this.stopTime = stopTime
        return this
    }

    fun totalTime(
        totalTime: Double,
        updateStopTime: Boolean
    ): WorkoutHeaderBuilder {
        if (updateStopTime) {
            // total time is in seconds, but start / stop time is in milli-seconds
            stopTime = startTime + (1000.0 * totalTime).roundToInt()
        }
        this.totalTime = totalTime
        avgSpeed = if (totalTime > 0) {
            totalDistance / totalTime
        } else {
            0.0
        }
        return this
    }

    fun totalDistance(totalDistance: Double): WorkoutHeaderBuilder {
        this.totalDistance = totalDistance
        avgSpeed = if (totalTime > 0) {
            totalDistance / totalTime
        } else {
            0.0
        }
        return this
    }

    fun maxSpeed(maxSpeed: Double): WorkoutHeaderBuilder {
        this.maxSpeed = maxSpeed
        return this
    }

    fun avgSpeed(avgSpeed: Double): WorkoutHeaderBuilder {
        this.avgSpeed = avgSpeed
        return this
    }

    fun energyConsumption(energyConsumption: Double): WorkoutHeaderBuilder {
        this.energyConsumption = energyConsumption
        return this
    }

    fun startPosition(startPosition: Point?): WorkoutHeaderBuilder {
        this.startPosition = startPosition
        return this
    }

    fun centerPosition(centerPosition: Point?): WorkoutHeaderBuilder {
        this.centerPosition = centerPosition
        return this
    }

    fun stopPosition(stopPosition: Point?): WorkoutHeaderBuilder {
        this.stopPosition = stopPosition
        return this
    }

    fun polyline(polyline: String?): WorkoutHeaderBuilder {
        this.polyline = polyline
        return this
    }

    fun heartRateAvg(heartRateAvg: Double): WorkoutHeaderBuilder {
        this.heartRateAverage = heartRateAvg
        return this
    }

    fun heartRateAvgPercentage(heartRateAvgPercentage: Double): WorkoutHeaderBuilder {
        this.heartRateAvgPercentage = heartRateAvgPercentage
        return this
    }

    fun heartRateMax(heartRateMax: Double): WorkoutHeaderBuilder {
        this.heartRateMax = heartRateMax
        return this
    }

    fun heartRateMaxPercentage(heartRateMaxPercentage: Double): WorkoutHeaderBuilder {
        this.heartRateMaxPercentage = heartRateMaxPercentage
        return this
    }

    fun heartRateUserSetMax(heartRateUserSetMax: Double): WorkoutHeaderBuilder {
        this.heartRateUserSetMax = heartRateUserSetMax
        return this
    }

    fun averageCadence(averageCadence: Int): WorkoutHeaderBuilder {
        this.averageCadence = averageCadence
        return this
    }

    fun maxCadence(maxCadence: Int): WorkoutHeaderBuilder {
        this.maxCadence = maxCadence
        return this
    }

    fun stepCount(stepCount: Int): WorkoutHeaderBuilder {
        this.stepCount = stepCount
        return this
    }

    fun pictureCount(pictureCount: Int): WorkoutHeaderBuilder {
        this.pictureCount = pictureCount
        return this
    }

    fun commentCount(commentCount: Int): WorkoutHeaderBuilder {
        this.commentCount = commentCount
        return this
    }

    fun reactionCount(reactionCount: Int): WorkoutHeaderBuilder {
        this.reactionCount = reactionCount
        return this
    }

    fun viewCount(viewCount: Int): WorkoutHeaderBuilder {
        this.viewCount = viewCount
        return this
    }

    fun sharingFlags(sharingFlags: Int): WorkoutHeaderBuilder {
        this.sharingFlags = sharingFlags
        return this
    }

    fun manuallyCreated(manuallyCreated: Boolean): WorkoutHeaderBuilder {
        this.manuallyAdded = manuallyCreated
        return this
    }

    fun deleted(deleted: Boolean): WorkoutHeaderBuilder {
        this.deleted = deleted
        return this
    }

    fun deleted(): WorkoutHeaderBuilder {
        deleted = true
        return this
    }

    fun synced(): WorkoutHeaderBuilder {
        locallyChanged = false
        return this
    }

    fun locallyChanged(locallyChanged: Boolean): WorkoutHeaderBuilder {
        this.locallyChanged = locallyChanged
        return this
    }

    fun totalAscent(totalAscent: Double): WorkoutHeaderBuilder {
        this.totalAscent = totalAscent
        return this
    }

    fun totalDescent(totalDescent: Double): WorkoutHeaderBuilder {
        this.totalDescent = totalDescent
        return this
    }

    fun seen(seen: Boolean): WorkoutHeaderBuilder {
        this.seen = seen
        return this
    }

    fun recoveryTime(recoveryTime: Long): WorkoutHeaderBuilder {
        this.recoveryTime = recoveryTime
        return this
    }

    fun maxAltitude(maxAltitude: Double): WorkoutHeaderBuilder {
        this.maxAltitude = maxAltitude
        return this
    }

    fun minAltitude(minAltitude: Double): WorkoutHeaderBuilder {
        this.minAltitude = minAltitude
        return this
    }

    fun extensionsFetched(extensionsFetched: Boolean): WorkoutHeaderBuilder {
        this.extensionsFetched = extensionsFetched
        return this
    }

    fun tss(tss: TSS?): WorkoutHeaderBuilder {
        this.tss = tss
        return this
    }

    fun tssList(tssList: List<TSS>): WorkoutHeaderBuilder {
        this.tssList = tssList
        return this
    }

    fun suuntoTags(suuntoTags: List<SuuntoTag>): WorkoutHeaderBuilder {
        this.suuntoTags = suuntoTags
        return this
    }

    fun userTags(userTags: List<UserTag>): WorkoutHeaderBuilder {
        this.userTags = userTags
        return this
    }

    fun zoneSense(zoneSense: ZoneSense?): WorkoutHeaderBuilder {
        this.zoneSense = zoneSense
        return this
    }

    fun estimatedFloorsClimbed(estimatedFloorsClimbed: Int): WorkoutHeaderBuilder {
        this.estimatedFloorsClimbed = estimatedFloorsClimbed
        return this
    }
}
