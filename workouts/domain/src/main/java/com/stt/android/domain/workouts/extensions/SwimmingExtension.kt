package com.stt.android.domain.workouts.extensions

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class SwimmingExtension(
    override val workoutId: Int,
    val avgSwolf: Int,
    val avgStrokeRate: Float,
    val breathingRate: Int? = null,
    val breaststrokeDuration: Int? = null,
    val breaststrokePercentage: Int? = null,
    val breaststrokeGlideTime: Int? = null,
    val breaststrokeMaxBreathAngle: Int? = null,
    val breaststrokeAvgBreathAngle: Int? = null,
    val freestyleDuration: Int? = null,
    val freestylePercentage: Int? = null,
    val freestyleMaxBreathAngle: Int? = null,
    val freestyleAvgBreathAngle: Int? = null,
    val freestylePitchAngle: Int? = null,
    val breaststrokeHeadAngle: Int? = null,
) : WorkoutExtension(TYPE_SWIMMING, workoutId), Parcelable {
    override fun copyWithNewWorkoutId(newWorkoutId: Int): WorkoutExtension {
        return copy(workoutId = newWorkoutId)
    }
}
