package com.stt.android.domain.sml

import com.stt.android.domain.workouts.extensions.SMLExtension
import javax.inject.Inject

class SmlExtensionUseCase @Inject constructor(
    private val smlDataSource: SmlDataSource,
) {
    suspend fun fetchSmlExtension(workoutId: Int, workoutKey: String?): SMLExtension? =
        smlDataSource.fetchSmlExtension(workoutId, workoutKey)

    suspend fun saveSmlExtension(smlExtension: SMLExtension) {
        smlDataSource.saveSmlExtension(smlExtension)
    }
}
