package com.stt.android.domain.workouts.extensions.intensity

import com.stt.android.core.domain.workouts.CoreActivityType

internal val CoreActivityType.hasUnclassifiedCardioImpact: Boolean
    get() = when (this) {
        CoreActivityType.BOWLING,
        CoreActivityType.CIRCUIT_TRAINING,
        CoreActivityType.COMBAT_SPORTS,
        CoreActivityType.FISHING,
        CoreActivityType.FREEDIVING,
        CoreActivityType.GYM,
        CoreActivityType.GYMNASTICS,
        CoreActivityType.HUNTING,
        CoreActivityType.KETTLEBELL,
        CoreActivityType.MERMAIDING,
        CoreActivityType.MOTOR_SPORTS,
        CoreActivityType.OTHER_1,
        CoreActivityType.OTHER_2,
        CoreActivityType.OTHER_3,
        CoreActivityType.OTHER_4,
        CoreActivityType.OTHER_5,
        CoreActivityType.OTHER_6,
        CoreActivityType.OUTDOOR_GYM,
        CoreActivityType.PARAGLIDING,
        CoreActivityType.SAILING,
        CoreActivityType.SCUBADIVING,
        CoreActivityType.SNORKELING,
        CoreActivityType.STRETCHING,
        CoreActivityType.YOGA,
        CoreActivityType.JUMP_ROPE,
        CoreActivityType.CALISTHENICS,
        CoreActivityType.MEDITATION,
        CoreActivityType.PILATES,
        CoreActivityType.CHORES,
        CoreActivityType.NEW_YOGA -> true

        CoreActivityType.WALKING,
        CoreActivityType.RUNNING,
        CoreActivityType.CYCLING,
        CoreActivityType.CROSS_COUNTRY_SKIING,
        CoreActivityType.MOUNTAIN_BIKING,
        CoreActivityType.HIKING,
        CoreActivityType.ROLLER_SKATING,
        CoreActivityType.DOWNHILL_SKIING,
        CoreActivityType.PADDLING,
        CoreActivityType.ROWING,
        CoreActivityType.GOLF,
        CoreActivityType.INDOOR,
        CoreActivityType.PARKOUR,
        CoreActivityType.BALLGAMES,
        CoreActivityType.SWIMMING,
        CoreActivityType.TRAIL_RUNNING,
        CoreActivityType.NORDIC_WALKING,
        CoreActivityType.HORSEBACK_RIDING,
        CoreActivityType.SKATEBOARDING,
        CoreActivityType.WATER_SPORTS,
        CoreActivityType.CLIMBING,
        CoreActivityType.SNOWBOARDING,
        CoreActivityType.SKI_TOURING,
        CoreActivityType.FITNESS_CLASS,
        CoreActivityType.SOCCER,
        CoreActivityType.TENNIS,
        CoreActivityType.BASKETBALL,
        CoreActivityType.BADMINTON,
        CoreActivityType.BASEBALL,
        CoreActivityType.VOLLEYBALL,
        CoreActivityType.AMERICAN_FOOTBALL,
        CoreActivityType.TABLE_TENNIS,
        CoreActivityType.RACQUETBALL,
        CoreActivityType.SQUASH,
        CoreActivityType.FLOORBALL,
        CoreActivityType.HANDBALL,
        CoreActivityType.SOFTBALL,
        CoreActivityType.CRICKET,
        CoreActivityType.RUGBY,
        CoreActivityType.ICE_SKATING,
        CoreActivityType.ICE_HOCKEY,
        CoreActivityType.INDOOR_CYCLING,
        CoreActivityType.TREADMILL,
        CoreActivityType.CROSSFIT,
        CoreActivityType.CROSSTRAINER,
        CoreActivityType.ROLLER_SKIING,
        CoreActivityType.INDOOR_ROWING,
        CoreActivityType.TRACK_AND_FIELD,
        CoreActivityType.ORIENTEERING,
        CoreActivityType.SUP,
        CoreActivityType.DANCING,
        CoreActivityType.SNOWSHOEING,
        CoreActivityType.FRISBEE_GOLF,
        CoreActivityType.FUTSAL,
        CoreActivityType.MULTISPORT,
        CoreActivityType.AEROBICS,
        CoreActivityType.TREKKING,
        CoreActivityType.KAYAKING,
        CoreActivityType.TRIATHLON,
        CoreActivityType.PADEL,
        CoreActivityType.CHEERLEADING,
        CoreActivityType.BOXING,
        CoreActivityType.ADVENTURE_RACING,
        CoreActivityType.CANOEING,
        CoreActivityType.MOUNTAINEERING,
        CoreActivityType.TELEMARKSKIING,
        CoreActivityType.OPENWATER_SWIMMING,
        CoreActivityType.WINDSURFING,
        CoreActivityType.KITESURFING_KITING,
        CoreActivityType.SURFING,
        CoreActivityType.SWIMRUN,
        CoreActivityType.DUATHLON,
        CoreActivityType.AQUATHLON,
        CoreActivityType.OBSTACLE_RACING,
        CoreActivityType.GRAVEL_CYCLING,
        CoreActivityType.TRACK_RUNNING,
        CoreActivityType.E_BIKING,
        CoreActivityType.E_MTB,
        CoreActivityType.BACKCOUNTRY_SKIING,
        CoreActivityType.WHEELCHAIRING,
        CoreActivityType.HANDCYCLING,
        CoreActivityType.SPLITBOARDING,
        CoreActivityType.BIATHLON,
        CoreActivityType.FIELD_HOCKEY,
        CoreActivityType.CYCLOCROSS,
        CoreActivityType.VERTICAL_RUN,
        CoreActivityType.SKI_MOUNTAINEERING,
        CoreActivityType.SKATE_SKIING,
        CoreActivityType.CLASSIC_SKIING -> false
    }
