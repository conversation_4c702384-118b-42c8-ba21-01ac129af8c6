package com.stt.android.domain.graphanalysis

import com.stt.android.core.domain.GraphType
import kotlinx.coroutines.flow.Flow

interface ActivityTypeGraphAnalysisSelectionsDataSource {
    fun observeSelectedGraphsForActivityType(activityTypeId: Int): Flow<List<GraphType>?>

    fun saveSelectedGraphTypesForActivityType(
        activityTypeId: Int,
        mainGraphType: GraphType,
        comparisonGraphType: GraphType,
        backgroundGraphType: GraphType,
    )
}
