package com.stt.android.domain.workouts.extensions

import com.stt.android.domain.MappingException
import com.stt.android.domain.workouts.logbookentry.LogbookEntry
import com.stt.android.logbook.SuuntoLogbookZapp

/**
 * @param pte Peak training effect range 1.0 ... 5.0
 * @param feeling WorkoutFeeling ( 0-5 )
 * @param avgTemperature Temperature, units unknown although spec says K
 * @param peakEpoc EPOC, unit l/kg
 * @param avgPower unit W
 * @param avgCadence unit Hz
 * @param ascentTime maximum ascent time, unit s
 * @param descentTime maximum decent time, unit s
 * @param performanceLevel unit 1 (100% = 1)
 *
 * Ascent, Descent, and RecoveryTime also exist in WorkoutHeader.
 * If those values are null in summary extension, we use the values that exist in WorkoutHeader
 */
data class SummaryExtension constructor(
    override val workoutId: Int,
    val pte: Float?,
    val feeling: Int?,
    val avgTemperature: Float?,
    val peakEpoc: Float?,
    val avgPower: Float?,
    val avgCadence: Float?,
    val avgSpeed: Float?,
    val ascentTime: Float?,
    val descentTime: Float?,
    val performanceLevel: Float?,
    val recoveryTime: Long?,
    @Deprecated("Use value from WorkoutHeader")
    val ascent: Double?,
    @Deprecated("Use value from WorkoutHeader")
    val descent: Double?,
    val deviceHardwareVersion: String?,
    val deviceSoftwareVersion: String?,
    val productType: String?,
    // displayName is a human readable device's name, ex: Suunto 9 Peak
    val displayName: String?,
    // deviceName is the identifier of the device, ex: nagano
    val deviceName: String?,
    val deviceSerialNumber: String?,
    val deviceManufacturer: String?,
    val exerciseId: String?,
    val zapps: List<SuuntoLogbookZapp>,
    val repetitionCount: Int?,
    val maxCadence: Float?,
    val avgStrideLength: Float?,
    val fatConsumption: Int?,
    val carbohydrateConsumption: Int?,
    val avgGroundContactTime: Float?,
    val avgVerticalOscillation: Float?,
    val avgLeftGroundContactBalance: Float?,
    val avgRightGroundContactBalance: Float?,
    val lacticThHr: Float?,
    val lacticThPace: Float?,
    val avgAscentSpeed: Float?,
    val maxAscentSpeed: Float?,
    val avgDescentSpeed: Float?,
    val maxDescentSpeed: Float?,
    val avgDistancePerStroke: Float?,
) : WorkoutExtension(TYPE_WORKOUT_SUMMARY, workoutId) {
    override fun copyWithNewWorkoutId(newWorkoutId: Int): WorkoutExtension {
        return SummaryExtension(
            workoutId = newWorkoutId,
            pte = pte,
            feeling = feeling,
            avgTemperature = avgTemperature,
            peakEpoc = peakEpoc,
            avgPower = avgPower,
            avgCadence = avgCadence,
            avgSpeed = avgSpeed,
            ascentTime = ascentTime,
            descentTime = descentTime,
            performanceLevel = performanceLevel,
            recoveryTime = recoveryTime,
            ascent = ascent,
            descent = descent,
            deviceHardwareVersion = deviceHardwareVersion,
            deviceSoftwareVersion = deviceSoftwareVersion,
            productType = productType,
            displayName = displayName,
            deviceName = deviceName,
            deviceManufacturer = deviceManufacturer,
            deviceSerialNumber = deviceSerialNumber,
            exerciseId = exerciseId,
            zapps = zapps.toList(),
            repetitionCount = repetitionCount,
            maxCadence = maxCadence,
            avgStrideLength = avgStrideLength,
            fatConsumption = fatConsumption,
            carbohydrateConsumption = carbohydrateConsumption,
            avgGroundContactTime = avgGroundContactTime,
            avgVerticalOscillation = avgVerticalOscillation,
            avgLeftGroundContactBalance = avgLeftGroundContactBalance,
            avgRightGroundContactBalance = avgRightGroundContactBalance,
            lacticThHr = lacticThHr,
            lacticThPace = lacticThPace,
            avgAscentSpeed = avgAscentSpeed,
            maxAscentSpeed = maxAscentSpeed,
            avgDescentSpeed = avgDescentSpeed,
            maxDescentSpeed = maxDescentSpeed,
            avgDistancePerStroke = avgDistancePerStroke
        )
    }

    internal constructor() : this(
        0,
        0.0f,
        0,
        0.0f,
        0.0f,
        0.0f,
        0.0f,
        0.0f,
        0.0f,
        0.0f,
        0.0f,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        listOf(),
        0,
        0.0f,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
    )

    /**
     * Maps summary extension to a [LogbookEntry]
     */
    @Throws(MappingException::class)
    fun toLogbookEntry(): LogbookEntry? {
        return LogbookEntry(
            entryId = try {
                exerciseId?.toLong()
            } catch (e: NumberFormatException) {
                null
            } ?: return null,
            workoutId = if (workoutId != 0) {
                workoutId
            } else {
                throw MappingException("Workout ID is null", this)
            },
            deviceName = deviceName,
            serialNumber = deviceSerialNumber,
            hwName = null, // FIXME update when/if hwName goes to backend
            swVersion = deviceSoftwareVersion
        )
    }
}
