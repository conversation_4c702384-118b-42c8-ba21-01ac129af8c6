package com.stt.android.domain.advancedlaps

import com.stt.android.logbook.SuuntoLogbookSummary
import com.suunto.connectivity.deviceid.SuuntoDeviceCapabilityInfoProvider

object AdvancedLapsUtils {
    fun calculateAscentDescentThreshold(header: SuuntoLogbookSummary?): Float {
        val hasBarometer = SuuntoDeviceCapabilityInfoProvider[header?.deviceName]
            .supportsBarometricAltitude()
        val hasAscent = header?.ascent != null
        // we use this threshold to register the minimum amount of altitude change to register a variation
        // in ascent and descent (kind of a low-pass filter)
        return when {
            !hasAscent -> Float.MAX_VALUE
            hasBarometer -> 3f
            else -> 7f
        }
    }
}
