package com.stt.android.domain.workouts

import com.suunto.algorithms.data.Length

// TODO remove this interface once workouts are migrated to Room TP #87987
interface WorkoutHeaderDataSource {

    /**
     * Saves a [WorkoutHeader] in the datasource using the required parameters to build it
     *
     * @return Workout ID
     */
    suspend fun storeWorkout(workoutHeader: WorkoutHeader)

    /**
     * Deletes a workout if it has never been synced to server otherwise marks as deleted
     * @param id workout id
     * @return true if the workout was deleted or false if it is marked for deletion
     */
    suspend fun markDeletedOrPermanentlyDelete(id: Int): Bo<PERSON>an

    /**
     * Set [WorkoutHeader.extensionsFetched] to true on database.
     */
    suspend fun markExtensionsFetched(id: Int): <PERSON><PERSON><PERSON>

    /**
     * Permanently removes a workout from local database
     * @param key workout key
     */
    suspend fun remove(key: String)

    /**
     * Get deleted workouts keys list
     *
     * @return list of deleted workouts keys
     */
    suspend fun getDeletedWorkoutsKeys(): List<String>

    /**
     * Gets modified workouts that have been previously synced to the server so
     * that their key is not null
     */
    suspend fun getLocallyModifiedWorkouts(): List<WorkoutHeader>

    /**
     * Marks the given workouts as synced, e.g.: sets their locallyChanged flag to false
     */
    suspend fun markWorkoutsAsSynced(workoutKeys: Set<String>)

    /**
     * Finds all manually created workouts
     */
    suspend fun findManuallyCreatedWorkouts(): List<WorkoutHeader>

    /**
     * Finds all new workouts that haven't been synced yet
     */
    suspend fun findNewUnsyncedWorkouts(): List<WorkoutHeader>

    fun syncWorkouts()

    suspend fun findByKey(key: String): WorkoutHeader?

    suspend fun findById(id: Int): WorkoutHeader?

    suspend fun findByIds(ids: List<Int>): List<WorkoutHeader>

    suspend fun findPagedOfType(
        ownerUsername: String,
        activityTypeId: Int,
        page: Int
    ): List<WorkoutHeader>

    suspend fun findPagedExcludingTypes(
        ownerUsername: String,
        excludedTypes: Set<Int>,
        page: Int
    ): List<WorkoutHeader>

    suspend fun findByStartTime(
        ownerUsername: String,
        minimumStartTime: Long,
        maximumStartTime: Long
    ): List<WorkoutHeader>

    suspend fun findNotDeletedByRange(
        ownerUsername: String,
        activityTypeId: Int?,
        sinceMs: Long,
        untilMs: Long
    ): List<WorkoutHeader>

    suspend fun findPagedByTimeRange(
        ownerUsername: String,
        sinceMs: Long,
        untilMs: Long,
        includeActivityTypeId: Int?,
        excludeActivityTypeIds: Set<Int>,
        page: Int,
        firstPageSize: Int,
        pageSize: Int
    ): List<WorkoutHeader>

    suspend fun loadActivityTypeCount(id: Int): Long

    suspend fun loadFastestOfActivityType(id: Int, since: Long, till: Long): WorkoutHeader?

    suspend fun loadTotalActivityCount(): Long

    suspend fun loadActivityCountInPeriod(since: Long, till: Long): Long

    suspend fun loadActivityTypeCountInPeriod(id: Int, since: Long, till: Long): Long

    suspend fun loadFarthestOfActivityType(id: Int, since: Long, till: Long): WorkoutHeader?

    suspend fun loadShortestTimeOfActivityTypeWithDistanceRange(id: Int, distanceRange: ClosedRange<Length>, since: Long, till: Long): WorkoutHeader?

    suspend fun loadLatestOfActivityType(id: Int, till: Long): WorkoutHeader?

    suspend fun loadLongestTimeOfActivityType(id: Int, since: Long, till: Long): WorkoutHeader?

    suspend fun loadHighestClimbOfActivityType(id: Int, since: Long, till: Long): WorkoutHeader?

    suspend fun loadHighestAltitudeOfActivityType(id: Int, since: Long, till: Long): WorkoutHeader?

    suspend fun loadFastestPaceOfActivityTypeInPeriod(id: Int, since: Long, till: Long): WorkoutHeader?

    suspend fun findWithUserTagsById(id: Int): WorkoutHeader?

    suspend fun findWorkoutsWithUnsyncedUserTags(): List<WorkoutHeader>

    suspend fun findOldestWorkout(ownerUsername: String, since: Long): WorkoutHeader?
}
