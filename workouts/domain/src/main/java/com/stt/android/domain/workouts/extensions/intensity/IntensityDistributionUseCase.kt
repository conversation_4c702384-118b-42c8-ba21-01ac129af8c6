package com.stt.android.domain.workouts.extensions.intensity

import com.stt.android.core.domain.workouts.CoreActivityGroup
import com.stt.android.core.domain.workouts.CoreActivityGroupByType
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.domain.workouts.BasicWorkoutHeader
import com.stt.android.domain.workouts.WorkoutDataSource
import com.stt.android.utils.atEndOfDay
import com.stt.android.utils.toEpochMilli
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.withContext
import java.time.LocalDate
import javax.inject.Inject

class IntensityDistributionUseCase
@Inject constructor(
    private val intensityExtensionUseCase: GetIntensityExtensionUseCase,
    private val workoutDataSource: WorkoutDataSource
) {

    /**
     * @param avgFactor the number of entities of a specific time period between [startDate] and
     * [endDateInclusive] for which the average should be calculated. For example, if you want
     * weekly avg, pass the number of weeks between those two dates.
     */
    suspend fun calculate(
        username: String,
        startDate: LocalDate,
        endDateInclusive: LocalDate,
        avgFactor: Float
    ): IntensityDistribution = withContext(IO) {
        val workouts = workoutDataSource.fetchUserWorkoutsBasic(
            username = username,
            minStartTime = startDate.atStartOfDay().toEpochMilli(),
            maxStartTime = endDateInclusive.atEndOfDay().toEpochMilli()
        )
        calculate(workouts, avgFactor)
    }

    suspend fun calculate(
        workouts: List<BasicWorkoutHeader>,
        avgFactor: Float
    ): IntensityDistribution = withContext(IO) {
        workouts.mapNotNull {
            val activityGroup = CoreActivityGroupByType[CoreActivityType.valueOf(it.activityTypeId)]
            val intensityExtension = intensityExtensionUseCase.loadLocalExtension(workoutId = it.id)
            intensityExtension?.let { ext -> ext to activityGroup }
        }.fold(IntensityDistribution()) { distribution, (intensity, activityGroup) ->
            IntensityDistribution(
                hrZones = distribution.hrZones + intensity.intensityZones.hr,
                speedZones = distribution.speedZones + intensity.intensityZones.speed,
                runningPowerZones = if (activityGroup == CoreActivityGroup.RUN) {
                    distribution.runningPowerZones + intensity.intensityZones.power
                } else {
                    distribution.runningPowerZones
                },
                cyclingPowerZones = if (activityGroup == CoreActivityGroup.RIDE) {
                    distribution.cyclingPowerZones + intensity.intensityZones.power
                } else {
                    distribution.cyclingPowerZones
                },
            )
        }.run {
            copy(
                hrZones = hrZones / avgFactor,
                speedZones = speedZones / avgFactor,
                runningPowerZones = runningPowerZones / avgFactor,
                cyclingPowerZones = cyclingPowerZones / avgFactor,
            )
        }
    }
}
