package com.stt.android.domain.workouts.extensions

import com.stt.android.domain.user.workoutextension.IntensityExtension
import com.stt.android.domain.workouts.WorkoutHeader
import kotlin.reflect.KClass

interface ExtensionsDataSource {
    suspend fun getExtensionsForWorkout(workoutHeader: WorkoutHeader): List<WorkoutExtension>

    /**
     * Updates or insert an extension
     *
     * @param workoutId The workout ID to which this extension is associated to
     * @param extension The extension to persist
     */
    suspend fun upsertExtension(workoutId: Int, extension: WorkoutExtension)

    /**
     * Finds all extensions of a specific extension class
     *
     * @param extensionClass The extension class to look for
     * @return a list of extensions of the requested type or null if none was found
     */
    suspend fun findExtensionsByType(extensionClass: KClass<out WorkoutExtension>): List<WorkoutExtension>

    /**
     * Loads all extensions available for workout by ID from local DB
     *
     * @param workoutId The local DB ID of the workout
     * @return List of [WorkoutExtension]
     */
    suspend fun loadExtensions(workoutId: Int): List<WorkoutExtension>

    /**
     * Loads intensity extension for workout by ID from local DB
     *
     * @param workoutId The local DB ID of the workout
     * @return List of [WorkoutExtension]
     */
    suspend fun loadIntensityExtension(workoutId: Int): IntensityExtension?

    /**
     * Fetches all extensions for workout by key from the server
     *
     * @param workoutId The local workout ID
     * @param workoutKey The remote workout key
     * @return list of [WorkoutExtension]
     */
    suspend fun fetchExtensions(workoutId: Int, workoutKey: String): List<WorkoutExtension>
}
