package com.stt.android.ski

import com.stt.android.domain.user.workoutextension.SlopeSkiSummary
import java.time.Duration
import kotlin.math.max

data class SlopeSki
@JvmOverloads constructor(
    val totalRuns: Int = 0,
    val totalDescents: Double = 0.0,
    val totalDescentDistance: Double = 0.0,
    val totalDescentTimeInSeconds: Double = 0.0,
    val maxSkiRunSpeedMetersPerSecond: Double = 0.0,
    val runs: List<Run> = emptyList()
) {
    data class Run(
        val startTimeInSeconds: Double,
        val endTimeInSeconds: Double,
        val descents: Double,
        val distance: Double,
        val altitudes: List<Double>,
        val maxSpeedMetersPerSecond: Double
    ) {
        fun duration(): Double = endTimeInSeconds - startTimeInSeconds
    }

    fun runsAndExtractTotals(runs: List<Run>): SlopeSki {
        var totalDescents = 0.0
        var totalDescentDistance = 0.0
        var totalDescentTimeInSeconds = 0.0
        var maxSkiRunSpeedMetersPerSecond = 0.0
        runs.forEach { run ->
            totalDescents += run.descents
            totalDescentDistance += run.distance
            totalDescentTimeInSeconds += run.duration()
            maxSkiRunSpeedMetersPerSecond =
                max(maxSkiRunSpeedMetersPerSecond, run.maxSpeedMetersPerSecond)
        }

        return SlopeSki(
            totalRuns = runs.size,
            totalDescents = totalDescents,
            totalDescentDistance = totalDescentDistance,
            totalDescentTimeInSeconds = totalDescentTimeInSeconds,
            maxSkiRunSpeedMetersPerSecond = maxSkiRunSpeedMetersPerSecond,
            runs = runs
        )
    }

    fun toSlopeSkiSummary(workoutId: Int): SlopeSkiSummary {
        return SlopeSkiSummary(
            workoutId,
            this.totalRuns,
            Duration.ofSeconds(this.totalDescentTimeInSeconds.toLong()).toMillis(),
            this.totalDescents,
            this.totalDescentDistance,
            this.maxSkiRunSpeedMetersPerSecond
        )
    }
}
