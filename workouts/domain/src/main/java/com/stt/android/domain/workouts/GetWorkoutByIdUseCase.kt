package com.stt.android.domain.workouts

import com.stt.android.domain.workouts.extensions.ExtensionsDataSource
import com.stt.android.domain.workouts.pictures.PicturesDataSource
import com.stt.android.domain.workouts.videos.VideoDataSource
import javax.inject.Inject

class GetWorkoutByIdUseCase
@Inject constructor(
    private val workoutHeaderDataSource: WorkoutHeaderDataSource,
    private val picturesDataSource: PicturesDataSource,
    private val videoDataSource: VideoDataSource,
    private val extensionsDataSource: ExtensionsDataSource
) {
    suspend operator fun invoke(workoutId: Int): DomainWorkout {
        val workoutHeader = workoutHeaderDataSource.findWithUserTagsById(workoutId)
        return DomainWorkout(
            header = workoutHeader
                ?: throw IllegalStateException("Workout ID $workoutId not found"),
            pictures = picturesDataSource.findByWorkoutId(workoutId),
            videos = videoDataSource.findByWorkoutId(workoutId),
            extensions = extensionsDataSource.getExtensionsForWorkout(workoutHeader).associateBy { it::class }
        )
    }
}
