package com.stt.android.domain.user.workoutextension

import com.stt.android.domain.workouts.extensions.WorkoutExtension

data class FitnessExtension
/**
 * @param workoutId id of related workout
 * @param maxHeartRate The highest heart rate in BPM achievable by the athlete at the time when
 * the workout was done.
 * https://en.wikipedia.org/wiki/Heart_rate#Maximum_heart_rate.
 * @param vo2Max maximal oxygen consumption. https://en.wikipedia.org/wiki/VO2_max
 */
@JvmOverloads constructor(
    override val workoutId: Int = 0,
    val maxHeartRate: Int = 0,
    /**
     * @return vo2Max (0 - 100)
     */
    val vo2Max: Float = 0.0f,
    val fitnessAge: Int?,
) : WorkoutExtension(TYPE_FITNESS, workoutId) {
    override fun copyWithNewWorkoutId(newWorkoutId: Int): WorkoutExtension {
        return FitnessExtension(
            workoutId = newWorkoutId,
            maxHeartRate = maxHeartRate,
            vo2Max = vo2Max,
            fitnessAge = fitnessAge,
        )
    }
}
