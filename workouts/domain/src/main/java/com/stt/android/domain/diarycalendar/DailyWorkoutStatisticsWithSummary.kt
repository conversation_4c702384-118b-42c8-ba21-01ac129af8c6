package com.stt.android.domain.diarycalendar

import com.stt.android.domain.user.workoutextension.SlopeSkiSummary
import com.stt.android.domain.workouts.ActivityGroup
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.menstrualcycle.domain.MenstrualDateType
import java.time.LocalDate

typealias TotalValuesByActivityType = Map<Int, DiaryCalendarTotalValues>

data class DiaryCalendarDailyData(
    val durationByActivityGroup: Map<ActivityGroup, Long>,
    val workoutIds: List<Int>,
    val menstrualDateType: MenstrualDateType
) {
    fun isEmpty() = workoutIds.isEmpty()

    companion object {
        val EMPTY = DiaryCalendarDailyData(
            durationByActivityGroup = emptyMap(),
            workoutIds = emptyList(),
            menstrualDateType = MenstrualDateType.NOTHING
        )
    }
}

data class DiaryCalendarTotalValues(
    val duration: Double,
    val distance: Double,
    val ascent: Double?,
    val energy: Double,
    val activityCount: Int,
    val workoutIdsToKeys: Map<Int?, String?> = emptyMap(),
    val diveCount: Int = 0,
    val maxDepth: Float? = null
) {

    fun getWorkoutCount(): Int {
        return if (activityCount == diveCount) {
            0 // Only dives, do not show count
        } else {
            activityCount // Show number of all activities when at least one non-dive exists
        }
    }

    companion object {
        val EMPTY = DiaryCalendarTotalValues(0.0, 0.0, null, 0.0, 0)
    }
}

data class LocationWithActivityType(
    val startTime: Long,
    val latitude: Double,
    val longitude: Double,
    val activityType: Int,
    val polyline: String?
)

data class DailyWorkoutStatisticsWithSummary(
    val dailyData: Map<LocalDate, DiaryCalendarDailyData>,
    val totalValuesByActivityType: TotalValuesByActivityType,
    val totalValues: DiaryCalendarTotalValues,
    val locations: List<LocationWithActivityType>
)

data class WorkoutWithExtensions(
    val workout: WorkoutHeader,
    val diveExtension: DiveExtension? = null,
    val slopeSkiSummary: SlopeSkiSummary? = null,
    val summaryExtension: SummaryExtension? = null
)
