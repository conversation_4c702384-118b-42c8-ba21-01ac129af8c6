package com.stt.android.domain.workouts

import android.content.SharedPreferences
import com.stt.android.domain.MappingException
import com.stt.android.domain.comments.WorkoutCommentDataSource
import com.stt.android.domain.ranking.RankingDataSource
import com.stt.android.domain.tags.UserTagsRepository
import com.stt.android.domain.workouts.extensions.ExtensionsDataSource
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.domain.workouts.logbookentry.LogbookEntryDataSource
import com.stt.android.domain.workouts.pictures.PicturesDataSource
import com.stt.android.domain.workouts.reactions.ReactionSummaryDataSource
import com.stt.android.domain.workouts.videos.VideoDataSource
import com.stt.android.utils.onEachFailure
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.flowOn
import timber.log.Timber
import javax.inject.Inject

/**
 * Use case for fetching own workouts from the server and then storing them in local store
 */
class FetchAndStoreOwnWorkoutsUseCase
@Inject constructor(
    private val workoutDataSource: WorkoutDataSource,
    private val logbookEntryDataSource: LogbookEntryDataSource,
    picturesDataSource: PicturesDataSource,
    videoDataSource: VideoDataSource,
    reactionDataSource: ReactionSummaryDataSource,
    rankingDataSource: RankingDataSource,
    workoutHeaderDataSource: WorkoutHeaderDataSource,
    workoutCommentDataSource: WorkoutCommentDataSource,
    extensionsDataSource: ExtensionsDataSource,
    sharedPreferences: SharedPreferences,
    userTagsRepository: UserTagsRepository
) : AbstractFetchAndStoreWorkouts(
    picturesDataSource,
    videoDataSource,
    reactionDataSource,
    rankingDataSource,
    workoutHeaderDataSource,
    workoutCommentDataSource,
    extensionsDataSource,
    sharedPreferences,
    userTagsRepository
) {
    suspend operator fun invoke() {
        Timber.d("Fetching own workouts")
        workoutDataSource.fetchOwnWorkoutsPaged()
            .flowOn(Dispatchers.IO)
            .collect { workouts ->
                Timber.d("Fetched ${workouts.size} workouts")
                val storedWorkoutsResult: List<Result<DomainWorkout>> =
                    store(workouts, ownWorkouts = true)
                        .onEachFailure { Timber.e(it, "Error storing workout") }
                val storedWorkouts = storedWorkoutsResult.mapNotNull { it.getOrNull() }
                Timber.d("Stored ${storedWorkouts.size} workouts")
                storeLogbookEntries(storedWorkouts)
            }
    }

    private suspend fun storeLogbookEntries(workouts: List<DomainWorkout>) {
        Timber.d("Storing logbook entries")
        workouts.forEach { workout ->
            val workoutId = workout.header.id
            try {
                val remoteEntry = (workout.extensions?.get(SummaryExtension::class) as? SummaryExtension)
                    ?.toLogbookEntry() ?: return@forEach

                val localEntry = logbookEntryDataSource.findByWorkoutId(workoutId)

                if (localEntry != null) {
                    if (localEntry.entryId != remoteEntry.entryId) {
                        logbookEntryDataSource.update(localEntry.copy(entryId = remoteEntry.entryId))
                    }
                } else {
                    logbookEntryDataSource.insert(remoteEntry)
                }
            } catch (e: MappingException) {
                Timber.w(e, "Unable to map summary extension to logbook entry. ${e.source}")
            } catch (e: Exception) {
                Timber.w(e, "An error has occurred while trying to persist logbook entry")
            }
        }
    }
}
