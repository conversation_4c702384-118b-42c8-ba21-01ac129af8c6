package com.stt.android.domain.workouts.extensions

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlin.math.roundToInt

@Parcelize
data class DiveExtension(
    override val workoutId: Int,
    val maxDepth: Float?,
    val algorithm: String?,
    val personalSetting: Int?,
    val diveNumberInSeries: Int?,
    val cns: Float?,
    val algorithmLock: Boolean?,
    val diveMode: String?,
    val otu: Float?,
    val pauseDuration: Float?,
    val gasConsumption: Float?,
    val altitudeSetting: Float?,
    val gasQuantities: Map<String, Float?>?,
    val surfaceTime: Float?,
    val diveTime: Float?,
    val gasesUsed: List<String>?,
    val maxDepthTemperature: Float?,
    val avgDepth: Float?,
    val minGF: Float?,
    val maxGF: Float?
) : WorkoutExtension(TYPE_DIVE, workoutId), Parcelable {
    override fun copyWithNewWorkoutId(newWorkoutId: Int): WorkoutExtension {
        return copy(workoutId = newWorkoutId)
    }

    fun hasOxygenToxicityValues(): Boolean =
        listOf(
            DiveMode.Nitrox.value,
            DiveMode.Trimix.value,
            DiveMode.CCR.value,
            DiveMode.CCRNitrox.value,
            DiveMode.CCRTrimix.value
        )
            .contains(diveMode)

    /**
     * Returns empty string if name is not found
     */
    fun getGasNameByIndex(gasIndex: Int) =
        if (gasesUsed != null && gasIndex > 0 && gasIndex <= gasesUsed.size) {
            gasesUsed[gasIndex - 1]
        } else {
            ""
        }

    companion object {
        val workoutIdFieldName: String
            get() = "workoutId"
        const val ALGORITHM_BUHLMANN = "Buhlmann"
        const val ALGORITHM_SUUNTO_FUSED_RGBM = "Suunto Fused RGBM"
        const val ALGORITHM_SUUNTO_FUSED_RGBM_2 = "Suunto Fused2 RGBM"
    }
}

fun DiveExtension?.getAltitudeSettingAsEnum(): AltitudeSetting? {
    return if (this?.altitudeSetting != null) {
        AltitudeSetting.fromInt(altitudeSetting.roundToInt())
    } else {
        null
    }
}
