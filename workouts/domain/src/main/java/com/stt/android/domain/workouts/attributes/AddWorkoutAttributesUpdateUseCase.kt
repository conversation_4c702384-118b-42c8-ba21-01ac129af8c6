package com.stt.android.domain.workouts.attributes

import com.stt.android.domain.CoroutineUseCase
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.domain.workouts.tss.TSS
import javax.inject.Inject

/**
 * Use case for storing a pending workout attributes update
 */
class AddWorkoutAttributesUpdateUseCase @Inject constructor(
    private val workoutAttributesUpdateRepository: WorkoutAttributesUpdateDataSource
) : CoroutineUseCase<Unit, AddWorkoutAttributesUpdateUseCase.Params> {
    fun getLocationUpdateParams(
        workoutId: Int,
        username: String,
        latitude: Double,
        longitude: Double,
        requiresUserConfirmation: Boolean = false
    ) = Params(
        workoutId,
        username,
        DomainWorkoutAttributes(
            startPosition = DomainWorkoutLocation(
                latitude,
                longitude
            ),
            tss = null,
            maxSpeed = null,
            totalAscent = null,
            totalDescent = null,
            suuntoTags = null
        ),
        requiresUserConfirmation
    )

    fun getTssUpdateParams(
        workoutId: Int,
        username: String,
        tss: TSS,
        requiresUserConfirmation: Boolean = false
    ) = Params(
        workoutId,
        username,
        DomainWorkoutAttributes(
            startPosition = null,
            tss = tss,
            maxSpeed = null,
            totalAscent = null,
            totalDescent = null,
            suuntoTags = null
        ),
        requiresUserConfirmation
    )

    fun getMaxSpeedUpdateParams(
        workoutId: Int,
        username: String,
        maxSpeed: Double,
        requiresUserConfirmation: Boolean = false
    ) = Params(
        workoutId,
        username,
        DomainWorkoutAttributes(
            startPosition = null,
            tss = null,
            maxSpeed = maxSpeed,
            totalAscent = null,
            totalDescent = null,
            suuntoTags = null
        ),
        requiresUserConfirmation
    )

    fun getTotalAscentUpdateParams(
        workoutId: Int,
        username: String,
        ascent: Double,
        requiresUserConfirmation: Boolean = false
    ) = Params(
        workoutId,
        username,
        DomainWorkoutAttributes(
            startPosition = null,
            tss = null,
            maxSpeed = null,
            totalAscent = ascent,
            totalDescent = null,
            suuntoTags = null
        ),
        requiresUserConfirmation
    )

    fun getTotalDescentUpdateParams(
        workoutId: Int,
        username: String,
        descent: Double,
        requiresUserConfirmation: Boolean = false
    ) = Params(
        workoutId,
        username,
        DomainWorkoutAttributes(
            startPosition = null,
            tss = null,
            maxSpeed = null,
            totalAscent = null,
            totalDescent = descent,
            suuntoTags = null
        ),
        requiresUserConfirmation
    )

    fun getSuuntoTagsUpdateParams(
        workoutId: Int,
        username: String,
        suuntoTags: List<SuuntoTag>,
        requiresUserConfirmation: Boolean = false
    ) = Params(
        workoutId,
        username,
        DomainWorkoutAttributes(
            startPosition = null,
            tss = null,
            maxSpeed = null,
            totalAscent = null,
            totalDescent = null,
            suuntoTags = suuntoTags
        ),
        requiresUserConfirmation
    )

    override suspend fun run(params: Params) {
        val paramsUpdate = DomainWorkoutAttributesUpdate(
            params.workoutId,
            params.username,
            params.attributes,
            emptyList(),
            params.requiresUserConfirmation
        )
        val pendingUpdate = workoutAttributesUpdateRepository.fetchUnsyncedWorkoutAttributesUpdate(
            params.workoutId,
            params.username
        )

        val newUpdate = pendingUpdate?.updateWith(paramsUpdate) ?: paramsUpdate
        workoutAttributesUpdateRepository.addWorkoutAttributesUpdate(newUpdate)
    }

    data class Params(
        val workoutId: Int,
        val username: String,
        val attributes: DomainWorkoutAttributes,
        val requiresUserConfirmation: Boolean
    )
}
