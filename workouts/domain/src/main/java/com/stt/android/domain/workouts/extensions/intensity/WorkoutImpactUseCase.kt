package com.stt.android.domain.workouts.extensions.intensity

import com.soy.algorithms.impact.WorkoutImpact
import com.soy.algorithms.impact.WorkoutImpactCalculationInput
import com.soy.algorithms.impact.WorkoutImpactCalculator
import com.soy.algorithms.intensity.IntensityZones
import com.stt.android.domain.workouts.BasicWorkoutHeader
import com.suunto.algorithms.ddfa.DynamicDFAZones
import javax.inject.Inject

class WorkoutImpactUseCase @Inject constructor(
    private val intensityExtensionUseCase: GetIntensityExtensionUseCase,
) {
    suspend fun calculateWorkoutImpacts(
        workoutHeader: BasicWorkoutHeader,
        intensityZones: IntensityZones? = null,
        dynamicDFAZones: DynamicDFAZones? = null,
    ): List<WorkoutImpact> {
        return WorkoutImpactCalculator.calculateWorkoutImpacts(
            input = WorkoutImpactCalculationInput(
                activityId = workoutHeader.activityTypeId,
                totalDurationWithoutPauses = workoutHeader.totalTime.toFloat(),
                trainingStressScore = workoutHeader.tss?.trainingStressScore,
                intensityZones = intensityZones
                    ?: intensityExtensionUseCase.loadLocalExtension(workoutHeader.id)?.intensityZones,
                dynamicDFAZones = dynamicDFAZones,
            ),
        )
    }
}
