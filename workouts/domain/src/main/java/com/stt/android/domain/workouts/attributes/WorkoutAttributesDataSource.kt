package com.stt.android.domain.workouts.attributes

import com.stt.android.domain.workouts.DomainWorkout

interface WorkoutAttributesDataSource {
    /**
     * Updates the attributes of specified workout
     * @param workoutKey Identifies the workout
     * @param attributes Updated attributes
     * @return Updated workout
     */
    suspend fun updateWorkoutAttributes(
        workoutId: Int,
        workoutKey: String,
        attributes: DomainWorkoutAttributes
    ): DomainWorkout

    /**
     * Deletes specified workout attributes
     * @param workoutKey Identifies the workout
     * @param fields Comma-separated list of attributes to be deleted
     * @return Updated workout
     */
    suspend fun deleteWorkoutAttributes(
        workoutId: Int,
        workoutKey: String,
        fields: String
    ): DomainWorkout
}
