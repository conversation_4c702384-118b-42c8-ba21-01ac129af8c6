package com.stt.android.domain.workouts.extensions

import androidx.annotation.IntDef

abstract class WorkoutExtension(
    @Type val type: Int,
    open val workoutId: Int,
) {

    abstract fun copyWithNewWorkoutId(newWorkoutId: Int): WorkoutExtension

    @IntDef(
        TYPE_SLOPE_SKI,
        TYPE_WORKOUT_SUMMARY,
        TYPE_FITNESS,
        TYPE_WORKOUT_INTENSITY,
        TYPE_DIVE,
        TYPE_SML,
        TYPE_SWIMMING,
        TYPE_SAILING,
        TYPE_WEATHER,
        TYPE_JUMP_ROPE,
    )
    @Retention(AnnotationRetention.SOURCE)
    annotation class Type

    companion object {
        const val TYPE_SLOPE_SKI = 1
        const val TYPE_WORKOUT_SUMMARY = 2
        const val TYPE_FITNESS = 3
        const val TYPE_WORKOUT_INTENSITY = 4
        const val TYPE_DIVE = 5
        const val TYPE_SML = 6
        const val TYPE_SWIMMING = 7
        const val TYPE_SAILING = 8
        const val TYPE_WEATHER = 9
        const val TYPE_JUMP_ROPE = 10
    }
}
