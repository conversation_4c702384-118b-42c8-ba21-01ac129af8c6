package com.stt.android.domain.workouts

import com.stt.android.core.domain.workouts.CoreActivityType
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.whenever

@RunWith(MockitoJUnitRunner::class)
class GetHasOnlyDiveWorkoutsUseCaseTest {

    @Mock
    lateinit var workoutHeaderDataSource: WorkoutHeaderDataSource

    lateinit var useCase: GetHasOnlyDiveWorkoutsUseCase

    @Before
    fun setUp() {
        useCase = GetHasOnlyDiveWorkoutsUseCase(workoutHeaderDataSource)
    }

    @Test
    fun `return false if no workouts at all`() = runTest {
        whenever(workoutHeaderDataSource.loadTotalActivityCount()).thenReturn(0L)

        assertThat(useCase()).isFalse()
    }

    @Test
    fun `return false if has only dive workouts`() = runTest {
        whenever(workoutHeaderDataSource.loadTotalActivityCount()).thenReturn(10L)
        whenever(workoutHeaderDataSource.loadActivityTypeCount(CoreActivityType.FREEDIVING.id)).thenReturn(0L)
        whenever(workoutHeaderDataSource.loadActivityTypeCount(CoreActivityType.SCUBADIVING.id)).thenReturn(0L)

        assertThat(useCase()).isFalse()
    }

    @Test
    fun `return false if has dives and other workouts`() = runTest {
        whenever(workoutHeaderDataSource.loadTotalActivityCount()).thenReturn(10L)
        whenever(workoutHeaderDataSource.loadActivityTypeCount(CoreActivityType.FREEDIVING.id)).thenReturn(2L)
        whenever(workoutHeaderDataSource.loadActivityTypeCount(CoreActivityType.SCUBADIVING.id)).thenReturn(1L)

        assertThat(useCase()).isFalse()
    }

    @Test
    fun `return true if only has free and scuba diving workouts`() = runTest {
        whenever(workoutHeaderDataSource.loadTotalActivityCount()).thenReturn(10L)
        whenever(workoutHeaderDataSource.loadActivityTypeCount(CoreActivityType.FREEDIVING.id)).thenReturn(3L)
        whenever(workoutHeaderDataSource.loadActivityTypeCount(CoreActivityType.SCUBADIVING.id)).thenReturn(7L)

        assertThat(useCase()).isTrue()
    }
}
