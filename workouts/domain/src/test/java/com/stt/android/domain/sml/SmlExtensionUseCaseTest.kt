package com.stt.android.domain.sml

import com.stt.android.domain.workouts.extensions.SMLExtension
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import kotlin.test.assertEquals

@RunWith(MockitoJUnitRunner::class)
class SmlExtensionUseCaseTest {
    @Mock
    private lateinit var smlDataSource: SmlDataSource
    private lateinit var smlExtensionUseCase: SmlExtensionUseCase

    @Before
    fun setup() {
        smlExtensionUseCase = SmlExtensionUseCase(smlDataSource)
    }

    @Test
    fun `should access the repository fetch`() = runTest {
        val smlExtension = SMLExtension(1, null)
        whenever(smlDataSource.fetchSmlExtension(1, "key"))
            .thenReturn(smlExtension)

        assertEquals(smlExtension, smlExtensionUseCase.fetchSmlExtension(1, "key"))

        verify(smlDataSource).fetchSmlExtension(1, "key")
    }
}
