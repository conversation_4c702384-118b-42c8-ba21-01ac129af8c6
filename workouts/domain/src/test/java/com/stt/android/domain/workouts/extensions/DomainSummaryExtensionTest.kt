package com.stt.android.domain.workouts.extensions

import com.stt.android.domain.MappingException
import com.stt.android.logbook.SuuntoLogbookZapp
import org.assertj.core.api.Assertions.assertThat
import org.junit.Test

class DomainSummaryExtensionTest {
    @Test
    fun `map extension to to LogbookEntry success`() {
        val expected =
            SummaryExtension(
                workoutId = 1,
                pte = 1.1F,
                feeling = 2,
                avgTemperature = 2.0F,
                peakEpoc = 3.0F,
                avgPower = 4.0F,
                avgCadence = 5.0F,
                avgSpeed = 6.0f,
                ascentTime = 7.0F,
                descentTime = 8.0F,
                performanceLevel = 9.0F,
                recoveryTime = 123L,
                ascent = 78.0,
                descent = 456.0,
                deviceHardwareVersion = "hwDevice",
                deviceSoftwareVersion = "softVer",
                productType = "productType",
                displayName = "displayName",
                deviceName = "deviceName",
                deviceSerialNumber = "serial",
                deviceManufacturer = "deviceManufacturer",
                exerciseId = "1",
                zapps = listOf(
                    SuuntoLogbookZapp(
                        id = "zapp",
                        authorId = null,
                        externalId = null,
                        name = "a name",
                        summaryOutputs = listOf(
                            SuuntoLogbookZapp.SummaryOutput(
                                "format",
                                "id",
                                "summaryOutput",
                                "postfix",
                                1.0
                            )
                        ),
                        channels = listOf(
                            SuuntoLogbookZapp.ZappChannel(
                                channelId = 0,
                                format = "format",
                                inverted = true,
                                name = "name",
                                variableId = "id",
                            )
                        )
                    )
                ),
                maxCadence = 0f,
                repetitionCount = 0,
                avgStrideLength = 0f,
                fatConsumption = 0,
                carbohydrateConsumption = 0,
                avgGroundContactTime = 0f,
                avgVerticalOscillation = 0f,
                avgLeftGroundContactBalance = 0f,
                avgRightGroundContactBalance = 0f,
                lacticThHr = null,
                lacticThPace = null,
                avgAscentSpeed = 10f,
                maxAscentSpeed = 10f,
                avgDescentSpeed = 10f,
                maxDescentSpeed = 10f,
                avgDistancePerStroke = 10f,
            )

        val actual = expected.toLogbookEntry()
        assertThat(actual?.workoutId).isEqualTo(expected.workoutId)
        assertThat(actual?.deviceName).isEqualTo(expected.deviceName)
        assertThat(actual?.entryId).isEqualTo(1)
        assertThat(actual?.hwName).isEqualTo(null)
        assertThat(actual?.serialNumber).isEqualTo(expected.deviceSerialNumber)
        assertThat(actual?.swVersion).isEqualTo(expected.deviceSoftwareVersion)
    }

    @Test
    fun `map extension to LogbookEntry returns null due to exercise format error`() {
        val expected =
            SummaryExtension(
                workoutId = 1,
                pte = 1.1F,
                feeling = 2,
                avgTemperature = 2.0F,
                peakEpoc = 3.0F,
                avgPower = 4.0F,
                avgCadence = 5.0F,
                avgSpeed = 6.0F,
                ascentTime = 7.0F,
                descentTime = 8.0F,
                performanceLevel = 9.0F,
                recoveryTime = 123L,
                ascent = 78.0,
                descent = 456.0,
                deviceHardwareVersion = "hwDevice",
                deviceSoftwareVersion = "softVer",
                productType = "productType",
                displayName = "displayName",
                deviceName = "deviceName",
                deviceSerialNumber = "serial",
                deviceManufacturer = "deviceManufacturer",
                exerciseId = "foo",
                zapps = listOf(
                    SuuntoLogbookZapp(
                        id = "zapp",
                        authorId = null,
                        externalId = null,
                        name = "a name",
                        summaryOutputs = listOf(
                            SuuntoLogbookZapp.SummaryOutput(
                                "format",
                                "id",
                                "summaryOutput",
                                "postfix",
                                1.0
                            )
                        ),
                        channels = listOf(
                            SuuntoLogbookZapp.ZappChannel(
                                channelId = 0,
                                format = "format",
                                inverted = true,
                                name = "name",
                                variableId = "id",
                            )
                        )
                    )
                ),
                maxCadence = 0f,
                repetitionCount = 0,
                avgStrideLength = 0f,
                fatConsumption = 0,
                carbohydrateConsumption = 0,
                avgGroundContactTime = 0f,
                avgVerticalOscillation = 0f,
                avgLeftGroundContactBalance = 0f,
                avgRightGroundContactBalance = 0f,
                lacticThHr = null,
                lacticThPace = null,
                avgAscentSpeed = 10f,
                maxAscentSpeed = 10f,
                avgDescentSpeed = 10f,
                maxDescentSpeed = 10f,
                avgDistancePerStroke = 10f,
            )

        expected.toLogbookEntry()
    }

    @Test(expected = MappingException::class)
    fun `map extension to to LogbookEntry fails due to null workout ID`() {
        val expected =
            SummaryExtension(
                workoutId = 0,
                pte = 1.1F,
                feeling = 2,
                avgTemperature = 2.0F,
                peakEpoc = 3.0F,
                avgPower = 4.0F,
                avgCadence = 5.0F,
                avgSpeed = 6.0F,
                ascentTime = 7.0F,
                descentTime = 8.0F,
                performanceLevel = 9.0F,
                recoveryTime = 123L,
                ascent = 78.0,
                descent = 456.0,
                deviceHardwareVersion = "hwDevice",
                deviceSoftwareVersion = "softVer",
                productType = "productType",
                displayName = "displayName",
                deviceName = "deviceName",
                deviceSerialNumber = "serial",
                deviceManufacturer = "deviceManufacturer",
                exerciseId = "1",
                zapps = listOf(
                    SuuntoLogbookZapp(
                        id = "zapp",
                        authorId = null,
                        externalId = null,
                        name = "a name",
                        summaryOutputs = listOf(
                            SuuntoLogbookZapp.SummaryOutput(
                                "format",
                                "id",
                                "summaryOutput",
                                "postfix",
                                1.0
                            )
                        ),
                        channels = listOf(
                            SuuntoLogbookZapp.ZappChannel(
                                channelId = 0,
                                format = "format",
                                inverted = true,
                                name = "name",
                                variableId = "id",
                            )
                        )
                    )
                ),
                maxCadence = 0f,
                repetitionCount = 0,
                avgStrideLength = 0f,
                fatConsumption = 0,
                carbohydrateConsumption = 0,
                avgGroundContactTime = 0f,
                avgVerticalOscillation = 0f,
                avgLeftGroundContactBalance = 0f,
                avgRightGroundContactBalance = 0f,
                lacticThHr = null,
                lacticThPace = null,
                avgAscentSpeed = 10f,
                maxAscentSpeed = 10f,
                avgDescentSpeed = 10f,
                maxDescentSpeed = 10f,
                avgDistancePerStroke = 10f,
            )

        expected.toLogbookEntry()
    }
}
