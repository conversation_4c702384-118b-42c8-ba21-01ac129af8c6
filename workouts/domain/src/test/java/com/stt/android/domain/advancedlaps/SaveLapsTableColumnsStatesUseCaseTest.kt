package com.stt.android.domain.advancedlaps

import com.stt.android.core.domain.LapsTableDataType
import com.stt.android.infomodel.SummaryItem
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.doNothing

@RunWith(MockitoJUnitRunner::class)
class SaveLapsTableColumnsStatesUseCaseTest {
    @Mock
    private lateinit var dataSource: LapsTableStateDataSource

    private lateinit var useCase: SaveLapsTableColumnsStatesUseCase

    @Before
    fun setup() {
        useCase = SaveLapsTableColumnsStatesUseCase(dataSource)
    }

    @Test
    fun `test that saving lapstable column state completes successfully`() {
        doNothing().`when`(dataSource).saveColumnsState(any(), any())
        val summaryItems = listOf(SummaryItem.AVGCADENCE, SummaryItem.AVGPOWER, SummaryItem.AVGHEARTRATE, SummaryItem.DURATION)
            .map { LapsTableDataType.Summary(it) }
        useCase(6, LapsTableType.DISTANCE_AUTO_LAP, summaryItems)
    }
}
