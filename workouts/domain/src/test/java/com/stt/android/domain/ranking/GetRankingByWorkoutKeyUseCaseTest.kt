package com.stt.android.domain.ranking

import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.whenever
import kotlin.test.assertEquals

@RunWith(MockitoJUnitRunner::class)
class GetRankingByWorkoutKeyUseCaseTest {
    private lateinit var getRankingsByWorkoutKeyUseCase: GetRankingsByWorkoutKeyUseCase

    @Mock
    private lateinit var rankingDataSource: RankingDataSource

    @Before
    fun setup() {
        getRankingsByWorkoutKeyUseCase = GetRankingsByWorkoutKeyUseCase(rankingDataSource)
    }

    @Test
    fun `get rankings by workout key returns flowable of ranking list`() = runTest {
        val workoutKey = "key"
        val expected = listOf(Ranking(key = workoutKey, type = "1", ranking = 1, numberOfWorkouts = 1))
        whenever(rankingDataSource.fetchRankings(workoutKey)).thenReturn(flowOf(expected))
        assertEquals(
            expected,
            getRankingsByWorkoutKeyUseCase(workoutKey).first(),
        )
    }
}
