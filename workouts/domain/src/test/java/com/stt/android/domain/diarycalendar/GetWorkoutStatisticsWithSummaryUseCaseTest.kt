package com.stt.android.domain.diarycalendar

import com.stt.android.domain.workouts.ActivityGroup
import com.stt.android.domain.workouts.ActivityGroupMapper
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.WorkoutHeaderDataSource
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.domain.workouts.extensions.DiveExtensionDataSource
import com.stt.android.menstrualcycle.domain.MenstrualCycleLocalDataSource
import com.stt.android.testutils.CoroutinesTestRule
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.anyLong
import org.mockito.ArgumentMatchers.anyString
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import java.time.LocalDate

@OptIn(ExperimentalCoroutinesApi::class)
@RunWith(MockitoJUnitRunner::class)
class GetWorkoutStatisticsWithSummaryUseCaseTest {

    @Rule
    @JvmField
    val coroutinesTestRule = CoroutinesTestRule()

    private lateinit var useCase: GetWorkoutStatisticsWithSummaryUseCase

    @Mock
    private lateinit var diveExtensionDataSource: DiveExtensionDataSource

    @Mock
    private lateinit var mapper: ActivityGroupMapper

    @Mock
    private lateinit var workoutsDataSource: WorkoutHeaderDataSource

    @Mock
    private lateinit var menstrualCycleLocalDataSource: MenstrualCycleLocalDataSource

    @Before
    fun setup() {
        useCase = GetWorkoutStatisticsWithSummaryUseCase(
            workoutsDataSource,
            mapper,
            diveExtensionDataSource,
            menstrualCycleLocalDataSource
        )

        whenever(mapper.activityTypeIdToGroup(cyclingTypeId)).thenReturn(ActivityGroup.Cycling)
        whenever(mapper.activityTypeIdToGroup(mountaingBikingTypeId)).thenReturn(ActivityGroup.Cycling)
        whenever(mapper.activityTypeIdToGroup(tennisTypeId)).thenReturn(ActivityGroup.TeamAndRacketSports)
        whenever(mapper.activityTypeIdToGroup(scubaDivingTypeId)).thenReturn(ActivityGroup.Diving)
    }

    @Test
    fun `daily durations should be grouped by activity group and have proper values`() {
        runTest {
            whenever(workoutsDataSource.findByStartTime(anyString(), anyLong(), anyLong()))
                .thenReturn(
                    listOf(cyclingWorkout, mountainBikingWorkout, tennisWorkout, diveWorkout)
                )
            whenever(
                menstrualCycleLocalDataSource.fetchInRange(any(), any())
            ).thenReturn(emptyList())

            val result = useCase(
                GetWorkoutStatisticsWithSummaryUseCase.Params(
                    "user",
                    LocalDate.of(2019, 12, 1),
                    LocalDate.of(2019, 12, 11),
                    loadMenstrualCycles = true,
                    loadPredictedMenstrualCycles = true,
                    loadWorkouts = true,
                )
            )
                .dailyData

            assertThat(
                result[LocalDate.of(2019, 12, 1)]?.durationByActivityGroup
                    ?.get(ActivityGroup.Cycling)
            )
                .isEqualTo(3_600_000L)

            assertThat(
                result[LocalDate.of(2019, 12, 5)]?.durationByActivityGroup
                    ?.get(ActivityGroup.Cycling)
            )
                .isEqualTo(7_200_000L)

            assertThat(
                result[LocalDate.of(2019, 12, 5)]?.durationByActivityGroup
                    ?.get(ActivityGroup.TeamAndRacketSports)
            )
                .isEqualTo(1_800_000L)

            assertThat(
                result[LocalDate.of(2019, 12, 11)]?.durationByActivityGroup
                    ?.get(ActivityGroup.Diving)
            )
                .isEqualTo(3_000_000L)
        }
    }

    @Test
    fun `total values should be grouped by activity type and have proper total values`() {
        runTest {
            val cyclingWorkout2 = mountainBikingWorkout.copy(activityTypeId = cyclingTypeId)
            whenever(diveExtensionDataSource.findById(3)).thenReturn(diveExtension)
            whenever(workoutsDataSource.findByStartTime(anyString(), anyLong(), anyLong()))
                .thenReturn(
                    listOf(cyclingWorkout, cyclingWorkout2, tennisWorkout, diveWorkout)
                )
            whenever(
                menstrualCycleLocalDataSource.fetchInRange(any(), any())
            ).thenReturn(emptyList())

            val result = useCase(
                GetWorkoutStatisticsWithSummaryUseCase.Params(
                    "user",
                    LocalDate.of(2019, 12, 1),
                    LocalDate.of(2019, 12, 11),
                    loadMenstrualCycles = true,
                    loadPredictedMenstrualCycles = true,
                    loadWorkouts = true,
                )
            )
                .totalValuesByActivityType

            assertThat(result.size).isEqualTo(3)
            assertThat(result[cyclingTypeId]).isEqualTo(
                DiaryCalendarTotalValues(
                    duration = 3600.0 + 7200.0,
                    distance = distance * 2,
                    ascent = 50.5 * 2,
                    energy = energy * 2,
                    activityCount = 2,
                    workoutIdsToKeys = mapOf(0 to "test1", 1 to "test2"),
                    diveCount = 0,
                    maxDepth = null
                )
            )

            assertThat(result[tennisTypeId]).isEqualTo(
                DiaryCalendarTotalValues(
                    duration = 1800.0,
                    distance = distance,
                    ascent = null, // because tennis is excluded from ascent
                    energy = energy,
                    activityCount = 1,
                    workoutIdsToKeys = mapOf(2 to "test3"),
                    diveCount = 0,
                    maxDepth = null
                )
            )

            assertThat(result[tennisTypeId]).isEqualTo(
                DiaryCalendarTotalValues(
                    duration = 1800.0,
                    distance = distance,
                    ascent = null, // because tennis is excluded from ascent
                    energy = energy,
                    activityCount = 1,
                    workoutIdsToKeys = mapOf(2 to "test3"),
                    diveCount = 0,
                    maxDepth = null
                )
            )

            assertThat(result[scubaDivingTypeId]).isEqualTo(
                DiaryCalendarTotalValues(
                    duration = 3000.0,
                    distance = distance,
                    ascent = null,
                    energy = energy,
                    activityCount = 1,
                    workoutIdsToKeys = mapOf(3 to "test4"),
                    diveCount = 1,
                    maxDepth = 20f
                )
            )
        }
    }

    @Test
    fun `runWithMultipleTimePeriods returns data equivalent with regular run`() = runTest {
        val fullTimePeriodParams = GetWorkoutStatisticsWithSummaryUseCase.Params(
            "user",
            LocalDate.of(2019, 12, 1),
            LocalDate.of(2019, 12, 11),
            loadMenstrualCycles = true,
            loadPredictedMenstrualCycles = true,
            loadWorkouts = true,
        )

        val shorterTimePeriodParams = GetWorkoutStatisticsWithSummaryUseCase.Params(
            "user",
            LocalDate.of(2019, 12, 4),
            LocalDate.of(2019, 12, 8),
            loadMenstrualCycles = true,
            loadPredictedMenstrualCycles = true,
            loadWorkouts = true,
        )

        // First make the datasource return workouts for the shorter period and
        // run the regular run for that period
        whenever(workoutsDataSource.findByStartTime(anyString(), anyLong(), anyLong()))
            .thenReturn(
                listOf(mountainBikingWorkout, tennisWorkout)
            )

        whenever(
            menstrualCycleLocalDataSource.fetchInRange(any(), any())
        ).thenReturn(emptyList())

        val regularShorterTimePeriodResult = useCase(shorterTimePeriodParams)

        // Then make datasource return workouts for the full period for rest of the test
        whenever(workoutsDataSource.findByStartTime(anyString(), anyLong(), anyLong()))
            .thenReturn(
                listOf(cyclingWorkout, mountainBikingWorkout, tennisWorkout, diveWorkout)
            )
        val regularFullTimePeriodResult = useCase(fullTimePeriodParams)

        val multipleTimePeriodsResult = useCase.runWithMultipleTimePeriods(
            listOf(fullTimePeriodParams, shorterTimePeriodParams)
        )

        assertThat(multipleTimePeriodsResult).isEqualTo(
            listOf(regularFullTimePeriodResult, regularShorterTimePeriodResult)
        )
    }

    private val distance = 11.4
    private val energy = 120.0
    private val cyclingTypeId = 2
    private val mountaingBikingTypeId = 10
    private val tennisTypeId = 34
    private val scubaDivingTypeId = 78

    private val diveExtension = DiveExtension(
        workoutId = 3,
        maxDepth = 20f,
        algorithm = null,
        personalSetting = null,
        diveNumberInSeries = null,
        cns = null,
        algorithmLock = false,
        diveMode = null,
        otu = null,
        pauseDuration = null,
        gasConsumption = null,
        altitudeSetting = null,
        gasQuantities = null,
        surfaceTime = null,
        diveTime = null,
        gasesUsed = null,
        maxDepthTemperature = null,
        avgDepth = null,
        minGF = null,
        maxGF = null
    )

    private val cyclingWorkout = WorkoutHeader(
        id = 0,
        key = "test1",
        totalDistance = distance,
        maxSpeed = 20.0,
        activityTypeId = cyclingTypeId,
        avgSpeed = 0.0,
        description = "",
        startPosition = null,
        stopPosition = null,
        centerPosition = null,
        startTime = 1575206596000, // 1.12.2019 13:23:16
        stopTime = 110,
        totalTime = 3600.0,
        energyConsumption = energy,
        username = "test",
        heartRateAverage = 0.0,
        heartRateAvgPercentage = 0.0,
        heartRateMax = 0.0,
        heartRateMaxPercentage = 0.0,
        heartRateUserSetMax = 0.0,
        averageCadence = 0,
        maxCadence = 0,
        pictureCount = 0,
        viewCount = 0,
        commentCount = 0,
        sharingFlags = 0,
        stepCount = 100,
        polyline = null,
        manuallyAdded = false,
        reactionCount = 0,
        totalAscent = 50.5,
        totalDescent = 10.2,
        recoveryTime = 200,
        locallyChanged = false,
        deleted = false,
        seen = true,
        maxAltitude = 20.0,
        minAltitude = 15.0,
        extensionsFetched = false,
        suuntoTags = listOf(),
        userTags = listOf()
    )

    private val mountainBikingWorkout = WorkoutHeader(
        id = 1,
        key = "test2",
        totalDistance = distance,
        maxSpeed = 20.0,
        activityTypeId = mountaingBikingTypeId,
        avgSpeed = 0.0,
        description = "",
        startPosition = null,
        stopPosition = null,
        centerPosition = null,
        startTime = 1575552196000, // 5.12.2019
        stopTime = 110,
        totalTime = 7200.0,
        energyConsumption = energy,
        username = "test",
        heartRateAverage = 0.0,
        heartRateAvgPercentage = 0.0,
        heartRateMax = 0.0,
        heartRateMaxPercentage = 0.0,
        heartRateUserSetMax = 0.0,
        averageCadence = 0,
        maxCadence = 0,
        pictureCount = 0,
        viewCount = 0,
        commentCount = 0,
        sharingFlags = 0,
        stepCount = 100,
        polyline = null,
        manuallyAdded = false,
        reactionCount = 0,
        totalAscent = 50.5,
        totalDescent = 10.2,
        recoveryTime = 200,
        locallyChanged = false,
        deleted = false,
        seen = true,
        maxAltitude = 20.0,
        minAltitude = 15.0,
        extensionsFetched = false,
        suuntoTags = listOf(),
        userTags = listOf()
    )

    private val tennisWorkout = WorkoutHeader(
        id = 2,
        key = "test3",
        totalDistance = distance,
        maxSpeed = 20.0,
        activityTypeId = tennisTypeId,
        avgSpeed = 0.0,
        description = "",
        startPosition = null,
        stopPosition = null,
        centerPosition = null,
        startTime = 1575552196000, // 5.12.2019
        stopTime = 110,
        totalTime = 1800.0,
        energyConsumption = energy,
        username = "test",
        heartRateAverage = 0.0,
        heartRateAvgPercentage = 0.0,
        heartRateMax = 0.0,
        heartRateMaxPercentage = 0.0,
        heartRateUserSetMax = 0.0,
        averageCadence = 0,
        maxCadence = 0,
        pictureCount = 0,
        viewCount = 0,
        commentCount = 0,
        sharingFlags = 0,
        stepCount = 100,
        polyline = null,
        manuallyAdded = false,
        reactionCount = 0,
        totalAscent = 50.5,
        totalDescent = 10.2,
        recoveryTime = 200,
        locallyChanged = false,
        deleted = false,
        seen = true,
        maxAltitude = 20.0,
        minAltitude = 15.0,
        extensionsFetched = false,
        suuntoTags = listOf(),
        userTags = listOf()
    )

    private val diveWorkout = WorkoutHeader(
        id = 3,
        key = "test4",
        totalDistance = distance,
        maxSpeed = 20.0,
        activityTypeId = scubaDivingTypeId,
        avgSpeed = 0.0,
        description = "",
        startPosition = null,
        stopPosition = null,
        centerPosition = null,
        startTime = 1576070596000, // 11.12.2019
        stopTime = 100,
        totalTime = 3000.0,
        energyConsumption = energy,
        username = "test",
        heartRateAverage = 0.0,
        heartRateAvgPercentage = 0.0,
        heartRateMax = 0.0,
        heartRateMaxPercentage = 0.0,
        heartRateUserSetMax = 0.0,
        averageCadence = 0,
        maxCadence = 0,
        pictureCount = 0,
        viewCount = 0,
        commentCount = 0,
        sharingFlags = 0,
        stepCount = 100,
        polyline = null,
        manuallyAdded = false,
        reactionCount = 0,
        totalAscent = 50.5,
        totalDescent = 10.2,
        recoveryTime = 200,
        locallyChanged = false,
        deleted = false,
        seen = true,
        maxAltitude = 20.0,
        minAltitude = 15.0,
        extensionsFetched = false,
        suuntoTags = listOf(),
        userTags = listOf()
    )
}
