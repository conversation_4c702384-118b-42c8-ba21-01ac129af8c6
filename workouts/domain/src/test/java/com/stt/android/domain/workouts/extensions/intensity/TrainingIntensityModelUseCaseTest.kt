package com.stt.android.domain.workouts.extensions.intensity

import com.google.common.truth.Truth.assertThat
import com.soy.algorithms.intensity.WorkoutIntensity
import com.stt.android.domain.workouts.BasicWorkoutHeader
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever

@Suppress("ktlint:standard:wrapping", "ktlint:standard:argument-list-wrapping")
@RunWith(MockitoJUnitRunner::class)
class TrainingIntensityModelUseCaseTest {
    @Mock
    lateinit var workoutIntensityUseCase: WorkoutIntensityUseCase

    @Mock
    lateinit var workoutHeader: BasicWorkoutHeader

    lateinit var trainingIntensityModelUseCase: TrainingIntensityModelUseCase

    @Before
    fun setUp() {
        trainingIntensityModelUseCase = TrainingIntensityModelUseCase(workoutIntensityUseCase)
    }

    // these test examples refer to documentation in the "Intensity Distribution" tab of
    // this excel file https://amersportsonline.sharepoint.com/:x:/r/sites/Partnerstotouchpoints/_layouts/15/Doc.aspx?sourcedoc=%7B9710CDF2-3422-43AF-8AEF-315C9D0D0EE5%7D&file=Training%20Insights%20V1.xlsx&action=default&mobileredirect=true

    @Test
    fun example1() = runTest {
        whenever(workoutIntensityUseCase.calculateWorkoutIntensity(any())).thenReturn(
            WorkoutIntensity.ZONE_5,
            WorkoutIntensity.ZONE_4, WorkoutIntensity.ZONE_4,
            WorkoutIntensity.ZONE_3, WorkoutIntensity.ZONE_3, WorkoutIntensity.ZONE_3,
            WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2,
            WorkoutIntensity.ZONE_2,
            WorkoutIntensity.ZONE_1, WorkoutIntensity.ZONE_1, WorkoutIntensity.ZONE_1,
            WorkoutIntensity.ZONE_1, WorkoutIntensity.ZONE_1
        )
        assertThat(
            trainingIntensityModelUseCase.calculate(List(15) { workoutHeader }).trainingIntensityModel
        ).isEqualTo(TrainingIntensityModel.PYRAMID)
    }

    @Test
    fun example2() = runTest {
        whenever(workoutIntensityUseCase.calculateWorkoutIntensity(any())).thenReturn(
            WorkoutIntensity.ZONE_5,
            WorkoutIntensity.ZONE_4,
            WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2,
            WorkoutIntensity.ZONE_1, WorkoutIntensity.ZONE_1, WorkoutIntensity.ZONE_1,
            WorkoutIntensity.ZONE_1, WorkoutIntensity.ZONE_1, WorkoutIntensity.ZONE_1,
            WorkoutIntensity.ZONE_1, WorkoutIntensity.ZONE_1, WorkoutIntensity.ZONE_1,
            WorkoutIntensity.ZONE_1
        )
        assertThat(
            trainingIntensityModelUseCase.calculate(List(14) { workoutHeader }).trainingIntensityModel
        ).isEqualTo(TrainingIntensityModel.POLARIZED)
    }

    @Test
    fun example3() = runTest {
        whenever(workoutIntensityUseCase.calculateWorkoutIntensity(any())).thenReturn(
            WorkoutIntensity.ZONE_5,
            WorkoutIntensity.ZONE_4, WorkoutIntensity.ZONE_4,
            WorkoutIntensity.ZONE_3, WorkoutIntensity.ZONE_3, WorkoutIntensity.ZONE_3,
            WorkoutIntensity.ZONE_3, WorkoutIntensity.ZONE_3,
            WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2,
            WorkoutIntensity.ZONE_1,
        )
        assertThat(
            trainingIntensityModelUseCase.calculate(List(11) { workoutHeader }).trainingIntensityModel
        ).isEqualTo(TrainingIntensityModel.SWEETSPOT)
    }

    @Test
    fun example4() = runTest {
        whenever(workoutIntensityUseCase.calculateWorkoutIntensity(any())).thenReturn(
            WorkoutIntensity.ZONE_5,
            WorkoutIntensity.ZONE_4,
            WorkoutIntensity.ZONE_3, WorkoutIntensity.ZONE_3, WorkoutIntensity.ZONE_3,
            WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2,
            WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2,
            WorkoutIntensity.ZONE_1,
        )
        assertThat(
            trainingIntensityModelUseCase.calculate(List(11) { workoutHeader }).trainingIntensityModel
        ).isEqualTo(TrainingIntensityModel.PYRAMID)
    }

    @Test
    fun example5() = runTest {
        whenever(workoutIntensityUseCase.calculateWorkoutIntensity(any())).thenReturn(
            WorkoutIntensity.ZONE_5, WorkoutIntensity.ZONE_5, WorkoutIntensity.ZONE_5,
            WorkoutIntensity.ZONE_5,
            WorkoutIntensity.ZONE_4, WorkoutIntensity.ZONE_4, WorkoutIntensity.ZONE_4,
            WorkoutIntensity.ZONE_3, WorkoutIntensity.ZONE_3,
            WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2,
            WorkoutIntensity.ZONE_1,
        )
        assertThat(
            trainingIntensityModelUseCase.calculate(List(12) { workoutHeader }).trainingIntensityModel
        ).isEqualTo(TrainingIntensityModel.HIGH_INTENSITY)
    }

    @Test
    fun example6() = runTest {
        whenever(workoutIntensityUseCase.calculateWorkoutIntensity(any())).thenReturn(
            WorkoutIntensity.ZONE_5, WorkoutIntensity.ZONE_5, WorkoutIntensity.ZONE_5,
            WorkoutIntensity.ZONE_4, WorkoutIntensity.ZONE_4, WorkoutIntensity.ZONE_4,
            WorkoutIntensity.ZONE_3,
            WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2,
            WorkoutIntensity.ZONE_1, WorkoutIntensity.ZONE_1,
        )
        assertThat(
            trainingIntensityModelUseCase.calculate(List(11) { workoutHeader }).trainingIntensityModel
        ).isEqualTo(TrainingIntensityModel.HIGH_INTENSITY)
    }

    @Test
    fun example7() = runTest {
        whenever(workoutIntensityUseCase.calculateWorkoutIntensity(any())).thenReturn(
            WorkoutIntensity.ZONE_5,
            WorkoutIntensity.ZONE_4, WorkoutIntensity.ZONE_4, WorkoutIntensity.ZONE_4,
            WorkoutIntensity.ZONE_4, WorkoutIntensity.ZONE_4,
            WorkoutIntensity.ZONE_3, WorkoutIntensity.ZONE_3,
            WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2,
            WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2,
            WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2,
            WorkoutIntensity.ZONE_1, WorkoutIntensity.ZONE_1, WorkoutIntensity.ZONE_1,
        )
        assertThat(
            trainingIntensityModelUseCase.calculate(List(19) { workoutHeader }).trainingIntensityModel
        ).isEqualTo(TrainingIntensityModel.UNIDENTIFIED)
    }

    @Test
    fun example8() = runTest {
        whenever(workoutIntensityUseCase.calculateWorkoutIntensity(any())).thenReturn(
            WorkoutIntensity.ZONE_4,
            WorkoutIntensity.ZONE_3,
            WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2,
            WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2,
            WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2,
            WorkoutIntensity.ZONE_1, WorkoutIntensity.ZONE_1, WorkoutIntensity.ZONE_1,
            WorkoutIntensity.ZONE_1,
        )
        assertThat(
            trainingIntensityModelUseCase.calculate(List(14) { workoutHeader }).trainingIntensityModel
        ).isEqualTo(TrainingIntensityModel.BASE_ENDURANCE)
    }

    @Test
    fun example9() = runTest {
        whenever(workoutIntensityUseCase.calculateWorkoutIntensity(any())).thenReturn(
            WorkoutIntensity.ZONE_5,
            WorkoutIntensity.ZONE_4,
            WorkoutIntensity.ZONE_3,
            WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2,
            WorkoutIntensity.ZONE_1, WorkoutIntensity.ZONE_1,
        )
        assertThat(
            trainingIntensityModelUseCase.calculate(List(7) { workoutHeader }).trainingIntensityModel
        ).isEqualTo(TrainingIntensityModel.UNIDENTIFIED)
    }

    @Test
    fun example10() = runTest {
        whenever(workoutIntensityUseCase.calculateWorkoutIntensity(any())).thenReturn(
            WorkoutIntensity.ZONE_5, WorkoutIntensity.ZONE_5,
            WorkoutIntensity.ZONE_4, WorkoutIntensity.ZONE_4, WorkoutIntensity.ZONE_4,
            WorkoutIntensity.ZONE_4,
            WorkoutIntensity.ZONE_3, WorkoutIntensity.ZONE_3, WorkoutIntensity.ZONE_3,
            WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2,
            WorkoutIntensity.ZONE_1,
        )
        assertThat(
            trainingIntensityModelUseCase.calculate(List(12) { workoutHeader }).trainingIntensityModel
        ).isEqualTo(TrainingIntensityModel.HIGH_INTENSITY)
    }

    @Test
    fun example11() = runTest {
        whenever(workoutIntensityUseCase.calculateWorkoutIntensity(any())).thenReturn(
            WorkoutIntensity.ZONE_5,
            WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2,
            WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2,
            WorkoutIntensity.ZONE_1, WorkoutIntensity.ZONE_1, WorkoutIntensity.ZONE_1,
            WorkoutIntensity.ZONE_1, WorkoutIntensity.ZONE_1,
        )
        assertThat(
            trainingIntensityModelUseCase.calculate(List(12) { workoutHeader }).trainingIntensityModel
        ).isEqualTo(TrainingIntensityModel.BASE_ENDURANCE)
    }

    @Test
    fun example12() = runTest {
        whenever(workoutIntensityUseCase.calculateWorkoutIntensity(any())).thenReturn(
            WorkoutIntensity.ZONE_4,
            WorkoutIntensity.ZONE_3, WorkoutIntensity.ZONE_3,
            WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2,
            WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2,
            WorkoutIntensity.ZONE_1, WorkoutIntensity.ZONE_1, WorkoutIntensity.ZONE_1,
            WorkoutIntensity.ZONE_1, WorkoutIntensity.ZONE_1, WorkoutIntensity.ZONE_1,
        )
        assertThat(
            trainingIntensityModelUseCase.calculate(List(14) { workoutHeader }).trainingIntensityModel
        ).isEqualTo(TrainingIntensityModel.PYRAMID)
    }

    @Test
    fun example13() = runTest {
        whenever(workoutIntensityUseCase.calculateWorkoutIntensity(any())).thenReturn(
            WorkoutIntensity.ZONE_5,
            WorkoutIntensity.ZONE_4,
            WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2, WorkoutIntensity.ZONE_2,
            WorkoutIntensity.ZONE_1, WorkoutIntensity.ZONE_1,
        )
        assertThat(
            trainingIntensityModelUseCase.calculate(List(7) { workoutHeader }).trainingIntensityModel
        ).isEqualTo(TrainingIntensityModel.POLARIZED)
    }
}
