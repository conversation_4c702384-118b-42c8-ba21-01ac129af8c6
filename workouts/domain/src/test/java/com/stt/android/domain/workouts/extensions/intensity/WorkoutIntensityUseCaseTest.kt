package com.stt.android.domain.workouts.extensions.intensity

import com.google.common.truth.Truth.assertThat
import com.soy.algorithms.intensity.IntensityZones
import com.soy.algorithms.intensity.IntensityZonesData
import com.soy.algorithms.intensity.WorkoutIntensity
import com.stt.android.domain.user.workoutextension.IntensityExtension
import com.stt.android.domain.workouts.BasicWorkoutHeader
import com.stt.android.domain.workouts.extensions.FakeExtensionsDataSource
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.whenever

@RunWith(MockitoJUnitRunner::class)
class WorkoutIntensityUseCaseTest {

    private val fakeExtensionsDataSource = FakeExtensionsDataSource()

    @Mock
    lateinit var mockWorkoutHeader: BasicWorkoutHeader

    lateinit var workoutIntensityUseCase: WorkoutIntensityUseCase

    @Before
    fun setUp() {
        workoutIntensityUseCase =
            WorkoutIntensityUseCase(GetIntensityExtensionUseCase(fakeExtensionsDataSource))
    }

    // these test examples refer to documentation in the "Workout Intensity" tab of
    // this excel file https://amersportsonline.sharepoint.com/:x:/r/sites/Partnerstotouchpoints/_layouts/15/Doc.aspx?sourcedoc=%7B9710CDF2-3422-43AF-8AEF-315C9D0D0EE5%7D&file=Training%20Insights%20V1.xlsx&action=default&mobileredirect=true

    @Test
    fun nullIntensityCase() = runTest {
        fakeExtensionsDataSource.extensions.clear()
        whenever(mockWorkoutHeader.id).thenReturn(1)
        assertThat(workoutIntensityUseCase.calculateWorkoutIntensity(mockWorkoutHeader))
            .isNull()
    }

    @Test
    fun emptyIntensityCase() = runTest {
        fakeExtensionsDataSource.extensions[1] = listOf(emptyExample)
        whenever(mockWorkoutHeader.id).thenReturn(1)
        assertThat(workoutIntensityUseCase.calculateWorkoutIntensity(mockWorkoutHeader))
            .isNull()
    }

    @Test
    fun hrOnlyExample1() = runTest {
        fakeExtensionsDataSource.extensions[1] = listOf(hrOnlyExample1)
        whenever(mockWorkoutHeader.id).thenReturn(1)
        assertThat(workoutIntensityUseCase.calculateWorkoutIntensity(mockWorkoutHeader))
            .isEqualTo(WorkoutIntensity.ZONE_4)
    }

    @Test
    fun hrOnlyExample4() = runTest {
        fakeExtensionsDataSource.extensions[1] = listOf(hrOnlyExample4)
        whenever(mockWorkoutHeader.id).thenReturn(1)
        assertThat(workoutIntensityUseCase.calculateWorkoutIntensity(mockWorkoutHeader))
            .isEqualTo(WorkoutIntensity.ZONE_2)
    }

    @Test
    fun hrAndPaceExample1() = runTest {
        fakeExtensionsDataSource.extensions[1] = listOf(hrAndPaceExample1)
        whenever(mockWorkoutHeader.id).thenReturn(1)
        assertThat(workoutIntensityUseCase.calculateWorkoutIntensity(mockWorkoutHeader))
            .isEqualTo(WorkoutIntensity.ZONE_5)
    }

    @Test
    fun hrAndPowerExample1() = runTest {
        fakeExtensionsDataSource.extensions[1] = listOf(hrAndPowerExample1)
        whenever(mockWorkoutHeader.id).thenReturn(1)
        assertThat(workoutIntensityUseCase.calculateWorkoutIntensity(mockWorkoutHeader))
            .isEqualTo(WorkoutIntensity.ZONE_3)
    }

    private val emptyExample = IntensityExtension(
        workoutId = 1,
        intensityZones = IntensityZones(
            hr = IntensityZonesData(
                /* zone1Duration = */
                0f,
                /* zone2Duration = */
                0f,
                /* zone3Duration = */
                0f,
                /* zone4Duration = */
                0f,
                /* zone5Duration = */
                0f,
                /* zone2LowerLimit = */
                0f,
                /* zone3LowerLimit = */
                0f,
                /* zone4LowerLimit = */
                0f,
                /* zone5LowerLimit = */
                0f,
            ),
            speed = null,
            power = null,
        ),
    )

    private val hrOnlyExample1 = IntensityExtension(
        workoutId = 1,
        intensityZones = IntensityZones(
            hr = IntensityZonesData(
                /* zone1Duration = */
                10 * 60f,
                /* zone2Duration = */
                10 * 60f,
                /* zone3Duration = */
                12 * 60f,
                /* zone4Duration = */
                8 * 60f,
                /* zone5Duration = */
                9 * 60f,
                /* zone2LowerLimit = */
                0f,
                /* zone3LowerLimit = */
                0f,
                /* zone4LowerLimit = */
                0f,
                /* zone5LowerLimit = */
                0f,
            ),
            speed = null,
            power = null,
        ),
    )

    private val hrOnlyExample4 = IntensityExtension(
        workoutId = 1,
        intensityZones = IntensityZones(
            hr = IntensityZonesData(
                /* zone1Duration = */
                57 * 60f,
                /* zone2Duration = */
                38 * 60f,
                /* zone3Duration = */
                1 * 60f,
                /* zone4Duration = */
                0 * 60f,
                /* zone5Duration = */
                0 * 60f,
                /* zone2LowerLimit = */
                0f,
                /* zone3LowerLimit = */
                0f,
                /* zone4LowerLimit = */
                0f,
                /* zone5LowerLimit = */
                0f,
            ),
            speed = null,
            power = null,
        ),
    )

    private val hrAndPaceExample1 = IntensityExtension(
        workoutId = 1,
        intensityZones = IntensityZones(
            hr = IntensityZonesData(
                /* zone1Duration = */
                16 * 60f,
                /* zone2Duration = */
                25 * 60f,
                /* zone3Duration = */
                7 * 60f,
                /* zone4Duration = */
                13 * 60f,
                /* zone5Duration = */
                1 * 60f,
                /* zone2LowerLimit = */
                0f,
                /* zone3LowerLimit = */
                0f,
                /* zone4LowerLimit = */
                0f,
                /* zone5LowerLimit = */
                0f,
            ),
            speed = IntensityZonesData(
                /* zone1Duration = */
                16 * 60f,
                /* zone2Duration = */
                14 * 60f,
                /* zone3Duration = */
                9 * 60f,
                /* zone4Duration = */
                4 * 60f,
                /* zone5Duration = */
                15 * 60f,
                /* zone2LowerLimit = */
                0f,
                /* zone3LowerLimit = */
                0f,
                /* zone4LowerLimit = */
                0f,
                /* zone5LowerLimit = */
                0f,
            ),
            power = null,
        ),
    )

    private val hrAndPowerExample1 = IntensityExtension(
        workoutId = 1,
        intensityZones = IntensityZones(
            hr = IntensityZonesData(
                /* zone1Duration = */
                156 * 60f,
                /* zone2Duration = */
                77 * 60f,
                /* zone3Duration = */
                30 * 60f,
                /* zone4Duration = */
                0 * 60f,
                /* zone5Duration = */
                0 * 60f,
                /* zone2LowerLimit = */
                0f,
                /* zone3LowerLimit = */
                0f,
                /* zone4LowerLimit = */
                0f,
                /* zone5LowerLimit = */
                0f,
            ),
            speed = null,
            power = IntensityZonesData(
                /* zone1Duration = */
                38 * 60f,
                /* zone2Duration = */
                126 * 60f,
                /* zone3Duration = */
                87 * 60f,
                /* zone4Duration = */
                15 * 60f,
                /* zone5Duration = */
                2 * 60f,
                /* zone2LowerLimit = */
                0f,
                /* zone3LowerLimit = */
                0f,
                /* zone4LowerLimit = */
                0f,
                /* zone5LowerLimit = */
                0f,
            )
        ),
    )
}
