package com.stt.android.domain.workouts

import android.content.SharedPreferences
import com.stt.android.data.workout.FakeWorkoutHeaderDataSource
import com.stt.android.domain.comments.WorkoutCommentDataSource
import com.stt.android.domain.ranking.RankingDataSource
import com.stt.android.domain.tags.UserTagsRepository
import com.stt.android.domain.workout.WorkoutEntityFactory
import com.stt.android.domain.workouts.extensions.ExtensionsDataSource
import com.stt.android.domain.workouts.pictures.PicturesDataSource
import com.stt.android.domain.workouts.reactions.ReactionSummaryDataSource
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.domain.workouts.tag.TagConstants
import com.stt.android.domain.workouts.videos.VideoDataSource
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.never
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

@OptIn(ExperimentalCoroutinesApi::class)
@RunWith(MockitoJUnitRunner::class)
class AbstractFetchAndStoreWorkoutsTest {
    private val workoutHeaderDataSource = FakeWorkoutHeaderDataSource()

    @Mock
    private lateinit var picturesDataSource: PicturesDataSource

    @Mock
    private lateinit var videoDataSource: VideoDataSource

    @Mock
    private lateinit var extensionsDataSource: ExtensionsDataSource

    @Mock
    private lateinit var reactionDataSource: ReactionSummaryDataSource

    @Mock
    private lateinit var rankingDataSource: RankingDataSource

    @Mock
    private lateinit var workoutCommentDataSource: WorkoutCommentDataSource

    @Mock
    private lateinit var sharedPreferences: SharedPreferences

    @Mock
    private lateinit var sharedPreferencesEditor: SharedPreferences.Editor

    @Mock
    private lateinit var userTagsRepository: UserTagsRepository

    private lateinit var fetchAndStoreWorkouts: FakeFetchAndStoreWorkouts

    @Before
    fun setUp() {
        fetchAndStoreWorkouts = FakeFetchAndStoreWorkouts(
            picturesDataSource,
            videoDataSource,
            reactionDataSource,
            rankingDataSource,
            workoutHeaderDataSource,
            workoutCommentDataSource,
            extensionsDataSource,
            sharedPreferences,
            userTagsRepository
        )
    }

    @Test
    fun `store workout should update existing one`() = runTest {
        val localWorkoutHeader = WorkoutEntityFactory.createDomainWorkoutHeader()
            .copy(seen = true)
        workoutHeaderDataSource.storeWorkout(localWorkoutHeader)

        val remoteWorkout = DomainWorkout(
            header = WorkoutEntityFactory.createDomainWorkoutHeader()
        )

        fetchAndStoreWorkouts.store(remoteWorkout)

        val storedWorkoutHeader = workoutHeaderDataSource.findByKey(remoteWorkout.header.key!!)
        assertThat(storedWorkoutHeader?.id).isEqualTo(localWorkoutHeader.id)
    }

    @Test
    fun `store workout should insert new one with valid workout ID`() = runTest {
        val remoteWorkout = DomainWorkout(
            header = WorkoutEntityFactory.createDomainWorkoutHeader()
        )

        fetchAndStoreWorkouts.store(remoteWorkout)

        val storedWorkoutHeader = workoutHeaderDataSource.findByKey(remoteWorkout.header.key!!)
        assertThat(storedWorkoutHeader?.id).isEqualTo(remoteWorkout.header.id)
    }

    @Test
    fun `Update shared pref if a workout is auto tagged from backend`() = runTest {
        whenever(sharedPreferences.edit()).thenReturn(sharedPreferencesEditor)
        whenever(
            sharedPreferences.getBoolean(
                TagConstants.HAS_SHOWN_AUTO_TAGGED_DIALOG,
                false
            )
        ).thenReturn(false)

        val localWorkoutHeader =
            WorkoutEntityFactory.createDomainWorkoutHeader()
        workoutHeaderDataSource.storeWorkout(localWorkoutHeader)

        val remoteWorkout = DomainWorkout(
            header = localWorkoutHeader.copy(suuntoTags = listOf(SuuntoTag.COMMUTE))
        )

        fetchAndStoreWorkouts.store(remoteWorkout)

        verify(sharedPreferences.edit()).putString(
            TagConstants.AUTO_TAGGED_WORKOUT_KEY,
            remoteWorkout.header.key
        )
    }

    @Test
    fun `Only the latest workout should be saved in prefs in auto tagging`() = runTest {
        whenever(sharedPreferences.edit()).thenReturn(sharedPreferencesEditor)
        whenever(
            sharedPreferences.getBoolean(
                TagConstants.HAS_SHOWN_AUTO_TAGGED_DIALOG,
                false
            )
        ).thenReturn(false)

        val localWorkoutHeader1 =
            WorkoutEntityFactory.createDomainWorkoutHeader().copy(startTime = 10)
        val localWorkoutHeader2 =
            WorkoutEntityFactory.createDomainWorkoutHeader()
                .copy(
                    id = localWorkoutHeader1.id + 1,
                    key = "key2",
                    startTime = localWorkoutHeader1.startTime + 10
                )
        val localWorkoutHeader3 =
            WorkoutEntityFactory.createDomainWorkoutHeader()
                .copy(
                    id = localWorkoutHeader2.id + 1,
                    key = "key3",
                    startTime = localWorkoutHeader2.startTime - 1
                )
        workoutHeaderDataSource.storeWorkout(localWorkoutHeader1)
        workoutHeaderDataSource.storeWorkout(localWorkoutHeader2)
        workoutHeaderDataSource.storeWorkout(localWorkoutHeader3)

        val remoteWorkout1 = DomainWorkout(
            header = localWorkoutHeader1.copy(suuntoTags = listOf(SuuntoTag.COMMUTE))
        )
        val remoteWorkout2 = DomainWorkout(
            header = localWorkoutHeader2.copy(suuntoTags = listOf(SuuntoTag.COMMUTE))
        )
        val remoteWorkout3 = DomainWorkout(
            header = localWorkoutHeader3.copy(suuntoTags = listOf(SuuntoTag.COMMUTE))
        )

        fetchAndStoreWorkouts.storeMultiple(listOf(remoteWorkout1, remoteWorkout2, remoteWorkout3))

        verify(sharedPreferences.edit()).putString(
            TagConstants.AUTO_TAGGED_WORKOUT_KEY,
            remoteWorkout1.header.key
        )
        verify(sharedPreferences.edit()).putString(
            TagConstants.AUTO_TAGGED_WORKOUT_KEY,
            remoteWorkout2.header.key
        )
        // Workout 2 is before workout 3, workout 3 key shouldn't be saved
        verify(sharedPreferences.edit(), never()).putString(
            TagConstants.AUTO_TAGGED_WORKOUT_KEY,
            remoteWorkout3.header.key
        )
    }

    class FakeFetchAndStoreWorkouts(
        picturesDataSource: PicturesDataSource,
        videoDataSource: VideoDataSource,
        reactionDataSource: ReactionSummaryDataSource,
        rankingDataSource: RankingDataSource,
        workoutHeaderDataSource: WorkoutHeaderDataSource,
        workoutCommentDataSource: WorkoutCommentDataSource,
        extensionsDataSource: ExtensionsDataSource,
        sharedPreferences: SharedPreferences,
        userTagsRepository: UserTagsRepository
    ) : AbstractFetchAndStoreWorkouts(
        picturesDataSource,
        videoDataSource,
        reactionDataSource,
        rankingDataSource,
        workoutHeaderDataSource,
        workoutCommentDataSource,
        extensionsDataSource,
        sharedPreferences,
        userTagsRepository
    ) {
        suspend fun store(workout: DomainWorkout) {
            super.store(listOf(workout), true)
        }

        suspend fun storeMultiple(workouts: List<DomainWorkout>) {
            super.store(workouts, true)
        }
    }
}
