package com.stt.android.domain.workouts.attributes

import com.soy.algorithms.tss.TSSCalculationMethod
import com.stt.android.domain.workouts.tss.TSS
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

@OptIn(ExperimentalCoroutinesApi::class)
@RunWith(MockitoJUnitRunner::class)
class DeleteWorkoutAttributesUpdateUseCaseTest {
    @Mock
    private lateinit var dataSource: WorkoutAttributesUpdateDataSource

    private lateinit var useCase: DeleteWorkoutAttributesUpdateUseCase

    @Before
    fun setup() {
        useCase = DeleteWorkoutAttributesUpdateUseCase(dataSource)
    }

    @Test
    fun `inserts new location deletion to dataSource`() = runTest {
        whenever(dataSource.fetchUnsyncedWorkoutAttributesUpdate(any(), any())).thenReturn(null)

        useCase(useCase.getDeleteStartPositionParams(1, "username"))

        verify(dataSource, times(1)).addWorkoutAttributesUpdate(
            DomainWorkoutAttributesUpdate(
                1,
                "username",
                DomainWorkoutAttributes(null, null, null, null, null, null),
                listOf("startPosition"),
                false
            )
        )
    }

    @Test
    fun `updates pending update with location deletion to dataSource`() = runTest {
        val pendingUpdate = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(
                DomainWorkoutLocation(10.0, 10.0),
                TSS(20f, TSSCalculationMethod.MANUAL),
                null,
                null,
                null,
                null,
            ),
            listOf("dummy"),
            false
        )
        whenever(
            dataSource.fetchUnsyncedWorkoutAttributesUpdate(
                pendingUpdate.workoutId,
                pendingUpdate.ownerUsername
            )
        ).thenReturn(pendingUpdate)

        useCase(useCase.getDeleteStartPositionParams(1, "username"))

        verify(dataSource, times(1)).addWorkoutAttributesUpdate(
            DomainWorkoutAttributesUpdate(
                1,
                "username",
                DomainWorkoutAttributes(null, pendingUpdate.tss, null, null, null, null),
                listOf("dummy", "startPosition"),
                false
            )
        )
    }

    @Test
    fun `inserts new TSS deletion to dataSource`() = runTest {
        whenever(dataSource.fetchUnsyncedWorkoutAttributesUpdate(any(), any())).thenReturn(null)

        useCase(useCase.getDeleteTssParams(1, "username"))

        verify(dataSource, times(1)).addWorkoutAttributesUpdate(
            DomainWorkoutAttributesUpdate(
                1,
                "username",
                DomainWorkoutAttributes(null, null, null, null, null, null),
                listOf("tss"),
                false
            )
        )
    }

    @Test
    fun `updates pending update with TSS deletion to dataSource`() = runTest {
        val pendingUpdate = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(
                DomainWorkoutLocation(10.0, 10.0),
                TSS(20f, TSSCalculationMethod.HR),
                null,
                null,
                null,
                null,
            ),
            listOf("dummy"),
            false
        )
        whenever(
            dataSource.fetchUnsyncedWorkoutAttributesUpdate(
                pendingUpdate.workoutId,
                pendingUpdate.ownerUsername
            )
        ).thenReturn(pendingUpdate)

        useCase(useCase.getDeleteTssParams(1, "username"))

        verify(dataSource, times(1)).addWorkoutAttributesUpdate(
            DomainWorkoutAttributesUpdate(
                1,
                "username",
                DomainWorkoutAttributes(pendingUpdate.workoutLocation, null, null, null, null, null),
                listOf("dummy", "tss"),
                false
            )
        )
    }

    @Test
    fun `inserts new maxSpeed deletion to dataSource`() = runTest {
        whenever(dataSource.fetchUnsyncedWorkoutAttributesUpdate(any(), any())).thenReturn(null)

        useCase(useCase.getDeleteMaxSpeedParams(1, "username"))

        verify(dataSource, times(1)).addWorkoutAttributesUpdate(
            DomainWorkoutAttributesUpdate(
                1,
                "username",
                DomainWorkoutAttributes(null, null, null, null, null, null),
                listOf("maxSpeed"),
                false
            )
        )
    }

    @Test
    fun `updates pending update with maxSpeed deletion to dataSource`() = runTest {
        val pendingUpdate = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(
                DomainWorkoutLocation(10.0, 10.0),
                TSS(20f, TSSCalculationMethod.HR),
                12.7,
                null,
                null,
                null,
            ),
            listOf("dummy"),
            false
        )
        whenever(
            dataSource.fetchUnsyncedWorkoutAttributesUpdate(
                pendingUpdate.workoutId,
                pendingUpdate.ownerUsername
            )
        ).thenReturn(pendingUpdate)

        useCase(useCase.getDeleteMaxSpeedParams(1, "username"))

        verify(dataSource, times(1)).addWorkoutAttributesUpdate(
            DomainWorkoutAttributesUpdate(
                1,
                "username",
                DomainWorkoutAttributes(pendingUpdate.workoutLocation, pendingUpdate.tss, null, null, null, null),
                listOf("dummy", "maxSpeed"),
                false
            )
        )
    }

    @Test
    fun `inserts new ascent deletion to dataSource`() = runTest {
        whenever(dataSource.fetchUnsyncedWorkoutAttributesUpdate(any(), any())).thenReturn(null)

        useCase(useCase.getDeleteTotalAscentParams(1, "username"))

        verify(dataSource, times(1)).addWorkoutAttributesUpdate(
            DomainWorkoutAttributesUpdate(
                1,
                "username",
                DomainWorkoutAttributes(null, null, null, null, null, null),
                listOf("totalAscent"),
                false
            )
        )
    }

    @Test
    fun `updates pending update with ascent deletion to dataSource`() = runTest {
        val pendingUpdate = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(
                null,
                null,
                12.7,
                42.0,
                null,
                null,
            ),
            listOf("dummy"),
            false
        )
        whenever(
            dataSource.fetchUnsyncedWorkoutAttributesUpdate(
                pendingUpdate.workoutId,
                pendingUpdate.ownerUsername
            )
        ).thenReturn(pendingUpdate)

        useCase(useCase.getDeleteTotalAscentParams(1, "username"))

        verify(dataSource, times(1)).addWorkoutAttributesUpdate(
            DomainWorkoutAttributesUpdate(
                1,
                "username",
                DomainWorkoutAttributes(null, null, pendingUpdate.maxSpeed, null, null, null),
                listOf("dummy", "totalAscent"),
                false
            )
        )
    }

    @Test
    fun `inserts new descent deletion to dataSource`() = runTest {
        whenever(dataSource.fetchUnsyncedWorkoutAttributesUpdate(any(), any())).thenReturn(null)

        useCase(useCase.getDeleteTotalDescentParams(1, "username"))

        verify(dataSource, times(1)).addWorkoutAttributesUpdate(
            DomainWorkoutAttributesUpdate(
                1,
                "username",
                DomainWorkoutAttributes(null, null, null, null, null, null),
                listOf("totalDescent"),
                false
            )
        )
    }

    @Test
    fun `updates pending update with descent deletion to dataSource`() = runTest {
        val pendingUpdate = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(
                null,
                null,
                null,
                42.0,
                24.0,
                null
            ),
            listOf("dummy"),
            false
        )
        whenever(
            dataSource.fetchUnsyncedWorkoutAttributesUpdate(
                pendingUpdate.workoutId,
                pendingUpdate.ownerUsername
            )
        ).thenReturn(pendingUpdate)

        useCase(useCase.getDeleteTotalDescentParams(1, "username"))

        verify(dataSource, times(1)).addWorkoutAttributesUpdate(
            DomainWorkoutAttributesUpdate(
                1,
                "username",
                DomainWorkoutAttributes(null, null, null, pendingUpdate.totalAscent, null, null),
                listOf("dummy", "totalDescent"),
                false
            )
        )
    }
}
