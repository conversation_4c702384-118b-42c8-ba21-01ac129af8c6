package com.stt.android.domain.sml

import com.stt.android.domain.workouts.extensions.SMLExtension
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

@RunWith(MockitoJUnitRunner::class)
class FetchSmlUseCaseTest {
    @Mock
    private lateinit var smlExtensionUseCase: SmlExtensionUseCase
    private lateinit var fetchSmlUseCase: FetchSmlUseCase

    @Before
    fun setup() {
        fetchSmlUseCase = FetchSmlUseCase(smlExtensionUseCase)
    }

    @Test
    fun `should access fetchSmlExtensionUseCase`() = runTest {
        val smlExtension = SMLExtension(1, null)
        whenever(smlExtensionUseCase.fetchSmlExtension(1, "key"))
            .thenReturn(smlExtension)

        fetchSmlUseCase.fetchSml(1, "key")

        verify(smlExtensionUseCase).fetchSmlExtension(1, "key")
    }
}
