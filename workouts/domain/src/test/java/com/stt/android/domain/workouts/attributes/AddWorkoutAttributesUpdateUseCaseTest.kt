package com.stt.android.domain.workouts.attributes

import com.soy.algorithms.tss.TSSCalculationMethod
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.domain.workouts.tss.TSS
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

@OptIn(ExperimentalCoroutinesApi::class)
@RunWith(MockitoJUnitRunner::class)
class AddWorkoutAttributesUpdateUseCaseTest {

    @Mock
    private lateinit var dataSource: WorkoutAttributesUpdateDataSource

    private lateinit var useCase: AddWorkoutAttributesUpdateUseCase

    @Before
    fun setup() {
        useCase = AddWorkoutAttributesUpdateUseCase(dataSource)
    }

    @Test
    fun `inserts new location update to dataSource`() = runTest {
        whenever(dataSource.fetchUnsyncedWorkoutAttributesUpdate(any(), any())).thenReturn(null)

        useCase(useCase.getLocationUpdateParams(1, "username", 10.0, 10.0))

        verify(dataSource, times(1)).addWorkoutAttributesUpdate(
            DomainWorkoutAttributesUpdate(
                1,
                "username",
                DomainWorkoutAttributes(DomainWorkoutLocation(10.0, 10.0), null, null, null, null, null),
                emptyList(),
                false
            )
        )
    }

    @Test
    fun `updates pending update with location update to dataSource`() = runTest {
        val pendingUpdate = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(
                DomainWorkoutLocation(10.0, 10.0),
                TSS(20f, TSSCalculationMethod.MANUAL),
                null,
                null,
                null,
                null
            ),
            listOf("dummy"),
            false
        )
        whenever(
            dataSource.fetchUnsyncedWorkoutAttributesUpdate(
                pendingUpdate.workoutId,
                pendingUpdate.ownerUsername
            )
        ).thenReturn(pendingUpdate)

        useCase(useCase.getLocationUpdateParams(1, "username", 20.0, 20.0))

        verify(dataSource, times(1)).addWorkoutAttributesUpdate(
            DomainWorkoutAttributesUpdate(
                1,
                "username",
                DomainWorkoutAttributes(DomainWorkoutLocation(20.0, 20.0), pendingUpdate.tss, null, null, null, null),
                pendingUpdate.fieldsToDelete,
                false
            )
        )
    }

    @Test
    fun `inserts new TSS update to dataSource`() = runTest {
        whenever(dataSource.fetchUnsyncedWorkoutAttributesUpdate(any(), any())).thenReturn(null)

        useCase(useCase.getTssUpdateParams(1, "username", TSS(50f, TSSCalculationMethod.MANUAL)))

        verify(dataSource, times(1)).addWorkoutAttributesUpdate(
            DomainWorkoutAttributesUpdate(
                1,
                "username",
                DomainWorkoutAttributes(
                    null,
                    TSS(50f, TSSCalculationMethod.MANUAL),
                    null,
                    null,
                    null,
                    null
                ),
                emptyList(),
                false
            )
        )
    }

    @Test
    fun `updates pending update with TSS update to dataSource`() = runTest {
        val pendingUpdate = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(
                DomainWorkoutLocation(10.0, 10.0),
                TSS(20f, TSSCalculationMethod.HR),
                null,
                null,
                null,
                null
            ),
            listOf("dummy"),
            false
        )
        whenever(
            dataSource.fetchUnsyncedWorkoutAttributesUpdate(
                pendingUpdate.workoutId,
                pendingUpdate.ownerUsername
            )
        ).thenReturn(pendingUpdate)

        useCase(useCase.getTssUpdateParams(1, "username", TSS(60f, TSSCalculationMethod.MANUAL)))

        verify(dataSource, times(1)).addWorkoutAttributesUpdate(
            DomainWorkoutAttributesUpdate(
                1,
                "username",
                DomainWorkoutAttributes(
                    pendingUpdate.workoutLocation,
                    TSS(60f, TSSCalculationMethod.MANUAL),
                    null,
                    null,
                    null,
                    null
                ),
                pendingUpdate.fieldsToDelete,
                false
            )
        )
    }

    @Test
    fun `inserts new maxSpeed update to dataSource`() = runTest {
        whenever(dataSource.fetchUnsyncedWorkoutAttributesUpdate(any(), any())).thenReturn(null)

        useCase(useCase.getMaxSpeedUpdateParams(1, "username", 12.7))

        verify(dataSource, times(1)).addWorkoutAttributesUpdate(
            DomainWorkoutAttributesUpdate(
                1,
                "username",
                DomainWorkoutAttributes(
                    null,
                    null,
                    12.7,
                    null,
                    null,
                    null
                ),
                emptyList(),
                false
            )
        )
    }

    @Test
    fun `updates pending update with maxSpeed update to dataSource`() = runTest {
        val pendingUpdate = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(
                DomainWorkoutLocation(10.0, 10.0),
                null,
                12.7,
                null,
                null,
                null,
            ),
            listOf("dummy"),
            false
        )
        whenever(
            dataSource.fetchUnsyncedWorkoutAttributesUpdate(
                pendingUpdate.workoutId,
                pendingUpdate.ownerUsername
            )
        ).thenReturn(pendingUpdate)

        useCase(useCase.getMaxSpeedUpdateParams(1, "username", 20.0))

        verify(dataSource, times(1)).addWorkoutAttributesUpdate(
            DomainWorkoutAttributesUpdate(
                1,
                "username",
                DomainWorkoutAttributes(
                    pendingUpdate.workoutLocation,
                    null,
                    20.0,
                    null,
                    null,
                    null,
                ),
                pendingUpdate.fieldsToDelete,
                false
            )
        )
    }

    @Test
    fun `inserts new totalAscent update to dataSource`() = runTest {
        whenever(dataSource.fetchUnsyncedWorkoutAttributesUpdate(any(), any())).thenReturn(null)

        useCase(useCase.getTotalAscentUpdateParams(1, "username", 42.0))

        verify(dataSource, times(1)).addWorkoutAttributesUpdate(
            DomainWorkoutAttributesUpdate(
                1,
                "username",
                DomainWorkoutAttributes(
                    null,
                    null,
                    null,
                    42.0,
                    null,
                    null,
                ),
                emptyList(),
                false
            )
        )
    }

    @Test
    fun `updates pending update with totalAscent update to dataSource`() = runTest {
        val pendingUpdate = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(
                DomainWorkoutLocation(10.0, 10.0),
                null,
                12.7,
                42.0,
                null,
                null,
            ),
            listOf("dummy"),
            false
        )
        whenever(
            dataSource.fetchUnsyncedWorkoutAttributesUpdate(
                pendingUpdate.workoutId,
                pendingUpdate.ownerUsername
            )
        ).thenReturn(pendingUpdate)

        useCase(useCase.getTotalAscentUpdateParams(1, "username", 20.0))

        verify(dataSource, times(1)).addWorkoutAttributesUpdate(
            DomainWorkoutAttributesUpdate(
                1,
                "username",
                DomainWorkoutAttributes(
                    pendingUpdate.workoutLocation,
                    null,
                    pendingUpdate.maxSpeed,
                    20.0,
                    null,
                    null,
                ),
                pendingUpdate.fieldsToDelete,
                false
            )
        )
    }

    @Test
    fun `inserts new totalDescent update to dataSource`() = runTest {
        whenever(dataSource.fetchUnsyncedWorkoutAttributesUpdate(any(), any())).thenReturn(null)

        useCase(useCase.getTotalDescentUpdateParams(1, "username", 12.0))

        verify(dataSource, times(1)).addWorkoutAttributesUpdate(
            DomainWorkoutAttributesUpdate(
                1,
                "username",
                DomainWorkoutAttributes(
                    null,
                    null,
                    null,
                    null,
                    12.0,
                    null
                ),
                emptyList(),
                false
            )
        )
    }

    @Test
    fun `updates pending update with totalDescent update to dataSource`() = runTest {
        val pendingUpdate = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(
                DomainWorkoutLocation(10.0, 10.0),
                null,
                null,
                42.0,
                null,
                null
            ),
            listOf("dummy"),
            false
        )
        whenever(
            dataSource.fetchUnsyncedWorkoutAttributesUpdate(
                pendingUpdate.workoutId,
                pendingUpdate.ownerUsername
            )
        ).thenReturn(pendingUpdate)

        useCase(useCase.getTotalDescentUpdateParams(1, "username", 20.0))

        verify(dataSource, times(1)).addWorkoutAttributesUpdate(
            DomainWorkoutAttributesUpdate(
                1,
                "username",
                DomainWorkoutAttributes(
                    pendingUpdate.workoutLocation,
                    null,
                    null,
                    pendingUpdate.totalAscent,
                    20.0,
                    null
                ),
                pendingUpdate.fieldsToDelete,
                false
            )
        )
    }

    @Test
    fun `updates pending update with suuntoTags update to dataSource`() = runTest {
        val pendingUpdate = DomainWorkoutAttributesUpdate(
            1,
            "username",
            DomainWorkoutAttributes(
                DomainWorkoutLocation(10.0, 10.0),
                null,
                null,
                null,
                null,
                suuntoTags = listOf(SuuntoTag.COMMUTE)
            ),
            listOf("dummy"),
            false
        )
        whenever(
            dataSource.fetchUnsyncedWorkoutAttributesUpdate(
                pendingUpdate.workoutId,
                pendingUpdate.ownerUsername
            )
        ).thenReturn(pendingUpdate)

        useCase(useCase.getSuuntoTagsUpdateParams(1, "username", listOf()))

        verify(dataSource, times(1)).addWorkoutAttributesUpdate(
            DomainWorkoutAttributesUpdate(
                1,
                "username",
                DomainWorkoutAttributes(
                    pendingUpdate.workoutLocation,
                    null,
                    null,
                    null,
                    null,
                    listOf()
                ),
                pendingUpdate.fieldsToDelete,
                false
            )
        )
    }

    @Test
    fun `inserts new SuuntoTag update to dataSource`() = runTest {
        whenever(dataSource.fetchUnsyncedWorkoutAttributesUpdate(any(), any())).thenReturn(null)

        useCase(
            useCase.getSuuntoTagsUpdateParams(
                1,
                "username",
                suuntoTags = listOf(SuuntoTag.COMMUTE)
            )
        )

        verify(dataSource, times(1)).addWorkoutAttributesUpdate(
            DomainWorkoutAttributesUpdate(
                1,
                "username",
                DomainWorkoutAttributes(
                    null,
                    null,
                    null,
                    null,
                    null,
                    listOf(SuuntoTag.COMMUTE)
                ),
                emptyList(),
                false
            )
        )
    }
}
