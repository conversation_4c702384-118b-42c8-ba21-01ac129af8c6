package com.stt.android.domain.advancedlaps

import com.stt.android.core.domain.LapsTableDataType
import com.stt.android.core.domain.MeasurementUnit
import com.stt.android.core.domain.SuuntoPlusChannel
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.sml.SmlStreamData
import com.stt.android.domain.sml.reader.SmlFactory
import com.stt.android.infomodel.SummaryItem
import com.stt.android.logbook.SmlFromJson
import com.stt.android.logbook.SmlParser
import com.stt.android.logbook.SuuntoLogbookSummary
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.within
import org.assertj.core.data.Percentage.withPercentage
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.junit.MockitoJUnitRunner
import java.util.zip.GZIPInputStream
import kotlin.math.abs

@RunWith(MockitoJUnitRunner::class)
class AdvancedLapsUseCaseTest {

    private lateinit var usecase: AdvancedLapsUseCase

    private val parser = SmlParser()

    @Before
    fun setup() {
        usecase = AdvancedLapsUseCase()
    }

    @Test
    fun `test that autogenerated 1km laps match with 1km autolaps`() {
        val runningAutoLapsSml = getRunningAutolapsSml()
        val autoGenerated = usecase.getAutoGeneratedLaps(
            runningAutoLapsSml.streamData,
            null,
            runningAutoLapsSml.summary.header,
            LapsTableType.ONE_KM_AUTO_LAP,
            MeasurementUnit.METRIC
        )

        val autoLaps = usecase.getLapsTableData(runningAutoLapsSml, MeasurementUnit.METRIC)
            .firstOrNull { lapsTable -> lapsTable.lapsType == LapsTableType.DISTANCE_AUTO_LAP }!!

        assertThat(autoGenerated.lapsTableRows.mapNotNull { it.ascent }.toFloatArray())
            .containsExactly(autoLaps.lapsTableRows.mapNotNull { it.ascent }.toFloatArray(), within(1f))

        assertThat(autoGenerated.lapsTableRows.mapNotNull { it.descent }.toFloatArray())
            .containsExactly(autoLaps.lapsTableRows.mapNotNull { it.descent }.toFloatArray(), within(1f))

        autoGenerated.lapsTableRows.forEachIndexed { index, row ->
            assertThat(row.distance).describedAs("Autogenerate at index %d", index)
                .isEqualTo(autoLaps.lapsTableRows[index].distance, within(6.0f))
            assertThat(row.cumulatedDistance).describedAs("Autogenerate at index %d", index)
                .isEqualTo(autoLaps.lapsTableRows[index].cumulatedDistance, within(9.0f))
            assertThat(row.minAltitude).describedAs("Autogenerate at index %d", index)
                .isEqualTo(autoLaps.lapsTableRows[index].minAltitude, within(0.5f))
            assertThat(row.maxAltitude).describedAs("Autogenerate at index %d", index)
                .isEqualTo(autoLaps.lapsTableRows[index].maxAltitude, within(0.8f))
            assertThat(row.avgAltitude).describedAs("Autogenerate at index %d", index)
                .isEqualTo(autoLaps.lapsTableRows[index].avgAltitude, within(0.17f))
            assertThat(row.minCadence).describedAs("Autogenerate at index %d", index)
                .isEqualTo(autoLaps.lapsTableRows[index].minCadence, within(0.8f))
            assertThat(row.maxCadence).describedAs("Autogenerate at index %d", index)
                .isEqualTo(autoLaps.lapsTableRows[index].maxCadence, within(0.0001f))
            assertThat(row.avgCadence).describedAs("Autogenerate at index %d", index)
                .isEqualTo(autoLaps.lapsTableRows[index].avgCadence, within(0.01f))
            // minSpeed cannot be compared, because samples have also zero in speed but window laps don't
            assertThat(row.maxSpeed).describedAs("Autogenerate at index %d", index)
                .isEqualTo(autoLaps.lapsTableRows[index].maxSpeed, within(0.011f))
            assertThat(row.avgSpeed).describedAs("Autogenerate at index %d", index)
                .isEqualTo(autoLaps.lapsTableRows[index].avgSpeed, within(0.3f))
            assertThat(row.minVerticalSpeed).describedAs("Autogenerate at index %d", index)
                .isEqualTo(autoLaps.lapsTableRows[index].minVerticalSpeed, within(0.021f))
            assertThat(row.maxVerticalSpeed).describedAs("Autogenerate at index %d", index)
                .isEqualTo(autoLaps.lapsTableRows[index].maxVerticalSpeed, within(0.021f))
            assertThat(row.avgVerticalSpeed).describedAs("Autogenerate at index %d", index)
                .isEqualTo(autoLaps.lapsTableRows[index].avgVerticalSpeed, within(0.015f))
            assertThat(row.duration).describedAs("Autogenerate at index %d", index)
                .isEqualTo(autoLaps.lapsTableRows[index].duration, within(2.7f))
            assertThat(row.cumulatedDuration).describedAs("Autogenerate at index %d", index)
                .isEqualTo(autoLaps.lapsTableRows[index].cumulatedDuration, within(3.5f))
        }
    }

    @Test
    fun `test that autogenerated 5km laps are created correctly`() {
        val runningAutoLapsSml = getRunningAutolapsSml()
        val autoGenerated = usecase.getAutoGeneratedLaps(
            runningAutoLapsSml.streamData,
            null,
            runningAutoLapsSml.summary.header,
            LapsTableType.FIVE_KM_AUTO_LAP,
            MeasurementUnit.METRIC
        )

        val row = autoGenerated.lapsTableRows.firstOrNull()
        assertThat(row?.distance).isEqualTo(5000.0f)
        assertThat(row?.minAltitude).isEqualTo(11.0f)
        assertThat(row?.maxAltitude).isEqualTo(38.0f)
        assertThat(row?.avgAltitude).isEqualTo(23.407022f)
        assertThat(row?.ascent).isEqualTo(7.0f)
        assertThat(row?.descent).isEqualTo(7.000001f)
        assertThat(row?.minCadence).isEqualTo(1.467f)
        assertThat(row?.maxCadence).isEqualTo(1.533f)
        assertThat(row?.avgCadence).isEqualTo(1.5016387f)
        assertThat(row?.duration).isEqualTo(1374.013f)
        assertThat(row?.minHR).isEqualTo(1.42f)
        assertThat(row?.maxHR).isEqualTo(3.47f)
        assertThat(row?.avgHR).isEqualTo(3.0516179f)
        assertThat(row?.minSpeed).isEqualTo(2.52f)
        assertThat(row?.maxSpeed).isEqualTo(4.32f)
        assertThat(row?.avgSpeed).isEqualTo(3.6389759f)
        assertThat(row?.minVerticalSpeed).isEqualTo(-0.16f)
        assertThat(row?.maxVerticalSpeed).isEqualTo(0.22f)
        assertThat(row?.avgVerticalSpeed).isEqualTo(0.00557425f)
        assertThat(row?.cumulatedDistance).isEqualTo(5005.0f)
        assertThat(row?.cumulatedDuration).isEqualTo(1374.013f)
    }

    @Test
    fun `test that autogenerated 1mile laps are generated correctly`() {
        val runningAutoLapsSml = getRunningAutolapsSml()
        val autoGenerated = usecase.getAutoGeneratedLaps(
            runningAutoLapsSml.streamData,
            null,
            runningAutoLapsSml.summary.header,
            LapsTableType.ONE_MILE_AUTO_LAP,
            MeasurementUnit.IMPERIAL
        )

        val row = autoGenerated.lapsTableRows.firstOrNull()
        assertThat(row?.distance).isEqualTo(1609.34f)
        assertThat(row?.minAltitude).isEqualTo(11.0f)
        assertThat(row?.maxAltitude).isEqualTo(19.2f)
        assertThat(row?.avgAltitude).isEqualTo(14.024609f)
        assertThat(row?.ascent).isEqualTo(0.0f)
        assertThat(row?.descent).isEqualTo(7.000001f)
        assertThat(row?.minCadence).isEqualTo(1.467f)
        assertThat(row?.maxCadence).isEqualTo(1.533f)
        assertThat(row?.avgCadence).isEqualTo(1.4858367f)
        assertThat(row?.duration).isEqualTo(454.013f)
        assertThat(row?.minHR).isEqualTo(1.42f)
        assertThat(row?.maxHR).isEqualTo(3.23f)
        assertThat(row?.avgHR).isEqualTo(2.913915f)
        assertThat(row?.minSpeed).isEqualTo(2.52f)
        assertThat(row?.maxSpeed).isEqualTo(3.8f)
        assertThat(row?.avgSpeed).isEqualTo(3.5447001f)
        assertThat(row?.minVerticalSpeed).isEqualTo(-0.08f)
        assertThat(row?.maxVerticalSpeed).isEqualTo(0.02f)
        assertThat(row?.avgVerticalSpeed).isEqualTo(-0.010290828f)
        assertThat(row?.cumulatedDistance).isEqualTo(1612.0f)
        assertThat(row?.cumulatedDuration).isEqualTo(454.013f)
    }

    @Test
    fun `test that last lap is generated correctly for LapsTable`() {
        val runningAutoLapsSml = getRunningAutolapsSml()
        val autoGenerated = usecase.getAutoGeneratedLaps(
            runningAutoLapsSml.streamData,
            null,
            runningAutoLapsSml.summary.header,
            LapsTableType.ONE_KM_AUTO_LAP,
            MeasurementUnit.METRIC
        )

        val lastRow = autoGenerated.lapsTableRows.lastOrNull()
        assertThat(lastRow?.distance).isEqualTo(67.0f)
        assertThat(lastRow?.minAltitude).isEqualTo(14.0f)
        assertThat(lastRow?.maxAltitude).isEqualTo(15.4f)
        assertThat(lastRow?.avgAltitude).isEqualTo(14.561539f)
        assertThat(lastRow?.ascent).isEqualTo(0.0f)
        assertThat(lastRow?.descent).isEqualTo(0.0f)
        assertThat(lastRow?.minCadence).isEqualTo(1.367f)
        assertThat(lastRow?.maxCadence).isEqualTo(1.417f)
        assertThat(lastRow?.avgCadence).isEqualTo(1.4034231f)
        assertThat(lastRow?.duration).isEqualTo(26.0f)
        assertThat(lastRow?.minHR).isEqualTo(2.8f)
        assertThat(lastRow?.maxHR).isEqualTo(2.92f)
        assertThat(lastRow?.avgHR).isEqualTo(2.8396153f)
        assertThat(lastRow?.minSpeed).isEqualTo(2.48f)
        assertThat(lastRow?.maxSpeed).isEqualTo(2.78f)
        assertThat(lastRow?.avgSpeed).isEqualTo(2.576923f)
        assertThat(lastRow?.minVerticalSpeed).isEqualTo(0.0f)
        assertThat(lastRow?.maxVerticalSpeed).isEqualTo(0.08f)
        assertThat(lastRow?.avgVerticalSpeed).isEqualTo(0.046153843f)
        assertThat(lastRow?.cumulatedDistance).isEqualTo(17070.0f)
        assertThat(lastRow?.cumulatedDuration).isEqualTo(4634.013f)
    }

    @Test
    fun `test that LapsTable for auto laps is created correctly`() {
        val runningAutoLapsSml = getRunningAutolapsSml()
        val lapsList = usecase.getLapsTableData(runningAutoLapsSml, MeasurementUnit.METRIC)
        val autoLaps = lapsList.filter { lapsTable -> lapsTable.lapsType == LapsTableType.DISTANCE_AUTO_LAP }
        val lapRows = autoLaps.firstOrNull()?.lapsTableRows
        assertThat(lapRows?.size).isEqualTo(18)
        val row = lapRows?.firstOrNull()
        assertThat(row?.type).isEqualTo(WindowType.AUTOLAP)
        assertThat(row?.rowid).isEqualTo(0)
        assertThat(row?.lapNumber).isEqualTo(1)
        assertThat(row?.distance).isEqualTo(1000.0f)
        assertThat(row?.minAltitude).isEqualTo(11.0f)
        assertThat(row?.maxAltitude).isEqualTo(20.0f)
        assertThat(row?.avgAltitude).isEqualTo(13.9f)
        assertThat(row?.ascent).isEqualTo(0.0f)
        assertThat(row?.ascentTime).isEqualTo(0.0f)
        assertThat(row?.descent).isEqualTo(7.0f)
        assertThat(row?.descentTime).isEqualTo(142.0f)
        assertThat(row?.maxDescent).isEqualTo(7.0f)
        assertThat(row?.minCadence).isEqualTo(0.683f)
        assertThat(row?.maxCadence).isEqualTo(1.533f)
        assertThat(row?.avgCadence).isEqualTo(1.48f)
        assertThat(row?.duration).isEqualTo(281.6f)
        assertThat(row?.energy).isEqualTo(373881.2f)
        assertThat(row?.minHR).isEqualTo(1.417f)
        assertThat(row?.maxHR).isEqualTo(3.1f)
        assertThat(row?.avgHR).isEqualTo(2.833f)
        assertThat(row?.recoveryTime).isEqualTo(7140)
        assertThat(row?.minSpeed).isEqualTo(1.09f)
        assertThat(row?.maxSpeed).isEqualTo(3.79f)
        assertThat(row?.avgSpeed).isEqualTo(3.552f)
        assertThat(row?.minVerticalSpeed).isEqualTo(-0.1f)
        assertThat(row?.maxVerticalSpeed).isEqualTo(0.033f)
        assertThat(row?.avgVerticalSpeed).isEqualTo(-0.026f)
        assertThat(row?.cumulatedDistance).isEqualTo(1000.0f)
        assertThat(row?.cumulatedDuration).isEqualTo(281.6f)
    }

    @Test
    fun `test that LapsTable for manual laps is created correctly`() {
        val runningManualLapsSml = getRunningManuallapsSml()
        val lapsList = usecase.getLapsTableData(runningManualLapsSml, MeasurementUnit.METRIC)
        val manualLaps = lapsList.filter { lapsTable -> lapsTable.lapsType == LapsTableType.MANUAL }
        val lapRows = manualLaps.firstOrNull()?.lapsTableRows
        assertThat(lapRows?.size).isEqualTo(21)
        val row = lapRows?.firstOrNull()
        assertThat(row?.type).isEqualTo(WindowType.LAP)
        assertThat(row?.rowid).isEqualTo(0)
        assertThat(row?.lapNumber).isEqualTo(1)
        assertThat(row?.distance).isEqualTo(1337.0f)
        assertThat(row?.ascent).isEqualTo(0.0f)
        assertThat(row?.ascentTime).isEqualTo(0.0f)
        assertThat(row?.descent).isEqualTo(0.0f)
        assertThat(row?.descentTime).isEqualTo(0.0f)
        assertThat(row?.maxDescent).isEqualTo(0.0f)
        assertThat(row?.minCadence).isEqualTo(0.0f)
        assertThat(row?.maxCadence).isEqualTo(1.4f)
        assertThat(row?.avgCadence).isEqualTo(1.264f)
        assertThat(row?.duration).isEqualTo(513.8f)
        assertThat(row?.energy).isEqualTo(382673.5f)
        assertThat(row?.minHR).isEqualTo(1.583f)
        assertThat(row?.maxHR).isEqualTo(2.3f)
        assertThat(row?.avgHR).isEqualTo(2.073f)
        assertThat(row?.recoveryTime).isEqualTo(3840)
        assertThat(row?.minSpeed).isEqualTo(0.0f)
        assertThat(row?.maxSpeed).isEqualTo(4.4f)
        assertThat(row?.avgSpeed).isEqualTo(2.6f)
        assertThat(row?.cumulatedDistance).isEqualTo(1337.0f)
        assertThat(row?.cumulatedDuration).isEqualTo(513.8f)
    }

    @Test
    fun `test that LapsTable for intervals is created correctly`() {
        val swimmingIntervalsSml = getSwimmingIntervalsSml()
        val lapsList = usecase.getLapsTableData(swimmingIntervalsSml, MeasurementUnit.METRIC)
        val intervalLaps = lapsList.filter { lapsTable -> lapsTable.lapsType == LapsTableType.INTERVAL }
        val lapRows = intervalLaps.firstOrNull()?.lapsTableRows
        assertThat(lapRows?.size).isEqualTo(28)
        val row = lapRows?.lastOrNull()
        assertThat(row?.type).isEqualTo(WindowType.INTERVAL)
        assertThat(row?.rowid).isEqualTo(27)
        assertThat(row?.lapNumber).isEqualTo(2)
        assertThat(row?.distance).isEqualTo(700.0f)
        assertThat(row?.duration).isEqualTo(971.7f)
        assertThat(row?.minHR).isEqualTo(1.35f)
        assertThat(row?.maxHR).isEqualTo(2.35f)
        assertThat(row?.avgHR).isEqualTo(1.84f)
        assertThat(row?.recoveryTime).isEqualTo(4260)
        assertThat(row?.minSpeed).isEqualTo(0.6f)
        assertThat(row?.maxSpeed).isEqualTo(0.9f)
        assertThat(row?.avgSpeed).isEqualTo(0.7f)
        assertThat(row?.minStrokeRate).isEqualTo(0.3f)
        assertThat(row?.maxStrokeRate).isEqualTo(0.4f)
        assertThat(row?.avgStrokeRate).isEqualTo(0.4f)
        assertThat(row?.minStrokes).isEqualTo(21.0f)
        assertThat(row?.maxStrokes).isEqualTo(37.0f)
        assertThat(row?.avgStrokes).isEqualTo(27.1f)
        assertThat(row?.minSwolf).isEqualTo(77.2f)
        assertThat(row?.maxSwolf).isEqualTo(124.0f)
        assertThat(row?.avgSwolf).isEqualTo(96.5f)
        assertThat(row?.minTemperature).isEqualTo(299.4f)
        assertThat(row?.maxTemperature).isEqualTo(299.6f)
        assertThat(row?.avgTemperature).isEqualTo(299.6f)
        assertThat(row?.cumulatedDistance).isEqualTo(1300.0f)
        assertThat(row?.cumulatedDuration).isEqualTo(1807.6001f)
    }

    @Test
    fun `test that LapsTable for downhill laps is created correctly`() {
        val downhillLapsSml = getDownhillLapsSml()
        val lapsList = usecase.getLapsTableData(downhillLapsSml, MeasurementUnit.METRIC)
        val intervalLaps = lapsList.filter { lapsTable -> lapsTable.lapsType == LapsTableType.DOWNHILL }
        val lapRows = intervalLaps.firstOrNull()?.lapsTableRows
        assertThat(lapRows?.size).isEqualTo(26)
        val row = lapRows?.lastOrNull()
        assertThat(row?.type).isEqualTo(WindowType.DOWNHILL)
        assertThat(row?.rowid).isEqualTo(25)
        assertThat(row?.lapNumber).isEqualTo(26)
        assertThat(row?.distance).isEqualTo(637.0f)
        assertThat(row?.duration).isEqualTo(142.8f)
        assertThat(row?.descent).isEqualTo(144.5f)
        assertThat(row?.maxHR).isEqualTo(2.45f)
        assertThat(row?.avgHR).isEqualTo(2.252f)
        assertThat(row?.maxSpeed).isEqualTo(12.7f)
        assertThat(row?.avgSpeed).isEqualTo(4.5f)
        assertThat(row?.minStrokeRate).isNull()
        assertThat(row?.maxStrokeRate).isNull()
        assertThat(row?.avgStrokeRate).isNull()
        assertThat(row?.minStrokes).isNull()
        assertThat(row?.maxStrokes).isNull()
        assertThat(row?.avgStrokes).isNull()
        assertThat(row?.minSwolf).isNull()
        assertThat(row?.maxSwolf).isNull()
        assertThat(row?.avgSwolf).isNull()
        assertThat(row?.cumulatedDistance).isEqualTo(23554.0f)
        assertThat(row?.cumulatedDuration).isEqualTo(15991.262f)
    }

    @Test
    fun `test that LapsTable for dive auto laps is created correctly`() {
        val diveAutoLapsSml = getDiveAutoLapsSml()
        val lapsList = usecase.getLapsTableData(diveAutoLapsSml, MeasurementUnit.METRIC)
        val intervalLaps = lapsList.filter { lapsTable -> lapsTable.lapsType == LapsTableType.DIVE }
        val lapRows = intervalLaps.firstOrNull()?.lapsTableRows
        assertThat(lapRows?.size).isEqualTo(1)
        val row = lapRows?.lastOrNull()
        assertThat(row?.type).isEqualTo(WindowType.DIVE)
        assertThat(row?.rowid).isEqualTo(28)
        assertThat(row?.diveInWorkout).isEqualTo(1)
        assertThat(row?.diveRecoveryTime).isEqualTo(60.0f)
        assertThat(row?.diveTime).isEqualTo(31f)
        assertThat(row?.cumulatedDistance).isEqualTo(1300.0f)
        assertThat(row?.cumulatedDuration).isEqualTo(1825.4f)
        assertThat(row?.minDepth).isEqualTo(0f)
        assertThat(row?.avgDepth).isEqualTo(3.5f)
        assertThat(row?.maxDepth).isEqualTo(10.2f)
    }

    @Test
    fun `test that lap numbers are generated correctly`() {
        val runningAutoLapsSml = getRunningAutolapsSml()
        val lapsList = usecase.getLapsTableData(runningAutoLapsSml, MeasurementUnit.METRIC)
        val autoLaps = lapsList.filter { lapsTable -> lapsTable.lapsType == LapsTableType.DISTANCE_AUTO_LAP }
        val lapRows = autoLaps.firstOrNull()?.lapsTableRows
        assertThat(lapRows?.firstOrNull()?.lapNumber).isEqualTo(1)
        assertThat(lapRows?.lastOrNull()?.lapNumber).isEqualTo(18)
    }

    @Test
    fun `test that available summaryItems are filtered correctly`() {
        val runningAutoLapsSml = getRunningAutolapsSml()
        val lapsList = usecase.getLapsTableData(runningAutoLapsSml, MeasurementUnit.METRIC)
        val autoLaps = lapsList.firstOrNull { lapsTable -> lapsTable.lapsType == LapsTableType.DISTANCE_AUTO_LAP }
        val summaryItems = autoLaps?.dataTypes
        assertThat(summaryItems?.size).isEqualTo(27)
        assertThat(summaryItems?.any { item -> item == LapsTableDataType.Summary(SummaryItem.DURATION) }).isEqualTo(true)
        assertThat(summaryItems?.any { item -> item == LapsTableDataType.Summary(SummaryItem.AVGPOWER) }).isEqualTo(false)
        assertThat(summaryItems?.any { item -> item == LapsTableDataType.Summary(SummaryItem.GASCONSUMPTION) }).isEqualTo(false)
    }

    @Test
    fun `test that intervals with subrows are created correctly`() {
        val swimmingIntervalsSml = getSwimmingIntervalsSml()
        val lapsList = usecase.getLapsTableData(swimmingIntervalsSml, MeasurementUnit.METRIC)
        val intervalLaps = lapsList.filter { lapsTable -> lapsTable.lapsType == LapsTableType.INTERVAL }
        val lapRows = intervalLaps.firstOrNull()?.lapsTableRows
        // Sum of first interval's pool length windows distances
        val firstIntervalSum = lapRows?.take(12)?.sumOf { it.distance?.toInt() ?: 0 }
        // First interval's window distance
        val firstIntervalDistance = lapRows?.get(12)?.distance?.toInt()
        assertThat(firstIntervalSum).isEqualTo(firstIntervalDistance)
        // Sum of all interval windows' distances
        val intervalsDistance = lapRows?.filter { row -> row.type == WindowType.INTERVAL }?.sumOf { it.distance?.toInt() ?: 0 }
        // Sum of all pool length windows' distances
        val poolLengthsDistance = lapRows?.filter { row -> row.type == WindowType.POOLLENGTH }?.sumOf { it.distance?.toInt() ?: 0 }
        assertThat(intervalsDistance).isEqualTo(poolLengthsDistance)
    }

    @Test
    fun `test that LapsTable for manual laps with suuntoPlus values is generated correctly`() {
        val suuntoPlusSml = getSuuntoPlusSml()
        val lapsList = usecase.getLapsTableData(suuntoPlusSml, MeasurementUnit.METRIC)
        val manualLaps = lapsList.filter { lapsTable -> lapsTable.lapsType == LapsTableType.MANUAL }
        val lapRows = manualLaps.firstOrNull()?.lapsTableRows
        assertThat(lapRows?.size).isEqualTo(10)
        val row = lapRows?.firstOrNull()
        assertThat(row?.type).isEqualTo(WindowType.LAP)
        val suuntoPlusData = row?.suuntoPlusData
        assertThat(suuntoPlusData).isNotNull
        assertThat(suuntoPlusData!!.entries.size).isEqualTo(2)
        assertThat(
            suuntoPlusData[
                SuuntoPlusChannel(
                    zappId = "zzaaaa01",
                    channelId = 1,
                    format = "Distance_Threedigits",
                    inverted = false,
                    name = "Increasing distance",
                    variableId = "value_1"
                )
            ]!!.avg
        ).isCloseTo(472.0, withPercentage(0.5))
    }

    private fun getRunningAutolapsSml() = getSmlFromResource("sml/reader/running_autolaps.gzipped")

    private fun getSwimmingIntervalsSml() = getSmlFromResource("sml/reader/swimming_intervals.gzipped")

    private fun getRunningManuallapsSml() = getSmlFromResource("sml/reader/running_manuallaps.gzipped")

    private fun getDownhillLapsSml() = getSmlFromResource("sml/reader/snowboarding_with_pauses.gzipped")

    private fun getDiveAutoLapsSml() = getSmlFromResource("sml/reader/snorkeling_auto_dive_laps.gzipped")

    private fun getSuuntoPlusSml() = getSmlFromResource("sml/reader/suuntoplus_laps.gzipped")

    private fun getSmlFromResource(path: String): Sml {
        val smlFromJson: SmlFromJson = parser.parseSml(
            GZIPInputStream(
                javaClass.classLoader?.getResourceAsStream(path)
            )
        )
        return SmlFactory.create(smlFromJson.summaryContent, smlFromJson.data.samples, smlFromJson.data.statistics)
    }
}

private fun AdvancedLapsUseCase.getAutoGeneratedLaps(
    streamData: SmlStreamData,
    multisportPartActivity: MultisportPartActivity?,
    header: SuuntoLogbookSummary?,
    type: LapsTableType,
    measurementUnit: MeasurementUnit
) = getAllAutoGeneratedLaps(streamData, multisportPartActivity, header, measurementUnit)
    .first { abs(it.autoLapLength - autoGeneratedLapDistances[type]!!) < 0.001f }
