package com.stt.android.domain.workouts

import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import java.time.LocalDate
import java.time.ZoneId

@OptIn(ExperimentalCoroutinesApi::class)
@RunWith(MockitoJUnitRunner::class)
class GetWorkoutHeadersForDateUseCaseTest {

    @Test
    fun `fetch workout headers for date`() = runTest {
        val workoutHeaderDataSource: WorkoutHeaderDataSource = mock()

        val useCase = GetWorkoutHeadersForDateUseCase(workoutHeaderDataSource)
        useCase(
            GetWorkoutHeadersForDateUseCase.Params(
                "testuser",
                LocalDate.of(2020, 1, 1),
                ZoneId.of("UTC-8") // Pacific Standard Time = UTC -8 hours
            )
        )

        verify(workoutHeaderDataSource, times(1)).findByStartTime(
            "testuser",
            1577865600000L, // January 1, 2020 08:00:00 GMT
            1577951999999L // January 2, 2020 07:59:59.999 GMT
        )
    }
}
