package com.stt.android.domain.workout

import com.soy.algorithms.tss.TSSCalculationMethod
import com.stt.android.domain.Point
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.tss.TSS

/**
 * Factory object to generate entities for testing
 */
object WorkoutEntityFactory {
    fun createDomainWorkoutHeader(): WorkoutHeader =
        WorkoutHeader(
            id = 1,
            key = "key",
            totalDistance = 0.0,
            maxSpeed = 0.0,
            activityTypeId = 0,
            avgSpeed = 0.0,
            description = "desc",
            startPosition = Point(1.0, 2.0, 3.0),
            stopPosition = Point(1.0, 2.0, 3.0),
            centerPosition = Point(1.0, 2.0, 3.0),
            startTime = 0,
            stopTime = 0,
            totalTime = 0.0,
            energyConsumption = 0.0,
            username = "",
            heartRateAverage = 0.0,
            heartRateAvgPercentage = 0.0,
            heartRateMax = 0.0,
            heartRateMaxPercentage = 0.0,
            heartRateUserSetMax = 0.0,
            averageCadence = 0,
            maxCadence = 0,
            pictureCount = 0,
            viewCount = 0,
            commentCount = 0,
            sharingFlags = 0,
            stepCount = 0,
            polyline = "poly",
            manuallyAdded = false,
            reactionCount = 0,
            totalAscent = 0.0,
            totalDescent = 0.0,
            recoveryTime = 0,
            locallyChanged = false,
            deleted = false,
            seen = false,
            maxAltitude = 0.0,
            minAltitude = 0.0,
            extensionsFetched = false,
            tss = TSS(
                trainingStressScore = 1f,
                calculationMethod = TSSCalculationMethod.POWER,
                intensityFactor = 2f,
                normalizedPower = 3f,
                averageGradeAdjustedPace = 4f
            ),
            suuntoTags = listOf(),
            userTags = listOf()
        )
}
