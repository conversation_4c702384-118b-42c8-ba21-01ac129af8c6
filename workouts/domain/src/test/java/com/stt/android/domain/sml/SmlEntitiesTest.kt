package com.stt.android.domain.sml

import org.assertj.core.api.Assertions.assertThat
import org.junit.Test

class SmlEntitiesTest {

    private val suuntoPlusSamples = listOf(
        SmlExtensionStreamPoint(timestamp = 0, cumulativeDistance = 0f, value = 0f),
        SmlExtensionStreamPoint(timestamp = 2, cumulativeDistance = 2f, value = 2f),
        SmlExtensionStreamPoint(timestamp = 3, cumulativeDistance = 3f, value = 3f),
        SmlExtensionStreamPoint(timestamp = 6, cumulativeDistance = 6f, value = 6f),
        SmlExtensionStreamPoint(timestamp = 7, cumulativeDistance = 7f, value = 7f),
    )

    @Test
    fun testSamplesFilling() {
        val result = suuntoPlusSamples.toMutableList().fillConstantWithSampling(
            samplingMillis = 1,
            untilTimestampMillis = 10
        )
        assertThat(result).isEqualTo(
            listOf(
                SmlExtensionStreamPoint(timestamp = 0, cumulativeDistance = 0f, value = 0f),
                SmlExtensionStreamPoint(timestamp = 1, cumulativeDistance = null, value = 0f),
                SmlExtensionStreamPoint(timestamp = 2, cumulativeDistance = 2f, value = 2f),
                SmlExtensionStreamPoint(timestamp = 3, cumulativeDistance = 3f, value = 3f),
                SmlExtensionStreamPoint(timestamp = 4, cumulativeDistance = null, value = 3f),
                SmlExtensionStreamPoint(timestamp = 5, cumulativeDistance = null, value = 3f),
                SmlExtensionStreamPoint(timestamp = 6, cumulativeDistance = 6f, value = 6f),
                SmlExtensionStreamPoint(timestamp = 7, cumulativeDistance = 7f, value = 7f),
                SmlExtensionStreamPoint(timestamp = 8, cumulativeDistance = null, value = 7f),
                SmlExtensionStreamPoint(timestamp = 9, cumulativeDistance = null, value = 7f),
            )
        )
    }
}
