package com.stt.android.data.workout

import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.WorkoutHeaderDataSource
import com.suunto.algorithms.data.Length

class FakeWorkoutHeaderDataSource : WorkoutHeaderDataSource {

    private val workoutHeaderStore = mutableMapOf<String, WorkoutHeader>()

    override suspend fun findByKey(key: String): WorkoutHeader? {
        return workoutHeaderStore[key]
    }

    override suspend fun findById(id: Int): WorkoutHeader? {
        return workoutHeaderStore.asSequence().firstOrNull { it.component2().id == id }
            ?.component2()
    }

    override suspend fun findByIds(ids: List<Int>): List<WorkoutHeader> = emptyList()

    override suspend fun findPagedOfType(
        ownerUsername: String,
        activityTypeId: Int,
        page: Int
    ): List<WorkoutHeader> = emptyList()

    override suspend fun findPagedExcludingTypes(
        ownerUsername: String,
        excludedTypes: Set<Int>,
        page: Int
    ): List<WorkoutHeader> = emptyList()

    override fun syncWorkouts() {
        TODO("not implemented") // To change body of created functions use File | Settings | File Templates.
    }

    override suspend fun storeWorkout(workoutHeader: WorkoutHeader) {
        val workoutKey =
            workoutHeader.key ?: throw IllegalStateException("Workout key cannot be null")
        val workoutId = workoutHeader.id
        workoutHeaderStore[workoutKey] = workoutHeader.copy(id = workoutId)
    }

    override suspend fun markDeletedOrPermanentlyDelete(id: Int): Boolean {
        return true
    }

    override suspend fun markExtensionsFetched(id: Int): Boolean {
        return workoutHeaderStore.entries.firstOrNull { it.value.id == id }
            ?.let {
                storeWorkout(workoutHeader = it.value.copy(extensionsFetched = true))
                true
            } ?: false
    }

    override suspend fun remove(key: String) {
    }

    override suspend fun getDeletedWorkoutsKeys(): List<String> {
        return emptyList()
    }

    override suspend fun getLocallyModifiedWorkouts(): List<WorkoutHeader> {
        return emptyList()
    }

    override suspend fun markWorkoutsAsSynced(workoutKeys: Set<String>) {
    }

    override suspend fun findManuallyCreatedWorkouts(): List<WorkoutHeader> {
        return emptyList()
    }

    override suspend fun findNewUnsyncedWorkouts(): List<WorkoutHeader> {
        return emptyList()
    }

    override suspend fun findByStartTime(
        ownerUsername: String,
        minimumStartTime: Long,
        maximumStartTime: Long
    ): List<WorkoutHeader> = emptyList()

    override suspend fun findNotDeletedByRange(
        ownerUsername: String,
        activityTypeId: Int?,
        sinceMs: Long,
        untilMs: Long
    ): List<WorkoutHeader> = emptyList()

    override suspend fun findPagedByTimeRange(
        ownerUsername: String,
        sinceMs: Long,
        untilMs: Long,
        includeActivityTypeId: Int?,
        excludeActivityTypeIds: Set<Int>,
        page: Int,
        firstPageSize: Int,
        pageSize: Int
    ): List<WorkoutHeader> = emptyList()

    override suspend fun loadActivityTypeCount(id: Int): Long {
        return 0L
    }

    override suspend fun loadFastestOfActivityType(
        id: Int,
        since: Long,
        till: Long
    ): WorkoutHeader? {
        return null
    }

    override suspend fun loadFarthestOfActivityType(
        id: Int,
        since: Long,
        till: Long
    ): WorkoutHeader? {
        return null
    }

    override suspend fun loadShortestTimeOfActivityTypeWithDistanceRange(
        id: Int,
        distanceRange: ClosedRange<Length>,
        since: Long,
        till: Long
    ): WorkoutHeader? {
        return null
    }

    override suspend fun loadLatestOfActivityType(
        id: Int,
        till: Long
    ): WorkoutHeader? {
        return null
    }

    override suspend fun loadLongestTimeOfActivityType(
        id: Int,
        since: Long,
        till: Long
    ): WorkoutHeader? {
        return null
    }

    override suspend fun loadHighestClimbOfActivityType(
        id: Int,
        since: Long,
        till: Long
    ): WorkoutHeader? {
        return null
    }

    override suspend fun loadHighestAltitudeOfActivityType(
        id: Int,
        since: Long,
        till: Long
    ): WorkoutHeader? {
        return null
    }

    override suspend fun loadFastestPaceOfActivityTypeInPeriod(
        id: Int,
        since: Long,
        till: Long
    ): WorkoutHeader? {
        TODO("Not yet implemented")
    }

    override suspend fun findWithUserTagsById(id: Int): WorkoutHeader? {
        return null
    }

    override suspend fun findWorkoutsWithUnsyncedUserTags(): List<WorkoutHeader> {
        return emptyList()
    }

    override suspend fun loadTotalActivityCount(): Long {
        return 0L
    }

    override suspend fun loadActivityCountInPeriod(since: Long, till: Long): Long {
        return 0L
    }

    override suspend fun loadActivityTypeCountInPeriod(id: Int, since: Long, till: Long): Long {
        return 0L
    }

    override suspend fun findOldestWorkout(ownerUsername: String, since: Long): WorkoutHeader? {
        return null
    }
}
