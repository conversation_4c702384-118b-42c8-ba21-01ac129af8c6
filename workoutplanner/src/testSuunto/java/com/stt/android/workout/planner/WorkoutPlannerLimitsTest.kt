package com.stt.android.workout.planner

import com.google.common.truth.Truth.assertThat
import com.stt.android.domain.user.MeasurementUnit
import org.junit.Test

class WorkoutPlannerLimitsTest {

    @Test
    fun `should have proper limits for metric pace`() {
        val minPaceMinutesPerKm = MeasurementUnit.METRIC.toPaceUnit(
            WorkoutPlannerLimits.paceMetric.start
        )
        val maxPaceMinutesPerKm = MeasurementUnit.METRIC.toPaceUnit(
            WorkoutPlannerLimits.paceMetric.endInclusive
        )

        assertThat(minPaceMinutesPerKm).isWithin(0.01).of(30.0) // 30'00 /km
        assertThat(maxPaceMinutesPerKm).isWithin(0.01).of(15.0 / 60.0) // 0'15 /km
    }

    @Test
    fun `should have proper limits for metric speed`() {
        val minKilometersPerHour = MeasurementUnit.METRIC.toSpeedUnit(
            WorkoutPlannerLimits.speedMetric.start
        )
        val maxKilometersPerHour = MeasurementUnit.METRIC.toSpeedUnit(
            WorkoutPlannerLimits.speedMetric.endInclusive
        )

        assertThat(minKilometersPerHour).isWithin(0.01).of(0.0) // 0 km/h
        assertThat(maxKilometersPerHour).isWithin(0.01).of(250.0) // 250 km/h
    }

    @Test
    fun `should have proper limits for imperial pace`() {
        val minPaceMinutesPerMile = MeasurementUnit.IMPERIAL.toPaceUnit(
            WorkoutPlannerLimits.paceImperial.start
        )
        val maxPaceMinutesPerMile = MeasurementUnit.IMPERIAL.toPaceUnit(
            WorkoutPlannerLimits.paceImperial.endInclusive
        )

        assertThat(minPaceMinutesPerMile).isWithin(0.01).of(50.0) // 50'00 /mi
        assertThat(maxPaceMinutesPerMile).isWithin(0.01).of(24.0 / 60.0) // 0'24 /mi
    }

    @Test
    fun `should have proper limits for imperial speed`() {
        val minMilesPerHour = MeasurementUnit.IMPERIAL.toSpeedUnit(
            WorkoutPlannerLimits.speedImperial.start
        )
        val maxMilesPerHour = MeasurementUnit.IMPERIAL.toSpeedUnit(
            WorkoutPlannerLimits.speedImperial.endInclusive
        )

        assertThat(minMilesPerHour).isWithin(0.01).of(0.0) // 0 mph
        assertThat(maxMilesPerHour).isWithin(0.01).of(155.0) // 155 mph
    }
}
