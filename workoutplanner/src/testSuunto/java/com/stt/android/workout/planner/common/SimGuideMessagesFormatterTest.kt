package com.stt.android.workout.planner.common

import android.content.res.Resources
import com.google.common.truth.Truth.assertThat
import com.stt.android.SimGuideMessagesFormatter
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.mapping.InfoModelFormatter
import org.junit.Before
import org.junit.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import com.stt.android.core.R as CR

// Consider converting to an Android test if adding tests that really use InfoModelFormatter. For
// now it is more convenient to run as JVM unit test.
class SimGuideMessagesFormatterTest {

    private lateinit var messagesFormatter: SimGuideMessagesFormatter
    private var resources: Resources = mock()
    private var infoModelFormatter: InfoModelFormatter = mock()

    @Before
    fun setup() {
        whenever(resources.getString(CR.string.per_km)).thenReturn("/km")
        whenever(resources.getString(CR.string.per_mi)).thenReturn("/mi")

        messagesFormatter = SimGuideMessagesFormatter(
            resources = resources,
            infoModelFormatter = infoModelFormatter
        )
    }

    @Test
    fun testFormatDuration() {
        assertThat(messagesFormatter.formatDuration(0.0)).isEqualTo("0'00")
        assertThat(messagesFormatter.formatDuration(90.0)).isEqualTo("1'30")
        assertThat(messagesFormatter.formatDuration(601.0)).isEqualTo("10'01")
        assertThat(messagesFormatter.formatDuration(7801.0)).isEqualTo("2:10'01")
    }

    @Test
    fun formatMetricPace() {
        whenever(infoModelFormatter.unit).thenReturn(MeasurementUnit.METRIC)
        assertThat(messagesFormatter.formatPaceFromSpeed(10.0)).isEqualTo("1'40" to "/km")
    }

    @Test
    fun formatImperialPace() {
        whenever(infoModelFormatter.unit).thenReturn(MeasurementUnit.IMPERIAL)
        assertThat(messagesFormatter.formatPaceFromSpeed(10.0)).isEqualTo("2'41" to "/mi")
        assertThat(messagesFormatter.formatPaceFromSpeed(67.05600006158423)).isEqualTo("0'24" to "/mi")
    }
}
