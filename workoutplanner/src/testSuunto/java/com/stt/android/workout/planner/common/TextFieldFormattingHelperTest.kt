package com.stt.android.workout.planner.common

import com.google.common.truth.Truth.assertThat
import com.stt.android.domain.user.MeasurementUnit
import org.junit.Test

class TextFieldFormattingHelperTest {
    @Test
    fun `should correctly format kilometers`() {
        val kilometer = TextFieldFormattingHelper.formatDistance(MeasurementUnit.METRIC, 1000.0)
        assertThat(kilometer).isEqualTo("1.00")
    }

    @Test
    fun `should correctly format miles`() {
        val kilometerInMiles = TextFieldFormattingHelper.formatDistance(MeasurementUnit.IMPERIAL, 1000.0)
        assertThat(kilometerInMiles).isEqualTo("0.62")
    }

    @Test
    fun `should correctly parse duration with hours`() {
        val seconds = TextFieldFormattingHelper.parseDurationAsSeconds("020000")
        assertThat(seconds).isEqualTo(7200) // two hours
    }

    @Test
    fun `should correctly parse duration with minutes`() {
        val seconds = TextFieldFormattingHelper.parseDurationAsSeconds("0200")
        assertThat(seconds).isEqualTo(120) // two minutes
    }

    @Test
    fun `should correctly parse duration with seconds`() {
        val seconds = TextFieldFormattingHelper.parseDurationAsSeconds("07")
        assertThat(seconds).isEqualTo(7) // seven seconds
    }

    @Test
    fun `should correctly parse hhmmss duration`() {
        val seconds = TextFieldFormattingHelper.parseDurationAsSeconds("123456")
        assertThat(seconds).isEqualTo(45_296) // 12 hours, 34 min, 45 seconds = 45 296 seconds
    }
}
