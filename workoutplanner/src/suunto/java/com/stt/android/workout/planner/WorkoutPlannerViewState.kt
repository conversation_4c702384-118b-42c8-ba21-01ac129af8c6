package com.stt.android.workout.planner

import androidx.compose.runtime.Immutable
import com.soy.algorithms.planner.GuideLimits
import com.stt.android.domain.workout.ActivityType
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import java.time.LocalDate
import java.time.YearMonth

@Immutable
data class WorkoutPlannerBasicDetailsState(
    val today: LocalDate,
    val initialMonth: YearMonth,
    val name: String,
    val description: String,
    val activityType: ActivityType,
    val skipDateSelection: Boolean,
    val date: LocalDate?,
    val isEditing: Boolean,
) {
    val basicDetailsValid: Boolean
        get() {
            val nameValid = name.isNotBlank() && name.length in 1..GuideLimits.name
            val descriptionValid = description.length in 0..GuideLimits.description
            val dateSelectionValid = isEditing || skipDateSelection || (date != null && date >= today)

            return nameValid && descriptionValid && dateSelectionValid
        }
}

@Immutable
data class WorkoutPlannerEditStepsState(
    val steps: ImmutableList<WorkoutPlanStepsListItem>,
) {
    val stepsValid: Boolean
        get() = steps.any { it is WorkoutPlanStepsListItem.ExerciseStep }

    companion object {
        val EMPTY = WorkoutPlannerEditStepsState(persistentListOf())
    }
}
