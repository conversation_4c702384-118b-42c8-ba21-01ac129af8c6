package com.stt.android.workout.planner

import com.soy.algorithms.planner.WorkoutStep

sealed class WorkoutPlanStepsListItem(
    val key: String, // Key for LazyColumn items
) {
    interface IndexedStepsListItem {
        val plannerOverallIndex: Int // WorkoutPlanner overall index for this step
    }

    object BreadcrumbItem : WorkoutPlanStepsListItem("Breadcrumb")
    object AddExerciseOrRepeatButton : WorkoutPlanStepsListItem("Add buttons")

    data class ExerciseStep(
        val step: WorkoutStep.Exercise,
        val insideRepeatBlock: Boolean,
        override val plannerOverallIndex: Int,
    ) : WorkoutPlanStepsListItem(step.uuid), IndexedStepsListItem

    data class RepeatStart(
        val repeatStep: WorkoutStep.Repeat,
        override val plannerOverallIndex: Int,
    ) : WorkoutPlanStepsListItem("Start repeat ${repeatStep.uuid}"), IndexedStepsListItem {
        val times: Int = repeatStep.times
    }

    data class RepeatEnd(
        val uuid: String, // UUID of repeat block, the end marker has no separate UUID in planner
        override val plannerOverallIndex: Int,
    ) : WorkoutPlanStepsListItem("End repeat $uuid"), IndexedStepsListItem
}
