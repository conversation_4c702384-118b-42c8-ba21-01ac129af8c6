package com.stt.android.workout.planner

import android.content.Context
import com.stt.android.watch.WorkoutPlannerNavigator
import javax.inject.Inject

class WorkoutPlannerNavigatorImpl @Inject constructor() : WorkoutPlannerNavigator {
    override fun newListWorkoutPlansIntent(
        context: Context,
        analyticsSource: String,
    ) = WorkoutPlannerActivity.newListWorkoutPlansIntent(
        context = context,
        analyticsSource = analyticsSource
    )

    override fun newCreateWorkoutPlanIntent(
        context: Context,
        showWorkoutListAfterCreation: Boolean
    ) = WorkoutPlannerActivity.newCreateWorkoutPlanIntent(
        context = context,
        showWorkoutListAfterCreation = showWorkoutListAfterCreation,
        analyticsSource = null
    )
}
