package com.stt.android.workout.planner.summary

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Scaffold
import androidx.compose.material.SnackbarResult
import androidx.compose.material.rememberScaffoldState
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.findNavController
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.util.conditionallyCollectAsState
import com.stt.android.ui.utils.formatAsRelativeDate
import com.stt.android.utils.CalendarProvider
import com.stt.android.workout.planner.R
import com.stt.android.workout.planner.WorkoutPlannerActivity
import com.stt.android.workout.planner.WorkoutPlannerViewModel
import com.stt.android.workout.planner.common.AppBarWithBackNavigation
import com.stt.android.workout.planner.common.WorkoutPlannerBottomButtons
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import com.stt.android.R as BaseR

@AndroidEntryPoint
class SummaryFragment : Fragment() {
    private val viewModel: WorkoutPlannerViewModel by activityViewModels()

    @Inject
    lateinit var calendarProvider: CalendarProvider

    private var exitingView = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ) = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )

        setContent {
            AppTheme {
                val scaffoldState = rememberScaffoldState()
                val viewState by viewModel.basicDetailsViewState.conditionallyCollectAsState { !exitingView }

                Scaffold(
                    scaffoldState = scaffoldState,
                    topBar = {
                        val titleResource by viewModel.titleResource.collectAsState()
                        AppBarWithBackNavigation(
                            title = stringResource(titleResource),
                            fragment = this@SummaryFragment
                        )
                    },
                    bottomBar = {
                        val savingState by viewModel.savingState.collectAsState()

                        WorkoutPlannerBottomButtons(
                            secondaryButtonText = stringResource(R.string.workout_planner_bottom_button_back),
                            onSecondaryButtonClick = { findNavController().popBackStack() },
                            primaryButtonEnabled = viewState.basicDetailsValid,
                            primaryButtonText = stringResource(R.string.workout_planner_bottom_button_save),
                            onPrimaryButtonClick = {
                                exitingView = true
                                viewModel.savePlanAsGuideOrUpdateExisting()
                            },
                            showProgressBarInPrimaryButton = savingState == WorkoutPlannerViewModel.PlanSavingState.SAVING
                        )
                    }
                ) { internalPadding ->
                    val loadingState by viewModel.loadingState.conditionallyCollectAsState { !exitingView }
                    val totalDuration by viewModel.totalDuration.conditionallyCollectAsState { !exitingView }
                    val totalDistance by viewModel.totalDistance.conditionallyCollectAsState { !exitingView }
                    val savingState by viewModel.savingState.collectAsState()

                    var showActivityTypePicker by rememberSaveable { mutableStateOf(false) }
                    var showDatePicker by rememberSaveable { mutableStateOf(false) }
                    var selectedMonth by rememberSaveable { mutableStateOf(viewState.initialMonth) }

                    LaunchedEffect(savingState) {
                        if (savingState == WorkoutPlannerViewModel.PlanSavingState.FAILED) {
                            val snackbarResult = scaffoldState.snackbarHostState.showSnackbar(
                                message = getString(BaseR.string.error_generic_try_again),
                                actionLabel = getString(BaseR.string.retry_action),
                            )

                            if (snackbarResult == SnackbarResult.ActionPerformed) {
                                // Retry saving
                                viewModel.savePlanAsGuideOrUpdateExisting()
                            }
                        }
                    }

                    val date = if (!viewState.skipDateSelection) {
                        (viewState.date ?: viewState.today).formatAsRelativeDate()
                    } else {
                        null
                    }

                    SummaryView(
                        isSaving = savingState == WorkoutPlannerViewModel.PlanSavingState.SAVING,
                        name = viewState.name,
                        onNameChange = { viewModel.setName(it) },
                        description = viewState.description,
                        onDescriptionChange = { viewModel.setDescription(it) },
                        activityType = viewState.activityType,
                        onSelectActivityType = { showActivityTypePicker = true },
                        date = date,
                        onSelectDate = { showDatePicker = true },
                        totalDuration = totalDuration,
                        totalDistance = totalDistance,
                        showDeleteButton = loadingState == WorkoutPlannerViewModel.PlanLoadingState.LOADED,
                        onDeleteConfirm = {
                            exitingView = true
                            viewModel.deletePlan()
                            (requireActivity() as? WorkoutPlannerActivity)?.finishOrReturnToPlansList(
                                savedSuccessfully = false
                            )
                        },
                        modifier = Modifier.padding(internalPadding)
                    )

                    if (showActivityTypePicker) {
                        ActivityTypePicker(
                            onDismissRequest = { showActivityTypePicker = false },
                            activityTypes = viewModel.availableActivityTypes,
                            selected = viewState.activityType,
                            onSelectedChange = {
                                showActivityTypePicker = false
                                viewModel.setActivityType(it)
                            },
                        )
                    }
                    if (showDatePicker) {
                        DatePicker(
                            onDismissRequest = { showDatePicker = false },
                            weekFields = calendarProvider.getWeekFields(),
                            skipDateSelection = viewState.skipDateSelection,
                            onSkipDateSelectionChange = {
                                viewModel.setSkipDateSelection(it)
                            },
                            date = viewState.date,
                            onDateChange = { viewModel.setDate(it) },
                            minDate = viewState.today,
                            selectedMonth = selectedMonth,
                            onPreviousMonthClick = {
                                selectedMonth = selectedMonth.minusMonths(1L)
                            },
                            onNextMonthClick = {
                                selectedMonth = selectedMonth.plusMonths(1L)
                            },
                            enablePreviousMonth = !viewState.skipDateSelection && selectedMonth > viewState.initialMonth,
                        )
                    }
                }
            }
        }
    }
}
