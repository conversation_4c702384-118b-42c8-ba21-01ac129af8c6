package com.stt.android.workout.planner.steps

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.spacing

/**
 * A card with a colored vertical stripe on the left
 */
@Composable
fun ExerciseStepCard(
    color: Color,
    onClick: (() -> Unit)?,
    modifier: Modifier = Modifier,
    elevation: Dp = 0.dp,
    content: @Composable () -> Unit = {}
) {
    val clickableModifier = if (onClick != null) {
        Modifier.clickable(
            indication = null,
            interactionSource = remember { MutableInteractionSource() },
            onClick = onClick
        )
    } else {
        Modifier
    }

    Surface(
        modifier = modifier
            .fillMaxWidth()
            .height(IntrinsicSize.Min)
            .then(clickableModifier),
        elevation = elevation,
    ) {
        Row {
            // Box for the vertical stripe
            Box(
                Modifier
                    .width(8.dp)
                    .fillMaxHeight()
                    .background(color)
            )

            // Card content
            content()
        }
    }
}

@Composable
@Preview
private fun ExerciseStepCardPreview() {
    AppTheme {
        Surface(color = MaterialTheme.colors.background) {
            ExerciseStepCard(
                modifier = Modifier.padding(vertical = MaterialTheme.spacing.medium),
                color = Color(0xFF21CE56),
                onClick = {},
            ) {
                Text(
                    text = "Warm up",
                    modifier = Modifier.padding(MaterialTheme.spacing.medium)
                )
            }
        }
    }
}
