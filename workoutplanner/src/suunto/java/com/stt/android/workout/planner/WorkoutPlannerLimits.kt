package com.stt.android.workout.planner

import com.stt.android.domain.user.MeasurementUnit

object WorkoutPlannerLimits {
    val maxDistance = 10_000_000.0 // 10 000 km in meters
    val heartRateBpm = 50..220
    val powerWatt = 50..2500

    private val minPaceMetric = MeasurementUnit.METRIC.fromPaceUnit(30.0)
    private val maxPaceMetric = MeasurementUnit.METRIC.fromPaceUnit(15.0 / 60.0)
    val paceMetric = minPaceMetric..maxPaceMetric // 0'15 /km to 30'00 /km as m/s

    private val minPaceImperial = MeasurementUnit.IMPERIAL.fromPaceUnit(50.0)
    private val maxPaceImperial = MeasurementUnit.IMPERIAL.fromPaceUnit(24.0 / 60.0)
    val paceImperial = minPaceImperial..maxPaceImperial // 0'24 /mi to 50'00 /mi as m/s

    private val minSpeedMetric = MeasurementUnit.METRIC.fromSpeedUnit(0.0)
    private val maxSpeedMetric = MeasurementUnit.METRIC.fromSpeedUnit(250.0)
    val speedMetric = minSpeedMetric..maxSpeedMetric // 0..250 km/h m/s

    private val minSpeedImperial = MeasurementUnit.IMPERIAL.fromSpeedUnit(0.0)
    private val maxSpeedImperial = MeasurementUnit.IMPERIAL.fromSpeedUnit(155.0)
    val speedImperial = minSpeedImperial..maxSpeedImperial // 0..155 mph as m/s

    fun maxSpeedInUserUnit(unit: MeasurementUnit): Double =
        if (unit == MeasurementUnit.IMPERIAL) {
            unit.toSpeedUnit(speedImperial.endInclusive)
        } else {
            unit.toSpeedUnit(speedMetric.endInclusive)
        }

    fun coerceSpeedToLimit(speedMetersPerSec: Double, unit: MeasurementUnit): Double =
        if (unit == MeasurementUnit.IMPERIAL) {
            speedMetersPerSec.coerceIn(speedImperial)
        } else {
            speedMetersPerSec.coerceIn(speedMetric)
        }

    fun coercePaceToLimit(paceMetersPerSec: Double, unit: MeasurementUnit): Double =
        if (unit == MeasurementUnit.IMPERIAL) {
            paceMetersPerSec.coerceIn(paceImperial)
        } else {
            paceMetersPerSec.coerceIn(paceMetric)
        }
}
