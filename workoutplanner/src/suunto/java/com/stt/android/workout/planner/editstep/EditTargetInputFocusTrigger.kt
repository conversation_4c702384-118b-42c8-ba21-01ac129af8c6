package com.stt.android.workout.planner.editstep

import androidx.compose.ui.text.input.TextFieldValue
import com.stt.android.workout.planner.WorkoutPlannerLimits

object EditTargetInputFocusTrigger {
    fun shouldFocusNextHeartRateInputField(input: TextFieldValue): Bo<PERSON>an {
        // Focus max value input field after entering 3 digits to min value
        return input.text.length >= 3
    }

    @Suppress("ReplaceRangeStartEndInclusiveWithFirstLast")
    fun shouldFocusNextPowerInputField(input: TextFieldValue): Boolean {
        // Focus max power input field when min input field has either
        // a) 4 digits, or
        // b) 3 digits with a numerical value of 251 or above (4th digit would exceed limit)
        //
        // This way user can enter values of 251 and higher without manually changing focus. For
        // values of 250 and below, we cannot know if the user is trying to enter 3 or 4 digits so
        // let's not mess with the focus in that case.
        val maxThreeDigitValue = WorkoutPlannerLimits.powerWatt.endInclusive / 10
        val intValue = input.text.toIntOrNull() ?: 0
        return input.text.length >= 4 || (input.text.length >= 3 && intValue > maxThreeDigitValue)
    }

    fun shouldFocusNextSpeedInputField(input: TextFieldValue): Boolean {
        // Focus max value input field after entering one decimal to min value
        return input.text matches AT_LEAST_ONE_DECIMAL_DIGIT_REGEX
    }

    fun shouldFocusNextPaceInputField(input: TextFieldValue): Boolean {
        // Focus max value input after entering 4 digits to min value
        return input.text.length >= 4
    }

    private val AT_LEAST_ONE_DECIMAL_DIGIT_REGEX = Regex("\\d*\\.\\d$")
}
