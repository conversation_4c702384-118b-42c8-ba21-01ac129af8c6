package com.stt.android.workout.planner.editstep

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.MaterialTheme
import androidx.compose.material.OutlinedTextField
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.sp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.darkGreyText
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.isCursorAtEnd
import com.stt.android.compose.util.withAllSelected
import com.stt.android.workout.planner.R
import com.stt.android.workout.planner.common.DurationVisualTransformation
import kotlinx.coroutines.delay

enum class EditRangeInputOrder {
    MIN_IS_FIRST,
    MAX_IS_FIRST
}

@Composable
fun EditTargetRangeInput(
    state: EditStepTargetMinMaxState,
    onStateChange: (EditStepTargetMinMaxStatePartialUpdate) -> Unit,
    valueValidator: (TextFieldValue) -> Boolean,
    visualTransformation: VisualTransformation,
    focusSecondValueTrigger: (TextFieldValue) -> Boolean,
    modifier: Modifier = Modifier,
    inputOrder: EditRangeInputOrder = EditRangeInputOrder.MIN_IS_FIRST,
) {
    if (inputOrder == EditRangeInputOrder.MIN_IS_FIRST) {
        EditTargetRangeInput(
            firstValue = state.minValue,
            onFirstValueChange = { onStateChange(EditStepTargetMinMaxStatePartialUpdate(minValue = it)) },
            firstValueLabel = stringResource(R.string.workout_planner_step_target_range_minimum),
            secondValue = state.maxValue,
            onSecondValueChange = { onStateChange(EditStepTargetMinMaxStatePartialUpdate(maxValue = it)) },
            secondValueLabel = stringResource(R.string.workout_planner_step_target_range_maximum),
            valueValidator = valueValidator,
            visualTransformation = visualTransformation,
            unitText = state.unitText,
            focusSecondValueTrigger = focusSecondValueTrigger,
            modifier = modifier
        )
    } else {
        EditTargetRangeInput(
            firstValue = state.maxValue,
            onFirstValueChange = { onStateChange(EditStepTargetMinMaxStatePartialUpdate(maxValue = it)) },
            firstValueLabel = stringResource(R.string.workout_planner_step_target_range_maximum),
            secondValue = state.minValue,
            onSecondValueChange = { onStateChange(EditStepTargetMinMaxStatePartialUpdate(minValue = it)) },
            secondValueLabel = stringResource(R.string.workout_planner_step_target_range_minimum),
            valueValidator = valueValidator,
            visualTransformation = visualTransformation,
            unitText = state.unitText,
            focusSecondValueTrigger = focusSecondValueTrigger,
            modifier = modifier
        )
    }
}

@Composable
private fun EditTargetRangeInput(
    firstValue: TextFieldValue,
    onFirstValueChange: (TextFieldValue) -> Unit,
    firstValueLabel: String,
    secondValue: TextFieldValue,
    onSecondValueChange: (TextFieldValue) -> Unit,
    secondValueLabel: String,
    valueValidator: (TextFieldValue) -> Boolean,
    visualTransformation: VisualTransformation,
    unitText: String,
    focusSecondValueTrigger: (TextFieldValue) -> Boolean,
    modifier: Modifier = Modifier,
) {
    val focusManager = LocalFocusManager.current
    val firstValueFocusRequester = remember { FocusRequester() }
    val secondValueFocusRequester = remember { FocusRequester() }

    Row(
        modifier
            .background(color = MaterialTheme.colors.background)
            .padding(horizontal = MaterialTheme.spacing.medium)
            .padding(vertical = MaterialTheme.spacing.xsmall),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.End
    ) {
        val maxWidth = with(LocalDensity.current) { 100.sp.toDp() }
        OutlinedTextField(
            modifier = Modifier
                .widthIn(max = maxWidth)
                .focusRequester(firstValueFocusRequester),
            label = { Text(text = firstValueLabel) },
            value = firstValue,
            onValueChange = { newValue ->
                if (valueValidator(newValue)) {
                    onFirstValueChange(newValue)
                    if (newValue.text.length > firstValue.text.length && newValue.isCursorAtEnd() && focusSecondValueTrigger(newValue)) {
                        // Automatically move focus to second value after inputting enough
                        // characters to the first value.
                        // This is only done when user is inputting characters at the end of the
                        // first input field.
                        onSecondValueChange(secondValue.withAllSelected())
                        // todo this autofocus crashes for some reason, prob compose bug. Temporarily disabling for release
//                        secondValueFocusRequester.requestFocus()
                    }
                }
            },
            singleLine = true,
            keyboardOptions = KeyboardOptions.Default.copy(
                keyboardType = KeyboardType.Number,
                imeAction = ImeAction.Next
            ),
            keyboardActions = KeyboardActions(
                onNext = {
                    onSecondValueChange(secondValue.withAllSelected())
                    secondValueFocusRequester.requestFocus()
                }
            ),
            visualTransformation = visualTransformation,
            textStyle = MaterialTheme.typography.bodyXLargeBold
        )

        Spacer(modifier = Modifier.width(MaterialTheme.spacing.xsmall))
        Text("–")
        Spacer(modifier = Modifier.width(MaterialTheme.spacing.xsmall))

        OutlinedTextField(
            modifier = Modifier
                .widthIn(max = maxWidth)
                .focusRequester(secondValueFocusRequester),
            label = { Text(text = secondValueLabel) },
            value = secondValue,
            onValueChange = { newValue ->
                if (valueValidator(newValue)) {
                    onSecondValueChange(newValue)
                }
            },
            singleLine = true,
            keyboardOptions = KeyboardOptions.Default.copy(keyboardType = KeyboardType.Number),
            keyboardActions = KeyboardActions(
                onDone = {
                    focusManager.clearFocus()
                }
            ),
            visualTransformation = visualTransformation,
            textStyle = MaterialTheme.typography.bodyXLargeBold
        )

        Text(
            text = unitText,
            modifier = Modifier.padding(start = MaterialTheme.spacing.small)
        )

        LaunchedEffect(Unit) {
            // Slight delay helps make the opening transition a bit nicer since the duration/target
            // popup is faded in first before the keyboard slides into view.
            delay(150)
            firstValueFocusRequester.requestFocus()
            onFirstValueChange(firstValue.withAllSelected())
        }
    }
}

@Preview
@Composable
private fun EditTargetRangeInputPreview() {
    AppTheme {
        Surface {
            var minValue by remember { mutableStateOf(TextFieldValue("455")) }
            var maxValue by remember { mutableStateOf(TextFieldValue("435")) }
            EditTargetRangeInput(
                firstValue = minValue,
                onFirstValueChange = { minValue = it },
                firstValueLabel = "Min",
                secondValue = maxValue,
                onSecondValueChange = { maxValue = it },
                secondValueLabel = "Max",
                valueValidator = { true },
                visualTransformation = DurationVisualTransformation(SpanStyle(color = MaterialTheme.colors.darkGreyText)),
                unitText = "unit",
                focusSecondValueTrigger = { false }
            )
        }
    }
}
