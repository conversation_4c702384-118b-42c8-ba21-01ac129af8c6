package com.stt.android.workout.planner.common

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.bodySmallBold
import com.stt.android.compose.theme.disabledColor
import com.stt.android.compose.theme.spacing
import com.stt.android.utils.CalendarUtils
import java.time.LocalDate
import java.time.YearMonth
import java.time.temporal.WeekFields

@Composable
fun MonthCalendar(
    month: YearMonth,
    weekFields: WeekFields,
    selectedDate: LocalDate?,
    onSelectedDateChange: (LocalDate) -> Unit,
    minDate: LocalDate,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
) {
    val firstOfMonth = month.atDay(1)
    val lastOfMonth = month.atEndOfMonth()
    val dayOfWeek = weekFields.dayOfWeek()
    val startOfFirstRow = firstOfMonth.with(dayOfWeek, 1)
    var date = startOfFirstRow

    val dayOfWeekStyle =
        MaterialTheme.typography.bodySmall.copy(color = MaterialTheme.colors.disabledColor)
    val dayNumberStyle = MaterialTheme.typography.bodySmallBold

    Column(modifier = modifier) {
        val weekDayLabels =
            CalendarUtils.buildDayOfWeekLabels(startOfFirstRow, dayOfWeek)

        Row(
            modifier = Modifier.padding(bottom = MaterialTheme.spacing.small),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small)
        ) {
            for (weekDayLabel in weekDayLabels) {
                Text(
                    weekDayLabel.take(3),
                    modifier = Modifier.width(40.dp),
                    textAlign = TextAlign.Center,
                    style = dayOfWeekStyle
                )
            }
        }

        while (date <= lastOfMonth) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small)
            ) {
                repeat(7) {
                    val textStyle = when {
                        selectedDate == date ->
                            dayNumberStyle.copy(color = MaterialTheme.colors.onPrimary)
                        !enabled || date < minDate ->
                            dayNumberStyle.copy(color = MaterialTheme.colors.disabledColor)
                        else ->
                            dayNumberStyle
                    }

                    val backgroundModifier = when {
                        // Not in current month (not clickable)
                        date !in firstOfMonth..lastOfMonth ->
                            Modifier.size(40.dp)

                        // Selected and enabled (not clickable)
                        enabled && selectedDate == date ->
                            Modifier
                                .size(40.dp)
                                .clip(CircleShape)
                                .background(MaterialTheme.colors.primary)

                        // Selected but disabled and dimmed (not clickable)
                        !enabled && selectedDate == date ->
                            Modifier
                                .size(40.dp)
                                .clip(CircleShape)
                                .background(MaterialTheme.colors.disabledColor)

                        // Not selected but otherwise valid date (clickable)
                        enabled && date >= minDate -> {
                            // Grab a copy of the LocalDate instance since the reference changes
                            val localDate = LocalDate.from(date)
                            Modifier
                                .size(40.dp)
                                .clip(CircleShape)
                                .clickable { onSelectedDateChange(localDate) }
                        }

                        else -> Modifier.size(40.dp)
                    }

                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = backgroundModifier
                    ) {
                        if (date in firstOfMonth..lastOfMonth) {
                            Text(
                                text = date.dayOfMonth.toString(),
                                modifier = Modifier.align(Alignment.Center),
                                style = textStyle
                            )
                        }
                    }
                    date = date.plusDays(1L)
                }
            }
        }
    }
}

@Composable
@Preview
private fun MonthCalendarPreview() {
    AppTheme {
        Surface {
            MonthCalendar(
                modifier = Modifier.padding(MaterialTheme.spacing.medium),
                month = YearMonth.of(2022, 4),
                weekFields = WeekFields.SUNDAY_START,
                selectedDate = LocalDate.of(2022, 4, 16),
                onSelectedDateChange = {},
                minDate = LocalDate.of(2022, 4, 12)
            )
        }
    }
}

@Composable
@Preview
private fun DisabledMonthCalendarPreview() {
    AppTheme {
        Surface {
            MonthCalendar(
                modifier = Modifier.padding(MaterialTheme.spacing.medium),
                month = YearMonth.of(2022, 4),
                weekFields = WeekFields.SUNDAY_START,
                selectedDate = LocalDate.of(2022, 4, 16),
                onSelectedDateChange = {},
                enabled = false,
                minDate = LocalDate.of(2022, 4, 12)
            )
        }
    }
}
