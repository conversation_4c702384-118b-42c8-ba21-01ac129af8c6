package com.stt.android.workout.planner.editstep

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.MaterialTheme
import androidx.compose.material.OutlinedTextField
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.darkGreyText
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.withAllSelected
import com.stt.android.workout.planner.common.DurationVisualTransformation
import kotlinx.coroutines.delay

@Composable
fun EditTargetInput(
    value: TextFieldValue,
    onValueChange: (TextFieldValue) -> Unit,
    valueValidator: (TextFieldValue) -> Boolean,
    visualTransformation: VisualTransformation,
    unitText: String,
    modifier: Modifier = Modifier
) {
    val focusManager = LocalFocusManager.current
    val focusRequester = remember { FocusRequester() }

    Row(
        modifier
            .focusRequester(focusRequester)
            .background(color = MaterialTheme.colors.background)
            .padding(horizontal = MaterialTheme.spacing.medium)
            .padding(vertical = MaterialTheme.spacing.xsmall),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.End
    ) {
        OutlinedTextField(
            modifier = Modifier
                .defaultMinSize(60.dp),
            value = value,
            onValueChange = { newValue ->
                if (valueValidator(newValue)) {
                    onValueChange(newValue)
                }
            },
            singleLine = true,
            keyboardOptions = KeyboardOptions.Default.copy(keyboardType = KeyboardType.Number),
            keyboardActions = KeyboardActions(
                onDone = {
                    focusManager.clearFocus()
                }
            ),
            visualTransformation = visualTransformation,
            textStyle = MaterialTheme.typography.bodyXLargeBold
        )

        Text(
            text = unitText,
            modifier = Modifier.padding(start = MaterialTheme.spacing.small)
        )

        LaunchedEffect(Unit) {
            // Slight delay helps make the opening transition a bit nicer since the duration/target
            // popup is faded in first before the keyboard slides into view.
            delay(150)
            focusRequester.requestFocus()
            onValueChange(value.withAllSelected())
        }
    }
}

@Preview
@Composable
private fun EditTargetInputPreview() {
    AppTheme {
        Surface {
            var value by remember { mutableStateOf(TextFieldValue("1234")) }
            EditTargetInput(
                value = value,
                onValueChange = { value = it },
                valueValidator = { true },
                visualTransformation = DurationVisualTransformation(SpanStyle(color = MaterialTheme.colors.darkGreyText)),
                unitText = "unit"
            )
        }
    }
}
