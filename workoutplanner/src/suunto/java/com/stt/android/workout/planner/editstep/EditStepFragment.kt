package com.stt.android.workout.planner.editstep

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Scaffold
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.findNavController
import androidx.navigation.fragment.navArgs
import com.soy.algorithms.planner.WorkoutStep
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.util.conditionallyCollectAsState
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.workout.planner.R
import com.stt.android.workout.planner.WorkoutPlanStepsListItem
import com.stt.android.workout.planner.WorkoutPlannerViewModel
import com.stt.android.workout.planner.common.AppBarWithBackNavigation
import com.stt.android.SimGuideMessagesFormatter
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.map
import javax.inject.Inject

@AndroidEntryPoint
class EditStepFragment : Fragment() {
    private val viewModel: WorkoutPlannerViewModel by activityViewModels()
    private val args: EditStepFragmentArgs by navArgs()

    @Inject
    lateinit var formatter: SimGuideMessagesFormatter

    @Inject
    lateinit var unit: MeasurementUnit

    private var exitingView = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ) = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )

        exitingView = false

        val stepFlow = viewModel.stepsState.map { viewState ->
            viewState.steps
                .filterIsInstance<WorkoutPlanStepsListItem.ExerciseStep>()
                .firstOrNull { step -> step.step.uuid == args.workoutStepUuid }
        }

        setContent {
            AppTheme {
                Scaffold(
                    topBar = {
                        AppBarWithBackNavigation(
                            title = stringResource(R.string.workout_planner_edit_step_title),
                            fragment = this@EditStepFragment
                        )
                    },
                ) { internalPadding ->
                    val step by stepFlow.conditionallyCollectAsState(initial = null) { !exitingView }

                    EditStepView(
                        modifier = Modifier
                            .padding(internalPadding),
                        formatter = formatter,
                        unit = unit,
                        phase = step?.step?.phase
                            ?: WorkoutStep.Exercise.Phase.WARM_UP,
                        duration = step?.step?.duration
                            ?: WorkoutStep.Exercise.Duration.LapButton,
                        target = step?.step?.target,
                        name = step?.step?.name,
                        onNameChange = { newName ->
                            step?.step?.let { step ->
                                viewModel.editStepName(step, newName)
                            }
                        },
                        onPhaseChange = { newPhase ->
                            step?.step?.let { step ->
                                viewModel.editStepPhase(step, newPhase)
                            }
                        },
                        onTargetChange = { newTarget ->
                            step?.step?.let { step ->
                                viewModel.editStepTarget(step, newTarget)
                            }
                        },
                        onDurationChange = { newDuration ->
                            step?.step?.let { step ->
                                viewModel.editStepDuration(step, newDuration)
                            }
                        },
                        onDeleteConfirm = {
                            exitingView = true
                            step?.step?.uuid?.let { uuid ->
                                viewModel.deleteStep(uuid)
                                viewModel.deleteEmptyRepeatBlocks()
                            }
                            findNavController().popBackStack()
                        }
                    )
                }
            }
        }
    }
}
