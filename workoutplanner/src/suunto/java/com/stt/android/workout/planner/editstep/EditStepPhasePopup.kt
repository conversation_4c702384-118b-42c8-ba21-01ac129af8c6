package com.stt.android.workout.planner.editstep

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.RadioButton
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.soy.algorithms.planner.WorkoutStep
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.header
import com.stt.android.compose.theme.spacing
import com.stt.android.workout.planner.R
import com.stt.android.workout.planner.common.WorkoutStepPhaseSwatch
import com.stt.android.utils.displayNameResource
import java.util.Locale
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

@Composable
fun EditStepPhasePopup(
    phase: WorkoutStep.Exercise.Phase,
    onPhaseChange: (WorkoutStep.Exercise.Phase) -> Unit,
    onDismissRequest: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Dialog(
        onDismissRequest = onDismissRequest,
        // work around https://issuetracker.google.com/issues/221643630
        properties = DialogProperties(usePlatformDefaultWidth = false)
    ) {
        Surface(
            modifier = modifier
                .widthIn(max = dimensionResource(CR.dimen.dialog_max_width))
                .padding(horizontal = MaterialTheme.spacing.large)
                .fillMaxWidth(),
            shape = MaterialTheme.shapes.medium
        ) {
            Column(
                modifier = Modifier.verticalScroll(rememberScrollState())
            ) {
                Text(
                    text = stringResource(R.string.workout_planner_edit_step_phase_title)
                        .uppercase(Locale.getDefault()),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(MaterialTheme.spacing.medium),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.header
                )

                val phaseValues = listOf(
                    WorkoutStep.Exercise.Phase.WARM_UP,
                    WorkoutStep.Exercise.Phase.INTERVAL,
                    WorkoutStep.Exercise.Phase.REST,
                    WorkoutStep.Exercise.Phase.COOL_DOWN
                )

                for (phaseValue in phaseValues) {
                    val selected = phase == phaseValue
                    Row(
                        Modifier
                            .fillMaxWidth()
                            .selectable(
                                selected = selected,
                                onClick = { onPhaseChange(phaseValue) }
                            )
                            .padding(start = MaterialTheme.spacing.medium),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = selected,
                            onClick = { onPhaseChange(phaseValue) }
                        )
                        WorkoutStepPhaseSwatch(phase = phaseValue)
                        Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))
                        Text(
                            text = stringResource(phaseValue.displayNameResource),
                            modifier = Modifier.weight(1f)
                        )
                    }
                }

                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.CenterEnd
                ) {
                    TextButton(
                        onClick = onDismissRequest,
                        modifier = Modifier.padding(MaterialTheme.spacing.medium),
                    ) {
                        Text(stringResource(BaseR.string.ok))
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun EditPhasePopupPreview() {
    // Use interactive mode if bottom sheet is not visible
    AppTheme {
        EditStepPhasePopup(
            phase = WorkoutStep.Exercise.Phase.INTERVAL,
            onPhaseChange = {},
            onDismissRequest = {}
        )
    }
}
