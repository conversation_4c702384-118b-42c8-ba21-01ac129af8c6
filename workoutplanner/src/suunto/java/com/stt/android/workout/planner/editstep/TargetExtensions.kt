package com.stt.android.workout.planner.editstep

import com.soy.algorithms.planner.WorkoutStep
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.workout.planner.WorkoutPlannerLimits
import kotlin.math.roundToInt

fun WorkoutStep.Exercise.Target?.minHeartRateOrNull(): Int? =
    (this as? WorkoutStep.Exercise.Target.HeartRate)?.let {
        (min ?: value)?.roundToInt()?.coerceIn(WorkoutPlannerLimits.heartRateBpm)
    }

fun WorkoutStep.Exercise.Target?.maxHeartRateOrNull(): Int? =
    (this as? WorkoutStep.Exercise.Target.HeartRate)?.let {
        (max ?: value)?.roundToInt()?.coerceIn(WorkoutPlannerLimits.heartRateBpm)
    }

fun WorkoutStep.Exercise.Target?.minPowerOrNull(): Int? =
    (this as? WorkoutStep.Exercise.Target.Power)?.let {
        (min ?: value)?.roundToInt()?.coerceIn(WorkoutPlannerLimits.powerWatt)
    }

fun WorkoutStep.Exercise.Target?.maxPowerOrNull(): Int? =
    (this as? WorkoutStep.Exercise.Target.Power)?.let {
        (max ?: value)?.roundToInt()?.coerceIn(WorkoutPlannerLimits.powerWatt)
    }

fun WorkoutStep.Exercise.Target?.minSpeedOrNull(unit: MeasurementUnit): Double? =
    (this as? WorkoutStep.Exercise.Target.Speed)?.let {
        min ?: value
    }?.let { WorkoutPlannerLimits.coerceSpeedToLimit(it, unit) }

fun WorkoutStep.Exercise.Target?.maxSpeedOrNull(unit: MeasurementUnit): Double? =
    (this as? WorkoutStep.Exercise.Target.Speed)?.let {
        max ?: value
    }?.let { WorkoutPlannerLimits.coerceSpeedToLimit(it, unit) }

fun WorkoutStep.Exercise.Target?.minPaceOrNull(unit: MeasurementUnit): Double? =
    (this as? WorkoutStep.Exercise.Target.Pace)?.let {
        min ?: value
    }?.let { WorkoutPlannerLimits.coerceSpeedToLimit(it, unit) }

fun WorkoutStep.Exercise.Target?.maxPaceOrNull(unit: MeasurementUnit): Double? =
    (this as? WorkoutStep.Exercise.Target.Pace)?.let {
        max ?: value
    }?.let { WorkoutPlannerLimits.coerceSpeedToLimit(it, unit) }
