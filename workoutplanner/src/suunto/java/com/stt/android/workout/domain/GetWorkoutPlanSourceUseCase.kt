package com.stt.android.workout.domain

import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuideId
import com.stt.android.device.remote.suuntoplusguide.SuuntoPlusGuideRemoteDataSource
import com.stt.android.di.InternalFilesDir
import com.stt.android.utils.ZipUtils
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileNotFoundException
import java.util.zip.ZipEntry
import java.util.zip.ZipInputStream
import javax.inject.Inject

class GetWorkoutPlanSourceUseCase @Inject constructor(
    private val remoteDataSource: SuuntoPlusGuideRemoteDataSource,
    @InternalFilesDir private val internalFilesDir: File
) {
    suspend fun getWorkoutPlanSourceJson(guideId: SuuntoPlusGuideId): String = withContext(IO) {
        val zipFile = remoteDataSource.fetchSource(guideId)

        var bytes: ByteArray? = null

        ZipInputStream(zipFile.inputStream()).use { zinput ->
            var zipEntry: ZipEntry? = zinput.nextEntry
            while (zipEntry != null) {
                // usage of internalFilesDir for zip path traversal checking is just arbitrary
                // as we don't actually save these files anywhere
                ZipUtils.checkZipPathTraversal(internalFilesDir, zipEntry)
                if (zipEntry.name.equals("guide.json", ignoreCase = true)) {
                    bytes = zinput.readBytes()
                    break
                }
                zipEntry = zinput.nextEntry
            }
        }

        bytes?.decodeToString() ?: throw FileNotFoundException("guide.json not found from ZIP file")
    }
}
