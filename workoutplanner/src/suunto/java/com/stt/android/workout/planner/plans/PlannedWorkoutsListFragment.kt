package com.stt.android.workout.planner.plans

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.material.rememberScaffoldState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.navigation.NavDirections
import androidx.navigation.fragment.findNavController
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.core.utils.EventThrottler
import com.stt.android.workout.planner.R
import com.stt.android.workout.planner.WorkoutPlannerWatchStateViewModel
import com.stt.android.workout.planner.instructions.WorkoutPlannerInstructionsBottomSheetDialog
import dagger.hilt.android.AndroidEntryPoint
import java.util.Locale
import com.stt.android.R as BaseR

@AndroidEntryPoint
class PlannedWorkoutsListFragment : Fragment() {

    // Use a separate view model for listing plans. Other fragments use a shared view model
    // for showing and modifying the plan in progress.
    private val viewModel: PlannedWorkoutsListViewModel by viewModels()

    // View model for listening to watch and sync state
    private val watchStateViewModel: WorkoutPlannerWatchStateViewModel by activityViewModels()

    private val bottomSheetOpeningThrottler = EventThrottler()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ) = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )

        setContent {
            val scaffoldState = rememberScaffoldState()

            AppTheme {
                Scaffold(
                    scaffoldState = scaffoldState,
                    topBar = {
                        TopAppBar(
                            title = {
                                Text(
                                    stringResource(R.string.workout_planner_my_workout_plans_title)
                                        .uppercase(Locale.getDefault())
                                )
                            },
                            contentColor = MaterialTheme.colors.onSurface,
                            backgroundColor = MaterialTheme.colors.surface, // default background is Primary
                            navigationIcon = {
                                SuuntoIconButton(
                                    icon = SuuntoIcons.ActionBack,
                                    onClick = { requireActivity().finish() },
                                    contentDescription = stringResource(BaseR.string.back),
                                )
                            },
                            actions = {
                                IconButton(
                                    onClick = { showInstructions() }
                                ) {
                                    Icon(
                                        painter = painterResource(BaseR.drawable.ic_info_outline),
                                        contentDescription = stringResource(R.string.workout_planner_empty_state_learn_how_text),
                                        tint = Color.Unspecified
                                    )
                                }
                            }
                        )
                    },
                ) { internalPadding ->
                    ContentCenteringColumn(
                        modifier = Modifier
                            .padding(internalPadding)
                    ) {
                        val plannedWorkouts by viewModel.plannedWorkoutsAsGuides.collectAsState()
                        val watchStateIndication by watchStateViewModel.watchStateIndicationData.collectAsState(null)

                        PlannedWorkoutsList(
                            workoutPlanList = plannedWorkouts,
                            onWorkoutPlanClick = { guideId ->
                                navigateSafely(
                                    PlannedWorkoutsListFragmentDirections.editExistingWorkoutPlan(
                                        guideId.id
                                    )
                                )
                            },
                            onNewWorkoutPlanClick = {
                                navigateSafely(
                                    PlannedWorkoutsListFragmentDirections.newWorkoutPlan(null)
                                )
                            },
                            onGetStartedClick = {
                                showInstructions()
                            },
                            onDeleteWorkoutPlanClick = {
                                viewModel.deleteWorkoutPlan(it)
                            },
                            watchStateIndicationData = watchStateIndication
                        )
                    }
                }
            }
        }
    }

    override fun onStart() {
        super.onStart()

        viewModel.syncWithRemote()
        watchStateViewModel.refreshWatchConnectionAndGuideSupportStatus()
    }

    private fun navigateSafely(directions: NavDirections) = with(findNavController()) {
        if (currentDestination?.id == R.id.plannedWorkoutsListFragment) {
            navigate(directions)
        }
    }

    private fun showInstructions() {
        if (!bottomSheetOpeningThrottler.checkAcceptEvent()) return

        WorkoutPlannerInstructionsBottomSheetDialog()
            .show(childFragmentManager, INSTRUCTIONS_BOTTOM_SHEET_TAG)
    }

    companion object {
        private const val INSTRUCTIONS_BOTTOM_SHEET_TAG =
            "com.stt.android.workout.planner.INSTRUCTIONS"
    }
}
