package com.stt.android.workout.planner.common

import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.spacing
import com.stt.android.workout.planner.R
import kotlinx.collections.immutable.ImmutableList
import com.stt.android.R as BaseR

data class BreadcrumbStep(
    @StringRes val titleResource: Int,
    val stepIdentifier: String, // Number, but could be A, B, C
)

@Composable
fun BreadcrumbStepIndicator(
    steps: ImmutableList<BreadcrumbStep>,
    currentStepIndex: Int,
    modifier: Modifier = Modifier,
    completedColor: Color = MaterialTheme.colors.primary,
    incompleteColor: Color = Color(0xFFD2D2D2),
) {
    Column(
        modifier = modifier.then(Modifier.fillMaxWidth()),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
    ) {
        Box(
            modifier = Modifier.fillMaxWidth(),
            contentAlignment = Alignment.Center,
        ) {
            // Horizontal line for background
            Row(
                modifier = Modifier.fillMaxWidth((steps.size - 1f) / (steps.size)),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                for (i in 0 until steps.lastIndex) {
                    val color = if (i < currentStepIndex) completedColor else incompleteColor
                    Box(
                        modifier = Modifier
                            .background(color)
                            .height(dimensionResource(R.dimen.breadcrumb_step_indicator_line_thickness))
                            .weight(1f)
                    )
                }
            }

            // Bubbles for steps
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceAround,
            ) {
                steps.forEachIndexed { index, step ->
                    val done = index < currentStepIndex
                    val current = index == currentStepIndex

                    BreadcrumbStepIndicatorBubble(step = step, done = done, current = current)
                }
            }
        }

        // Labels for steps
        // Spread around to match the bubble positions horizontally
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceAround,
        ) {
            steps.forEachIndexed { index, step ->
                Text(
                    modifier = Modifier
                        .weight(1f)
                        .padding(horizontal = MaterialTheme.spacing.small),
                    text = stringResource(step.titleResource),
                    fontSize = 12.sp,
                    textAlign = TextAlign.Center,
                    fontWeight = if (index == currentStepIndex) FontWeight.Bold else null,
                    color = if (index == currentStepIndex) completedColor else MaterialTheme.colors.onBackground
                )
            }
        }
    }
}

@Composable
private fun BreadcrumbStepIndicatorBubble(
    step: BreadcrumbStep,
    done: Boolean,
    current: Boolean,
    modifier: Modifier = Modifier,
    completedColor: Color = MaterialTheme.colors.primary,
    incompleteColor: Color = Color(0xFFD2D2D2),
) {
    val backgroundAndBorderModifier = when {
        done ->
            Modifier
                .background(MaterialTheme.colors.surface, shape = CircleShape)
                .border(
                    width = dimensionResource(R.dimen.breadcrumb_step_indicator_bubble_border),
                    color = completedColor,
                    shape = CircleShape
                )

        current ->
            Modifier
                .shadow(
                    16.dp,
                    shape = CircleShape,
                    ambientColor = completedColor,
                    spotColor = completedColor,
                )
                .background(completedColor)

        else ->
            Modifier
                .background(MaterialTheme.colors.surface, shape = CircleShape)
                .border(
                    width = dimensionResource(R.dimen.breadcrumb_step_indicator_bubble_border),
                    color = incompleteColor,
                    shape = CircleShape
                )
    }

    Box(
        modifier = modifier
            .size(dimensionResource(R.dimen.breadcrumb_step_indicator_bubble_size))
            .then(backgroundAndBorderModifier)
    ) {
        if (done) {
            Icon(
                modifier = Modifier.align(Alignment.Center),
                painter = painterResource(BaseR.drawable.ic_check_white_24),
                tint = completedColor,
                contentDescription = step.stepIdentifier
            )
        } else {
            Text(
                modifier = Modifier.align(Alignment.Center),
                text = step.stepIdentifier,
                style = MaterialTheme.typography.bodyBold,
                color = if (current) MaterialTheme.colors.surface else incompleteColor
            )
        }
    }
}

@Composable
@Preview(widthDp = 320)
@Preview(widthDp = 640)
private fun BreadcrumbStepIndicatorPreview() {
    AppTheme {
        Column(
            Modifier
                .background(MaterialTheme.colors.surface)
                .padding(MaterialTheme.spacing.medium),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium)
        ) {
            BreadcrumbStepIndicator(
                steps = BreadcrumbSteps.steps,
                currentStepIndex = 0
            )
            BreadcrumbStepIndicator(
                steps = BreadcrumbSteps.steps,
                currentStepIndex = 1
            )
        }
    }
}
