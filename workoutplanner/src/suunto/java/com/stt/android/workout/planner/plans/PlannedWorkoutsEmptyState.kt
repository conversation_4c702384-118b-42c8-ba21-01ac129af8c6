package com.stt.android.workout.planner.plans

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.darkGreyText
import com.stt.android.compose.theme.spacing
import com.stt.android.workout.planner.R

@Composable
fun PlannedWorkoutsEmptyState(
    onGetStartedClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val iconSize = 64.dp

    Box(modifier.fillMaxWidth()) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    top = iconSize / 2, // Half of whistle icon height to align vertically
                )
                .background(MaterialTheme.colors.surface)
                .padding(
                    start = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.medium,
                    bottom = MaterialTheme.spacing.medium,
                ),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Spacer(Modifier.height(iconSize / 2)) // Don't put content under icon

            Text(
                modifier = Modifier.padding(
                    start = MaterialTheme.spacing.large,
                    end = MaterialTheme.spacing.large,
                    top = MaterialTheme.spacing.large,
                    bottom = MaterialTheme.spacing.small,
                ),
                color = MaterialTheme.colors.darkGreyText,
                text = stringResource(R.string.workout_planner_empty_state_text)
            )

            TextButton(onClick = onGetStartedClick) {
                Text(
                    stringResource(R.string.workout_planner_empty_state_how_to_get_started)
                )
            }
        }

        Box(
            modifier = Modifier
                .align(Alignment.TopCenter)
                .size(iconSize)
                .border(
                    width = 6.dp,
                    color = MaterialTheme.colors.background,
                    shape = CircleShape
                )
                .clip(CircleShape)
                .background(MaterialTheme.colors.surface),
            contentAlignment = Alignment.Center,
        ) {
            Icon(
                painter = painterResource(R.drawable.adaptive_training_guidance_outline),
                tint = Color.Unspecified,
                contentDescription = null,
            )
        }
    }
}

@Composable
@Preview
private fun PlannedWorkoutsEmptyStatePreview() {
    AppTheme {
        Surface(color = MaterialTheme.colors.background) {
            PlannedWorkoutsEmptyState(onGetStartedClick = {})
        }
    }
}
