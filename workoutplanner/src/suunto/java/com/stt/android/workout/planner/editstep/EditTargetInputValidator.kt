package com.stt.android.workout.planner.editstep

import androidx.compose.ui.text.input.TextFieldValue
import com.stt.android.compose.util.isEmpty
import com.stt.android.workout.planner.WorkoutPlannerLimits
import com.stt.android.workout.planner.common.TextFieldFormattingHelper

object EditTargetInputValidator {
    fun isValidHeartRateInput(input: TextFieldValue): Boolean {
        // Allow empty values and values smaller than WorkoutPlannerLimits.heartRate.start
        // in order to make sure user can clear the input and enter some digits.
        return input.isEmpty() || TextFieldFormattingHelper.isValidInteger(
            value = input,
            range = 0..WorkoutPlannerLimits.heartRateBpm.last
        )
    }

    fun isValidPowerInput(input: TextFieldValue): Boolean {
        // Allow empty values and values smaller than WorkoutPlannerLimits.power.start
        // in order to make sure user can clear the input and enter some digits.
        return input.isEmpty() || TextFieldFormattingHelper.isValidInteger(
            value = input,
            range = 0..WorkoutPlannerLimits.powerWatt.last
        )
    }

    fun isValidSpeedInput(input: TextFieldValue, maxSpeedInUserUnits: Double): Boolean {
        return input.isEmpty() || TextFieldFormattingHelper.isValidFloatingPointValue(
            value = input,
            maxDecimals = 1,
            maximumValue = maxSpeedInUserUnits
        )
    }

    fun isValidPaceInput(input: TextFieldValue): Boolean {
        // Cannot really limit values during pace input as a single digit needs to be
        // allowed and those mean ludicrously high speeds.
        return input.isEmpty() || TextFieldFormattingHelper.isValidInteger(
            value = input,
            range = 0..9999
        )
    }
}
