package com.stt.android.workout.planner.common

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.TextDecoratorIcon
import com.stt.android.workout.planner.R
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

@Composable
fun DurationAndDistanceInfoRow(
    duration: String,
    distance: DistanceAndUnit,
    modifier: Modifier = Modifier
) {
    FallBackWhenSizeExceededLayout(
        modifier = modifier,
        measuredContent = {
            DurationAndDistanceInfoRow(
                duration = duration,
                distance = distance,
                singleLine = true,
                expandWidth = false,
            )
        },
        primaryContent = {
            DurationAndDistanceInfoRow(
                duration = duration,
                distance = distance,
                singleLine = true,
                expandWidth = true,
            )
        },
        fallbackContent = {
            DurationAndDistanceInfoRow(
                duration = duration,
                distance = distance,
                singleLine = false,
                expandWidth = true,
            )
        }
    )
}

@Composable
private fun DurationAndDistanceInfoRow(
    duration: String,
    distance: DistanceAndUnit,
    singleLine: Boolean,
    expandWidth: Boolean,
    modifier: Modifier = Modifier
) {
    val separator = if (singleLine) " " else "\n"

    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        Row(
            modifier = if (expandWidth) {
                Modifier
                    .widthIn(max = dimensionResource(CR.dimen.content_max_width))
                    .padding(MaterialTheme.spacing.medium)
            } else {
                Modifier
                    .padding(MaterialTheme.spacing.medium)
            },
            verticalAlignment = Alignment.Top,
        ) {
            Text(
                modifier = if (expandWidth) Modifier.weight(1f) else Modifier,
                text = stringResource(R.string.workout_planner_totals_row_text),
            )

            TextDecoratorIcon(
                modifier = Modifier.padding(start = MaterialTheme.spacing.small),
                drawableResource = BaseR.drawable.ic_summary_item_duration,
            )

            Text(
                modifier = Modifier.padding(start = MaterialTheme.spacing.xsmall),
                text = buildAnnotatedString {
                    append(stringResource(R.string.workout_planner_totals_row_duration))
                    append(separator)
                    withStyle(SpanStyle(fontWeight = FontWeight.Bold)) {
                        append(duration)
                    }
                },
                textAlign = TextAlign.End,
            )

            TextDecoratorIcon(
                modifier = Modifier.padding(start = MaterialTheme.spacing.small),
                drawableResource = BaseR.drawable.ic_summary_item_distance,
            )

            Text(
                modifier = Modifier.padding(start = MaterialTheme.spacing.xsmall),
                text = buildAnnotatedString {
                    append(stringResource(R.string.workout_planner_totals_row_distance))
                    append(separator)
                    append(distance.asAnnotatedString())
                },
                textAlign = TextAlign.End,
            )
        }
    }
}

@Preview(showBackground = true, backgroundColor = 0xFFFFFF, widthDp = 320)
@Preview(showBackground = true, backgroundColor = 0xFFFFFF, widthDp = 480)
@Preview(showBackground = true, backgroundColor = 0xFFFFFF, widthDp = 960)
@Composable
private fun DurationAndDistanceInfoRowPreview() {
    DurationAndDistanceInfoRow(
        duration = "1:30'00",
        distance = DistanceAndUnit(formattedValue = "22.00", unit = "km")
    )
}
