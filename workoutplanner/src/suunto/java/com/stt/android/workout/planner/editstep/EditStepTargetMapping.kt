package com.stt.android.workout.planner.editstep

import androidx.compose.ui.text.input.TextFieldValue
import com.soy.algorithms.planner.WorkoutStep
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.workout.planner.WorkoutPlannerLimits
import com.stt.android.workout.planner.common.TextFieldFormattingHelper
import com.stt.android.workout.planner.defaultHeartRateTarget
import com.stt.android.workout.planner.defaultPowerTarget
import com.stt.android.workout.planner.getDefaultPaceTargetRange
import com.stt.android.workout.planner.getDefaultSpeedTargetRange

/**
 * Convert existing workout planner step target as EditStepTargetState where all the TextFieldValues
 * are filled with properly formatted target values or default target values if not explicitly set.
 */
// TODO: Refactor target handling to avoid using library types and doing unit conversions on the UI level
@Suppress("ReplaceRangeStartEndInclusiveWithFirstLast")
fun WorkoutStep.Exercise.Target?.asEditStepTargetState(
    heartRateUnitString: String,
    powerUnitString: String,
    speedUnitString: String,
    paceUnitString: String,
    unit: MeasurementUnit, // Measurement unit for unit conversions
    phase: WorkoutStep.Exercise.Phase // Phase for determining default values
) = EditStepTargetState(
    editMode = asModeEnum(),
    phase = phase,
    unit = unit,
    heartRateTarget = EditStepTargetMinMaxState(
        minValue = TextFieldValue(minHeartRateOrNull() ?: phase.defaultHeartRateTarget.start),
        maxValue = TextFieldValue(maxHeartRateOrNull() ?: phase.defaultHeartRateTarget.endInclusive),
        unitText = heartRateUnitString,
    ),
    powerTarget = EditStepTargetMinMaxState(
        minValue = TextFieldValue(minPowerOrNull() ?: phase.defaultPowerTarget.start),
        maxValue = TextFieldValue(maxPowerOrNull() ?: phase.defaultPowerTarget.endInclusive),
        unitText = powerUnitString,
    ),
    speedTarget = EditStepTargetMinMaxState(
        minValue = TextFieldValue(
            TextFieldFormattingHelper.formatSpeed(
                unit,
                minSpeedOrNull(unit) ?: phase.getDefaultSpeedTargetRange(unit).start
            )
        ),
        maxValue = TextFieldValue(
            TextFieldFormattingHelper.formatSpeed(
                unit,
                maxSpeedOrNull(unit) ?: phase.getDefaultSpeedTargetRange(unit).endInclusive
            )
        ),
        unitText = speedUnitString,
    ),
    paceTarget = EditStepTargetMinMaxState(
        minValue = TextFieldValue(
            TextFieldFormattingHelper.formatPaceAsMMSS(
                unit,
                minPaceOrNull(unit) ?: phase.getDefaultPaceTargetRange(unit).start
            )
        ),
        maxValue = TextFieldValue(
            TextFieldFormattingHelper.formatPaceAsMMSS(
                unit,
                maxPaceOrNull(unit) ?: phase.getDefaultPaceTargetRange(unit).endInclusive
            )
        ),
        unitText = paceUnitString,
    )
)

/**
 * Convert the user inputted texts in EditStepTargetState as a workout planner step target object
 */
fun EditStepTargetState.asWorkoutStepTarget(
    unit: MeasurementUnit
) = when (editMode) {
    EditStepTargetMode.HEART_RATE -> {
        val minMaxValue = adjustMinMaxAndCalculateValue(
            inputtedMin = heartRateTarget.minValue.text.toIntOrNull()?.toDouble(),
            inputtedMax = heartRateTarget.maxValue.text.toIntOrNull()?.toDouble(),
            range = WorkoutPlannerLimits.heartRateBpm.asDoubleRange(),
            defaultRange = phase.defaultHeartRateTarget.asDoubleRange()
        )

        WorkoutStep.Exercise.Target.HeartRate(
            value = minMaxValue.value,
            min = minMaxValue.min,
            max = minMaxValue.max,
        )
    }

    EditStepTargetMode.POWER -> {
        val minMaxValue = adjustMinMaxAndCalculateValue(
            inputtedMin = powerTarget.minValue.text.toIntOrNull()?.toDouble(),
            inputtedMax = powerTarget.maxValue.text.toIntOrNull()?.toDouble(),
            range = WorkoutPlannerLimits.powerWatt.asDoubleRange(),
            defaultRange = phase.defaultPowerTarget.asDoubleRange()
        )

        WorkoutStep.Exercise.Target.Power(
            value = minMaxValue.value,
            min = minMaxValue.min,
            max = minMaxValue.max,
        )
    }

    // TODO: There is already a TODO comment at the top, but this really needs to refactored
    EditStepTargetMode.PACE -> {
        val minText = paceTarget.minValue.text
        val min = TextFieldFormattingHelper.parseDurationAsSeconds(minText)?.let {
            WorkoutPlannerLimits.coercePaceToLimit(unit.fromPaceUnit(it.toDouble() / 60.0), unit)
        }

        val maxText = paceTarget.maxValue.text
        val max = TextFieldFormattingHelper.parseDurationAsSeconds(maxText)?.let {
            WorkoutPlannerLimits.coercePaceToLimit(unit.fromPaceUnit(it.toDouble() / 60.0), unit)
        }

        val minMaxValue = adjustMinMaxAndCalculateValue(
            inputtedMin = min,
            inputtedMax = max,
            defaultRange = phase.getDefaultPaceTargetRange(unit),
        )

        val value = if (min != null && max != null) {
            // Convert to pace unit for calculating average in order to calculate the harmonic
            // mean speed (e.g. the average of 4'00 /km and 5'00 /km is 4'30 /km exactly.
            unit.fromPaceUnit((unit.toPaceUnit(min) + unit.toPaceUnit(max)) / 2.0)
        } else {
            minMaxValue.value
        }

        WorkoutStep.Exercise.Target.Pace(
            value = value,
            min = minMaxValue.min,
            max = minMaxValue.max,
        )
    }

    EditStepTargetMode.SPEED -> {
        val min = speedTarget.minValue.text.toDoubleOrNull()?.let {
            unit.fromSpeedUnit(it)
        }

        val max = speedTarget.maxValue.text.toDoubleOrNull()?.let {
            unit.fromSpeedUnit(it)
        }

        val minMaxValue = adjustMinMaxAndCalculateValue(
            inputtedMin = min,
            inputtedMax = max,
            defaultRange = phase.getDefaultSpeedTargetRange(unit),
        )

        WorkoutStep.Exercise.Target.Speed(
            value = minMaxValue.value?.let { WorkoutPlannerLimits.coerceSpeedToLimit(it, unit) },
            min = minMaxValue.min?.let { WorkoutPlannerLimits.coerceSpeedToLimit(it, unit) },
            max = minMaxValue.max?.let { WorkoutPlannerLimits.coerceSpeedToLimit(it, unit) },
        )
    }
    EditStepTargetMode.NONE -> null
}

private data class MinMaxValue(
    val value: Double?,
    val min: Double?,
    val max: Double?,
)

// Calculate value based on the average of given min and max values. Handle the case where user
// has inputted min and max values in the wrong order.
// TODO: Implement proper input validation, show errors to user and remove this automatic swapping
private fun adjustMinMaxAndCalculateValue(
    inputtedMin: Double?,
    inputtedMax: Double?,
    range: ClosedFloatingPointRange<Double> = 0.0..Double.POSITIVE_INFINITY,
    defaultRange: ClosedFloatingPointRange<Double>,
): MinMaxValue = if (inputtedMin != null && inputtedMax != null) {
    val min = minOf(inputtedMin, inputtedMax).coerceIn(range)
    val max = maxOf(inputtedMin, inputtedMax).coerceIn(range)
    MinMaxValue(
        value = (min + max) / 2.0,
        min = min.takeIf { min != max },
        max = max.takeIf { min != max },
    )
} else if (inputtedMin != null) {
    MinMaxValue(
        value = inputtedMin.coerceIn(range),
        min = null,
        max = null,
    )
} else if (inputtedMax != null) {
    MinMaxValue(
        value = inputtedMax.coerceIn(range),
        min = null,
        max = null,
    )
} else {
    MinMaxValue(
        value = (defaultRange.start + defaultRange.endInclusive) / 2,
        min = defaultRange.start,
        max = defaultRange.endInclusive,
    )
}

private fun IntRange.asDoubleRange() = first.toDouble()..last.toDouble()

private fun TextFieldValue(intValue: Int) = TextFieldValue(text = intValue.toString())
