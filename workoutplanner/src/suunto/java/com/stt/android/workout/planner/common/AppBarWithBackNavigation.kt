package com.stt.android.workout.planner.common

import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.theme.SuuntoIcons
import java.util.Locale
import com.stt.android.R as BaseR

@Composable
internal fun AppBarWithBackNavigation(
    title: String,
    fragment: Fragment,
    modifier: Modifier = Modifier,
    onBack: (() -> Unit)? = null,
) {
    AppBarWithBackNavigation(
        title = title,
        action = onBack ?: {
            with(fragment.findNavController()) {
                if (currentBackStackEntry == null) {
                    fragment.requireActivity().finish()
                } else {
                    popBackStack()
                }
                Unit
            }
        },
        modifier = modifier
    )
}

@Composable
private fun AppBarWithBackNavigation(
    title: String,
    action: () -> Unit,
    modifier: Modifier = Modifier
) {
    TopAppBar(
        title = { Text(title.uppercase(Locale.getDefault())) },
        modifier = modifier,
        contentColor = MaterialTheme.colors.onSurface,
        backgroundColor = MaterialTheme.colors.surface, // default background is Primary
        navigationIcon = {
            SuuntoIconButton(
                icon = SuuntoIcons.ActionBack,
                onClick = action,
                contentDescription = stringResource(BaseR.string.back),
            )
        }
    )
}
