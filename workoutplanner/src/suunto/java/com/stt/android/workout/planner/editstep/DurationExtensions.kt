package com.stt.android.workout.planner.editstep

import com.soy.algorithms.planner.WorkoutStep
import kotlin.math.roundToInt

fun WorkoutStep.Exercise.Duration.secondsOrNull(): Int? {
    (this as? WorkoutStep.Exercise.Duration.Or)?.durations
        ?.filterIsInstance<(WorkoutStep.Exercise.Duration.Time)>()
        ?.firstOrNull()?.let {
            return it.seconds.roundToInt()
        } ?: return null
}

fun WorkoutStep.Exercise.Duration?.distanceOrNull(): Double? {
    (this as? WorkoutStep.Exercise.Duration.Or)?.durations
        ?.filterIsInstance<(WorkoutStep.Exercise.Duration.Distance)>()
        ?.firstOrNull()?.let {
            return it.meters
        } ?: return null
}
