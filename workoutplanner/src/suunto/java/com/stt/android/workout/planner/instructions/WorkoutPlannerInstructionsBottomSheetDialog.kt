package com.stt.android.workout.planner.instructions

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.LocalRippleConfiguration
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.platform.ComposeView
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.ui.utils.ExpandedBottomSheetDialogFragment
import com.stt.android.SimGuideMessagesFormatter
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class WorkoutPlannerInstructionsBottomSheetDialog : ExpandedBottomSheetDialogFragment() {
    @Inject
    lateinit var formatter: SimGuideMessagesFormatter

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        (view.parent as? View)?.let {
            // Get rid of default background in order to show background using Compose
            it.background = null
        }
    }

    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): ComposeView {
        return ComposeView(requireContext()).apply {
            setContentWithM3Theme {
                CompositionLocalProvider(LocalRippleConfiguration provides null) {
                    WorkoutPlannerInstructionsBottomSheet(
                        formatter = formatter
                    )
                }
            }
        }
    }
}
