package com.stt.android.workout.planner.common

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.compose.widgets.SecondaryButton
import com.stt.android.core.R as CR

@OptIn(ExperimentalAnimationApi::class)
@Composable
fun WorkoutPlannerBottomButtons(
    modifier: Modifier = Modifier,
    secondaryButtonText: String? = null,
    onSecondaryButtonClick: (() -> Unit)? = null,
    secondaryButtonEnabled: Boolean = true,
    primaryButtonText: String? = null,
    onPrimaryButtonClick: (() -> Unit)? = null,
    primaryButtonEnabled: Boolean = true,
    showProgressBarInPrimaryButton: Boolean = false,
) {
    Box(
        modifier
            .background(MaterialTheme.colors.surface)
            .fillMaxWidth()
            .padding(bottom = MaterialTheme.spacing.medium)
    ) {
        Divider(color = MaterialTheme.colors.background)

        Row(
            modifier = Modifier
                .widthIn(max = dimensionResource(CR.dimen.content_max_width))
                .padding(MaterialTheme.spacing.medium)
                .align(Alignment.Center),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium)
        ) {
            if (secondaryButtonText != null && onSecondaryButtonClick != null) {
                SecondaryButton(
                    modifier = Modifier
                        .weight(1f),
                    text = secondaryButtonText,
                    enabled = secondaryButtonEnabled,
                    onClick = onSecondaryButtonClick,
                )
            }

            if (showProgressBarInPrimaryButton || (primaryButtonText != null && onPrimaryButtonClick != null)) {
                AnimatedContent(
                    targetState = showProgressBarInPrimaryButton,
                    modifier = Modifier.weight(1f)
                ) { showProgress ->
                    if (showProgress) {
                        PrimaryButton(
                            onClick = { /* ignore click when showing progress indicator */ },
                            enabled = primaryButtonEnabled,
                        ) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(32.dp),
                                color = MaterialTheme.colors.onPrimary
                            )
                        }
                    } else if (primaryButtonText != null && onPrimaryButtonClick != null) {
                        PrimaryButton(
                            text = primaryButtonText,
                            onClick = onPrimaryButtonClick,
                            enabled = primaryButtonEnabled,
                        )
                    }
                }
            }
        }
    }
}

@Preview("Back and Save")
@Composable
private fun WorkoutPlannerBackSaveButtonsPreview() = AppTheme {
    Surface {
        WorkoutPlannerBottomButtons(
            secondaryButtonText = "Back",
            onSecondaryButtonClick = {},
            primaryButtonText = "Save",
            onPrimaryButtonClick = {}
        )
    }
}

@Preview("Back only")
@Composable
private fun WorkoutPlannerBackButtonPreview() = AppTheme {
    Surface {
        WorkoutPlannerBottomButtons(
            secondaryButtonText = "Back",
            onSecondaryButtonClick = {},
        )
    }
}

@Preview("Save only")
@Composable
private fun WorkoutPlannerSaveButtonPreview() = AppTheme {
    Surface {
        WorkoutPlannerBottomButtons(
            primaryButtonText = "Save",
            onPrimaryButtonClick = {}
        )
    }
}

@Preview("Back and Save disabled")
@Composable
private fun WorkoutPlannerDisabledButtonsPreview() = AppTheme {
    Surface {
        WorkoutPlannerBottomButtons(
            secondaryButtonText = "Back",
            secondaryButtonEnabled = false,
            onSecondaryButtonClick = {},
            primaryButtonText = "Save",
            primaryButtonEnabled = false,
            onPrimaryButtonClick = {}
        )
    }
}

@Preview("Back and Save wide", widthDp = 1280)
@Composable
private fun WorkoutPlannerBackSaveButtonsWidePreview() = AppTheme {
    Surface {
        WorkoutPlannerBottomButtons(
            secondaryButtonText = "Back",
            onSecondaryButtonClick = {},
            primaryButtonText = "Save",
            onPrimaryButtonClick = {}
        )
    }
}
