package com.stt.android.workout.planner.editstep

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.selection.selectable
import androidx.compose.material.MaterialTheme
import androidx.compose.material.RadioButton
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.darkGreyText
import com.stt.android.compose.theme.spacing
import com.stt.android.workout.planner.R
import com.stt.android.workout.planner.common.DurationVisualTransformation

@Composable
fun EditTargetColumn(
    selected: Boolean,
    onClick: () -> Unit,
    title: String,
    modifier: Modifier = Modifier,
    editor: @Composable ColumnScope.() -> Unit
) {
    Column(modifier) {
        Row(
            Modifier
                .fillMaxWidth()
                .selectable(
                    selected = selected,
                    onClick = onClick
                )
                .padding(start = MaterialTheme.spacing.medium),
            verticalAlignment = Alignment.CenterVertically
        ) {
            RadioButton(
                selected = selected,
                onClick = onClick
            )
            Text(
                text = title,
                modifier = Modifier.weight(1f)
            )
        }

        AnimatedVisibility(visible = selected) {
            Column {
                editor()
            }
        }
    }
}

@Preview
@Composable
private fun EditTargetRowPreview() {
    AppTheme {
        Surface {
            Column(Modifier.padding(MaterialTheme.spacing.medium)) {
                EditTargetColumn(
                    selected = true,
                    onClick = {},
                    title = "Distance",
                ) {
                    EditTargetInput(
                        value = TextFieldValue("12.34"),
                        onValueChange = {},
                        valueValidator = { true },
                        visualTransformation = VisualTransformation.None,
                        unitText = "km"
                    )
                }

                EditTargetColumn(
                    selected = true,
                    onClick = {},
                    title = "Time"
                ) {
                    EditTargetInput(
                        value = TextFieldValue("100"),
                        onValueChange = {},
                        valueValidator = { true },
                        visualTransformation = DurationVisualTransformation(
                            SpanStyle(color = MaterialTheme.colors.darkGreyText)
                        ),
                        unitText = stringResource(R.string.workout_planner_step_duration_time_unit)
                    )
                }
            }
        }
    }
}
