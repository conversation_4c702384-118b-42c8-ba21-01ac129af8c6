package com.stt.android.workout.planner.common

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.widgets.ErrorToast
import com.stt.android.compose.widgets.ProgressToast
import com.stt.android.compose.widgets.Toast
import com.stt.android.R as BaseR

@Composable
fun WatchStateIndication(
    data: WatchStateIndicationData,
    modifier: Modifier = Modifier
) {
    val text = stringResource(data.textResource)
    when {
        data.isError -> ErrorToast(
            text = text,
            modifier = modifier
        )

        data.showProgress -> ProgressToast(
            text = text,
            modifier = modifier
        )

        else -> Toast(
            text = text,
            modifier = modifier
        )
    }
}

@Preview
@Composable
private fun WatchStateIndicationPreview() {
    AppTheme {
        WatchStateIndication(
            data = WatchStateIndicationData(
                textResource = BaseR.string.suunto_plus_sync_watch_busy,
                showProgress = true,
                isError = false
            )
        )
    }
}

@Preview
@Composable
private fun WatchStateErrorIndicationPreview() {
    AppTheme {
        WatchStateIndication(
            data = WatchStateIndicationData(
                textResource = BaseR.string.error_generic_try_again,
                showProgress = false,
                isError = true
            )
        )
    }
}
