package com.stt.android.workout.planner

import android.os.Bundle
import androidx.core.os.bundleOf
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.soy.algorithms.planner.GuideLimits
import com.soy.algorithms.planner.WorkoutPlanner
import com.soy.algorithms.planner.WorkoutStep
import com.soy.algorithms.planner.WorkoutStep.Exercise.Phase
import com.soy.algorithms.planner.WorkoutTypesPlanner
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.EmarsysAnalyticsImpl
import com.stt.android.common.coroutines.CoroutineViewModel
import com.stt.android.common.coroutines.CoroutinesDispatcherProvider
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.combine
import com.stt.android.data.toEpochMilli
import com.stt.android.device.domain.suuntoplusguide.DeleteGuideUseCase
import com.stt.android.device.domain.suuntoplusguide.InsertNewGuideOrUpdateExistingUseCase
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuideId
import com.stt.android.device.suuntoplusguide.SuuntoPlusGuideAnalyticsUtils
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.ActivityType
import com.stt.android.watch.background.SuuntoPlusGuideRemoteSyncJobLauncher
import com.stt.android.workout.domain.GetWorkoutPlanSourceUseCase
import com.stt.android.workout.domain.ListPlannedWorkoutsAsGuidesUseCase
import com.stt.android.workout.domain.SaveWorkoutPlanAsGuideUseCase
import com.stt.android.workout.planner.common.DistanceAndUnit
import com.stt.android.SimGuideMessagesFormatter
import com.stt.android.controllers.CurrentUserController
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import timber.log.Timber
import java.nio.charset.Charset
import java.time.Clock
import java.time.Instant
import java.time.LocalDate
import java.time.YearMonth
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import javax.inject.Inject
import kotlin.math.roundToInt

@HiltViewModel
class WorkoutPlannerViewModel
@Inject constructor(
    savedStateHandle: SavedStateHandle,
    coroutinesDispatchers: CoroutinesDispatchers = CoroutinesDispatcherProvider(),
    clock: Clock,
    private val formatter: SimGuideMessagesFormatter,
    private val saveWorkoutPlanAsGuideUseCase: SaveWorkoutPlanAsGuideUseCase,
    private val insertNewGuideOrUpdateExistingUseCase: InsertNewGuideOrUpdateExistingUseCase,
    private val getWorkoutPlanSourceUseCase: GetWorkoutPlanSourceUseCase,
    private val deleteGuideUseCase: DeleteGuideUseCase,
    private val syncJobLauncher: SuuntoPlusGuideRemoteSyncJobLauncher,
    private val measurementUnit: MeasurementUnit,
    private val emarsysAnalytics: EmarsysAnalyticsImpl,
    private val listPlannedWorkoutsUseCase: ListPlannedWorkoutsAsGuidesUseCase,
    private val suuntoPlusGuideAnalyticsUtils: SuuntoPlusGuideAnalyticsUtils,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
) : CoroutineViewModel(coroutinesDispatchers) {
    private val today = LocalDate.now(clock)
    private val initialMonth = YearMonth.of(today.year, today.month)

    // Original plan for comparing if there has been any changes
    private var hasChanges: Boolean = false

    // Planner state
    enum class PlanLoadingState {
        NEW,
        LOADING,
        LOADED,
        FAILED // Fetching or opening existing plan for loading failed
    }

    enum class PlanSavingState {
        IDLE,
        SAVING,
        SAVED,
        FAILED
    }

    val loadingState: StateFlow<PlanLoadingState>
        get() = _loadingState
    private val _loadingState = MutableStateFlow(PlanLoadingState.NEW)

    val savingState: StateFlow<PlanSavingState>
        get() = _savingState
    private val _savingState = MutableStateFlow(PlanSavingState.IDLE)

    private val isEditing = MutableStateFlow(false)

    val titleResource: StateFlow<Int>
        get() = isEditing
            .map {
                if (it) {
                    R.string.workout_planner_edit_plan_title
                } else {
                    R.string.workout_planner_new_plan_title
                }
            }
            .stateIn(
                scope = viewModelScope,
                started = SharingStarted.Eagerly,
                initialValue = R.string.workout_planner_new_plan_title
            )

    // Step 1. Basic details
    private val activityType = MutableStateFlow(WorkoutPlannerDefaults.defaultActivityType)
    private val name = MutableStateFlow("")
    private val description = MutableStateFlow("")
    private val skipDateSelection = MutableStateFlow(true)
    private val date = MutableStateFlow(today)

    val availableActivityTypes = WorkoutTypesPlanner.getWorkoutTypes().map { ActivityType.valueOf(it) }

    val basicDetailsViewState: StateFlow<WorkoutPlannerBasicDetailsState> = combine(
        name,
        description,
        activityType,
        skipDateSelection,
        date,
        isEditing,
    ) { name, description, activityType, skipDateSelection, date, isEditing ->
        WorkoutPlannerBasicDetailsState(
            today = today,
            initialMonth = initialMonth,
            name = name,
            description = description,
            activityType = activityType,
            skipDateSelection = skipDateSelection,
            date = date,
            isEditing = isEditing
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Eagerly,
        initialValue = WorkoutPlannerBasicDetailsState(
            today = today,
            initialMonth = initialMonth,
            name = name.value,
            description = description.value,
            activityType = activityType.value,
            skipDateSelection = skipDateSelection.value,
            date = date.value,
            isEditing = isEditing.value
        )
    )

    // Step 2. Edit steps
    val stepsState: StateFlow<WorkoutPlannerEditStepsState>
        get() = _stepsState
    private val _stepsState = MutableStateFlow(WorkoutPlannerEditStepsState.EMPTY)

    // Duration and distance
    val totalDuration: StateFlow<String>
        get() = _totalDuration
    private val _totalDuration = MutableStateFlow("")
    private var totalDurationMinutes = 0

    val totalDistance: StateFlow<DistanceAndUnit>
        get() = _totalDistance
    private val _totalDistance = MutableStateFlow(DistanceAndUnit.EMPTY)
    private var totalDistanceInMeters = 0

    private val planner = WorkoutPlanner().apply {
        setFormatter(formatter)
        setNewStructuredWorkoutEnabled(true)
    }

    private var guideId: SuuntoPlusGuideId? = null

    init {
        savedStateHandle.get<Bundle>(SAVED_STATE_KEY_WORKOUT_PLANNER)?.let {
            isEditing.value = it.getBoolean(SAVED_STATE_KEY_IS_EDITING, false)
            guideId = it.getString(SAVED_STATE_KEY_GUIDE_ID)?.let { id -> SuuntoPlusGuideId(id) }
            hasChanges = it.getBoolean(SAVED_STATE_KEY_HAS_CHANGES, false)
            it.getString(SAVED_STATE_KEY_JSON)?.let { json ->
                initStateFromJson(json)
            }
            activityType.value = it.getInt(
                SAVED_STATE_KEY_ACTIVITY_TYPE,
                WorkoutPlannerDefaults.defaultActivityType.id,
            ).let { id ->
                ActivityType.valueOf(id)
            }
            name.value = it.getString(SAVED_STATE_KEY_NAME, "")
            description.value = it.getString(SAVED_STATE_KEY_DESCRIPTION, "")
            skipDateSelection.value = it.getBoolean(SAVED_STATE_KEY_SKIP_DATE_SELECTION)
            date.value = it.getLong(SAVED_STATE_KEY_DATE, 0L).let { millis ->
                Instant.ofEpochMilli(millis).atZone(ZoneId.systemDefault()).toLocalDate()
            }
        }
        savedStateHandle.setSavedStateProvider(SAVED_STATE_KEY_WORKOUT_PLANNER) {
            try {
                planner.getGuideJson()
            } catch (_: Exception) {
                null
            }?.let {
                // if the json is not correctly retrieved, we don't save the rest of the state
                bundleOf(
                    SAVED_STATE_KEY_JSON to it,
                    SAVED_STATE_KEY_IS_EDITING to isEditing.value,
                    SAVED_STATE_KEY_GUIDE_ID to guideId?.id,
                    SAVED_STATE_KEY_HAS_CHANGES to hasChanges,
                    SAVED_STATE_KEY_ACTIVITY_TYPE to activityType.value.id,
                    SAVED_STATE_KEY_NAME to name.value,
                    SAVED_STATE_KEY_DESCRIPTION to description.value,
                    SAVED_STATE_KEY_SKIP_DATE_SELECTION to skipDateSelection.value,
                    SAVED_STATE_KEY_DATE to date.value.atStartOfDay(ZoneId.systemDefault())
                        .toEpochMilli(),
                )
            } ?: bundleOf()
        }
    }

    fun startNewPlan() {
        isEditing.value = false
        planner.startNewIntervalPlan(
            name = "Workout", // avoid [WorkoutPlanner] throwing exception
            description = null,
            activityType = WorkoutPlannerDefaults.defaultActivityType.id,
            localDate = null,
        )
        setupInitialSteps()
    }

    fun startEditingExistingPlan(guideId: SuuntoPlusGuideId) {
        Timber.d("Start editing of existing plan with ID $guideId")
        clearPlan()
        this.guideId = guideId
        totalDistanceInMeters = 0
        totalDurationMinutes = 0
        isEditing.value = true
        _loadingState.value = PlanLoadingState.LOADING

        launch {
            runCatching {
                getWorkoutPlanSourceUseCase.getWorkoutPlanSourceJson(guideId)
            }.onSuccess { json ->
                Timber.d("Downloaded ${json.length} chars worth of JSON")
                initStateFromJson(json)
            }.onFailure {
                Timber.w(
                    it,
                    "Failed to get workout plan source json for guideId= $guideId chars worth of JSON"
                )
                _loadingState.value = PlanLoadingState.FAILED
            }
        }
    }

    private fun initStateFromJson(json: String) {
        try {
            Timber.d("JSON=$json")
            planner.startPlanEdit(json)
            planner.getWorkoutPlan().let { plan ->
                name.value = plan.name.trim()
                description.value = plan.description.orEmpty()
                skipDateSelection.value = plan.localDate == null
                date.value =
                    plan.localDate?.let { dateString -> LocalDate.parse(dateString) } ?: today
                activityType.value = ActivityType.valueOf(plan.activityType)
            }
            _loadingState.value = PlanLoadingState.LOADED
            Timber.d("After startPlanEdit: have ${planner.getWorkoutPlan().exercises.size} step(s)")
            refreshSteps()
        } catch (e: Exception) {
            Timber.w(e, "Failed to start editing existing plan")
            _loadingState.value = PlanLoadingState.FAILED
        }
    }

    private fun refreshSteps() {
        val items = buildList {
            // Map plan content to list items including breadcrumb item at the top and add
            // exercise/repeat buttons at the bottom
            add(WorkoutPlanStepsListItem.BreadcrumbItem)

            for (workoutStep in planner.getWorkoutPlan().exercises.filter { (it as? WorkoutStep.Exercise)?.phase != Phase.FINISHED }) {
                val index = planner.getOverallIndexByUUID(workoutStep.uuid)
                when (workoutStep) {
                    is WorkoutStep.Exercise ->
                        add(
                            WorkoutPlanStepsListItem.ExerciseStep(
                                step = workoutStep,
                                insideRepeatBlock = false,
                                plannerOverallIndex = index
                            )
                        )

                    is WorkoutStep.Repeat -> {
                        add(WorkoutPlanStepsListItem.RepeatStart(workoutStep, index))

                        var repeatedStepIndex = index + 1
                        for (repeatedStep in workoutStep.exercises) {
                            // Nested repeats are not supported
                            if (repeatedStep is WorkoutStep.Exercise) {
                                add(
                                    WorkoutPlanStepsListItem.ExerciseStep(
                                        step = repeatedStep,
                                        insideRepeatBlock = true,
                                        plannerOverallIndex = repeatedStepIndex++
                                    )
                                )
                            }
                        }

                        add(WorkoutPlanStepsListItem.RepeatEnd(workoutStep.uuid, repeatedStepIndex))
                    }

                    else -> Timber.w("Unsupported step: $workoutStep")
                }
            }

            add(WorkoutPlanStepsListItem.AddExerciseOrRepeatButton)
        }

        _stepsState.value = WorkoutPlannerEditStepsState(items.toImmutableList())

        planner.getWorkoutPlan().let { plan ->
            totalDurationMinutes = (plan.totalTimeSeconds / 60.0).roundToInt()
            _totalDuration.value = formatter.formatDuration(plan.totalTimeSeconds)

            totalDistanceInMeters = plan.totalDistanceMeters.roundToInt()
            _totalDistance.value =
                formatter.formatDistance(plan.totalDistanceMeters).let { (value, unit) ->
                    DistanceAndUnit(value, unit)
                }
        }
    }

    fun setName(name: String) {
        name
            .replace("\n", " ") // Don't allow newlines or tabs in name
            .replace("\t", " ")
            .truncateByBytes(GuideLimits.name).let {
                this.name.value = it
                hasChanges = true
            }
    }

    fun setDescription(description: String) {
        description
            .replace("\n", " ") // Don't allow newlines or tabs in description
            .replace("\t", " ")
            .take(GuideLimits.shortDescription)
            .let {
                this.description.value = it
                hasChanges = true
            }
    }

    fun setActivityType(activityType: ActivityType) {
        // Only a single activity type is supported for now
        this.activityType.value = activityType
        hasChanges = true
    }

    fun setSkipDateSelection(skipDateSelection: Boolean) {
        this.skipDateSelection.value = skipDateSelection
        hasChanges = true
    }

    fun setDate(date: LocalDate) {
        this.date.value = date
        hasChanges = true
    }

    fun addExerciseStep() {
        editPlannerSteps {
            addExercise(
                WorkoutPlannerDefaults.getIntervalStepDefaults(measurementUnit)
                    .asPlannerStep()
            )
        }
    }

    fun addRepeatStep() {
        editPlannerSteps {
            addRepeatedExercises(
                times = WorkoutPlannerDefaults.newRepeatStepCount,
                WorkoutPlannerDefaults.getRepeatStepDefaults(measurementUnit)
                    .map { it.asPlannerStep() }
            )
        }
    }

    // Indexes are overall indices for the planner library
    fun moveExercise(fromIndex: Int, toIndex: Int) {
        editPlannerSteps {
            moveExercise(fromIndex, toIndex)
        }
    }

    fun deleteStep(uuid: String) {
        editPlannerSteps {
            val index = getOverallIndexByUUID(uuid)
            if (index < 0) {
                throw IllegalStateException("Step with UUID $uuid not found")
            }

            removeExerciseAt(index)
        }
    }

    fun duplicateStep(uuid: String) {
        editPlannerSteps {
            val index = getOverallIndexByUUID(uuid)
            if (index < 0) {
                throw IllegalStateException("Step with UUID $uuid not found")
            }

            duplicateExerciseAt(index)
        }
    }

    fun decreaseRepeatCount(step: WorkoutStep.Repeat) {
        if (step.times > 1) {
            editPlannerSteps {
                editExercise(step.copy(times = step.times - 1))
            }
        }
    }

    fun increaseRepeatCount(step: WorkoutStep.Repeat) {
        editPlannerSteps {
            editExercise(step.copy(times = step.times + 1))
        }
    }

    fun editStepPhase(step: WorkoutStep.Exercise, newPhase: WorkoutStep.Exercise.Phase) {
        editPlannerSteps {
            editExercise(step.copy(phase = newPhase))
        }
    }

    fun editStepName(step: WorkoutStep.Exercise, newName: String?) {
        editPlannerSteps {
            val name = newName
                ?.replace("\n", " ") // Don't allow newlines or tabs in step name
                ?.replace("\t", " ")
                ?.take(GuideLimits.stepTitle)
                ?.takeUnless { it.isBlank() }
            editExercise(step.copy(name = name))
        }
    }

    fun editStepTarget(step: WorkoutStep.Exercise, newTarget: WorkoutStep.Exercise.Target?) {
        editPlannerSteps {
            editExercise(step.copy(target = newTarget))
        }
    }

    fun editStepDuration(step: WorkoutStep.Exercise, newDuration: WorkoutStep.Exercise.Duration) {
        editPlannerSteps {
            editExercise(step.copy(duration = newDuration))
        }
    }

    fun savePlanAsGuideOrUpdateExisting() {
        if (_savingState.value == PlanSavingState.SAVING) {
            Timber.d("savePlanAsGuide: already saving, ignoring call")
            return
        }

        launch {
            _savingState.value = PlanSavingState.SAVING
            try {
                planner.editName(name.value.trim())
                planner.editDescription(description.value.takeUnless { it.isBlank() })
                planner.editActivityType(activityType.value.id)
                if (skipDateSelection.value) {
                    planner.editLocalDate(null)
                } else {
                    planner.editLocalDate(date.value.format(DateTimeFormatter.ISO_LOCAL_DATE))
                }

                val id = guideId
                val guide = if (id != null) {
                    Timber.d("Updating existing guide content for guide ID $id")
                    saveWorkoutPlanAsGuideUseCase.updateExistingPlan(
                        guideId = id,
                        guideJson = planner.getGuideJson(),
                        name = name.value,
                        description = description.value,
                    )
                } else {
                    Timber.d("Uploading a new guide")
                    saveWorkoutPlanAsGuideUseCase.savePlanAsGuide(
                        guideJson = planner.getGuideJson(),
                        name = name.value,
                        description = description.value,
                    )
                }

                // Update local database immediately and schedule sync job to download the ZAPP
                // plug-in and install to watch, if possible.
                try {
                    insertNewGuideOrUpdateExistingUseCase.updateOrInsert(guide)
                    syncJobLauncher.enqueueRemoteSyncJob()
                } catch (e: Exception) {
                    Timber.w(e, "Failed to save guide to database: will update on next sync")
                }

                suuntoPlusGuideAnalyticsUtils.updatePlannedWorkoutsUserProperty()

                sendWorkoutPlannerPlanSaved()
                _savingState.value = PlanSavingState.SAVED
                clearPlan()
            } catch (e: Exception) {
                Timber.w(e, "Failed to add guide")
                _savingState.value = PlanSavingState.FAILED
            }
        }
    }

    fun deleteEmptyRepeatBlocks() {
        // This is called from WorkoutPlanStepsListFragment.onDragInterrupted,
        // Somehow onDragInterrupted is called after process death which causes getWorkoutPlan to throw an exception
        // Wrapping this with try catch to workaround it.
        try {
            planner.getWorkoutPlan().exercises
        } catch (_: IllegalStateException) {
            emptyList()
        }.filterIsInstance<WorkoutStep.Repeat>()
            .filter { it.exercises.isEmpty() }
            .forEach {
                deleteStep(it.uuid)
            }
    }

    fun clearPlan() {
        planner.close()
        planner.setFormatter(formatter) // Formatter instance gets cleared on close()
        isEditing.value = false
        hasChanges = false
        guideId = null
        _loadingState.value = PlanLoadingState.NEW
        _savingState.value = PlanSavingState.IDLE

        activityType.value = WorkoutPlannerDefaults.defaultActivityType
        name.value = ""
        description.value = ""
        date.value = today
        skipDateSelection.value = true
        _stepsState.value = WorkoutPlannerEditStepsState.EMPTY

        _totalDuration.value = ""
        _totalDistance.value = DistanceAndUnit.EMPTY
    }

    fun deletePlan() {
        Timber.d("Deleting workout plan with guide ID $guideId")
        guideId?.let {
            launch {
                try {
                    deleteGuideUseCase.deleteGuide(it)
                    clearPlan()
                    syncJobLauncher.enqueueRemoteSyncJob()
                    suuntoPlusGuideAnalyticsUtils.updatePlannedWorkoutsUserProperty()
                } catch (_: Exception) {
                    Timber.w("Failed to delete workout plan")
                }
            }
        }
    }

    fun shouldShowDiscardChangesConfirmation(): Boolean {
        return !isEditing.value || hasChanges
    }

    fun sendWorkoutPlannerPlanStartedAnalyticsEvent() {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.SUUNTO_WORKOUT_PLANNER_PLAN_STARTED,
            AnalyticsProperties().put(
                AnalyticsEventProperty.PLAN_TYPE,
                AnalyticsPropertyValue.WorkoutPlanner.PLAN_TYPE_STRUCTURED_INTERVAL
            )
        )
    }

    fun sendWorkoutPlannerStepCompletedAnalyticsEvent(stepName: String) {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.SUUNTO_WORKOUT_PLANNER_STEP_COMPLETED,
            AnalyticsProperties()
                .put(AnalyticsEventProperty.PLANNER_STEP, stepName)
                .put(AnalyticsEventProperty.ACTIVITY_TYPE, activityType.value.simpleName)
                .putYesNo(AnalyticsEventProperty.PLANNER_SPECIFIC_DATE, !skipDateSelection.value)
                .putYesNo(AnalyticsEventProperty.DESCRIPTION_ADDED, description.value.isNotBlank())
        )

        emarsysAnalytics.trackEventWithProperties(
            AnalyticsEvent.SUUNTO_WORKOUT_PLANNER_STEP_COMPLETED,
            mapOf(
                AnalyticsEventProperty.PLANNER_STEP to stepName,
                AnalyticsEventProperty.ACTIVITY_TYPE to activityType.value.simpleName
            )
        )
    }

    private fun sendWorkoutPlannerPlanSaved() {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.SUUNTO_WORKOUT_PLANNER_PLAN_SAVED,
            AnalyticsProperties()
                .put(
                    AnalyticsEventProperty.PLAN_TYPE,
                    AnalyticsPropertyValue.WorkoutPlanner.PLAN_TYPE_STRUCTURED_INTERVAL
                )
                .put(AnalyticsEventProperty.ACTIVITY_TYPE, activityType.value.simpleName)
                .putYesNo(AnalyticsEventProperty.PLANNER_SPECIFIC_DATE, !skipDateSelection.value)
                .putYesNo(AnalyticsEventProperty.DESCRIPTION_ADDED, description.value.isNotBlank())
                .put(AnalyticsEventProperty.TOTAL_DURATION, totalDurationMinutes)
                .put(AnalyticsEventProperty.TOTAL_DISTANCE, totalDistanceInMeters)
        )

        emarsysAnalytics.trackEventWithProperties(
            AnalyticsEvent.SUUNTO_WORKOUT_PLANNER_PLAN_SAVED,
            mapOf(
                AnalyticsEventProperty.ACTIVITY_TYPE to activityType.value.simpleName
            )
        )
    }

    fun sendPlannerScreenEvent(source: String) {
        launch {
            try {
                val plansCount =
                    listPlannedWorkoutsUseCase.listPlannedWorkoutsAsGuides().first().size

                amplitudeAnalyticsTracker.trackEvent(
                    AnalyticsEvent.SUUNTO_WORKOUT_PLANNER_SCREEN,
                    AnalyticsProperties()
                        .put(AnalyticsEventProperty.SOURCE, source)
                        .put(AnalyticsEventProperty.NUM_WORKOUT_PLANS, plansCount)
                )
            } catch (e: Exception) {
                Timber.w(e, "Failed to send WorkoutPlannerScreen event")
            }
        }
    }

    private fun editPlannerSteps(block: WorkoutPlanner.() -> Unit) {
        try {
            planner.block()
        } catch (e: Exception) {
            Timber.w(e, "Editing workout plan failed")
        }
        hasChanges = true
        refreshSteps()
    }

    private fun setupInitialSteps() {
        editPlannerSteps {
            addExercise(WorkoutPlannerDefaults.getWarmupStepDefaults().asPlannerStep())
            addRepeatedExercises(
                times = WorkoutPlannerDefaults.initialContentRepeatStepCount,
                WorkoutPlannerDefaults.getRepeatStepDefaults(measurementUnit)
                    .map { it.asPlannerStep() }
            )
            addExercise(WorkoutPlannerDefaults.getCoolDownStepDefaults().asPlannerStep())
        }
    }

    override fun onCleared() {
        super.onCleared()
        planner.close()
    }

    private fun String.truncateByBytes(
        maxBytes: Int,
        charset: Charset = Charsets.UTF_8,
    ): String {
        var byteCount = 0
        var index = 0
        while (index < length) {
            val codePoint = codePointAt(index)
            val codePointString = String(Character.toChars(codePoint))
            val codePointBytes = codePointString.toByteArray(charset)
            if (byteCount + codePointBytes.size > maxBytes) {
                break
            }
            byteCount += codePointBytes.size
            index += Character.charCount(codePoint)
        }
        return substring(0, index)
    }

    companion object {
        private const val SAVED_STATE_KEY_WORKOUT_PLANNER = "SAVED_STATE_KEY_WORKOUT_PLANNER"
        private const val SAVED_STATE_KEY_JSON = "SAVED_STATE_KEY_JSON"
        private const val SAVED_STATE_KEY_IS_EDITING = "SAVED_STATE_KEY_IS_EDITING"
        private const val SAVED_STATE_KEY_GUIDE_ID = "SAVED_STATE_KEY_GUIDE_ID"
        private const val SAVED_STATE_KEY_HAS_CHANGES = "SAVED_STATE_KEY_HAS_CHANGES"
        private const val SAVED_STATE_KEY_ACTIVITY_TYPE = "SAVED_STATE_KEY_ACTIVITY_TYPE"
        private const val SAVED_STATE_KEY_NAME = "SAVED_STATE_KEY_NAME"
        private const val SAVED_STATE_KEY_DESCRIPTION = "SAVED_STATE_KEY_DESCRIPTION"
        private const val SAVED_STATE_KEY_SKIP_DATE_SELECTION =
            "SAVED_STATE_KEY_SKIP_DATE_SELECTION"
        private const val SAVED_STATE_KEY_DATE = "SAVED_STATE_KEY_DATE"
    }
}
