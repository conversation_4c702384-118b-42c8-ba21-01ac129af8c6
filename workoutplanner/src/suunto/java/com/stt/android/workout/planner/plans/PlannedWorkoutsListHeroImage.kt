package com.stt.android.workout.planner.plans

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.Button
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.ConstraintSet
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.header
import com.stt.android.compose.theme.spacing
import com.stt.android.workout.planner.R
import java.util.Locale
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

@Composable
fun PlannedWorkoutsListHeroImage(
    onNewWorkoutPlanClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    BottomButtonLayout(
        modifier = modifier.fillMaxWidth(),
        background = {
            Image(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp),
                contentScale = ContentScale.Crop,
                painter = painterResource(id = R.drawable.workout_planner_hero_image),
                contentDescription = null // Not relevant for screen reader
            )
        },
        bottomButton = {
            CreateNewButton(onClick = onNewWorkoutPlanClick)
        },
        overlay = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = MaterialTheme.spacing.medium),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = stringResource(BaseR.string.suunto_plus_guides_structured_workouts_card_title)
                        .uppercase(Locale.getDefault()),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.header,
                    color = Color.White
                )

                Text(
                    text = stringResource(BaseR.string.suunto_plus_guides_structured_workouts_card_body),
                    modifier = Modifier
                        .padding(top = MaterialTheme.spacing.small),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.bodyLarge.copy(lineHeight = 18.sp),
                    color = Color.White
                )
            }
        }
    )
}

@Composable
private fun CreateNewButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Button(
        modifier = modifier
            .fillMaxWidth()
            .height(dimensionResource(CR.dimen.height_elevated_button))
            .padding(horizontal = MaterialTheme.spacing.medium),
        onClick = onClick,
    ) {
        Icon(
            modifier = Modifier
                .padding(end = MaterialTheme.spacing.small)
                .size(24.dp),
            painter = painterResource(id = R.drawable.ic_plus_workout_planner),
            contentDescription = null,
        )

        Text(
            text = stringResource(R.string.workout_planner_my_workout_plans_create_new_button)
                .uppercase(Locale.getDefault()),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
        )
    }
}

@Composable
private fun BottomButtonLayout(
    background: @Composable () -> Unit,
    bottomButton: @Composable () -> Unit,
    overlay: @Composable () -> Unit,
    modifier: Modifier = Modifier,
) {
    val constraintSet = ConstraintSet {
        val backgroundRef = createRefFor("background")
        val bottomButtonRef = createRefFor("bottomButton")
        val overlayRef = createRefFor("overlay")

        constrain(backgroundRef) {
            linkTo(start = parent.start, end = parent.end)
            top.linkTo(parent.top)
        }

        constrain(bottomButtonRef) {
            // Vertically center to background bottom edge
            top.linkTo(backgroundRef.bottom)
            bottom.linkTo(backgroundRef.bottom)
        }

        constrain(overlayRef) {
            linkTo(start = parent.start, end = parent.end)
            top.linkTo(parent.top)
            bottom.linkTo(bottomButtonRef.top)
        }
    }

    ConstraintLayout(
        modifier = modifier,
        constraintSet = constraintSet
    ) {
        Box(Modifier.layoutId("background")) {
            background()
        }

        Box(Modifier.layoutId("bottomButton")) {
            bottomButton()
        }

        Box(Modifier.layoutId("overlay")) {
            overlay()
        }
    }
}

@Composable
@Preview(widthDp = 320)
@Preview(widthDp = 760)
private fun PlannedWorkoutsListHeroImagePreview() {
    AppTheme {
        Surface {
            PlannedWorkoutsListHeroImage(
                modifier = Modifier.padding(bottom = MaterialTheme.spacing.xlarge),
                onNewWorkoutPlanClick = {}
            )
        }
    }
}
