package com.stt.android.workout.planner.common

import androidx.compose.ui.text.input.TextFieldValue
import com.stt.android.domain.user.MeasurementUnit
import java.util.Locale
import kotlin.math.roundToInt

/**
 * Functions for formatting and parsing step duration and target values. SIM formatter is not used
 * because the text fields use a VisualTransformation for the separator characters and we need
 * to be sure parsing and formatting are fully aligned.
 */
object TextFieldFormattingHelper {
    // The input text field expects one decimal
    fun formatSpeed(
        unit: MeasurementUnit,
        speedInMetersPerSecond: Double
    ): String =
        String.format(Locale.ROOT, "%.1f", unit.toSpeedUnit(speedInMetersPerSecond))

    // The input text field expects two decimals
    fun formatDistance(
        unit: MeasurementUnit,
        distanceInMeters: Double
    ): String =
        String.format(Locale.ROOT, "%.2f", unit.toDistanceUnit(distanceInMeters))

    // The input text field expects "mmss" without any separators (either min/km or min/mile). The
    // separators are added by a visual transformation.
    fun formatPaceAsMMSS(
        unit: MeasurementUnit,
        speedInMetersPerSecond: Double
    ): String = unit.toPaceUnit(speedInMetersPerSecond).let { paceInMinutes ->
        val paceInSeconds = (paceInMinutes * 60).roundToInt()
        val minutes = paceInSeconds / 60
        val seconds = paceInSeconds % 60
        if (minutes > 0) {
            String.format(Locale.ROOT, "%d%02d", minutes, seconds)
        } else {
            "$seconds"
        }
    }

    // The input text field expects "hhmmss" without any separators. The separators are added by
    // a visual transformation.
    fun formatDurationAsHHMMSS(durationInSeconds: Int): String =
        with(durationInSeconds) {
            val hours = this / 60 / 60
            val minutes = (this / 60) % 60
            val seconds = this % 60
            if (hours > 0) {
                String.format(Locale.ROOT, "%d%02d%02d", hours, minutes, seconds)
            } else if (minutes > 0) {
                String.format(Locale.ROOT, "%d%02d", minutes, seconds)
            } else {
                "$seconds"
            }
        }

    fun parseDurationAsSeconds(hhmmss: String): Int? =
        hhmmss.toIntOrNull()?.let {
            val hours = it / 100 / 100
            val minutes = (it / 100) % 100
            val seconds = it % 100
            hours * 60 * 60 + minutes * 60 + seconds
        }

    fun isValidFloatingPointValue(
        value: TextFieldValue,
        maxDecimals: Int,
        maximumValue: Double? = null
    ): Boolean {
        val text = value.text
        val decimalPointCount = text.count { it == '.' }

        return when {
            text.isEmpty() -> false // Cannot call toDouble() on an empty string
            !text.all { it.isDigit() || it == '.' } -> false // Invalid characters
            decimalPointCount == 0 -> maximumValue == null || value.text.toDouble() <= maximumValue
            decimalPointCount > 1 -> false
            text == "." -> true // Allow decimal point without leading digits
            else -> {
                val decimals = text.takeLastWhile { it.isDigit() }
                decimals.length <= maxDecimals && (maximumValue == null || text.toDouble() <= maximumValue)
            }
        }
    }

    fun isValidInteger(value: TextFieldValue, range: IntRange): Boolean =
        value.text.toIntOrNull()?.let { it in range } ?: false
}
