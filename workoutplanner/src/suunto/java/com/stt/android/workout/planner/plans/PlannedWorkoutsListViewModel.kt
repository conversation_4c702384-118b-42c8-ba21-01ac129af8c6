package com.stt.android.workout.planner.plans

import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutineViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.device.domain.suuntoplusguide.DeleteGuideUseCase
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuideId
import com.stt.android.watch.background.SuuntoPlusGuideRemoteSyncJobLauncher
import com.stt.android.workout.domain.ListPlannedWorkoutsAsGuidesUseCase
import com.stt.android.workout.domain.PlannedWorkoutsGrouped
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import timber.log.Timber
import java.time.Clock
import java.time.LocalDate
import javax.inject.Inject

@HiltViewModel
class PlannedWorkoutsListViewModel @Inject constructor(
    listPlannedWorkoutsUseCase: ListPlannedWorkoutsAsGuidesUseCase,
    private val remoteSyncLauncher: SuuntoPlusGuideRemoteSyncJobLauncher,
    private val deleteGuideUseCase: DeleteGuideUseCase,
    clock: Clock,
    coroutinesDispatchers: CoroutinesDispatchers
) : CoroutineViewModel(coroutinesDispatchers) {
    private val today = LocalDate.now(clock)

    val plannedWorkoutsAsGuides: StateFlow<PlannedWorkoutsGrouped> =
        listPlannedWorkoutsUseCase.listPlannedWorkoutsGrouped(today)
            .catch {
                Timber.w(it, "Failed to list planned workouts as guides")
            }
            .stateIn(
                scope = viewModelScope,
                started = SharingStarted.Eagerly,
                initialValue = PlannedWorkoutsGrouped.EMPTY,
            )

    fun syncWithRemote() {
        remoteSyncLauncher.enqueueRemoteSyncJob()
    }

    fun deleteWorkoutPlan(id: SuuntoPlusGuideId) {
        launch {
            try {
                deleteGuideUseCase.deleteGuide(id)
                syncWithRemote()
            } catch (e: Exception) {
                // This should be extremely rare since we just set the soft-delete flag in the
                // database. No need to inform the user.
                Timber.w(e, "Failed to delete workout plan")
            }
        }
    }
}
