package com.stt.android.workout.planner.editstep

import com.soy.algorithms.planner.WorkoutStep

enum class EditStepDurationMode {
    DISTANCE,
    TIME,
    LAP_BUTTON_PRESS,
    NEVER,
}

fun WorkoutStep.Exercise.Duration.asModeEnum(): EditStepDurationMode = when (this) {
    is WorkoutStep.Exercise.Duration.Time -> EditStepDurationMode.TIME
    is WorkoutStep.Exercise.Duration.Distance -> EditStepDurationMode.DISTANCE
    is WorkoutStep.Exercise.Duration.LapButton -> EditStepDurationMode.LAP_BUTTON_PRESS
    is WorkoutStep.Exercise.Duration.Or ->
        durations.firstOrNull { it !is WorkoutStep.Exercise.Duration.LapButton }
            ?.asModeEnum()
            ?: EditStepDurationMode.LAP_BUTTON_PRESS

    WorkoutStep.Exercise.Duration.Never -> EditStepDurationMode.NEVER
}
