package com.stt.android.workout.planner

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.fragment.app.FragmentActivity
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.navigation.findNavController
import com.stt.android.coroutines.launchOnLifecycle
import com.stt.android.utils.STTConstants
import com.stt.android.window.setFlagsAndColors
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class WorkoutPlannerActivity : FragmentActivity() {
    private val viewModel: WorkoutPlannerViewModel by viewModels()

    private val createNewPlanDirectly: Boolean
        get() = intent.extras?.getBoolean(EXTRA_CREATE_NEW_WORKOUT_PLAN) == true

    private val showWorkoutListAfterCreation: Boolean
        get() = intent.extras?.getBoolean(EXTRA_SHOW_LIST_AFTER_CREATION) == true

    @Inject
    lateinit var analyticsTracker: WorkoutPlannerScreenAnalyticsTracker

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window?.setFlagsAndColors()
        setContentView(R.layout.activity_workout_planner)

        setupNavigation()
        setupAnalytics(restoringFromSavedState = savedInstanceState != null)

        launchOnLifecycle {
            viewModel.savingState.collect {
                if (it == WorkoutPlannerViewModel.PlanSavingState.SAVED) {
                    finishOrReturnToPlansList(savedSuccessfully = true)
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        analyticsTracker.destroy()
    }

    private fun setupNavigation() {
        val navController = findNavController(R.id.workout_planner_nav_fragment)
        if (createNewPlanDirectly && navController.currentDestination?.id == R.id.plannedWorkoutsListFragment) {
            // Skip showing workout plans list when using an intent for creating a new plan
            navController.navigate(R.id.workoutPlanStepsListFragment)
        }
    }

    private fun setupAnalytics(restoringFromSavedState: Boolean) {
        val source = intent?.extras?.getString(STTConstants.ExtraKeys.NAVIGATED_FROM_SOURCE)
        val navController = findNavController(R.id.workout_planner_nav_fragment)
        analyticsTracker.setup(
            navController = navController,
            viewModel = viewModel,
            source = source,
            restoringFromSavedState = restoringFromSavedState
        )
    }

    fun finishOrReturnToPlansList(savedSuccessfully: Boolean) {
        if (createNewPlanDirectly && !(savedSuccessfully && showWorkoutListAfterCreation)) {
            // Intent for creating new plan directly and saving was successful: finish activity
            finish()
        } else {
            // Intent for managing workout plans: return to listing
            if (savedSuccessfully) {
                analyticsTracker.setSource(
                    AnalyticsPropertyValue.WorkoutPlanner.PLANNER_SCREEN_SOURCE_PLAN_CREATED
                )
            }

            findNavController(R.id.workout_planner_nav_fragment).popBackStack(
                destinationId = R.id.plannedWorkoutsListFragment,
                inclusive = false
            )

            // When creating a new plan directly, reset the flag after returning to plans list.
            // This enables the user to stay in the list and edit other workout plans.
            intent.removeExtra(EXTRA_CREATE_NEW_WORKOUT_PLAN)
        }
    }

    companion object {
        private const val EXTRA_CREATE_NEW_WORKOUT_PLAN = "EXTRA_CREATE_NEW_WORKOUT_PLAN"
        private const val EXTRA_SHOW_LIST_AFTER_CREATION = "EXTRA_SHOW_LIST_AFTER_CREATION"

        fun newListWorkoutPlansIntent(
            context: Context,
            analyticsSource: String,
        ) = Intent(context, WorkoutPlannerActivity::class.java)
            .putExtra(STTConstants.ExtraKeys.NAVIGATED_FROM_SOURCE, analyticsSource)

        fun newCreateWorkoutPlanIntent(
            context: Context,
            showWorkoutListAfterCreation: Boolean,
            analyticsSource: String?,
        ): Intent = Intent(context, WorkoutPlannerActivity::class.java)
            .putExtra(EXTRA_CREATE_NEW_WORKOUT_PLAN, true)
            .putExtra(EXTRA_SHOW_LIST_AFTER_CREATION, showWorkoutListAfterCreation)
            .putExtra(STTConstants.ExtraKeys.NAVIGATED_FROM_SOURCE, analyticsSource)
    }
}
