package com.stt.android.workout.planner.steps

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.LocalContentColor
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.DragDropState
import com.stt.android.compose.util.dragHandle
import com.stt.android.suuntoplus.ui.ExerciseStepInfoRow
import com.stt.android.workout.planner.R
import de.charlex.compose.RevealDirection
import de.charlex.compose.RevealState
import de.charlex.compose.RevealSwipe
import de.charlex.compose.rememberRevealState
import com.stt.android.R as BaseR

@Composable
private fun actionButtonWidth(): Dp = with(LocalDensity.current) { 64.sp.toDp() }

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun ExerciseStepListItem(
    color: Color,
    title: String,
    @DrawableRes durationIconResource: Int,
    duration: String, // duration or distance
    @DrawableRes targetIconResource: Int?,
    target: String?, // formatted target HR, pace or power
    elevation: Dp,
    onClick: (() -> Unit)?,
    onDeleteClick: () -> Unit,
    onDuplicateClick: () -> Unit,
    revealState: RevealState,
    dragDropState: DragDropState?,
    listPositionInRoot: Offset,
    modifier: Modifier = Modifier,
    enableSwipe: Boolean = true
) {
    RevealSwipe(
        modifier = modifier,
        enableSwipe = enableSwipe,
        backgroundStartActionLabel = null,
        backgroundEndActionLabel = null,
        backgroundCardStartColor = MaterialTheme.colors.error,
        contentColor = MaterialTheme.colors.onError,
        shape = RoundedCornerShape(0.dp),
        directions = setOf(RevealDirection.StartToEnd),
        maxRevealDp = actionButtonWidth() * 2,
        hiddenContentStart = {
            ExerciseStepDeleteAction(
                modifier = Modifier
                    .fillMaxHeight(),
                onDeleteClick = onDeleteClick,
                onDuplicateClick = onDuplicateClick,
            )
        },
        state = revealState,
    ) {
        ExerciseStepListCard(
            color = color,
            title = title,
            durationIconResource = durationIconResource,
            duration = duration,
            targetIconResource = targetIconResource,
            target = target,
            elevation = elevation,
            onClick = onClick,
            dragDropState = dragDropState,
            listPositionInRoot = listPositionInRoot,
        )
    }
}

@Composable
fun ExerciseStepListCard(
    color: Color,
    title: String,
    @DrawableRes durationIconResource: Int,
    duration: String, // duration or distance
    @DrawableRes targetIconResource: Int?,
    target: String?, // formatted target HR, pace or power
    elevation: Dp,
    dragDropState: DragDropState?,
    listPositionInRoot: Offset,
    onClick: (() -> Unit)?,
    modifier: Modifier = Modifier,
) {
    ExerciseStepCard(
        modifier = modifier,
        color = color,
        onClick = onClick,
        elevation = elevation
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    top = MaterialTheme.spacing.medium,
                    bottom = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.small
                )
        ) {
            Image(
                modifier = Modifier
                    .then(dragDropState?.let {
                        Modifier.dragHandle(
                            dragDropState = it,
                            listPositionInRoot = listPositionInRoot,
                        )
                    } ?: Modifier)
                    .width(MaterialTheme.spacing.xlarge)
                    .fillMaxHeight(),
                painter = painterResource(id = R.drawable.exercise_card_dots),
                contentDescription = null,
                colorFilter = ColorFilter.tint(MaterialTheme.colors.lightGrey),
                contentScale = ContentScale.Inside,
            )

            Column(Modifier.weight(1f)) {
                Text(
                    text = title,
                    modifier = Modifier,
                    style = MaterialTheme.typography.bodyLargeBold
                )

                ExerciseStepInfoRow(
                    durationIconResource = durationIconResource,
                    duration = duration,
                    targetIconResource = targetIconResource,
                    target = target
                )
            }

            Icon(
                painter = painterResource(id = BaseR.drawable.chevron_right),
                tint = Color.Unspecified,
                contentDescription = null,
            )
        }
    }
}

@Composable
private fun ActionButton(
    color: Color,
    text: String,
    @DrawableRes drawableRes: Int,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    IconButton(
        modifier = modifier
            .fillMaxHeight()
            .width(actionButtonWidth())
            .background(color),
        onClick = onClick
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Icon(
                modifier = Modifier.size(24.dp),
                painter = painterResource(drawableRes),
                contentDescription = text
            )

            Text(
                text = text,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(horizontal = MaterialTheme.spacing.xsmall)
            )
        }
    }
}

@Composable
private fun ExerciseStepDeleteAction(
    onDeleteClick: () -> Unit,
    onDuplicateClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(modifier = modifier) {
        ActionButton(
            color = MaterialTheme.colors.error,
            drawableRes = BaseR.drawable.ic_delete_outline,
            text = stringResource(BaseR.string.delete),
            onClick = onDeleteClick
        )

        ActionButton(
            color = Color(0xFFC4C4C4),
            drawableRes = R.drawable.content_duplicate,
            text = stringResource(R.string.workout_planner_step_list_duplicate_action),
            onClick = onDuplicateClick
        )
    }
}

@Composable
@Preview(heightDp = 80)
private fun ExerciseStepDeleteActionPreview() {
    AppTheme {
        Surface(color = MaterialTheme.colors.error) {
            CompositionLocalProvider(
                LocalContentColor provides MaterialTheme.colors.onError,
            ) {
                ExerciseStepDeleteAction(
                    onDeleteClick = {},
                    onDuplicateClick = {},
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Preview
@Composable
private fun ExerciseStepListItemPreview() {
    AppTheme {
        Surface(color = MaterialTheme.colors.background) {
            ExerciseStepListItem(
                modifier = Modifier
                    .padding(vertical = MaterialTheme.spacing.medium),
                color = Color(0xFFDB315A),
                title = "Interval",
                durationIconResource = BaseR.drawable.ic_summary_item_duration,
                duration = "12 min",
                targetIconResource = BaseR.drawable.ic_summary_item_power,
                target = "250 W",
                elevation = 0.dp,
                onClick = {},
                onDeleteClick = {},
                onDuplicateClick = {},
                revealState = rememberRevealState(),
                dragDropState = null,
                listPositionInRoot = Offset.Zero,
            )
        }
    }
}
