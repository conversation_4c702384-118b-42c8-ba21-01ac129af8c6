package com.stt.android.workout.planner.common

import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.SubcomposeLayout
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.sp

/**
 * If [measuredContent] fits within parent constraints, show [primaryContent] else show
 * [fallbackContent].
 *
 * This is useful when creating adaptive layouts where using fixed breakpoints and
 * BoxWithConstraints is not suitable. A typical example is using the measured width of some dynamic
 * text to decide if the layout fits in a single row or not.
 *
 * [measuredContent] must always have bounded size (e.g. don't use Modifier.fillMaxWidth()).
 * [measuredContent] will never be placed in the component tree.
 *
 * [primaryContent] and [fallbackContent] can have unbounded size.
 */
@Composable
fun FallBackWhenSizeExceededLayout(
    measuredContent: @Composable () -> Unit,
    primaryContent: @Composable () -> Unit,
    fallbackContent: @Composable () -> Unit,
    modifier: Modifier = Modifier,
) {
    SubcomposeLayout(modifier) { constraints ->
        // Ask measuredContent for size with no constraints
        val measurePlaceables = subcompose(LayoutSlot.MEASURE, measuredContent)
            .map { it.measure(Constraints()) }

        val fits = measurePlaceables.all {
            it.width <= constraints.maxWidth && it.height <= constraints.maxHeight
        }

        val content = if (fits) primaryContent else fallbackContent

        // Use proper constraints when measuring content
        val contentPlaceables = subcompose(LayoutSlot.CONTENT, content)
            .map { it.measure(constraints) }
        val width = contentPlaceables.maxOf { it.width }
        val height = contentPlaceables.maxOf { it.height }
        layout(width = width, height = height) {
            for (placeable in contentPlaceables) {
                placeable.placeRelative(0, 0)
            }
        }
    }
}

private enum class LayoutSlot {
    MEASURE,
    CONTENT
}

@Composable
@Preview(showBackground = true, backgroundColor = 0xFFFFFF, widthDp = 220)
@Preview(showBackground = true, backgroundColor = 0xFFFFFF, widthDp = 260)
@Preview(showBackground = true, backgroundColor = 0xFFFFFF, widthDp = 400)
@Preview(showBackground = true, backgroundColor = 0xFFFFFF, widthDp = 440)
private fun FallbackLayoutPreview() {
    val primaryText = "Primary text fits fine"
    val fallbackText = "Fallback text"

    FallBackWhenSizeExceededLayout(
        measuredContent = { Text(primaryText, fontSize = 32.sp) },
        primaryContent = { Text(primaryText, fontSize = 32.sp) },
        fallbackContent = { Text(fallbackText, fontSize = 32.sp) }
    )
}
