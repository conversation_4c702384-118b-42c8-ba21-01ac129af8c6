package com.stt.android.workout.planner.editstep

import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsFocusedAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Card
import androidx.compose.material.Divider
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material.TextField
import androidx.compose.material.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.soy.algorithms.planner.DefaultMessagesFormatter
import com.soy.algorithms.planner.GuideLimits
import com.soy.algorithms.planner.GuideMessagesFormatter
import com.soy.algorithms.planner.WorkoutStep
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.darkGreyText
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.ConfirmationDialog
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.utils.displayNameResource
import com.stt.android.utils.formatDurationText
import com.stt.android.utils.formatTargetText
import com.stt.android.workout.planner.R
import com.stt.android.workout.planner.common.WorkoutStepPhaseSwatch
import java.util.Locale
import com.stt.android.R as BaseR

@Composable
fun EditStepView(
    formatter: GuideMessagesFormatter,
    unit: MeasurementUnit,
    phase: WorkoutStep.Exercise.Phase,
    duration: WorkoutStep.Exercise.Duration,
    target: WorkoutStep.Exercise.Target?,
    name: String?,
    onNameChange: (String) -> Unit,
    onPhaseChange: (WorkoutStep.Exercise.Phase) -> Unit,
    onTargetChange: (WorkoutStep.Exercise.Target?) -> Unit,
    onDurationChange: (WorkoutStep.Exercise.Duration) -> Unit,
    onDeleteConfirm: () -> Unit,
    modifier: Modifier = Modifier,
) {
    var showDeleteConfirmationDialog by rememberSaveable { mutableStateOf(false) }
    var showTargetDialog by rememberSaveable { mutableStateOf(false) }
    var showDurationDialog by rememberSaveable { mutableStateOf(false) }
    var showPhaseDialog by rememberSaveable { mutableStateOf(false) }

    ContentCenteringColumn(
        modifier = modifier
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.Center
    ) {
        EditStepSubtitle(text = stringResource(R.string.workout_planner_step_details_phase))
        EditStepCard(onClick = { showPhaseDialog = true }) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(phase.displayNameResource),
                    modifier = Modifier.padding(start = MaterialTheme.spacing.medium)
                )

                WorkoutStepPhaseSwatch(phase = phase)
            }
        }

        EditStepSubtitle(text = stringResource(R.string.workout_planner_step_details_name))
        EditStepCard(onClick = {}, padding = false) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                EditStepNameTextField(
                    modifier = Modifier.weight(1f),
                    name = name,
                    labelText = stringResource(phase.displayNameResource),
                    onNameChange = onNameChange
                )
            }
        }

        EditStepSubtitle(text = stringResource(R.string.workout_planner_step_details_duration))
        EditStepCard(onClick = {
            showDurationDialog = true
        }) {
            Row(
                modifier = Modifier.height(IntrinsicSize.Min),
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small)
            ) {
                Text(
                    modifier = Modifier.padding(start = MaterialTheme.spacing.medium),
                    text = stringResource(duration.displayNameResource)
                )

                if (duration !is WorkoutStep.Exercise.Duration.LapButton) {
                    Divider(
                        modifier = Modifier
                            .width(1.dp)
                            .fillMaxHeight()
                    )

                    Text(
                        duration.formatDurationText(
                            LocalContext.current.resources,
                            formatter
                        )
                    )
                }
            }
        }

        EditStepSubtitle(text = stringResource(R.string.workout_planner_step_details_target))
        EditStepCard(onClick = {
            showTargetDialog = true
        }) {
            Row(
                modifier = Modifier.height(IntrinsicSize.Min),
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small)
            ) {
                Text(
                    modifier = Modifier.padding(start = MaterialTheme.spacing.medium),
                    text = stringResource(target?.displayNameResource ?: R.string.workout_planner_edit_step_target_none)
                )

                if (target != null) {
                    Divider(
                        modifier = Modifier
                            .width(1.dp)
                            .fillMaxHeight()
                    )

                    Text(text = target.formatTargetText(formatter).orEmpty())
                }
            }
        }

        DeleteStepButton(onClick = { showDeleteConfirmationDialog = true })
    }

    if (showDeleteConfirmationDialog) {
        ConfirmationDialog(
            text = stringResource(R.string.workout_planner_step_details_delete_confirmation),
            confirmButtonText = stringResource(BaseR.string.delete),
            cancelButtonText = stringResource(BaseR.string.cancel),
            onDismissRequest = { showDeleteConfirmationDialog = false },
            onConfirm = onDeleteConfirm,
            useDestructiveColorForConfirm = true
        )
    }

    if (showTargetDialog) {
        EditStepTargetPopup(
            unit = unit,
            formatter = formatter,
            phase = phase,
            originalTarget = target,
            onNewTarget = onTargetChange,
            onDismissRequest = { showTargetDialog = false }
        )
    }

    if (showDurationDialog) {
        EditStepDurationPopup(
            unit = unit,
            phase = phase,
            originalDuration = duration,
            onNewDuration = onDurationChange,
            onDismissRequest = { showDurationDialog = false }
        )
    }

    if (showPhaseDialog) {
        EditStepPhasePopup(
            phase = phase,
            onPhaseChange = onPhaseChange,
            onDismissRequest = { showPhaseDialog = false }
        )
    }
}

@Composable
private fun EditStepNameTextField(
    name: String?,
    labelText: String,
    onNameChange: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    val focusManager = LocalFocusManager.current
    val interactionSource = remember { MutableInteractionSource() }
    val isFocused = interactionSource.collectIsFocusedAsState().value

    val label: @Composable (() -> Unit)? = if (!isFocused && name.isNullOrEmpty()) {
        { Text(labelText) }
    } else {
        null
    }

    TextField(
        modifier = modifier,
        value = name.orEmpty(),
        label = label,
        onValueChange = onNameChange,
        interactionSource = interactionSource,
        singleLine = true,
        leadingIcon = {
            Icon(
                modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium),
                painter = painterResource(id = BaseR.drawable.edit_outline),
                tint = Color(0xFF7E8084),
                contentDescription = null
            )
        },
        colors = TextFieldDefaults.textFieldColors(
            backgroundColor = Color.Unspecified,
            focusedIndicatorColor = Color.Unspecified,
            unfocusedIndicatorColor = Color.Unspecified
        ),
        keyboardActions = KeyboardActions(
            onDone = {
                focusManager.clearFocus()
            }
        )
    )
}

@Composable
fun EditStepSubtitle(
    text: String,
    modifier: Modifier = Modifier
) = Text(
    modifier = modifier.padding(
        start = MaterialTheme.spacing.medium,
        end = MaterialTheme.spacing.medium,
        top = MaterialTheme.spacing.large,
        bottom = MaterialTheme.spacing.small
    ),
    text = text,
    style = MaterialTheme.typography.bodyLarge,
    color = MaterialTheme.colors.darkGreyText
)

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun EditStepCard(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    padding: Boolean = true,
    content: @Composable () -> Unit,
) {
    Card(
        modifier = modifier.padding(horizontal = MaterialTheme.spacing.medium),
        onClick = onClick
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .then(
                    if (padding) {
                        Modifier.padding(vertical = MaterialTheme.spacing.large)
                    } else {
                        Modifier
                    }
                )
        ) {
            Box(Modifier.weight(1f)) {
                content()
            }

            Icon(
                painter = painterResource(id = BaseR.drawable.chevron_right),
                tint = Color.Unspecified,
                contentDescription = null,
            )
        }
    }
}

@Composable
fun DeleteStepButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    TextButton(
        modifier = modifier
            .fillMaxWidth()
            .padding(MaterialTheme.spacing.large),
        onClick = onClick
    ) {
        Text(
            text = stringResource(R.string.workout_planner_step_details_delete_step)
                .uppercase(Locale.getDefault()),
            color = MaterialTheme.colors.error,
            style = MaterialTheme.typography.bodyBold
        )
    }
}

@Preview(widthDp = 320)
@Preview(widthDp = 760)
@Composable
private fun EditStepViewPreview() {
    AppTheme {
        Surface(color = MaterialTheme.colors.background) {
            var name by remember { mutableStateOf<String?>(null) }

            EditStepView(
                formatter = DefaultMessagesFormatter(),
                unit = MeasurementUnit.METRIC,
                phase = WorkoutStep.Exercise.Phase.WARM_UP,
                duration = WorkoutStep.Exercise.Duration.Time(60.0),
                target = WorkoutStep.Exercise.Target.HeartRate(
                    value = 120.0,
                    min = null,
                    max = null,
                ),
                name = name,
                onNameChange = { newName ->
                    name = newName
                        .take(GuideLimits.stepTitle)
                        .takeUnless { it.isBlank() }
                },
                onPhaseChange = {},
                onDeleteConfirm = {},
                onTargetChange = {},
                onDurationChange = {}
            )
        }
    }
}
