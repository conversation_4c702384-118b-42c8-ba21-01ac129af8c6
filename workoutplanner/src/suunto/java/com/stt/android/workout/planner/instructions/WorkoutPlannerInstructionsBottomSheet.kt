package com.stt.android.workout.planner.instructions

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.soy.algorithms.planner.DefaultMessagesFormatter
import com.soy.algorithms.planner.GuideMessagesFormatter
import com.soy.algorithms.planner.WorkoutStep
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bottomSheetShape
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.rememberViewInteropNestedScrollConnection
import com.stt.android.compose.widgets.DraggableBottomSheetHandle
import com.stt.android.device.domain.suuntoplusguide.SUUNTO_PLANNER_CLIENT_ID
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuide
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuideId
import com.stt.android.domain.workout.ActivityType
import com.stt.android.utils.displayNameResource
import com.stt.android.utils.durationIconRes
import com.stt.android.utils.formatDurationText
import com.stt.android.utils.formatTargetText
import com.stt.android.utils.targetIconRes
import com.stt.android.utils.color
import com.stt.android.workout.planner.R
import com.stt.android.workout.planner.plans.PlannedWorkoutsListItem
import com.stt.android.suuntoplus.ui.EndRepeatItem
import com.stt.android.workout.planner.steps.ExerciseStepListItem
import com.stt.android.workout.planner.steps.RepeatBlockStepListItemConnector
import com.stt.android.workout.planner.steps.StartRepeatItem
import de.charlex.compose.rememberRevealState
import java.time.LocalDate

@Composable
fun WorkoutPlannerInstructionsBottomSheet(
    formatter: GuideMessagesFormatter,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier.nestedScroll(rememberViewInteropNestedScrollConnection()),
        shape = MaterialTheme.shapes.bottomSheetShape,
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium)
        ) {
            DraggableBottomSheetHandle()

            WorkoutPlannerInstructionsContent(
                modifier = Modifier
                    .verticalScroll(rememberScrollState())
                    .padding(vertical = MaterialTheme.spacing.large),
                formatter = formatter
            )
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun WorkoutPlannerInstructionsContent(
    formatter: GuideMessagesFormatter,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall)
    ) {
        Text(
            stringResource(R.string.workout_planner_get_started_1_build_workouts_title),
            style = MaterialTheme.typography.bodyBold
        )

        Text(
            stringResource(R.string.workout_planner_get_started_1_build_workouts_text),
            style = MaterialTheme.typography.body
        )

        Column(
            modifier = Modifier
                .padding(horizontal = MaterialTheme.spacing.large)
        ) {
            StartRepeatItem(count = 5, onDecreaseCountClick = {}, onIncreaseCountClick = {}, countClickEnabled = false)

            for (step in listOf(exampleIntervalStep, exampleRecoveryStep)) {
                ExerciseStepListItem(
                    modifier = Modifier
                        .pointerInput(Unit) {},
                    title = stringResource(step.phase.displayNameResource),
                    color = step.phase.color,
                    durationIconResource = step.duration.durationIconRes,
                    duration = step.duration.formatDurationText(
                        LocalContext.current.resources,
                        formatter
                    ),
                    targetIconResource = step.target?.targetIconRes,
                    target = step.target?.formatTargetText(formatter),
                    elevation = 16.dp,
                    onClick = null,
                    onDeleteClick = {},
                    onDuplicateClick = {},
                    revealState = rememberRevealState(),
                    enableSwipe = false,
                    dragDropState = null,
                    listPositionInRoot = Offset.Zero,
                )

                if (step == exampleIntervalStep) {
                    RepeatBlockStepListItemConnector()
                }
            }

            EndRepeatItem()
        }

        Text(
            stringResource(R.string.workout_planner_get_started_2_sync_to_watch_title),
            style = MaterialTheme.typography.bodyBold
        )

        Text(
            stringResource(R.string.workout_planner_get_started_2_sync_to_watch_text),
            style = MaterialTheme.typography.body
        )

        PlannedWorkoutsListItem(
            modifier = Modifier
                .padding(MaterialTheme.spacing.large),
            guide = exampleGuide.copy(name = stringResource(id = R.string.workout_planner_workout_plan)),
            onClick = null,
            onDeleteClick = {},
            elevation = 16.dp,
            enableSwipe = false
        )

        Text(
            stringResource(R.string.workout_planner_get_started_3_real_time_guidance_title),
            style = MaterialTheme.typography.bodyBold
        )

        Text(
            stringResource(R.string.workout_planner_get_started_3_real_time_guidance_text),
            style = MaterialTheme.typography.body
        )

        Icon(
            modifier = Modifier
                .align(Alignment.CenterHorizontally)
                .size(180.dp),
            painter = painterResource(R.drawable.structured_workout_example_on_watch),
            tint = Color.Unspecified,
            contentDescription = null
        )
    }
}

private val exampleIntervalStep = WorkoutStep.Exercise(
    phase = WorkoutStep.Exercise.Phase.INTERVAL,
    duration = WorkoutStep.Exercise.Duration.Distance(1000.0),
    target = WorkoutStep.Exercise.Target.HeartRate(
        value = 155.0,
        min = 150.0,
        max = 160.0,
    ),
)

private val exampleRecoveryStep = WorkoutStep.Exercise(
    phase = WorkoutStep.Exercise.Phase.REST,
    duration = WorkoutStep.Exercise.Duration.Time(60.0),
    target = WorkoutStep.Exercise.Target.HeartRate(
        value = 115.0,
        min = 110.0,
        max = 120.0,
    ),
)

private val exampleGuide = SuuntoPlusGuide(
    id = SuuntoPlusGuideId("demo1234"),
    catalogueId = null,
    modifiedMillis = 0L,
    name = "",
    owner = "",
    ownerId = SUUNTO_PLANNER_CLIENT_ID,
    date = LocalDate.now(),
    url = null,
    iconUrl = null,
    backgroundUrl = "",
    description = "",
    subTitle = "",
    richDescription = null,
    activityIds = listOf(ActivityType.RUNNING.id),
    pinned = false
)

@Composable
@Preview
private fun WorkoutPlannerInstructionsBottomSheetPreview() {
    AppTheme {
        WorkoutPlannerInstructionsBottomSheet(
            formatter = DefaultMessagesFormatter()
        )
    }
}
