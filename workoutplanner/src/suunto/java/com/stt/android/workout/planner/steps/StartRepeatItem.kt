package com.stt.android.workout.planner.steps

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.workout.planner.R
import com.stt.android.suuntoplus.ui.CurveDirection
import com.stt.android.suuntoplus.ui.CurvedLine
import com.stt.android.workout.planner.common.buildAnnotatedStringWithStyledNumbers

@Composable
fun StartRepeatItem(
    count: Int,
    onDecreaseCountClick: () -> Unit,
    onIncreaseCountClick: () -> Unit,
    modifier: Modifier = Modifier,
    countClickEnabled: Boolean = true,
) {
    Row(
        modifier = modifier
            .padding(top = MaterialTheme.spacing.xsmall)
            .fillMaxWidth()
            .height(40.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        CurvedLine(
            modifier = Modifier
                .padding(start = MaterialTheme.spacing.large)
                .weight(1f)
                .fillMaxHeight(),
            direction = CurveDirection.LEFT_DOWN
        )

        IconButton(
            onClick = onDecreaseCountClick,
            enabled = countClickEnabled
        ) {
            Box(
                modifier = Modifier
                    .size(28.dp)
                    .clip(RoundedCornerShape(4.dp))
                    .background(MaterialTheme.colors.surface)
                    .border(1.dp, Color(0xFFE8E8E8), RoundedCornerShape(4.dp)),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_minus),
                    contentDescription = null
                )
            }
        }

        val resources = LocalContext.current.resources
        val repeatCountText = resources.getQuantityString(
            R.plurals.workout_planner_step_list_repeat_count,
            count,
            count
        )

        Text(
            text = buildAnnotatedStringWithStyledNumbers(
                text = repeatCountText,
                numberStyle = SpanStyle(fontWeight = FontWeight.Bold)
            )
        )

        IconButton(
            onClick = onIncreaseCountClick,
            enabled = countClickEnabled
        ) {
            Box(
                modifier = Modifier
                    .size(28.dp)
                    .clip(RoundedCornerShape(4.dp))
                    .background(MaterialTheme.colors.surface)
                    .border(1.dp, Color(0xFFE8E8E8), RoundedCornerShape(4.dp)),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_plus_workout_planner),
                    contentDescription = null
                )
            }
        }

        CurvedLine(
            modifier = Modifier
                .padding(end = MaterialTheme.spacing.large)
                .weight(1f)
                .fillMaxHeight(),
            direction = CurveDirection.RIGHT_DOWN
        )
    }
}

@Preview
@Composable
private fun StartRepeatItemPreview() {
    AppTheme {
        Surface(color = MaterialTheme.colors.background) {
            var count by remember { mutableIntStateOf(3) }

            StartRepeatItem(
                count = count,
                onDecreaseCountClick = {
                    count -= 1
                },
                onIncreaseCountClick = {
                    count += 1
                }
            )
        }
    }
}
