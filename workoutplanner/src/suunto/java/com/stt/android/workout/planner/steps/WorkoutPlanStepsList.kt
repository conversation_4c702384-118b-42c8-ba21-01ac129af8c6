package com.stt.android.workout.planner.steps

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.Divider
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInRoot
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.soy.algorithms.planner.DefaultMessagesFormatter
import com.soy.algorithms.planner.GuideMessagesFormatter
import com.soy.algorithms.planner.WorkoutStep
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.DraggableItem
import com.stt.android.utils.color
import com.stt.android.utils.displayNameResource
import com.stt.android.compose.util.dragContainer
import com.stt.android.utils.durationIconRes
import com.stt.android.utils.formatDurationText
import com.stt.android.utils.formatTargetText
import com.stt.android.compose.util.rememberDragDropState
import com.stt.android.suuntoplus.ui.EndRepeatItem
import com.stt.android.utils.targetIconRes
import com.stt.android.workout.planner.WorkoutPlanStepsListItem
import com.stt.android.workout.planner.common.BreadcrumbStepIndicator
import com.stt.android.workout.planner.common.BreadcrumbSteps
import de.charlex.compose.rememberRevealState
import de.charlex.compose.reset
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.cancelChildren
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun WorkoutPlanStepsList(
    formatter: GuideMessagesFormatter,
    listItems: ImmutableList<WorkoutPlanStepsListItem>,
    canMoveStep: (index: Int) -> Boolean,
    canMoveStepOverItem: (index: Int) -> Boolean,
    onStepClick: (WorkoutStep.Exercise) -> Unit,
    onMoveStep: (fromIndex: Int, toIndex: Int) -> Unit,
    onAddExerciseClick: () -> Unit,
    onAddRepeatClick: () -> Unit,
    onDecreaseCountClick: (WorkoutStep.Repeat) -> Unit,
    onIncreaseCountClick: (WorkoutStep.Repeat) -> Unit,
    onDragInterrupted: () -> Unit,
    onDeleteClick: (WorkoutStep.Exercise) -> Unit,
    onDuplicateClick: (WorkoutStep.Exercise) -> Unit,
    modifier: Modifier = Modifier,
) {
    val listState = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()
    val dragDropState = rememberDragDropState(
        lazyListState = listState,
        canMove = canMoveStep,
        onMove = onMoveStep,
        canMoveOver = canMoveStepOverItem,
    )
    
    var listPositionInRoot by remember { mutableStateOf(Offset.Zero) }

    var closeRevealMenuTrigger: Int by remember { mutableIntStateOf(0) }

    LazyColumn(
        modifier = modifier
            .dragContainer(
                dragDropState = dragDropState,
                onDragStarted = {
                    closeRevealMenuTrigger += 1
                },
                onDragInterrupted = onDragInterrupted
            )
            .onGloballyPositioned {
                listPositionInRoot = it.positionInRoot()
            },
        state = listState,
        contentPadding = PaddingValues(vertical = MaterialTheme.spacing.small),
    ) {
        itemsIndexed(listItems, { _, item -> item.key }) { index, item ->
            when (item) {
                is WorkoutPlanStepsListItem.ExerciseStep -> {
                    DraggableItem(dragDropState, index) { isDragging ->
                        val elevation =
                            animateDpAsState(targetValue = if (isDragging) 8.dp else 0.dp)
                        val revealState = rememberRevealState()

                        // Close reveal menu whenever closeRevealMenuTrigger value changes
                        LaunchedEffect(closeRevealMenuTrigger) {
                            // Use NonCancellable to keep the animation running even if
                            // closeRevealMenuTrigger value changes rapidly multiple times
                            launch(NonCancellable) {
                                revealState.reset()
                            }
                        }

                        val nextIsStep =
                            listItems.getOrNull(index + 1) is WorkoutPlanStepsListItem.ExerciseStep

                        Column {
                            ExerciseStepListItem(
                                title = item.step.name
                                    ?: stringResource(item.step.phase.displayNameResource),
                                color = item.step.phase.color,
                                durationIconResource = item.step.duration.durationIconRes,
                                duration = item.step.duration.formatDurationText(
                                    LocalContext.current.resources,
                                    formatter
                                ),
                                targetIconResource = item.step.target?.targetIconRes,
                                target = item.step.target?.formatTargetText(formatter),
                                elevation = elevation.value,
                                onClick = {
                                    onStepClick(item.step)
                                },
                                onDeleteClick = {
                                    onDeleteClick(item.step)
                                    closeRevealMenuTrigger += 1
                                },
                                onDuplicateClick = {
                                    onDuplicateClick(item.step)
                                    closeRevealMenuTrigger += 1
                                },
                                revealState = revealState,
                                dragDropState = dragDropState,
                                listPositionInRoot = listPositionInRoot,
                            )

                            if (item.insideRepeatBlock && !isDragging && nextIsStep) {
                                // Steps inside repeat blocks have connecting lines (unless dragging)
                                RepeatBlockStepListItemConnector()
                            } else if (nextIsStep) {
                                // Steps outside repeat blocks have a simple margin
                                Spacer(Modifier.height(MaterialTheme.spacing.small))
                            }
                        }
                    }
                }
                is WorkoutPlanStepsListItem.AddExerciseOrRepeatButton -> {
                    AddExerciseOrRepeatButton(
                        onAddExerciseClick = {
                            onAddExerciseClick()
                            coroutineScope.coroutineContext.cancelChildren()
                            coroutineScope.launch {
                                // Small delay after adding item
                                // TODO: Implement insert animation
                                delay(300L)

                                // Scroll to end
                                listState.animateScrollToItem(
                                    listItems.lastIndex
                                )
                            }
                        },
                        onAddRepeatClick = onAddRepeatClick
                    )
                }
                is WorkoutPlanStepsListItem.BreadcrumbItem -> {
                    BreadcrumbStepIndicator(
                        modifier = Modifier.padding(MaterialTheme.spacing.medium),
                        steps = BreadcrumbSteps.steps,
                        currentStepIndex = 0,
                    )
                }
                is WorkoutPlanStepsListItem.RepeatEnd ->
                    EndRepeatItem()
                is WorkoutPlanStepsListItem.RepeatStart ->
                    StartRepeatItem(
                        item.times,
                        onDecreaseCountClick = { onDecreaseCountClick(item.repeatStep) },
                        onIncreaseCountClick = { onIncreaseCountClick(item.repeatStep) },
                    )
            }
        }
    }
}

@Composable
fun RepeatBlockStepListItemConnector(
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .padding(horizontal = MaterialTheme.spacing.large)
            .height(MaterialTheme.spacing.small)
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Divider(
            Modifier
                .fillMaxHeight()
                .width(1.dp),
            color = MaterialTheme.colors.mediumGrey
        )

        Divider(
            Modifier
                .fillMaxHeight()
                .width(1.dp),
            color = MaterialTheme.colors.mediumGrey
        )
    }
}

@Preview(widthDp = 320)
@Preview(widthDp = 760)
@Composable
private fun WorkoutPlanStepsListPreview() {
    // Use long press in interactive mode to reorder items using drag and drop

    val warmupStep = WorkoutStep.Exercise(
        phase = WorkoutStep.Exercise.Phase.WARM_UP,
        duration = WorkoutStep.Exercise.Duration.Time(5 * 60.0),
        target = WorkoutStep.Exercise.Target.HeartRate(
            value = 120.0,
            min = null,
            max = null,
        ),
    )

    val intervalStep = WorkoutStep.Exercise(
        phase = WorkoutStep.Exercise.Phase.INTERVAL,
        duration = WorkoutStep.Exercise.Duration.Distance(1500.0),
        target = WorkoutStep.Exercise.Target.HeartRate(
            value = 160.0,
            min = null,
            max = null,
        ),
    )

    val intervalStep2 = WorkoutStep.Exercise(
        phase = WorkoutStep.Exercise.Phase.INTERVAL,
        duration = WorkoutStep.Exercise.Duration.Time(360.0),
        target = WorkoutStep.Exercise.Target.HeartRate(
            value = 120.0,
            min = null,
            max = null,
        ),
    )

    val repeatStep = WorkoutStep.Repeat(
        times = 3,
        exercises = listOf(intervalStep, intervalStep2)
    )

    val coolDownStep = WorkoutStep.Exercise(
        phase = WorkoutStep.Exercise.Phase.COOL_DOWN,
        duration = WorkoutStep.Exercise.Duration.Time(5 * 60.0),
        target = WorkoutStep.Exercise.Target.HeartRate(
            value = 100.0,
            min = null,
            max = null,
        ),
    )

    val emptyRepeatStep = WorkoutStep.Repeat(
        times = 2,
        exercises = emptyList()
    )

    val steps = remember {
        mutableStateListOf(
            WorkoutPlanStepsListItem.BreadcrumbItem,
            WorkoutPlanStepsListItem.ExerciseStep(warmupStep, false, 0),
            WorkoutPlanStepsListItem.RepeatStart(repeatStep, 1),
            WorkoutPlanStepsListItem.ExerciseStep(intervalStep, true, 2),
            WorkoutPlanStepsListItem.ExerciseStep(intervalStep2, true, 3),
            WorkoutPlanStepsListItem.RepeatEnd("end-repeat-id", 4),
            WorkoutPlanStepsListItem.ExerciseStep(coolDownStep, false, 5),
            WorkoutPlanStepsListItem.RepeatStart(emptyRepeatStep, 6),
            WorkoutPlanStepsListItem.RepeatEnd("end-repeat-id2", 7),
            WorkoutPlanStepsListItem.AddExerciseOrRepeatButton
        )
    }

    AppTheme {
        Surface(color = MaterialTheme.colors.background) {
            WorkoutPlanStepsList(
                formatter = DefaultMessagesFormatter(),
                listItems = steps.toImmutableList(),
                canMoveStep = { index ->
                    val item = steps[index]
                    item is WorkoutPlanStepsListItem.ExerciseStep
                },
                canMoveStepOverItem = { index ->
                    val item = steps[index]
                    item is WorkoutPlanStepsListItem.ExerciseStep ||
                        item is WorkoutPlanStepsListItem.RepeatStart ||
                        item is WorkoutPlanStepsListItem.RepeatEnd
                },
                onStepClick = {},
                onMoveStep = { from, to ->
                    steps.add(to, steps.removeAt(from))
                },
                onAddExerciseClick = {},
                onAddRepeatClick = {},
                onDecreaseCountClick = {},
                onIncreaseCountClick = {},
                onDragInterrupted = {},
                onDeleteClick = {},
                onDuplicateClick = {}
            )
        }
    }
}
