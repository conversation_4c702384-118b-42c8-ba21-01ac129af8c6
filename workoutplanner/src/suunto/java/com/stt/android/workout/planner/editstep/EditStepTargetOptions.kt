package com.stt.android.workout.planner.editstep

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableDoubleStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import com.soy.algorithms.planner.DefaultMessagesFormatter
import com.soy.algorithms.planner.GuideMessagesFormatter
import com.soy.algorithms.planner.WorkoutStep
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.darkGreyText
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.workout.planner.R
import com.stt.android.workout.planner.WorkoutPlannerLimits
import com.stt.android.workout.planner.common.DurationVisualTransformation
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

/**
 * Inputs for editing step target. Five radio buttons control the type of the
 * target value for the step (heart rate, power, speed, pace, or none). All but none
 * have additional text inputs for entering values.
 */
@Composable
fun EditStepTargetOptions(
    state: EditStepTargetState,
    onStateUpdate: (EditStepTargetStatePartialUpdate) -> Unit,
    formatter: GuideMessagesFormatter,
    unit: MeasurementUnit,
    modifier: Modifier = Modifier
) {
    // km/h or mph
    val maxSpeedInUserUnits by remember(unit) {
        mutableDoubleStateOf(
            WorkoutPlannerLimits.maxSpeedInUserUnit(unit)
        )
    }

    fun setTargetMode(newEditMode: EditStepTargetMode) {
        onStateUpdate(EditStepTargetStatePartialUpdate(newEditMode = newEditMode))
    }

    Column(modifier) {
        EditTargetColumn(
            selected = state.editMode == EditStepTargetMode.HEART_RATE,
            onClick = { setTargetMode(EditStepTargetMode.HEART_RATE) },
            title = stringResource(BaseR.string.workout_planner_edit_step_target_heart_rate),
        ) {
            EditTargetRangeInput(
                state = state.heartRateTarget,
                onStateChange = { onStateUpdate(EditStepTargetStatePartialUpdate(newHeartRateMinMax = it)) },
                valueValidator = EditTargetInputValidator::isValidHeartRateInput,
                visualTransformation = VisualTransformation.None,
                focusSecondValueTrigger = EditTargetInputFocusTrigger::shouldFocusNextHeartRateInputField,
                modifier = Modifier.fillMaxWidth(),
            )

            CurrentTargetFromMinMaxText(
                target = state.asWorkoutStepTarget(unit),
                formatter = formatter,
                modifier = Modifier.fillMaxWidth()
            )
        }

        EditTargetColumn(
            selected = state.editMode == EditStepTargetMode.POWER,
            onClick = { setTargetMode(EditStepTargetMode.POWER) },
            title = stringResource(BaseR.string.workout_planner_edit_step_target_power),
        ) {
            EditTargetRangeInput(
                state = state.powerTarget,
                onStateChange = { onStateUpdate(EditStepTargetStatePartialUpdate(newPowerMinMax = it)) },
                valueValidator = EditTargetInputValidator::isValidPowerInput,
                visualTransformation = VisualTransformation.None,
                focusSecondValueTrigger = EditTargetInputFocusTrigger::shouldFocusNextPowerInputField,
                modifier = Modifier.fillMaxWidth(),
            )

            CurrentTargetFromMinMaxText(
                target = state.asWorkoutStepTarget(unit),
                formatter = formatter,
                modifier = Modifier.fillMaxWidth()
            )
        }

        EditTargetColumn(
            selected = state.editMode == EditStepTargetMode.SPEED,
            onClick = { setTargetMode(EditStepTargetMode.SPEED) },
            title = stringResource(BaseR.string.workout_planner_edit_step_target_speed),
        ) {
            EditTargetRangeInput(
                state = state.speedTarget,
                onStateChange = { onStateUpdate(EditStepTargetStatePartialUpdate(newSpeedMinMax = it)) },
                valueValidator = {
                    EditTargetInputValidator.isValidSpeedInput(
                        it,
                        maxSpeedInUserUnits
                    )
                },
                visualTransformation = VisualTransformation.None,
                focusSecondValueTrigger = EditTargetInputFocusTrigger::shouldFocusNextSpeedInputField,
                modifier = Modifier.fillMaxWidth(),
            )

            CurrentTargetFromMinMaxText(
                target = state.asWorkoutStepTarget(unit),
                formatter = formatter,
                modifier = Modifier.fillMaxWidth()
            )
        }

        EditTargetColumn(
            selected = state.editMode == EditStepTargetMode.PACE,
            onClick = { setTargetMode(EditStepTargetMode.PACE) },
            title = stringResource(BaseR.string.workout_planner_edit_step_target_pace),
        ) {
            // Max pace is on the left, min pace on the right. For example, 4'00-5'00 /km.
            EditTargetRangeInput(
                state = state.paceTarget,
                onStateChange = { onStateUpdate(EditStepTargetStatePartialUpdate(newPaceMinMax = it)) },
                valueValidator = EditTargetInputValidator::isValidPaceInput,
                visualTransformation = DurationVisualTransformation(SpanStyle(color = MaterialTheme.colors.darkGreyText)),
                focusSecondValueTrigger = EditTargetInputFocusTrigger::shouldFocusNextPaceInputField,
                modifier = Modifier.fillMaxWidth(),
                inputOrder = EditRangeInputOrder.MAX_IS_FIRST,
            )

            CurrentTargetFromMinMaxText(
                target = state.asWorkoutStepTarget(unit),
                formatter = formatter,
                modifier = Modifier.fillMaxWidth()
            )
        }

        EditTargetColumn(
            selected = state.editMode == EditStepTargetMode.NONE,
            onClick = { setTargetMode(EditStepTargetMode.NONE) },
            title = stringResource(R.string.workout_planner_edit_step_target_none),
        ) {
        }
    }
}

@Composable
@Preview
private fun EditStepTargetPreview() {
    AppTheme {
        Surface {
            val bpmString = stringResource(CR.string.TXT_BPM)
            val wattString = stringResource(CR.string.watt)
            val kmPerHourString = stringResource(CR.string.TXT_KMH)
            val minPerKmString = stringResource(CR.string.TXT_PER_KM)
            var state by remember {
                mutableStateOf(
                    EditStepTargetState(
                        editMode = EditStepTargetMode.HEART_RATE,
                        phase = WorkoutStep.Exercise.Phase.INTERVAL,
                        unit = MeasurementUnit.METRIC,
                        heartRateTarget = EditStepTargetMinMaxState(
                            minValue = TextFieldValue("100"),
                            maxValue = TextFieldValue("120"),
                            unitText = bpmString
                        ),
                        powerTarget = EditStepTargetMinMaxState(
                            minValue = TextFieldValue("250"),
                            maxValue = TextFieldValue("300"),
                            unitText = wattString
                        ),
                        speedTarget = EditStepTargetMinMaxState(
                            minValue = TextFieldValue("10.0"),
                            maxValue = TextFieldValue("12.0"),
                            unitText = kmPerHourString
                        ),
                        paceTarget = EditStepTargetMinMaxState(
                            minValue = TextFieldValue("600"), // 6'00 /km
                            maxValue = TextFieldValue("500"), // 5'00 /km
                            unitText = minPerKmString
                        ),
                    )
                )
            }

            EditStepTargetOptions(
                state = state,
                onStateUpdate = { state = state.withUpdateApplied(it) },
                formatter = DefaultMessagesFormatter(),
                unit = MeasurementUnit.METRIC
            )
        }
    }
}
