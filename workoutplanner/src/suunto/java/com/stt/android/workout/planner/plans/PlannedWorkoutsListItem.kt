package com.stt.android.workout.planner.plans

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.LocalContentColor
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.stt.android.compose.component.ActivityIconFlavor
import com.stt.android.compose.component.SuuntoActivityIcon
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuide
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuideId
import com.stt.android.device.domain.suuntoplusguide.isWorkoutPlan
import com.stt.android.domain.workout.ActivityType
import com.stt.android.ui.utils.formatAsRelativeDate
import com.stt.android.workout.planner.R
import de.charlex.compose.RevealDirection
import de.charlex.compose.RevealSwipe
import java.time.LocalDate
import com.stt.android.R as BaseR

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun PlannedWorkoutsListItem(
    guide: SuuntoPlusGuide,
    onClick: (() -> Unit)?,
    onDeleteClick: (SuuntoPlusGuideId) -> Unit,
    modifier: Modifier = Modifier,
    elevation: Dp = 1.dp,
    enableSwipe: Boolean = true
) {
    RevealSwipe(
        modifier = modifier,
        enableSwipe = enableSwipe,
        backgroundStartActionLabel = null,
        backgroundEndActionLabel = null,
        backgroundCardStartColor = MaterialTheme.colors.error,
        contentColor = MaterialTheme.colors.onError,
        shape = RoundedCornerShape(0.dp),
        directions = setOf(RevealDirection.StartToEnd),
        maxRevealDp = 86.dp,
        hiddenContentStart = {
            PlannedWorkoutsDeleteAction(
                modifier = Modifier.padding(start = MaterialTheme.spacing.medium),
                onDeleteClick = {
                    onDeleteClick(guide.id)
                }
            )
        },
    ) {
        PlannedWorkoutCard(
            guide = guide,
            onClick = onClick,
            elevation = elevation
        )
    }
}

@Composable
private fun PlannedWorkoutCard(
    guide: SuuntoPlusGuide,
    onClick: (() -> Unit)?,
    elevation: Dp,
    modifier: Modifier = Modifier
) {
    val cardModifier = if (onClick != null) {
        modifier.clickable(
            indication = null,
            interactionSource = remember { MutableInteractionSource() },
            onClick = onClick
        )
    } else {
        modifier
    }

    Card(
        modifier = cardModifier,
        shape = RectangleShape,
        elevation = elevation
    ) {
        Row(
            modifier = Modifier.padding(
                vertical = 20.dp,
                horizontal = MaterialTheme.spacing.medium
            ),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // Guide image
            Card(
                modifier = Modifier
                    .size(80.dp)
                    .align(Alignment.CenterVertically),
                shape = RoundedCornerShape(20.dp),
                elevation = 2.dp
            ) {
                if (LocalInspectionMode.current || guide.isWorkoutPlan) {
                    Image(
                        painter = painterResource(R.drawable.workout_planner_guide),
                        contentDescription = null,
                        contentScale = ContentScale.Inside
                    )
                } else {
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(guide.iconUrl)
                            .crossfade(true)
                            .build(),
                        contentDescription = null,
                        contentScale = ContentScale.Crop
                    )
                }
            }

            // Guide name, activity type and date
            Column(modifier = Modifier.weight(1f)) {
                // Guide name
                Text(
                    text = guide.name,
                    modifier = Modifier.fillMaxWidth(),
                    overflow = TextOverflow.Ellipsis,
                    maxLines = 2,
                    style = MaterialTheme.typography.bodyXLargeBold
                )

                // Date
                if (guide.date != null) {
                    Text(
                        modifier = Modifier.padding(top = MaterialTheme.spacing.small),
                        text = guide.date!!.formatAsRelativeDate(),
                        style = MaterialTheme.typography.body
                    )
                }

                // Activity type
                Row(Modifier.fillMaxWidth()) {
                    SuuntoActivityIcon(
                        activityTypeId = guide.activityType.id,
                        modifier = Modifier
                            .padding(
                                top = MaterialTheme.spacing.small,
                                end = MaterialTheme.spacing.small,
                                bottom = MaterialTheme.spacing.small,
                            ),
                        flavor = ActivityIconFlavor.FILL,
                        iconSize = MaterialTheme.iconSizes.small,
                    )

                    Text(
                        stringResource(id = guide.activityType.localizedStringIdForGuide()),
                        modifier = Modifier.align(Alignment.CenterVertically),
                        style = MaterialTheme.typography.body,
                        color = Color(0xFF7E8084)
                    )
                }
            }

            Icon(
                painter = painterResource(id = BaseR.drawable.chevron_right),
                tint = Color.Unspecified,
                contentDescription = null,
            )
        }
    }
}

@Composable
private fun PlannedWorkoutsDeleteAction(
    onDeleteClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    IconButton(
        modifier = modifier,
        onClick = onDeleteClick
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Icon(
                painter = painterResource(BaseR.drawable.ic_delete_outline),
                contentDescription = stringResource(BaseR.string.delete)
            )

            Text(
                text = stringResource(BaseR.string.delete),
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
}

fun ActivityType.localizedStringIdForGuide(): Int =
    if (id == ActivityType.OTHER_6.id) {
        R.string.workout_planner_activity_type_selection_any
    } else {
        localizedStringId
    }

private val SuuntoPlusGuide.activityType: ActivityType
    get() = ActivityType.valueOf(activityIds?.firstOrNull() ?: -1)

@Composable
@Preview
private fun PlannedWorkoutsDeleteActionPreview() {
    AppTheme {
        Surface(color = MaterialTheme.colors.error) {
            CompositionLocalProvider(
                LocalContentColor provides MaterialTheme.colors.onError,
            ) {
                PlannedWorkoutsDeleteAction(
                    onDeleteClick = {}
                )
            }
        }
    }
}

@Composable
@Preview
private fun PlannedWorkoutsListItemPreview() {
    val plan = SuuntoPlusGuide(
        id = SuuntoPlusGuideId("3iefmEf"),
        catalogueId = null,
        modifiedMillis = 1619870400000L,
        name = "A plan for preview",
        owner = "Suunto Workout Planner",
        ownerId = "5c2fa984-4425-4e72-8f7c-deeaa454b9c6",
        date = LocalDate.of(2022, 6, 22),
        url = null,
        iconUrl = "https://www.fillmurray.com/300/300",
        backgroundUrl = "https://www.fillmurray.com/300/300",
        description = "Work plan description here",
        subTitle = "Work plan subTitle here",
        richDescription = null,
        activityIds = listOf(ActivityType.CYCLING.id),
        pinned = false,
    )

    AppTheme {
        Surface {
            PlannedWorkoutsListItem(
                guide = plan,
                onClick = {},
                onDeleteClick = {},
            )
        }
    }
}

@Composable
@Preview
private fun PlannedWorkoutsListItemNoDatePreview() {
    val plan = SuuntoPlusGuide(
        id = SuuntoPlusGuideId("3iefmEf"),
        catalogueId = null,
        modifiedMillis = 1619870400000L,
        name = "A plan with no date for preview",
        owner = "Suunto Workout Planner",
        ownerId = "5c2fa984-4425-4e72-8f7c-deeaa454b9c6",
        date = null,
        url = null,
        iconUrl = "https://www.fillmurray.com/300/300",
        backgroundUrl = "https://www.fillmurray.com/300/300",
        description = "Work plan description here",
        subTitle = "Work plan subTitle here",
        richDescription = null,
        activityIds = listOf(ActivityType.CYCLING.id),
        pinned = false,
    )

    AppTheme {
        Surface {
            PlannedWorkoutsListItem(
                guide = plan,
                onClick = {},
                onDeleteClick = {},
            )
        }
    }
}
