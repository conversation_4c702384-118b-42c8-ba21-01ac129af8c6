package com.stt.android.workout.planner.plans

import androidx.annotation.StringRes
import androidx.compose.animation.Crossfade
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.items
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuide
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuideId
import com.stt.android.domain.workout.ActivityType
import com.stt.android.suuntoplus.ui.list.SuuntoPlusListSectionHeader
import com.stt.android.workout.domain.PlannedWorkoutsGrouped
import com.stt.android.workout.planner.R
import com.stt.android.workout.planner.common.WatchStateIndication
import com.stt.android.workout.planner.common.WatchStateIndicationData
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.delay
import java.time.LocalDate

@Composable
fun PlannedWorkoutsList(
    workoutPlanList: PlannedWorkoutsGrouped,
    onWorkoutPlanClick: (SuuntoPlusGuideId) -> Unit,
    onNewWorkoutPlanClick: () -> Unit,
    onGetStartedClick: () -> Unit,
    onDeleteWorkoutPlanClick: (SuuntoPlusGuideId) -> Unit,
    watchStateIndicationData: WatchStateIndicationData?,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.TopCenter,
    ) {
        LazyColumn {
            // Hero image including Create New button
            item("Hero image") {
                PlannedWorkoutsListHeroImage(
                    modifier = Modifier.padding(bottom = MaterialTheme.spacing.medium),
                    onNewWorkoutPlanClick = onNewWorkoutPlanClick
                )
            }

            // Current workout plans
            if (workoutPlanList.isEmpty()) {
                item("Empty state") {
                    PlannedWorkoutsEmptyState(
                        onGetStartedClick = onGetStartedClick
                    )
                }
            } else {
                workoutPlanListSection(
                    titleTextRes = R.string.workout_planner_my_workout_plans_upcoming_section,
                    workoutPlans = workoutPlanList.upcomingWorkouts,
                    onWorkoutPlanClick = onWorkoutPlanClick,
                    onDeleteWorkoutPlanClick = onDeleteWorkoutPlanClick,
                )

                workoutPlanListSection(
                    titleTextRes = R.string.workout_planner_my_workout_plans_no_date_section,
                    workoutPlans = workoutPlanList.workoutsWithNoSpecificDate,
                    onWorkoutPlanClick = onWorkoutPlanClick,
                    onDeleteWorkoutPlanClick = onDeleteWorkoutPlanClick,
                )

                workoutPlanListSection(
                    titleTextRes = R.string.workout_planner_my_workout_plans_past_section,
                    workoutPlans = workoutPlanList.pastWorkouts,
                    onWorkoutPlanClick = onWorkoutPlanClick,
                    onDeleteWorkoutPlanClick = onDeleteWorkoutPlanClick,
                )
            }
        }

        // Show toast like popup when watch is syncing, busy, not connected or incompatible
        PlannedWorkoutsWatchStateIndicator(watchStateIndicationData)
    }
}

private fun LazyListScope.workoutPlanListSection(
    @StringRes titleTextRes: Int,
    workoutPlans: ImmutableList<SuuntoPlusGuide>,
    onWorkoutPlanClick: (SuuntoPlusGuideId) -> Unit,
    onDeleteWorkoutPlanClick: (SuuntoPlusGuideId) -> Unit,
) {
    if (workoutPlans.isNotEmpty()) {
        item(key = titleTextRes) {
            SuuntoPlusListSectionHeader(stringResource(titleTextRes))
        }

        items(workoutPlans, { item -> item.id.id }) { item ->
            PlannedWorkoutsListItem(
                modifier = Modifier.padding(bottom = MaterialTheme.spacing.small),
                guide = item,
                onClick = { onWorkoutPlanClick(item.id) },
                onDeleteClick = onDeleteWorkoutPlanClick,
            )
        }
    }
}

@Composable
private fun PlannedWorkoutsWatchStateIndicator(
    watchStateIndicationData: WatchStateIndicationData?
) {
    var showWatchStatePopup by remember { mutableStateOf(false) }
    LaunchedEffect(watchStateIndicationData) {
        if (watchStateIndicationData != null) {
            showWatchStatePopup = true
            delay(4000L) // Matches SnackbarDuration.Short
        }
        showWatchStatePopup = false
    }

    Crossfade(showWatchStatePopup) { show ->
        if (show && watchStateIndicationData != null) {
            WatchStateIndication(
                data = watchStateIndicationData,
                modifier = Modifier.padding(MaterialTheme.spacing.large),
            )
        }
    }
}

@Preview
@Composable
private fun PlannedWorkoutsListPreview() {
    val plans = persistentListOf(
        SuuntoPlusGuide(
            id = SuuntoPlusGuideId("3iefLmEf"),
            catalogueId = null,
            modifiedMillis = 1619870400000L,
            name = "A plan for preview",
            owner = "Suunto Workout Planner",
            ownerId = "5c2fa984-4425-4e72-8f7c-deeaa454b9c6",
            date = LocalDate.of(2022, 6, 22),
            url = null,
            iconUrl = null,
            backgroundUrl = "",
            description = "",
            subTitle = "",
            richDescription = null,
            activityIds = listOf(ActivityType.CYCLING.id),
            pinned = false,
        ),
        SuuntoPlusGuide(
            id = SuuntoPlusGuideId("fUheWEF2"),
            catalogueId = null,
            modifiedMillis = 1619870400000L,
            name = "Plan B",
            owner = "Suunto Workout Planner",
            ownerId = "5c2fa984-4425-4e72-8f7c-deeaa454b9c6",
            date = LocalDate.of(2022, 6, 23),
            url = null,
            iconUrl = null,
            backgroundUrl = "",
            description = "",
            subTitle = "",
            richDescription = null,
            activityIds = listOf(ActivityType.TRAIL_RUNNING.id),
            pinned = false,
        )
    )

    AppTheme {
        Surface(color = MaterialTheme.colors.background) {
            PlannedWorkoutsList(
                workoutPlanList = PlannedWorkoutsGrouped(
                    upcomingWorkouts = plans,
                    workoutsWithNoSpecificDate = persistentListOf(),
                    pastWorkouts = persistentListOf()
                ),
                onWorkoutPlanClick = {},
                onNewWorkoutPlanClick = {},
                onGetStartedClick = {},
                onDeleteWorkoutPlanClick = {},
                watchStateIndicationData = null
            )
        }
    }
}

@Preview
@Composable
private fun PlannedWorkoutsListEmptyStatePreview() {
    AppTheme {
        Surface(color = MaterialTheme.colors.background) {
            PlannedWorkoutsList(
                workoutPlanList = PlannedWorkoutsGrouped.EMPTY,
                onWorkoutPlanClick = {},
                onNewWorkoutPlanClick = {},
                onGetStartedClick = {},
                onDeleteWorkoutPlanClick = {},
                watchStateIndicationData = null
            )
        }
    }
}

@Preview
@Composable
private fun PlannedWorkoutsListWatchStateIndicationPreview() {
    // Use interactive mode to see the popup
    AppTheme {
        Surface(color = MaterialTheme.colors.background) {
            PlannedWorkoutsList(
                workoutPlanList = PlannedWorkoutsGrouped.EMPTY,
                onWorkoutPlanClick = {},
                onNewWorkoutPlanClick = {},
                onGetStartedClick = {},
                onDeleteWorkoutPlanClick = {},
                watchStateIndicationData = WatchStateIndicationData(
                    textResource = R.string.workout_planner_watch_is_busy,
                    isError = false,
                    showProgress = true
                )
            )
        }
    }
}
