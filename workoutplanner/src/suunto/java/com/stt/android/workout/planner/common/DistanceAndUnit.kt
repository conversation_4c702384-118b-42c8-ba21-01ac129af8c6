package com.stt.android.workout.planner.common

import androidx.compose.runtime.Immutable
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle

@Immutable
data class DistanceAndUnit(
    val formattedValue: String,
    val unit: String,
) {
    fun asAnnotatedString() = buildAnnotatedString {
        withStyle(SpanStyle(fontWeight = FontWeight.Bold)) {
            append("$formattedValue ")
        }
        append(unit)
    }

    fun asString() = "$formattedValue $unit"

    companion object {
        val EMPTY = DistanceAndUnit("", "")
    }
}
