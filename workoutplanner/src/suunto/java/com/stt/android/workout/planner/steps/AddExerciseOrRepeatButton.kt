package com.stt.android.workout.planner.steps

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.workout.planner.R
import java.util.Locale

@Composable
fun AddExerciseOrRepeatButton(
    onAddExerciseClick: () -> Unit,
    onAddRepeatClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(IntrinsicSize.Min)
            .padding(
                horizontal = MaterialTheme.spacing.medium,
                vertical = MaterialTheme.spacing.large
            ),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        TextButton(onClick = onAddExerciseClick) {
            Icon(
                modifier = Modifier.size(MaterialTheme.spacing.medium),
                painter = painterResource(id = R.drawable.ic_plus_workout_planner),
                contentDescription = null,
            )

            Text(
                modifier = Modifier.padding(start = MaterialTheme.spacing.xsmall),
                text = stringResource(R.string.workout_planner_step_list_add_step_button)
                    .uppercase(Locale.getDefault()),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }

        Divider(
            modifier = Modifier
                .padding(
                    horizontal = MaterialTheme.spacing.xsmall,
                    vertical = MaterialTheme.spacing.small
                )
                .width(1.dp)
                .fillMaxHeight()
        )

        TextButton(onClick = onAddRepeatClick) {
            Icon(
                modifier = Modifier.size(MaterialTheme.spacing.medium),
                painter = painterResource(id = com.stt.android.suuntoplus.ui.R.drawable.repeat_outline),
                contentDescription = null,
                tint = MaterialTheme.colors.primary
            )

            Text(
                modifier = Modifier.padding(start = MaterialTheme.spacing.xsmall),
                text = stringResource(R.string.workout_planner_step_list_add_repeat_block_button)
                    .uppercase(Locale.getDefault()),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }
    }
}

@Preview
@Composable
private fun AddExerciseOrRepeatButtonPreview() {
    AppTheme {
        Surface(color = MaterialTheme.colors.background) {
            AddExerciseOrRepeatButton(
                onAddExerciseClick = {},
                onAddRepeatClick = {}
            )
        }
    }
}
