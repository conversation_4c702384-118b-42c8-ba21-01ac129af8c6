package com.stt.android.workout.planner.summary

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material3.BasicAlertDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.component.SuuntoActivityIcon
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.workout.ActivityType
import com.stt.android.workout.planner.R
import com.stt.android.workout.planner.plans.localizedStringIdForGuide

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ActivityTypePicker(
    onDismissRequest: () -> Unit,
    activityTypes: List<ActivityType>,
    selected: ActivityType?,
    onSelectedChange: (ActivityType) -> Unit,
    modifier: Modifier = Modifier,
) {
    BasicAlertDialog(
        modifier = modifier
            .clip(RoundedCornerShape(MaterialTheme.spacing.small))
            .background(MaterialTheme.colors.surface),
        onDismissRequest = onDismissRequest,
    ) {
        val state = rememberLazyListState(
            initialFirstVisibleItemIndex = activityTypes.indexOf(selected).takeIf { it != -1 } ?: 0,
        )
        Column {
            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        start = MaterialTheme.spacing.medium + MaterialTheme.spacing.smaller,
                        end = MaterialTheme.spacing.medium + MaterialTheme.spacing.smaller,
                        top = MaterialTheme.spacing.large,
                        bottom = MaterialTheme.spacing.medium,
                    ),
                text = stringResource(R.string.workout_planner_select_activity_type),
                color = MaterialTheme.colors.onSurface,
                style = MaterialTheme.typography.h2,
            )
            ActivityTypeList(
                state = state,
                activityTypes = activityTypes,
                selected = selected,
                onSelectedChange = onSelectedChange,
            )
        }
    }
}

@Composable
private fun ActivityTypeList(
    state: LazyListState,
    activityTypes: List<ActivityType>,
    selected: ActivityType?,
    onSelectedChange: (ActivityType) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        state = state,
        contentPadding = PaddingValues(bottom = MaterialTheme.spacing.medium),
    ) {
        items(activityTypes, key = { it.id }) { activityType ->
            ActivityTypeItem(
                activityType = activityType,
                isSelected = activityType == selected,
                onSelectedChange = onSelectedChange,
            )
        }
    }
}

@Composable
private fun ActivityTypeItem(
    activityType: ActivityType,
    isSelected: Boolean,
    onSelectedChange: (ActivityType) -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .clickableThrottleFirst { onSelectedChange(activityType) }
            .fillMaxWidth()
            .height(48.dp)
            .padding(horizontal = MaterialTheme.spacing.medium),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        val color = if (isSelected) MaterialTheme.colors.primary else MaterialTheme.colors.onSurface
        SuuntoActivityIcon(
            iconRes = activityType.iconId,
            tint = color,
            background = Color.Transparent,
        )

        Text(
            modifier = Modifier.weight(1f),
            text = stringResource(activityType.localizedStringIdForGuide()),
            color = color,
            style = if (isSelected) {
                MaterialTheme.typography.bodyBold
            } else {
                MaterialTheme.typography.body
            },
        )
    }
}

@Preview
@Composable
private fun ActivityTypeItemPreview() {
    AppTheme {
        Surface(color = MaterialTheme.colors.background) {
            ActivityTypeItem(
                activityType = ActivityType.RUNNING,
                isSelected = false,
                onSelectedChange = {},
            )
        }
    }
}
