package com.stt.android.workout.planner.summary

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsFocusedAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.soy.algorithms.planner.GuideLimits
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.ConfirmationDialog
import com.stt.android.domain.workout.ActivityType
import com.stt.android.workout.planner.R
import com.stt.android.workout.planner.common.BreadcrumbStepIndicator
import com.stt.android.workout.planner.common.BreadcrumbSteps
import com.stt.android.workout.planner.common.DistanceAndUnit
import com.stt.android.workout.planner.plans.localizedStringIdForGuide
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import java.util.Locale
import kotlin.math.max
import com.stt.android.R as BaseR

@Composable
fun SummaryView(
    isSaving: Boolean,
    name: String,
    onNameChange: (String) -> Unit,
    description: String,
    onDescriptionChange: (String) -> Unit,
    activityType: ActivityType,
    onSelectActivityType: () -> Unit,
    date: String?,
    onSelectDate: () -> Unit,
    totalDuration: String,
    totalDistance: DistanceAndUnit,
    showDeleteButton: Boolean,
    onDeleteConfirm: () -> Unit,
    modifier: Modifier = Modifier,
) {
    var showDeleteConfirmationDialog by rememberSaveable { mutableStateOf(false) }

    ContentCenteringColumn(modifier = modifier) {
        LazyColumn(contentPadding = PaddingValues(vertical = MaterialTheme.spacing.small)) {
            item {
                BreadcrumbStepIndicator(
                    modifier = Modifier.padding(MaterialTheme.spacing.medium),
                    steps = BreadcrumbSteps.steps,
                    currentStepIndex = 1,
                )

                Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
            }
            item {
                Text(
                    modifier = Modifier
                        .background(color = MaterialTheme.colors.surface)
                        .fillMaxWidth()
                        .padding(MaterialTheme.spacing.medium),
                    text = stringResource(R.string.workout_planner_summary_plan_as_guide_explanation),
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colors.onSurface,
                    textAlign = TextAlign.Center,
                )
                Divider(color = MaterialTheme.colors.dividerColor)
            }
            item {
                Row(modifier = Modifier.fillMaxWidth()) {
                    var minHeight by remember { mutableIntStateOf(0) }

                    SummaryViewDataColumn(
                        modifier = Modifier
                            .background(color = MaterialTheme.colors.surface)
                            .onSizeChanged { minHeight = max(minHeight, it.height) }
                            .defaultMinSize(minHeight = with(LocalDensity.current) { minHeight.toDp() })
                            .weight(1f),
                        title = stringResource(R.string.workout_planner_summary_total_duration),
                        value = totalDuration,
                    )

                    VerticalDivider(color = MaterialTheme.colors.dividerColor)

                    SummaryViewDataColumn(
                        modifier = Modifier
                            .background(color = MaterialTheme.colors.surface)
                            .onSizeChanged { minHeight = max(minHeight, it.height) }
                            .defaultMinSize(minHeight = with(LocalDensity.current) { minHeight.toDp() })
                            .weight(1f),
                        title = stringResource(R.string.workout_planner_summary_total_distance),
                        value = totalDistance.asString(),
                    )
                }
                Divider(color = MaterialTheme.colors.dividerColor)
            }
            item {
                Text(
                    modifier = Modifier
                        .background(color = MaterialTheme.colors.surface)
                        .fillMaxWidth()
                        .padding(
                            start = MaterialTheme.spacing.medium,
                            end = MaterialTheme.spacing.medium,
                            bottom = MaterialTheme.spacing.medium,
                            top = MaterialTheme.spacing.large,
                        ),
                    text = stringResource(R.string.workout_planner_summary_edit_workout_details),
                    style = MaterialTheme.typography.bodyXLargeBold,
                    color = MaterialTheme.colors.onSurface,
                )
            }
            item {
                FormInputField(
                    label = stringResource(R.string.workout_planner_name_your_workout_section),
                    hint = stringResource(R.string.workout_planner_name_text_hint),
                    value = name,
                    onValueChange = onNameChange,
                    enabled = !isSaving,
                )
                Divider(color = MaterialTheme.colors.dividerColor)
            }
            item {
                FormInputField(
                    label = stringResource(R.string.workout_planner_describe_your_workout_section),
                    hint = stringResource(R.string.workout_planner_description_text_hint),
                    value = description,
                    onValueChange = onDescriptionChange,
                    enabled = !isSaving,
                    imeAction = ImeAction.Done,
                    showSupportingText = true,
                    maxCharacters = GuideLimits.shortDescription,
                )
                Divider(color = MaterialTheme.colors.dividerColor)
            }
            item {
                SummaryListItem(
                    title = stringResource(R.string.workout_planner_summary_select_activity_type),
                    value = stringResource(activityType.localizedStringIdForGuide()),
                    onClick = onSelectActivityType,
                    enabled = !isSaving,
                )
                Divider(color = MaterialTheme.colors.dividerColor)
            }
            item {
                SummaryListItem(
                    title = stringResource(R.string.workout_planner_summary_select_date),
                    value = date
                        ?: stringResource(R.string.workout_planner_activity_type_selection_any),
                    onClick = onSelectDate,
                    enabled = !isSaving,
                )
                Divider(color = MaterialTheme.colors.dividerColor)
            }
            if (showDeleteButton) {
                item {
                    TextButton(
                        modifier = Modifier
                            .background(color = MaterialTheme.colors.surface)
                            .fillMaxWidth(),
                        contentPadding = PaddingValues(
                            horizontal = MaterialTheme.spacing.medium,
                            vertical = MaterialTheme.spacing.smaller + MaterialTheme.spacing.medium,
                        ),
                        onClick = { showDeleteConfirmationDialog = true },
                        enabled = !isSaving,
                    ) {
                        Text(
                            text = stringResource(R.string.workout_planner_summary_delete_button)
                                .uppercase(Locale.getDefault()),
                            color = MaterialTheme.colors.error,
                            style = MaterialTheme.typography.bodyBold,
                        )
                    }
                }
            }
        }
    }

    if (showDeleteConfirmationDialog) {
        ConfirmationDialog(
            text = stringResource(R.string.workout_planner_summary_delete_confirmation),
            confirmButtonText = stringResource(BaseR.string.delete),
            cancelButtonText = stringResource(BaseR.string.cancel),
            onDismissRequest = { showDeleteConfirmationDialog = false },
            onConfirm = onDeleteConfirm,
            useDestructiveColorForConfirm = true
        )
    }
}

@Composable
private fun SummaryWatchItem(device: SuuntoDeviceType) {
    Row(
        modifier = Modifier
            .background(color = MaterialTheme.colors.surface)
            .fillMaxWidth()
            .padding(MaterialTheme.spacing.medium),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Icon(
            painter = painterResource(R.drawable.ic_workout_planner_connected_watch),
            tint = MaterialTheme.colors.onSurface,
            contentDescription = null,
        )
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = stringResource(R.string.workout_planner_summary_connected_watch),
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colors.onSurface,
            )
            Text(
                text = device.displayName,
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colors.darkGrey,
            )
        }
    }
}

@Composable
private fun SummaryViewDataColumn(
    title: String,
    value: String,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.padding(
            horizontal = MaterialTheme.spacing.medium,
            vertical = MaterialTheme.spacing.smaller,
        ),
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.caption,
            color = MaterialTheme.colors.onSurface,
        )

        Spacer(modifier = Modifier.height(MaterialTheme.spacing.xxsmall))

        Spacer(modifier = Modifier.weight(1f))

        Text(
            text = value,
            style = MaterialTheme.typography.bodyLargeBold,
            color = MaterialTheme.colors.onSurface,
        )
    }
}

@Composable
private fun FormInputField(
    label: String,
    hint: String,
    value: String,
    onValueChange: (String) -> Unit,
    enabled: Boolean,
    modifier: Modifier = Modifier,
    imeAction: ImeAction = ImeAction.Next,
    showSupportingText: Boolean = false,
    maxCharacters: Int = Int.MAX_VALUE,
) {
    val interactionSource = remember { MutableInteractionSource() }
    BasicTextField(
        modifier = modifier.fillMaxWidth(),
        enabled = enabled,
        value = value,
        onValueChange = onValueChange,
        textStyle = MaterialTheme.typography.bodyLarge.copy(color = MaterialTheme.colors.primary),
        maxLines = 1,
        interactionSource = interactionSource,
        cursorBrush = SolidColor(MaterialTheme.colors.primary),
        keyboardOptions = KeyboardOptions(imeAction = imeAction),
        decorationBox = { innerTextField ->
            Column(
                modifier = Modifier
                    .background(MaterialTheme.colors.surface)
                    .fillMaxWidth()
                    .padding(vertical = MaterialTheme.spacing.medium),
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = MaterialTheme.spacing.medium),
                    verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xxsmall),
                ) {
                    Text(
                        modifier = Modifier.fillMaxWidth(),
                        text = label,
                        color = MaterialTheme.colors.onSurface,
                        style = MaterialTheme.typography.caption,
                    )
                    val isFocused by interactionSource.collectIsFocusedAsState()
                    if (isFocused || value.isNotEmpty()) {
                        innerTextField()
                    } else {
                        Text(
                            modifier = Modifier.fillMaxWidth(),
                            text = hint,
                            color = MaterialTheme.colors.darkGrey,
                            style = MaterialTheme.typography.bodyLarge,
                        )
                    }
                }
                if (showSupportingText) {
                    Divider(
                        modifier = Modifier
                            .padding(
                                top = MaterialTheme.spacing.medium,
                                bottom = MaterialTheme.spacing.small,
                            ),
                        color = MaterialTheme.colors.dividerColor,
                    )
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = MaterialTheme.spacing.medium),
                        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Text(
                            modifier = Modifier.weight(1f),
                            text = stringResource(
                                R.string.workout_planner_text_max_characters,
                                maxCharacters,
                            ),
                            color = MaterialTheme.colors.darkGrey,
                            style = MaterialTheme.typography.caption,
                        )
                        Text(
                            text = "${value.length}/$maxCharacters",
                            color = MaterialTheme.colors.darkGrey,
                            style = MaterialTheme.typography.caption,
                        )
                    }
                }
            }
        }
    )
}

@Composable
private fun SummaryListItem(
    title: String,
    value: String,
    onClick: () -> Unit,
    enabled: Boolean,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .clickable(enabled = enabled, onClick = onClick)
            .background(MaterialTheme.colors.surface)
            .fillMaxWidth()
            .padding(MaterialTheme.spacing.medium),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            modifier = Modifier.weight(1f),
            text = title,
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colors.onSurface,
        )

        Text(
            text = value,
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colors.primary,
        )
    }
}

@Composable
@Preview
private fun SummaryViewPreview() = AppTheme {
    Surface(color = MaterialTheme.colors.background) {
        SummaryView(
            isSaving = false,
            name = "Interval run",
            onNameChange = {},
            description = "Preparing for half marathon",
            onDescriptionChange = {},
            activityType = ActivityType.RUNNING,
            onSelectActivityType = {},
            date = "28.4.2022",
            onSelectDate = {},
            totalDuration = "33'00",
            totalDistance = DistanceAndUnit(
                formattedValue = "3.00",
                unit = "km"
            ),
            showDeleteButton = true,
            onDeleteConfirm = {},
        )
    }
}

@Composable
@Preview
private fun SummaryViewPreviewWithNoDate() = AppTheme {
    Surface(color = MaterialTheme.colors.background) {
        SummaryView(
            isSaving = true,
            name = "Interval run",
            onNameChange = {},
            description = "Preparing for half marathon",
            onDescriptionChange = {},
            activityType = ActivityType.RUNNING,
            onSelectActivityType = {},
            date = null,
            onSelectDate = {},
            totalDuration = "33'00",
            totalDistance = DistanceAndUnit(
                formattedValue = "3.00",
                unit = "km"
            ),
            showDeleteButton = true,
            onDeleteConfirm = {},
        )
    }
}
