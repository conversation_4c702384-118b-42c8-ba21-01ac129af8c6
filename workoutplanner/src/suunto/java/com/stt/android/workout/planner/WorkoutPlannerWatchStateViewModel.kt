package com.stt.android.workout.planner

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.device.domain.GetWatchCapabilitiesUseCase
import com.stt.android.device.domain.suuntoplusguide.IsSuuntoPlusGuideSyncOngoingUseCase
import com.stt.android.device.watch.SuuntoPlusWatchStateListener
import com.stt.android.device.watch.SuuntoPlusWatchStateListenerImpl
import com.stt.android.device.watch.WatchBusyState
import com.stt.android.domain.watch.IsWatchBusyUseCase
import com.stt.android.domain.watch.IsWatchConnectedUseCase
import com.stt.android.workout.planner.common.WatchStateIndicationData
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class WorkoutPlannerWatchStateViewModel @Inject constructor(
    isSyncOngoingUseCase: IsSuuntoPlusGuideSyncOngoingUseCase,
    isWatchBusyUseCase: IsWatchBusyUseCase,
    private val isWatchConnectedUseCase: IsWatchConnectedUseCase,
    private val currentWatchCapabilitiesUseCase: GetWatchCapabilitiesUseCase,
) : ViewModel(),
    SuuntoPlusWatchStateListener by SuuntoPlusWatchStateListenerImpl(
        isSyncOngoingUseCase,
        isWatchBusyUseCase
    ) {
    private enum class WatchConnectionState {
        GOOD,
        NO_WATCH_PAIRED,
        NOT_CONNECTED,
        NOT_SUUNTO_PLUS_COMPATIBLE
    }

    private val watchConnectionState = MutableStateFlow(WatchConnectionState.GOOD)

    val watchStateIndicationData: Flow<WatchStateIndicationData?> =
        combine(
            watchConnectionState,
            watchBusyState
        ) { connectionState, busyState ->
            when {
                connectionState == WatchConnectionState.NO_WATCH_PAIRED ||
                    connectionState == WatchConnectionState.NOT_CONNECTED ->
                    WatchStateIndicationData(
                        textResource = R.string.workout_planner_connect_watch_to_sync,
                        isError = false,
                        showProgress = false
                    )

                connectionState == WatchConnectionState.NOT_SUUNTO_PLUS_COMPATIBLE ->
                    WatchStateIndicationData(
                        textResource = R.string.workout_planner_incompatible_watch,
                        isError = true,
                        showProgress = false
                    )

                busyState == WatchBusyState.SYNC_ONGOING ->
                    WatchStateIndicationData(
                        textResource = R.string.workout_planner_watch_is_syncing,
                        isError = false,
                        showProgress = true
                    )

                busyState == WatchBusyState.BUSY ->
                    WatchStateIndicationData(
                        textResource = R.string.workout_planner_watch_is_busy,
                        isError = false,
                        showProgress = true
                    )

                else -> null
            }
        }

    fun refreshWatchConnectionAndGuideSupportStatus() {
        // Emit GOOD to make sure a new value is emitted and the error popup is triggered again
        watchConnectionState.value = WatchConnectionState.GOOD

        viewModelScope.launch {
            watchConnectionState.value = runSuspendCatching {
                val (serial, capabilities) = currentWatchCapabilitiesUseCase.getCurrentCapabilities()
                when {
                    serial == null ->
                        WatchConnectionState.NO_WATCH_PAIRED

                    !isWatchConnectedUseCase.invoke().first() ->
                        WatchConnectionState.NOT_CONNECTED

                    capabilities?.areSuuntoPlusGuidesSupported != true ->
                        WatchConnectionState.NOT_SUUNTO_PLUS_COMPATIBLE

                    else -> null
                }
            }.onFailure { e ->
                Timber.w(e, "Failed to refresh watch connection status")
            }.getOrNull() ?: WatchConnectionState.GOOD
        }
    }
}
