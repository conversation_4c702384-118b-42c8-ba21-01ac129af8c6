package com.stt.android.workout.domain

import com.stt.android.device.datasource.suuntoplusguide.SuuntoPlusGuidesLocalDataSource
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuide
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.time.LocalDate
import javax.inject.Inject

data class PlannedWorkoutsGrouped(
    val upcomingWorkouts: ImmutableList<SuuntoPlusGuide>,
    val workoutsWithNoSpecificDate: ImmutableList<SuuntoPlusGuide>,
    val pastWorkouts: ImmutableList<SuuntoPlusGuide>,
) {
    fun isEmpty() =
        upcomingWorkouts.isEmpty() && workoutsWithNoSpecificDate.isEmpty() && pastWorkouts.isEmpty()

    companion object {
        val EMPTY = PlannedWorkoutsGrouped(persistentListOf(), persistentListOf(), persistentListOf())
    }
}

class ListPlannedWorkoutsAsGuidesUseCase
@Inject constructor(
    private val suuntoPlusGuidesLocalDataSource: SuuntoPlusGuidesLocalDataSource
) {
    fun listPlannedWorkoutsAsGuides(): Flow<List<SuuntoPlusGuide>> =
        suuntoPlusGuidesLocalDataSource.listAllPlannedWorkoutsAsGuides()

    fun listPlannedWorkoutsGrouped(
        today: LocalDate
    ): Flow<PlannedWorkoutsGrouped> = listPlannedWorkoutsAsGuides()
        .map { plans ->
            val plansInNameOrder = plans.sortedBy { it.name }

            PlannedWorkoutsGrouped(
                upcomingWorkouts = plansInNameOrder
                    .filter { it.date != null && it.date!! >= today }
                    .sortedBy { it.date!! } // sortedBy() is stable so name order is kept for identical dates
                    .toImmutableList(),
                workoutsWithNoSpecificDate = plansInNameOrder
                    .filter { it.date == null }
                    .toImmutableList(),
                pastWorkouts = plansInNameOrder
                    .filter { it.date != null && it.date!! < today }
                    .sortedByDescending { it.date!! }
                    .toImmutableList(),
            )
        }
}
