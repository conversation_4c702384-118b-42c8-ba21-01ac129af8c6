<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="workout_planner_new_plan_title">インターバルの作成</string>
    <string name="workout_planner_edit_plan_title">インターバルの編集</string>
    <string name="workout_planner_workout_plan">ワークアウトプラン</string>

    <!-- Workout planner: breadcrumb step indicator -->
    <string name="workout_planner_step_1_intervals">インターバル</string>
    <string name="workout_planner_step_2_info">情報</string>

    <!-- Workout planner: basic details -->
    <string name="workout_planner_name_your_workout_section">ワークアウトの名前を設定してください</string>
    <string name="workout_planner_name_text_hint">ワークアウト名</string>
    <string name="workout_planner_describe_your_workout_section">ワークアウトの説明</string>
    <string name="workout_planner_description_text_hint">ワークアウトの説明</string>
    <string name="workout_planner_text_max_characters">最大 %d 文字</string>
    <string name="workout_planner_select_activity_type">アクティビティタイプを選択</string>
    <string name="workout_planner_date_picker_section">トレーニングを希望する時間</string>
    <string name="workout_planner_skip_date_selection">特定の日を指定しない</string>
    <string name="workout_planner_discard_changes_confirmation">変更を破棄してもよろしいですか？</string>
    <string name="workout_planner_failed_to_open_workout_plan_for_editing">ワークアウトの編集が開けませんでした</string>

    <!-- Workout planner: plans list -->
    <string name="workout_planner_my_workout_plans_title">インターバルワークアウト</string>
    <string name="workout_planner_my_workout_plans_upcoming_section">今後予定されているワークアウト</string>
    <string name="workout_planner_my_workout_plans_no_date_section">日時が指定されていません</string>
    <string name="workout_planner_my_workout_plans_past_section">過去のワークアウト</string>
    <string name="workout_planner_my_workout_plans_create_new_button">新規作成</string>
    <string name="workout_planner_empty_state_learn_how_text">新しい体系的なワークアウトの作成方法について学ぶ</string>
    <string name="workout_planner_empty_state_text">ワークアウトが作成されていません。</string>
    <string name="workout_planner_empty_state_how_to_get_started">使用開始方法</string>
    <string name="workout_planner_watch_is_busy">ウォッチが応答しません。メイン画面表示時に同期できます。</string>
    <string name="workout_planner_watch_is_syncing">ウォッチの同期</string>
    <string name="workout_planner_connect_watch_to_sync">ウォッチを接続してワークアウトを同期する</string>
    <string name="workout_planner_incompatible_watch">ウォッチはSuuntoPlus™ガイドに対応していないためワークアウトを同期できません</string>

    <!-- Workout planner: bottom buttons -->

    <string name="workout_planner_bottom_button_next">次へ</string>

    <!-- Workout planner: steps list -->
    <string name="workout_planner_step_list_add_step_button">ステップを追加</string>
    <string name="workout_planner_step_list_add_repeat_block_button">繰り返しを追加</string>
    <string name="workout_planner_step_list_duplicate_action">重複</string>
    <plurals name="workout_planner_step_list_repeat_count">
        <item quantity="other">%1$d繰り返す</item>
    </plurals>

    <!-- Workout planner: step details -->
    <string name="workout_planner_edit_step_title">ステップを編集</string>
    <string name="workout_planner_edit_step_phase_title">フェーズを編集</string>
    <string name="workout_planner_edit_step_duration_title">継続時間を編集</string>
    <string name="workout_planner_step_duration_time_unit">時間:分\'秒</string>
    <string name="workout_planner_step_duration_lap_button_press_description">ウォッチのラップボタンを押すと、手動でエクササイズを終了できます。</string>
    <string name="workout_planner_edit_step_target_title">ターゲットを編集</string>
    <string name="workout_planner_edit_step_target_none">ターゲットなし</string>
    <string name="workout_planner_step_details_phase">フェーズ</string>
    <string name="workout_planner_step_details_name">名前</string>
    <string name="workout_planner_step_details_duration">継続時間</string>
    <string name="workout_planner_step_details_target">ターゲット</string>
    <string name="workout_planner_step_details_delete_step">ステップを削除</string>
    <string name="workout_planner_step_details_delete_confirmation">このステップを本当に削除しますか？</string>
    <string name="workout_planner_step_target_range_minimum">最小</string>
    <string name="workout_planner_step_target_range_maximum">最大</string>
    <string name="workout_planner_step_calculated_target_value_with_unit">ターゲット: %1$s %2$s</string>

    <!-- Workout planner: total duration and distance row -->
    <string name="workout_planner_totals_row_text">合計</string>
    <string name="workout_planner_totals_row_distance">距離</string>
    <string name="workout_planner_totals_row_duration">継続時間</string>

    <!-- Workout planner: summary -->
    <string name="workout_planner_summary_date">日付</string>
    <string name="workout_planner_summary_total_duration">合計継続時間</string>
    <string name="workout_planner_summary_total_distance">合計距離</string>
    <string name="workout_planner_summary_plan_as_guide_explanation">同期されたワークアウトプランは、ウォッチのSuuntoPlus™ガイドとして有効化できます。</string>
    <string name="workout_planner_summary_edit_workout_details">ワークアウト詳細の編集</string>
    <string name="workout_planner_summary_select_activity_type">アクティビティタイプを選択</string>
    <string name="workout_planner_summary_select_date">日付を選択</string>
    <string name="workout_planner_summary_delete_button">ワークアウトを削除</string>
    <string name="workout_planner_summary_delete_confirmation">このワークアウトを本当に削除しますか？</string>
    <string name="workout_planner_summary_connected_watch">接続されたウォッチ</string>

    <!-- Workout planner: How to get started -->
    <string name="workout_planner_get_started_1_build_workouts_title">1.Suuntoアプリでワークアウトを構築する</string>
    <string name="workout_planner_get_started_1_build_workouts_text">独自のインターバルワークアウトをSuuntoアプリで直接作成できます。トレーニング目標に合わせた期間と運動強度のターゲットを設定します。</string>
    <string name="workout_planner_get_started_2_sync_to_watch_title">2.SuuntoPlus™ガイドとしてウォッチを同期する</string>
    <string name="workout_planner_get_started_2_sync_to_watch_text">ワークアウトグが作成されると、SuuntoPlus™ガイドとして接続されたウォッチに同期されます。</string>
    <string name="workout_planner_get_started_3_real_time_guidance_title">3.ワークアウトのリアルタイムガイダンスを受ける</string>
    <string name="workout_planner_get_started_3_real_time_guidance_text">ウォッチでエクササイズを開始する前に、SuuntoPlus™ガイドとしてワークアウトプランを選択します。</string>

    <string name="workout_planner_activity_type_selection_any">任意</string>
</resources>
