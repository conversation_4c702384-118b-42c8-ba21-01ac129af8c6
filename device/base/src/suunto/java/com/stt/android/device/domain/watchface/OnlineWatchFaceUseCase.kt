package com.stt.android.device.domain.watchface

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.device.datasource.watchface.WatchFaceDeviceApi
import com.stt.android.device.datasource.watchface.WatchFaceFileStorage
import com.stt.android.device.remote.watchface.WatchFaceRemoteDataSource
import com.stt.android.domain.device.DeviceConnectionStateUseCase
import com.stt.android.utils.takeIfNotEmpty
import com.suunto.connectivity.watchface.MdsWatchFace
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.reactive.awaitFirstOrNull
import javax.inject.Inject

class OnlineWatchFaceUseCase @Inject constructor(
    private val watchFaceDeviceApi: WatchFaceDeviceApi,
    private val watchFaceRemoteDataSource: WatchFaceRemoteDataSource,
    private val deviceConnectionStateUseCase: DeviceConnectionStateUseCase,
    private val watchFaceFileStorage: WatchFaceFileStorage,
) {
    private val _watchFaceState: MutableStateFlow<WatchFaceState> =
        MutableStateFlow(WatchFaceState.None)
    val watchFaceState: StateFlow<WatchFaceState>
        get() = _watchFaceState.asStateFlow()

    suspend fun fetAllOnlineWatchFaces(): WatchFaceListContainer {
        val variantName = runSuspendCatching {
            deviceConnectionStateUseCase.connectedWatchState()
                .awaitFirstOrNull()?.deviceInfo?.variantName
        }.getOrNull().orEmpty()
        val watchCapabilities =
            getWatchFaceCapabilities().takeIfNotEmpty()?.joinToString(",") ?: ""
        if (watchCapabilities.isBlank()) throw IllegalArgumentException("Watch capabilities are empty")
        return watchFaceRemoteDataSource.fetchAll(variantName, watchCapabilities)
    }

    suspend fun getWatchFaceInfo(watchFaceId: String): MdsWatchFace? {
        return watchFaceDeviceApi.getWatchFaceInfo(watchFaceId)
    }

    suspend fun getWatchFaceCapabilities(): List<String> {
        return runSuspendCatching {
            watchFaceDeviceApi.getWatchFaceCapabilities()
        }.getOrNull().orEmpty()
    }

    suspend fun getWatchFaceDetails(runFeatureCatalogueId: String): WatchFaceEntity {
        val watchFaceCapabilities =
            getWatchFaceCapabilities().takeIfNotEmpty()?.joinToString(",") ?: ""
        return watchFaceRemoteDataSource.getWatchFaceDetail(
            runFeatureCatalogueId,
            watchFaceCapabilities
        )
    }

    suspend fun getInstalledWatchFaceList(): List<MdsWatchFace> =
        runSuspendCatching {
            watchFaceDeviceApi.getInstalledWatchFaceList()
        }.getOrNull().orEmpty()

    private suspend fun fetchOnlineWatchFaceFile(
        runFeatureCatalogueId: String,
        targetInstallCapability: String
    ): ByteArray {
        _watchFaceState.value = WatchFaceState.Downloading
        return watchFaceRemoteDataSource.fetchWatchFaceFile(
            runFeatureCatalogueId,
            targetInstallCapability,
        )
    }

    suspend fun startInstallOnlineWatchFace(watchFace: WatchFaceEntity): Boolean {
        val runFeatureCatalogueId = watchFace.runFeatureCatalogueId
        val targetInstallCapability = watchFace.targetInstallCapability
        val targetInstallVersion = watchFace.targetInstallVersion
        val data = fetchOnlineWatchFaceFile(runFeatureCatalogueId, targetInstallCapability)
        watchFaceFileStorage.store(watchFace.watchfaceId, targetInstallCapability, targetInstallVersion, data)
        val fileLocalPath =
            watchFaceFileStorage.getAbsolutePath(watchFace.watchfaceId, targetInstallCapability, targetInstallVersion)
        _watchFaceState.value = WatchFaceState.Syncing
        val installWatchFace = watchFace.toInstallWatchFace()
        val result = watchFaceDeviceApi.startWatchFaceInstall(fileLocalPath, installWatchFace)
        _watchFaceState.value = if (result) WatchFaceState.Succeed else WatchFaceState.Failed
        return result
    }

    suspend fun setWatchFaceAsCurrent(watchFaceId: String) {
        watchFaceDeviceApi.setAsCurrentWatchFace(watchFaceId)
    }

    suspend fun uninstallWatchFace(watchFaceId: String) {
        watchFaceDeviceApi.uninstallWatchFace(watchFaceId)
    }

    suspend fun addToLibrary(id: String): Boolean {
        val watchCapabilities =
            getWatchFaceCapabilities().takeIfNotEmpty()?.joinToString(",") ?: ""
        if (watchCapabilities.isBlank()) throw IllegalArgumentException("Watch capabilities are empty")
        return watchFaceRemoteDataSource.addToLibrary(
            id,
            watchCapabilities,
            addToFavorite = true,
            addToWatch = false
        )
    }

    suspend fun removeFromLibrary(runFeatureCatalogueId: String): Boolean {
        val watchFaceCapabilities =
            getWatchFaceCapabilities().takeIfNotEmpty()?.joinToString(",") ?: ""
        if (watchFaceCapabilities.isBlank()) throw IllegalArgumentException("Watch capabilities are empty")
        return watchFaceRemoteDataSource.addToLibrary(
            runFeatureCatalogueId,
            watchFaceCapabilities,
            addToFavorite = true,
            addToWatch = false
        )
    }
}
