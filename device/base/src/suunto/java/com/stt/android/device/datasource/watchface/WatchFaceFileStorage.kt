package com.stt.android.device.datasource.watchface

import androidx.annotation.WorkerThread

interface WatchFaceFileStorage {

    @WorkerThread
    fun existsInCache(
        watchFaceId: String,
        installCapability: String,
        installVersion: String
    ): Boolean

    @WorkerThread
    fun store(
        watchFaceId: String,
        installCapability: String,
        installVersion: String,
        data: ByteArray
    )

    fun getAbsolutePath(
        watchFaceId: String,
        installCapability: String,
        installVersion: String
    ): String
}
