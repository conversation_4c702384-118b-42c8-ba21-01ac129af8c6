package com.stt.android.device.watch

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.data.source.local.watchface.WatchFaceSyncLogUtil
import com.stt.android.data.source.local.watchface.WatchFaceSyncLogUtilImpl
import com.stt.android.device.datasource.watchface.WatchFaceFileStorage
import com.stt.android.device.datasource.watchface.WatchFaceLocalDataSource
import com.stt.android.device.datasource.watchface.WatchFaceStatusDataSource
import com.stt.android.device.domain.watchface.WatchFace
import com.stt.android.device.domain.watchface.WatchFaceStatus
import com.stt.android.device.watchface.datasource.WatchFaceDataSource
import com.suunto.connectivity.suuntoplusguide.WatchBusyStateProvider
import com.suunto.connectivity.sync.WatchBusyException
import com.suunto.connectivity.watchface.MdsWatchFace
import com.suunto.connectivity.watchface.WatchFaceSyncLogicResult
import com.suunto.connectivity.watchface.WatchFaceSyncTrigger
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject

class WatchFaceSyncLogic @Inject constructor(
    private val localDataSource: WatchFaceLocalDataSource,
    private val statusDataSource: WatchFaceStatusDataSource,
    private val watchFaceDataSource: WatchFaceDataSource,
    private val watchFaceFileStorage: WatchFaceFileStorage,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : WatchFaceSyncTrigger, WatchFaceSyncLogUtil by WatchFaceSyncLogUtilImpl() {

    inner class InternalSyncState(
        val serial: String,
        val capabilities: List<String>,
        val errorsLogs: MutableList<String> = mutableListOf(),
        val watchWasUpdated: AtomicBoolean = AtomicBoolean(false),
        val triggerBackendSync: AtomicBoolean = AtomicBoolean(false),
        var currentWatchFacesInWatch: List<MdsWatchFace> = emptyList(),
        var localWatchFacesById: Map<String, WatchFace> = emptyMap(),
    )

    override suspend fun syncWatchFaces(
        serial: String,
        capabilities: List<String>,
        watchBusyStateProvider: WatchBusyStateProvider
    ): WatchFaceSyncLogicResult {
        return syncWatchFacesInternal(serial, capabilities, watchBusyStateProvider)
    }

    private suspend fun syncWatchFacesInternal(
        serial: String,
        capabilities: List<String>,
        watchBusyStateProvider: WatchBusyStateProvider
    ): WatchFaceSyncLogicResult = with(InternalSyncState(serial, capabilities)) {
        Timber.d("syncWatchFacesInternal ====")
        // Loads current watch faces from local DB & watch
        loadCurrentWatchFaces()

        // Delete removed watch faces from latest backend sync
        deleteWatchFacesFromWatch()

        installWatchFacesToWatch(watchBusyStateProvider)

        return processSyncErrorsAndStopSync(
            hasNewData = watchWasUpdated.get(),
            errorsLogs = errorsLogs,
            isWatchSync = true,
            triggerBackendSync = triggerBackendSync.get(),
            triggerWatchSync = false,
        )
    }

    private suspend fun InternalSyncState.loadCurrentWatchFaces() =
        withContext(coroutinesDispatchers.io) {
            localWatchFacesById = async { listLocalWatchFaces() }.await()
            currentWatchFacesInWatch = watchFaceDataSource.getInstalledWatchFaces()
        }

    private suspend fun InternalSyncState.listLocalWatchFaces() =
        localDataSource.listWatchFacesInGivenStates(
            watchSerial = serial,
            states = setOf(
                WatchFaceStatus.IN_WATCH,
                WatchFaceStatus.DOWNLOADED,
                WatchFaceStatus.WATCH_FULL,
                WatchFaceStatus.INSTALLING
            )
        ).associateBy { it.watchfaceId }

    private suspend fun InternalSyncState.installWatchFacesToWatch(
        watchBusyStateProvider: WatchBusyStateProvider,
    ) {
        Timber.d("installWatchFacesToWatch ====")
        // Install new watch face
        for (watchFace in localWatchFacesById.values) {
            watchBusyStateProvider.throwIfBusy()

            installWatchFaceIfNeeded(
                id = watchFace.id,
                watchFaceId = watchFace.watchfaceId,
                watchFaceName = watchFace.name,
            )
        }
    }

    private suspend fun InternalSyncState.installWatchFaceIfNeeded(
        id: String,
        watchFaceId: String,
        watchFaceName: String,
    ) {
        val existingWatchFace = currentWatchFacesInWatch.firstOrNull { it.id == watchFaceId }
        val watchFaceDeviceStatus = statusDataSource.getWatchFaceDeviceStatus(serial, id) ?: return
        val installCapability = watchFaceDeviceStatus.installCapability
        val installVersion = watchFaceDeviceStatus.installVersion
        if (existingWatchFace != null && existingWatchFace.version == watchFaceDeviceStatus.installVersion) {
            Timber.w("watch face:$watchFaceId had in watch already")
            statusDataSource.updateWatchStatus(
                watchSerial = serial,
                id = id,
                status = WatchFaceStatus.IN_WATCH
            )
            return
        }

        if (!watchFaceFileStorage.existsInCache(watchFaceId, installCapability, installVersion)) {
            Timber.w("Missing cached watch face file for ID $watchFaceId")
            triggerBackendSync.set(true)
            return
        }

        Timber.d("Installing new watch face: $watchFaceId, name: $watchFaceName")
        val watchFaceFilePath =
            watchFaceFileStorage.getAbsolutePath(watchFaceId, installCapability, installVersion)
        Timber.d("startInstallWatchFace: $watchFaceId, filePath: $watchFaceFilePath")
        statusDataSource.updateWatchStatus(
            watchSerial = serial,
            id = id,
            status = WatchFaceStatus.INSTALLING,
        )
        val result = watchFaceDataSource.installWatchFace(
            id = id,
            serial = serial,
            watchFaceId = watchFaceId,
            watchFaceName = watchFaceName,
            fileLocalPath = watchFaceFilePath,
        )

        Timber.d("startInstallWatchFace result: $result")
        statusDataSource.updateWatchStatus(
            watchSerial = serial,
            id = id,
            status = if (result) WatchFaceStatus.IN_WATCH else WatchFaceStatus.UNKNOWN,
        )
        watchWasUpdated.set(true)
    }

    private suspend fun InternalSyncState.deleteWatchFacesFromWatch() {
        // Delete removed watch faces from watch
        Timber.d("deleteWatchFacesFromWatch ====")
        val watchFaceIdsToDelete =
            currentWatchFacesInWatch.map(MdsWatchFace::id) - localWatchFacesById.keys
        if (watchFaceIdsToDelete.isNotEmpty()) {
            watchFaceIdsToDelete.forEach { watchFaceId ->
                Timber.d("Deleting watch face $watchFaceId from watch")
                watchFaceDataSource.uninstallWatchFace(watchFaceId = watchFaceId)
                watchWasUpdated.set(true)
            }
        }
    }
}

// Abort sync by throwing an exception if the watch is busy. This should only be done in a stage
// where the watch is left in a reasonably consistent state. The next full sync can then continue
// from there.
private suspend fun WatchBusyStateProvider.throwIfBusy() {
    if (isWatchBusy()) {
        Timber.w("Aborting SuuntoPlus plug-in sync. Watch is busy.")
        throw WatchBusyException()
    }
}
