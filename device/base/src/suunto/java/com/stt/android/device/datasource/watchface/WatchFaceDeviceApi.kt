package com.stt.android.device.datasource.watchface

import com.stt.android.coroutines.await
import com.stt.android.device.domain.watchface.InstallWatchFace
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.watchface.InstallWatchFaceContract
import com.suunto.connectivity.watchface.WatchFaceQueryConsumer
import com.suunto.connectivity.watchface.MdsWatchFace
import timber.log.Timber
import java.io.File
import javax.inject.Inject

class WatchFaceDeviceApi @Inject constructor(
    private val suuntoWatchModel: SuuntoWatchModel,
) : WatchFaceApi {

    override suspend fun getWatchFaceCapabilities(): List<String> {
        return invokeApi { consumer, macAddress ->
            consumer.getWatchFaceCapabilities(macAddress)
        }.capabilities
    }

    override suspend fun getInstalledWatchFaceList(): List<MdsWatchFace> {
        return invokeApi { consumer, macAddress ->
            consumer.getOnlineWatchFaceList(macAddress)
        }
    }

    override suspend fun getCurrentWatchFaceInfo(): MdsWatchFace? {
        return invokeApi { consumer, macAddress ->
            consumer.getCurrentWatchFaceInfo(macAddress)
        }
    }

    override suspend fun getWatchFaceInfo(id: String): MdsWatchFace? {
        return invokeApi { consumer, macAddress ->
            consumer.getWatchFaceInfo(macAddress, id)
        }
    }

    override suspend fun setAsCurrentWatchFace(watchFaceId: String) {
        invokeApi { consumer, macAddress ->
            consumer.setAsCurrentWatchFace(macAddress, watchFaceId)
        }
    }

    override suspend fun startWatchFaceInstall(
        fileLocalPath: String,
        installWatchFace: InstallWatchFace,
    ): Boolean {
        return try {
            Timber.d("Starting watch face installation for: ${installWatchFace.name}")

            // Validate local file exists and get file info
            val localFile = File(fileLocalPath)
            if (!localFile.exists()) {
                Timber.e("Watch face file not found: $fileLocalPath")
                return false
            }

            // Create enhanced watch face info with actual file data
            val watchFaceInfo = InstallWatchFaceContract(
                id = installWatchFace.watchFaceId,
                name = installWatchFace.name,
                version = installWatchFace.installVersion,
                capabilities = installWatchFace.installCapability,
                size = installWatchFace.fileSize,
                md5sum = installWatchFace.md5,
                preImgName = installWatchFace.preImgName
            )

            // Prepare watch face info on device first
            val prepareSuccess = invokeApi { consumer, macAddress ->
                consumer.putWatchFaceInfoBeforeInstall(macAddress, watchFaceInfo)
            }

            if (!prepareSuccess) {
                Timber.e("Failed to prepare watch face info on device")
                return false
            }

            // Get device install path
            val deviceInstallPath = getWatchFaceInstallPath()
            Timber.d("Device install path: $deviceInstallPath")

            // Transfer file to device
            Timber.d("Transferring file to device: $fileLocalPath -> $deviceInstallPath")
            suuntoWatchModel.putFile(fileLocalPath, deviceInstallPath)
            val installResult = startInternalWatchFaceInstall()
            Timber.d("Watch face installation completed with result: $installResult")
            installResult
        } catch (e: Exception) {
            Timber.e(e, "Failed to install watch face: ${installWatchFace.name}")
            false
        }
    }

    private suspend fun startInternalWatchFaceInstall(): Boolean =
        invokeApi { consumer, macAddress ->
            consumer.startInstallWatchFace(macAddress)
        }

    override suspend fun uninstallWatchFace(watchFaceId: String) {
        invokeApi { consumer, macAddress ->
            consumer.uninstallWatchFace(macAddress, watchFaceId)
        }
    }

    private suspend inline fun <reified T> invokeApi(
        crossinline apiBlock: suspend (consumer: WatchFaceQueryConsumer, macAddress: String) -> T
    ): T {
        val (consumer, macAddress) = onlineWatchFaceConsumerWithMacAddress()
        return apiBlock(consumer, macAddress)
    }

    private suspend fun onlineWatchFaceConsumerWithMacAddress(): Pair<WatchFaceQueryConsumer, String> {
        requireWatchFaceCapability()
        val spartan = suuntoWatchModel.currentWatch.await()
        val macAddress = spartan.suuntoBtDevice.macAddress
        return spartan.suuntoRepositoryClient.onlineWatchFaceConsumer to macAddress
    }

    private suspend fun requireWatchFaceCapability() {
        // TODO: add watch face capability check
    }

    /**
     * Get the device install path for watch faces
     */
    private suspend fun getWatchFaceInstallPath(): String {
        return invokeApi { consumer, macAddress ->
            consumer.getWatchFaceInstallPath(macAddress)
        }
    }
}
