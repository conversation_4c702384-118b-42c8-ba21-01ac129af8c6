package com.stt.android.device.datasource.watchface

import com.stt.android.device.domain.watchface.InstallWatchFace
import com.suunto.connectivity.watchface.MdsWatchFace

interface WatchFaceApi {

    suspend fun getWatchFaceCapabilities(): List<String>

    suspend fun getInstalledWatchFaceList(): List<MdsWatchFace>

    suspend fun getCurrentWatchFaceInfo(): MdsWatchFace?

    suspend fun getWatchFaceInfo(id: String): MdsWatchFace?

    suspend fun setAsCurrentWatchFace(watchFaceId: String)

    suspend fun startWatchFaceInstall(
        fileLocalPath: String,
        installWatchFace: InstallWatchFace,
    ): Boolean

    suspend fun uninstallWatchFace(watchFaceId: String)
}
