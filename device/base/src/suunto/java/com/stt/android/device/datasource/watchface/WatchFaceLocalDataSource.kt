package com.stt.android.device.datasource.watchface

import com.stt.android.data.source.local.watchface.LocalWatchFace
import com.stt.android.data.source.local.watchface.LocalWatchFaceWithDeviceStatus
import com.stt.android.data.source.local.watchface.WatchFaceDao
import com.stt.android.data.source.local.watchface.WatchFaceStatusDao
import com.stt.android.device.domain.watchface.WatchFace
import com.stt.android.device.domain.watchface.WatchFaceStatus
import com.stt.android.exceptions.device.SuuntoPlusFeatureNotFound
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

class WatchFaceLocalDataSource @Inject constructor(
    private val watchFaceDao: WatchFaceDao,
    private val deviceStatusDao: WatchFaceStatusDao,
) {
    suspend fun findById(id: String): WatchFace? =
        watchFaceDao.findById(id)?.toDomain()

    fun listWatchFaces(): Flow<List<WatchFace>> =
        watchFaceDao.fetchAllAsFlow()
            .map {
                it.map { localWatchFace ->
                    localWatchFace.toDomain()
                }
            }
            .distinctUntilChanged()
            .flowOn(Dispatchers.IO)

    suspend fun deleteByIds(ids: List<String>) {
        if (ids.isNotEmpty()) {
            Timber.d("Deleting local watch faces ids $ids")
            watchFaceDao.deleteByIds(ids)
            deviceStatusDao.deleteByFeatureIds(ids)
        }
    }

    suspend fun updateAddToWatchFlag(id: String, enabled: Boolean) {
        val changedRows = watchFaceDao.updateAddToWatchFlag(id, enabled)
        if (changedRows != 1) {
            throw SuuntoPlusFeatureNotFound("No watch face with ID $id")
        }
    }

    suspend fun upsert(watchFace: WatchFace) {
        watchFaceDao.upsert(watchFace.toLocal())
    }

    suspend fun listWatchFacesInGivenStates(
        watchSerial: String,
        states: Set<WatchFaceStatus>
    ): List<WatchFace> = withContext(Dispatchers.IO) {
        watchFaceDao.fetchAllWithStateAsFlow()
            .first()
            .filter { it.statusForSerial(watchSerial) in states }
            .map { it.watchFace.toDomain() }
    }
}

fun WatchFace.toLocal(): LocalWatchFace = LocalWatchFace(
    id = id,
    category = category,
    type = type,
    name = name,
    iconUrl = iconUrl,
    tileBannerUrl = tileBannerUrl,
    description = description,
    shortDescription = shortDescription,
    richText = richText,
    labels = labels,
    watchfaceId = watchfaceId,
    currentVersion = currentVersion,
    watchCapability = watchCapability,
    addToWatch = addToWatch,
    addToFavorite = addToFavorite,
    supported = supported,
    updated = updated,
    useDefaultImages = useDefaultImages
)

fun LocalWatchFace.toDomain(): WatchFace = WatchFace(
    id = id,
    category = category,
    type = type,
    name = name,
    iconUrl = iconUrl,
    tileBannerUrl = tileBannerUrl,
    description = description,
    shortDescription = shortDescription,
    richText = richText,
    labels = labels,
    watchfaceId = watchfaceId,
    currentVersion = currentVersion,
    watchCapability = watchCapability,
    addToWatch = addToWatch,
    addToFavorite = addToFavorite,
    supported = supported,
    updated = updated,
    useDefaultImages = useDefaultImages
)

private fun LocalWatchFaceWithDeviceStatus.statusForSerial(watchSerial: String): WatchFaceStatus =
    statuses.firstOrNull { status -> status.watchSerial == watchSerial }?.status?.toDomain()
        ?: WatchFaceStatus.UNKNOWN
