package com.stt.android.device.datasource.watchface

import com.stt.android.data.source.local.watchface.LocalWatchFaceDeviceStatus
import com.stt.android.data.source.local.watchface.LocalWatchFaceStatus
import com.stt.android.data.source.local.watchface.WatchFaceStatusDao
import com.stt.android.device.domain.watchface.WatchFaceStatus
import javax.inject.Inject

class WatchFaceStatusDataSource @Inject constructor(
    private val watchFaceStatusDao: WatchFaceStatusDao,
) {
    suspend fun updateWatchStatus(
        watchSerial: String,
        id: String,
        status: WatchFaceStatus
    ) {
        watchFaceStatusDao.updateStatus(watchSerial, id, status.toLocal())
    }

    suspend fun updateWatchStatus(
        watchSerial: String,
        id: String,
        watchFaceId: String,
        installCapability: String,
        installVersion: String,
        watchFacePreImgName: String,
        fileSize: Long,
        fileMd5: String,
        status: WatchFaceStatus,
    ) {
        watchFaceStatusDao.upsert(
            LocalWatchFaceDeviceStatus(
                watchSerial = watchSerial,
                id = id,
                watchFaceId = watchFaceId,
                installCapability = installCapability,
                installVersion = installVersion,
                preImgName = watchFacePreImgName,
                fileSize = fileSize,
                fileMd5 = fileMd5,
                status = status.toLocal(),
            )
        )
    }

    suspend fun getWatchFaceStatus(
        watchSerial: String,
        id: String,
    ): WatchFaceStatus? = getWatchFaceDeviceStatus(watchSerial, id)?.status?.toDomain()

    suspend fun getWatchFaceDeviceStatus(
        watchSerial: String,
        id: String,
    ): LocalWatchFaceDeviceStatus? =  watchFaceStatusDao.findWatchState(watchSerial, id)
}

fun LocalWatchFaceStatus.toDomain() = when (this) {
    LocalWatchFaceStatus.UNKNOWN -> WatchFaceStatus.UNKNOWN
    LocalWatchFaceStatus.NOT_SUPPORTED -> WatchFaceStatus.NOT_SUPPORTED
    LocalWatchFaceStatus.DOWNLOADING -> WatchFaceStatus.DOWNLOADING
    LocalWatchFaceStatus.DOWNLOADED -> WatchFaceStatus.DOWNLOADED
    LocalWatchFaceStatus.INSTALLING -> WatchFaceStatus.INSTALLING
    LocalWatchFaceStatus.IN_WATCH -> WatchFaceStatus.IN_WATCH
    LocalWatchFaceStatus.WATCH_FULL -> WatchFaceStatus.WATCH_FULL
}

fun WatchFaceStatus.toLocal() = when (this) {
    WatchFaceStatus.UNKNOWN -> LocalWatchFaceStatus.UNKNOWN
    WatchFaceStatus.NOT_SUPPORTED -> LocalWatchFaceStatus.NOT_SUPPORTED
    WatchFaceStatus.DOWNLOADING -> LocalWatchFaceStatus.DOWNLOADING
    WatchFaceStatus.DOWNLOADED -> LocalWatchFaceStatus.DOWNLOADED
    WatchFaceStatus.INSTALLING -> LocalWatchFaceStatus.INSTALLING
    WatchFaceStatus.IN_WATCH -> LocalWatchFaceStatus.IN_WATCH
    WatchFaceStatus.WATCH_FULL -> LocalWatchFaceStatus.WATCH_FULL
}
