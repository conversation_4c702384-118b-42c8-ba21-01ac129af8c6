package com.stt.android.workout.details

import android.content.Context
import android.content.SharedPreferences
import androidx.core.net.toUri
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewState2EpoxyController
import com.stt.android.core.domain.GraphType
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.reader.SmlFactory
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.competition.CompetitionStatus
import com.stt.android.domain.workouts.competition.CompetitionWorkoutSummaryData
import com.stt.android.domain.workouts.extensions.SwimmingExtension
import com.stt.android.domain.workouts.isMultisport
import com.stt.android.extensions.getResId
import com.stt.android.extensions.supportsLaps
import com.stt.android.infomodel.ActivitySummaryGroup
import com.stt.android.infomodel.SummaryGroup
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.fragments.workout.analysis.WorkoutAnalysisHelper
import com.stt.android.ui.utils.DialogHelper
import com.stt.android.ui.utils.ThrottlingOnModelClickListener
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_WORKOUT_VALUE_GROUPS
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_WORKOUT_VALUE_GROUPS_DEFAULT
import com.stt.android.utils.StartActivityHelper
import com.stt.android.utils.firstOfType
import com.stt.android.workout.details.advancedlaps.advancedLaps
import com.stt.android.workout.details.competition.competitionWorkoutSummary
import com.stt.android.workout.details.diveprofile.diveProfile
import com.stt.android.workout.details.divetrack.diveTrack
import com.stt.android.workout.details.intensity.zoneAnalysis
import com.stt.android.workout.details.laps.laps
import com.stt.android.workout.details.workoutvalues.workoutValuesNew
import com.stt.android.workoutdetail.workoutvalues.WorkoutValueGroupData
import com.stt.android.workoutdetail.workoutvalues.WorkoutValuesGridData
import com.stt.android.workoutdetail.workoutvalues.WorkoutValuesGridItemData
import com.stt.android.workoutdetail.workoutvalues.composables.WorkoutValuesGridType
import kotlinx.coroutines.CoroutineScope
import com.stt.android.R as BaseR

abstract class BaseWorkoutDetailsController(
    protected val context: Context,
    protected val fragmentManager: FragmentManager,
    protected val infoModelFormatter: InfoModelFormatter,
    protected val unitConverter: JScienceUnitConverter,
    protected val featureTogglePreferences: SharedPreferences
) : ViewState2EpoxyController<WorkoutDetailsViewState, AdvancedLapsData>() {
    var viewLifecycle: Lifecycle? = null
    var lifecycleScope: CoroutineScope? = null

    // Called by Epoxy's Java internals, no need to worry about named argument usage
    @Suppress("PARAMETER_NAME_CHANGED_ON_OVERRIDE")
    override fun buildModels(
        workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>,
        advancedLapsDataViewState: ViewState<AdvancedLapsData?>
    ) {
        val workoutHeader = workoutDetailsViewState.data?.workoutHeader?.data ?: return
        val showMultisportPartActivity =
            workoutDetailsViewState.data?.multisportPartActivity?.data != null
        val isDiving = workoutHeader.activityType.isDiving
        val supportsDiveProfile = workoutHeader.activityType.supportsDiveProfile
        val supportsDiveEvents =
            !longScreenshotLayout() && workoutHeader.activityType.supportsDiveEvents
        val supportsLaps = workoutHeader.activityType.supportsLaps()
        if (!showMultisportPartActivity) {
            addTopModel(workoutDetailsViewState)
            addDiveLocation(workoutDetailsViewState)
            if (isDiving) {
                addDiveTrack(workoutDetailsViewState)
            }
        }
        val swimmingExtension =
            workoutDetailsViewState.data?.workoutExtensionsData?.data?.workoutExtensions?.firstOfType<SwimmingExtension>()
        // only own workout can show suunto coach
        if (workoutHeader.username == workoutDetailsViewState.data?.currentUsername && swimmingExtension != null) {
            addSwimmingCoach(swimmingExtension)
        }
        addWorkoutValues(workoutDetailsViewState)
        // Wait for workout values to be ready before showing rest of the details
        if (workoutDetailsViewState.data?.workoutValues?.isLoading() == true) {
            workoutDetailsItemLoading {
                id("workoutValuesLoading")
            }
        } else {
            if (!showMultisportPartActivity) {
                addShareActivity(workoutDetailsViewState)
                if (supportsDiveProfile) {
                    addDiveProfile(workoutDetailsViewState, supportsDiveEvents)
                }
                addZoneAnalysisView(workoutDetailsViewState)
                var mergeCompetitionSummaryIntoComparison = false
                if (!supportsDiveProfile && !isDiving) {
                    mergeCompetitionSummaryIntoComparison =
                        addRecentTrend(workoutDetailsViewState) // aka Previous
                }
                if (!supportsDiveProfile && !isDiving) {
                    addRecentWorkoutSummary(workoutDetailsViewState) // aka 30-day summary
                }
                if (!supportsDiveProfile && !isDiving && !mergeCompetitionSummaryIntoComparison) {
                    addCompetitionWorkout(workoutDetailsViewState)
                }
                if (supportsLaps) {
                    addLaps(workoutDetailsViewState, advancedLapsDataViewState)
                }
                addHrBeltAd(workoutDetailsViewState)
            } else {
                addZoneAnalysisView(workoutDetailsViewState)
                addLaps(workoutDetailsViewState, advancedLapsDataViewState)
            }
        }
        super.buildModels(workoutDetailsViewState, advancedLapsDataViewState)
    }

    abstract fun addTopModel(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>)

    abstract fun addDiveLocation(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>)

    private fun addWorkoutValues(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        val workoutHeader = workoutDetailsViewState.data?.workoutHeader?.data ?: return
        val workoutValuesState = workoutDetailsViewState.data?.workoutValues
        val workoutValues = workoutValuesState?.data
        if (workoutValues == null) {
            if (workoutValuesState is ViewState.Error<WorkoutValues?>) {
                // TODO this error message item is a proof of concept
                // that each card in the RecyclerView have its own loading state
                // decide with design if we want such thing
                errorMessage {
                    id("workoutValuesError")
                    errorMessage(workoutValuesState.errorEvent.errorStringRes)
                }
            }
            return
        }

        workoutDetailsViewState.data
            ?.multisportPartActivity
            ?.data
            ?.let { multisportPartActivity ->
                workoutValues.multisportValuesContainers
                    .firstOrNull { it.multisportPartActivity == multisportPartActivity }
                    ?.let { workoutValuesContainer ->
                        showMultisportPartActivity(
                            id = "multisport_activity_${workoutValuesContainer.workoutValues.hashCode()}_compact_false",
                            sportValuesContainer = workoutValuesContainer,
                            addTopMargin = false,
                            enableCompactMode = false,
                            showDetailsButton = false,
                            workoutValues = workoutValues,
                        )
                    }
                return
            }

        showWorkoutValuesGrid(
            id = "workoutValuesNew",
            workoutValuesContainer = workoutValues.workoutValuesContainer,
            workoutValues = workoutValues,
            workoutValuesGridType = if (!longScreenshotLayout()) {
                WorkoutValuesGridType.NORMAL
            } else {
                WorkoutValuesGridType.LONG_SCREENSHOT
            },
            addTopMargin = !longScreenshotLayout(),
            showDetailsButton = false,
        )

        // For multisports
        if (workoutHeader.isMultisport && workoutDetailsViewState.data?.smlData?.isLoading() == true) {
            workoutDetailsItemLoading {
                id("multisportWorkoutValuesLoading")
            }
            return
        }

        workoutValues.multisportValuesContainers.forEachIndexed { index, sportValuesContainer ->
            val showSeparator = index > 0
            if (showSeparator) {
                // index > 0, safe to access array with index - 1
                val previousPart =
                    workoutValues.multisportValuesContainers[index - 1].multisportPartActivity
                val currentPart = sportValuesContainer.multisportPartActivity

                multisportValuesSeparator {
                    id("multisportValuesSeparator_$index")
                    formattedTransitionTime(
                        getFormattedTransitionTimeBetweenMultisportParts(previousPart, currentPart)
                    )
                }
            }

            showMultisportPartActivity(
                id = "multisport_activity_${sportValuesContainer.activityType}_${sportValuesContainer.multisportPartActivity?.startTime}_compact_true",
                sportValuesContainer = sportValuesContainer,
                addTopMargin = !showSeparator,
                enableCompactMode = true,
                showDetailsButton = !longScreenshotLayout(),
                workoutValues = workoutValues
            )
        }
    }

    private fun showMultisportPartActivity(
        id: String,
        sportValuesContainer: WorkoutValuesContainer,
        addTopMargin: Boolean,
        enableCompactMode: Boolean,
        showDetailsButton: Boolean,
        workoutValues: WorkoutValues
    ) {
        val workoutValuesGridType =
            if (enableCompactMode) {
                WorkoutValuesGridType.MULTISPORT_PART_COMPACT
            } else if (longScreenshotLayout()) {
                WorkoutValuesGridType.LONG_SCREENSHOT
            } else {
                WorkoutValuesGridType.MULTISPORT_PART_FULL
            }

        showWorkoutValuesGrid(
            id = id + "newGrid",
            workoutValuesContainer = sportValuesContainer,
            workoutValues = workoutValues,
            workoutValuesGridType = workoutValuesGridType,
            addTopMargin = addTopMargin,
            showDetailsButton = showDetailsButton
        )
    }

    private fun showWorkoutValuesGrid(
        id: String,
        workoutValuesContainer: WorkoutValuesContainer,
        workoutValues: WorkoutValues,
        workoutValuesGridType: WorkoutValuesGridType,
        addTopMargin: Boolean,
        showDetailsButton: Boolean
    ) {
        val activityType = ActivityType.valueOf(workoutValuesContainer.activityType)
        val activityName = activityType.getLocalizedName(context.resources)
        val items = workoutValuesContainer.workoutValues.map {
            val valueAndUnit = it.value?.let { value -> "$value ${it.getUnitLabel(context)}" } ?: ""
            WorkoutValuesGridItemData(
                value = valueAndUnit,
                name = it.label,
                showMoreInfoIcon = it.hasDescription && !longScreenshotLayout(),
                workoutValue = it
            )
        }

        val suuntoPlusGroups = workoutValuesContainer.suuntoPlusGroups.map { group ->
            WorkoutValueGroupData(
                name = group.name,
                items = emptyList(),
                highlight = emptyList(),
                workoutValues = group.workoutValues.map {
                    val valueAndUnit =
                        it.value?.let { value -> "$value ${it.getUnitLabel(context)}" } ?: ""
                    WorkoutValuesGridItemData(
                        value = valueAndUnit,
                        name = it.label.removePrefix(group.name).trim(),
                        showMoreInfoIcon = it.hasDescription && !longScreenshotLayout(),
                        workoutValue = it
                    )
                },
            )
        }

        val groups = workoutValuesContainer.summaryGroups.flatMap { group ->
            if (group.name == BaseR.string.summary_group_gas) {
                handleGasGroup(group, workoutValuesContainer)
            } else {
                handleDefaultGroup(group, workoutValuesContainer)
            }
        }

        val updatedGroups = includeUnhandledWorkoutValues(workoutValuesContainer, groups)

        workoutValuesNew {
            id(id)
            workoutValuesGridData(
                WorkoutValuesGridData(
                    showHeader = true,
                    activityName = activityName,
                    activityIcon = activityType.iconId,
                    addTopMargin = addTopMargin,
                    showDetailsButton = showDetailsButton,
                    workoutValues = items,
                    workoutValueGroups = updatedGroups + suuntoPlusGroups,
                    workoutValuesGridType = workoutValuesGridType,
                    onValueClicked = { workoutValue ->
                        workoutValues.onValueSelectedListener.onWorkoutValueClicked(workoutValue)
                    },
                    onMultisportDetailsClicked = {
                        workoutValues.onMultisportDetailsClicked?.let { multiSportClickHandler ->
                            workoutValuesContainer.multisportPartActivity?.let {
                                multiSportClickHandler(it)
                            }
                        }
                    },
                    // brief sharing don't show view more button, only show 10 values
                    onViewMoreClicked = if (briefSharing()) null else workoutValues.onViewMoreClicked
                )
            )
            enableWorkoutValueGroups(
                featureTogglePreferences.getBoolean(
                    KEY_ENABLE_WORKOUT_VALUE_GROUPS,
                    KEY_ENABLE_WORKOUT_VALUE_GROUPS_DEFAULT
                )
            )
        }
    }

    private fun includeUnhandledWorkoutValues(
        workoutValuesContainer: WorkoutValuesContainer,
        groups: List<WorkoutValueGroupData>
    ): MutableList<WorkoutValueGroupData> {
        // This is a fallback for items that were not handled by activitySummaryGroups.json
        // nor Suunto Plus handler (e.g. a new workout value not added to activitySummaryGroups.json.
        // Such items will be either
        // 1. added to the existing "Other" summary group
        // 2. or "Other" summary group will be created for them and shown in UI
        val unhandled = workoutValuesContainer.workoutValues.filter { value ->
            groups.none { it.items.contains(value.item) } &&
                workoutValuesContainer.suuntoPlusGroups.none { it.workoutValues.contains(value) }
        }

        // Check if there's already an OTHER group
        val otherGroupName = context.getString(SummaryGroup.OTHER.getResId)
        val existingOtherGroup = groups.find { it.name == otherGroupName }

        // Create a mutable list of groups that we can modify
        val updatedGroups = groups.toMutableList()

        if (unhandled.isNotEmpty()) {
            if (existingOtherGroup != null) {
                // Add unhandled items to the existing OTHER group
                val updatedOtherGroup = existingOtherGroup.copy(
                    workoutValues = existingOtherGroup.workoutValues + unhandled.map {
                        val valueAndUnit =
                            it.value?.let { value -> "$value ${it.getUnitLabel(context)}" }
                                ?: ""
                        WorkoutValuesGridItemData(
                            value = valueAndUnit,
                            name = it.label,
                            showMoreInfoIcon = it.hasDescription && !longScreenshotLayout(),
                            workoutValue = it
                        )
                    }
                )

                // Replace the existing OTHER group with the updated one in our list
                val otherGroupIndex = updatedGroups.indexOfFirst { it.name == otherGroupName }
                if (otherGroupIndex != -1) {
                    updatedGroups[otherGroupIndex] = updatedOtherGroup
                }
            } else {
                // Create a new OTHER group and add it as last to updatedGroups
                updatedGroups.add(
                    WorkoutValueGroupData(
                        name = otherGroupName,
                        items = emptyList(),
                        highlight = emptyList(),
                        workoutValues = unhandled.map {
                            val valueAndUnit =
                                it.value?.let { value -> "$value ${it.getUnitLabel(context)}" }
                                    ?: ""
                            WorkoutValuesGridItemData(
                                value = valueAndUnit,
                                name = it.label,
                                showMoreInfoIcon = it.hasDescription && !longScreenshotLayout(),
                                workoutValue = it
                            )
                        },
                    )
                )
            }
        }
        return updatedGroups
    }

    open fun addSwimmingCoach(swimmingExtension: SwimmingExtension) {}

    abstract fun addShareActivity(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>)

    protected open fun addDiveProfile(
        workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>,
        supportsDiveEvents: Boolean
    ) {
        val diveProfileData = workoutDetailsViewState.data?.diveProfileData?.data
        if (diveProfileData?.sml != null && diveProfileData.graphData != null) {
            diveProfile {
                id("diveProfile")
                diveExtension(diveProfileData.diveExtension)
                infoModelFormatter(infoModelFormatter)
                onChartClicked(diveProfileData.onDiveProfileTapped)
                supportsDiveEvents(supportsDiveEvents)
                onShowEventsClicked(diveProfileData.onShowEventsTapped)
                chartData(diveProfileData.graphData)
            }
        }
    }

    private fun addDiveTrack(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        // Dive track is rendered on a surface view, and it's difficult to capture its content.
        if (longScreenshotLayout()) {
            return
        }

        val diveTrackData = workoutDetailsViewState.data?.diveTrackData?.data ?: return
        diveTrack {
            id("diveTrack")
            diveTrackData(diveTrackData)
        }
    }

    protected open fun addZoneAnalysisView(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        val workoutDetailsViewStateData = workoutDetailsViewState.data ?: return
        val zoneAnalysisData = workoutDetailsViewStateData.zoneAnalysisData?.data ?: return

        val zoneAnalysisGraphTypes = zoneAnalysisData.availableGraphTypes.map {
            createZoneAnalysisGraphType(context, it)
        }.toSet()

        val mainGraphType = zoneAnalysisData.mainGraphData?.let {
            createZoneAnalysisGraphTypeWithUnit(
                context = context,
                graphType = it.lineChartData.graphType,
                isSwimming = zoneAnalysisData.isSwimming
            )
        } ?: return // Exit if no main graph data is available.

        val secondaryGraphType = zoneAnalysisData.secondaryGraphData?.let {
            createZoneAnalysisGraphTypeWithUnit(
                context = context,
                graphType = it.lineChartData.graphType,
                isSwimming = zoneAnalysisData.isSwimming
            )
        } ?: ZoneAnalysisGraphType.EMPTY

        zoneAnalysis {
            id("zoneAnalysis")
            zoneAnalysisData(zoneAnalysisData)
            mainGraphType(mainGraphType)
            secondaryGraphType(secondaryGraphType)
            zoneAnalysisGraphTypes(zoneAnalysisGraphTypes)
            showFullscreenButton(!longScreenshotLayout())
            onAerobicZoneInfoClick(if (!longScreenshotLayout()) workoutDetailsViewStateData.onAerobicZoneInfoClicked else null)
        }
    }

    private fun getFormattedTransitionTimeBetweenMultisportParts(
        previousPart: MultisportPartActivity?,
        currentPart: MultisportPartActivity?
    ): String? {
        val previousElapsed = previousPart?.elapsed ?: return null
        val currentElapsed = currentPart?.elapsed ?: return null

        val timeBetweenPartStarts = currentElapsed - previousElapsed
        val previousTotalTime = previousPart.stopTime - previousPart.startTime
        val transitionTime = timeBetweenPartStarts - previousTotalTime

        return if (transitionTime > 0) {
            infoModelFormatter.formatValue(SummaryItem.DURATION, (transitionTime) / 1000.0).value
        } else {
            null
        }
    }

    private fun addRecentTrend(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>): Boolean {
        val recentTrendData = workoutDetailsViewState.data?.recentTrendData?.data ?: return false
        val workoutHeader = workoutDetailsViewState.data?.workoutHeader?.data ?: return false
        if (workoutHeader.polyline?.isNotEmpty() == true) {
            val hasPremium = workoutDetailsViewState.data?.isSubscribedToPremium ?: true
            if (hasPremium) {
                val competitionWorkoutSummaryData =
                    workoutDetailsViewState.data?.competitionWorkoutSummaryData?.data?.takeIf {
                        shouldAddCompetitionSummary(workoutDetailsViewState)
                    }
                addRecentTrendData(
                    competitionWorkoutSummaryData,
                    workoutHeader,
                    recentTrendData,
                    workoutDetailsViewState.data?.onTrendPageSelected
                )
                return true
            } else {
                buyPremiumPlaceholder {
                    id("recentTrend-buy-premium-placeholder")
                    title(recentTrendData.routeSelection.toString(context.resources))
                    description(context.getString(BaseR.string.buy_premium_workout_recent_trends_description))
                    onClicked(
                        ThrottlingOnModelClickListener { _, _, _, _ ->
                            workoutDetailsViewState.data?.onOpenPremiumPromotionClicked?.invoke(
                                AnalyticsPropertyValue.PremiumPurchaseFlowScreenSource.WORKOUT_RECENT_TRENDS_VIEW
                            )
                        }
                    )
                }
            }
        }
        return false
    }

    abstract fun addRecentTrendData(
        competitionWorkoutSummaryData: CompetitionWorkoutSummaryData?,
        workoutHeader: WorkoutHeader,
        recentTrendData: RecentTrendData,
        onPageSelected: OnPageSelected?
    )

    private fun addCompetitionWorkout(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        if (!shouldAddCompetitionSummary(workoutDetailsViewState)) return
        competitionWorkoutSummary {
            id("competitionWorkout")
            workoutHeader(workoutDetailsViewState.data?.workoutHeader?.data)
            competitionWorkoutSummaryData(workoutDetailsViewState.data?.competitionWorkoutSummaryData?.data)
            infoModelFormatter(infoModelFormatter)
            onWorkoutCompetitionClick(
                ThrottlingOnModelClickListener { model, _, _, _ ->
                    model.competitionWorkoutSummaryData().onCompetitionWorkoutClick()
                }
            )
        }
    }

    private fun shouldAddCompetitionSummary(
        workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>
    ): Boolean {
        val status = workoutDetailsViewState.data
            ?.competitionWorkoutSummaryData
            ?.data
            ?.summary
            ?.status

        return status != null && status != CompetitionStatus.NOT_IN_COMPETITION.name
    }

    abstract fun addRecentWorkoutSummary(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>)

    protected open fun addLaps(
        workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>,
        advancedLapsDataViewState: ViewState<AdvancedLapsData?>
    ) {
        val smlData = workoutDetailsViewState.data?.smlData ?: return
        if (smlData.isLoading() || advancedLapsDataViewState.isLoading()) {
            addLapsLoadingModel()
        } else {
            val sml = smlData.data ?: SmlFactory.EMPTY
            if (sml == SmlFactory.EMPTY) {
                // Show old laps fragment for workouts without SML data
                addOldLaps(workoutDetailsViewState)
            } else {
                addAdvancedLaps(advancedLapsDataViewState)
            }
        }
    }

    private fun addOldLaps(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        val lapsDataState = workoutDetailsViewState.data?.lapsData ?: return
        val domainWorkoutHeader = workoutDetailsViewState.data?.workoutHeader?.data ?: return
        val activityType = domainWorkoutHeader.activityType
        when (lapsDataState) {
            is ViewState.Error -> {
                // do nothing
            }

            is ViewState.Loading -> addLapsLoadingModel()
            is ViewState.Loaded -> {
                val data = lapsDataState.data ?: return
                if (data.hasLaps) {
                    laps {
                        id("oldLaps")
                        activityType(activityType)
                        workoutHeader(domainWorkoutHeader)
                        lapsData(data)
                        infoModelFormatter(infoModelFormatter)
                    }
                }
            }
        }
    }

    private fun addAdvancedLaps(advancedLapsDataViewState: ViewState<AdvancedLapsData?>?) {
        when (advancedLapsDataViewState) {
            is ViewState.Error -> {
            } // do nothing
            is ViewState.Loading -> addLapsLoadingModel()
            is ViewState.Loaded -> {
                val data = advancedLapsDataViewState.data ?: return
                if (data.container.tables.isNotEmpty()) {
                    advancedLaps {
                        id("advancedLaps_${data.stId}")
                        stId(data.stId)
                        lapTables(data.container.tables)
                        currentLapTable(data.container.currentLapTable)
                        lapsTableColouringEnabled(data.container.isLapsTableColouringEnabled)
                        showLapCellColorInfoUi(data.container.showLapCellColorInfoUi)
                        onSelectColumnRequested(data.container.onSelectColumnRequested)
                        onHighlightVariancesToggled(data.container.onHighLightVariancesToggled)
                        infoModelFormatter(infoModelFormatter)
                        pageChangeListener(data.pageChangeListener)
                        lifecycleScope(lifecycleScope)
                    }
                }
            }

            null -> {
                // do nothing
            }
        }
    }

    /**
     * longScreenshot return true
     */
    protected open fun longScreenshotLayout(): Boolean = false

    private fun addLapsLoadingModel() {
        workoutDetailsItemLoading {
            id("advancedLapsLoading")
        }
    }

    protected open fun addHrBeltAd(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        val showAd = workoutDetailsViewState.data?.hrBeltAdData?.data?.isVisible ?: false
        if (showAd) {
            hrBeltAd {
                id("hrBeltAd")
                onClick(
                    ThrottlingOnModelClickListener { _, _, _, _ ->
                        openHrBeltAd()
                    }
                )
            }
        }
    }

    private fun openHrBeltAd() {
        if (!StartActivityHelper.startActivityExternally(
                context as FragmentActivity,
                context.getString(BaseR.string.shop_url_hr_sensor_workout_graph).toUri()
            )
        ) {
            DialogHelper.showDialog(context, BaseR.string.error_0)
        }
    }

    private fun createZoneAnalysisGraphType(
        context: Context,
        graphType: GraphType,
        unit: String = ""
    ): ZoneAnalysisGraphType {
        return ZoneAnalysisGraphType(
            graphType = graphType,
            graphTitle = WorkoutAnalysisHelper.getGraphNameTitle(context, graphType),
            graphUnit = unit
        )
    }

    private fun createZoneAnalysisGraphTypeWithUnit(
        context: Context,
        graphType: GraphType,
        isSwimming: Boolean
    ): ZoneAnalysisGraphType {
        val graphUnitResourceId =
            WorkoutAnalysisHelper.getGraphUnitStringRes(
                infoModelFormatter,
                graphType,
                infoModelFormatter.unit,
                isSwimming
            )

        val graphUnitText = graphUnitResourceId?.let { context.getString(it) } ?: ""
        return createZoneAnalysisGraphType(context, graphType, graphUnitText)
    }

    protected open fun briefSharing() = false

    /**
     * Special handling for GAS group.
     * Creates multiple groups from gas-related values when the first item is of type SummaryItem.DIVEGASES
     * followed by 0 or more values of other types. The next SummaryItem.DIVEGASES will start the next group.
     */
    private fun handleGasGroup(
        group: ActivitySummaryGroup,
        workoutValuesContainer: WorkoutValuesContainer
    ): List<WorkoutValueGroupData> {
        // Get all gas-related values
        val gasWorkoutValues = workoutValuesContainer.workoutValues
            .filter { value -> group.items.any { it == value.item } }

        if (gasWorkoutValues.isEmpty()) {
            return emptyList()
        }

        val gasGroups = mutableListOf<WorkoutValueGroupData>()
        val currentGroupValues = mutableListOf<WorkoutValuesGridItemData>()
        var gasGroupCounter = 1

        // Process all gas-related values in order
        gasWorkoutValues.forEach { workoutValue ->
            val valueAndUnit = workoutValue.value?.let { value ->
                "$value ${workoutValue.getUnitLabel(context)}"
            } ?: ""

            val gridItem = WorkoutValuesGridItemData(
                value = valueAndUnit,
                name = workoutValue.label,
                showMoreInfoIcon = workoutValue.hasDescription && !longScreenshotLayout(),
                workoutValue = workoutValue
            )

            // If this is a DIVEGASES item and we already have items in the current group,
            // finalize the current group and start a new one
            if (workoutValue.item == SummaryItem.DIVEGASES && currentGroupValues.isNotEmpty()) {
                gasGroups.add(
                    WorkoutValueGroupData(
                        name = "${context.getString(group.name)} $gasGroupCounter",
                        items = group.items,
                        highlight = group.highlight,
                        workoutValues = currentGroupValues.toList()
                    )
                )
                currentGroupValues.clear()
                gasGroupCounter++
            }

            currentGroupValues.add(gridItem)
        }

        // Add any remaining items as the last group
        if (currentGroupValues.isNotEmpty()) {
            gasGroups.add(
                WorkoutValueGroupData(
                    name = "${context.getString(group.name)} $gasGroupCounter",
                    items = group.items,
                    highlight = group.highlight,
                    workoutValues = currentGroupValues.toList()
                )
            )
        }

        return gasGroups
    }

    /**
     * Normal handling for other groups.
     * Creates a single group containing all values related to the group.
     */
    private fun handleDefaultGroup(
        group: ActivitySummaryGroup,
        workoutValuesContainer: WorkoutValuesContainer
    ): List<WorkoutValueGroupData> {
        return listOf(
            WorkoutValueGroupData(
                name = context.getString(group.name),
                items = group.items,
                highlight = group.highlight,
                workoutValues = workoutValuesContainer.workoutValues
                    .filter { value -> group.items.any { it == value.item } }
                    .map {
                        val valueAndUnit =
                            it.value?.let { value -> "$value ${it.getUnitLabel(context)}" } ?: ""
                        WorkoutValuesGridItemData(
                            value = valueAndUnit,
                            name = it.label,
                            showMoreInfoIcon = it.hasDescription && !longScreenshotLayout(),
                            workoutValue = it
                        )
                    }
            )
        )
    }
}
