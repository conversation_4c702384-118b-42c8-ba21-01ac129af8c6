package com.stt.android.workout.details.summary

import android.text.format.DateUtils
import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loaded
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.utils.traceSuspend
import com.stt.android.workout.details.NavigationEventDispatcher
import com.stt.android.workout.details.RecentWorkoutSummary
import com.stt.android.workout.details.RecentWorkoutSummaryData
import com.stt.android.workout.details.WorkoutDetailsRecentWorkoutSummaryActivityNavEvent
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

interface RecentWorkoutSummaryDataLoader {
    val summaryStateFlow: StateFlow<ViewState<RecentWorkoutSummaryData?>>
    suspend fun loadSummary(
        workoutHeader: WorkoutHeader,
        currentSummaryPage: Int
    ): StateFlow<ViewState<RecentWorkoutSummaryData?>>

    suspend fun update(workoutHeader: WorkoutHeader, currentSummaryPage: Int)
}

@ActivityRetainedScoped
class DefaultRecentWorkoutSummaryDataLoader
@Inject constructor(
    private val workoutHeaderController: WorkoutHeaderController,
    private val currentUserController: CurrentUserController,
    private val navigationEventDispatcher: NavigationEventDispatcher,
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope
) : RecentWorkoutSummaryDataLoader {
    lateinit var workoutHeader: WorkoutHeader

    override val summaryStateFlow: MutableStateFlow<ViewState<RecentWorkoutSummaryData?>> =
        MutableStateFlow(ViewState.Loading(null))

    override suspend fun loadSummary(
        workoutHeader: WorkoutHeader,
        currentSummaryPage: Int
    ): StateFlow<ViewState<RecentWorkoutSummaryData?>> {
        update(workoutHeader, currentSummaryPage)
        return summaryStateFlow
    }

    override suspend fun update(workoutHeader: WorkoutHeader, currentSummaryPage: Int) {
        this.workoutHeader = workoutHeader
        val isDiving = workoutHeader.activityType.isDiving
        val isOwnWorkout = workoutHeader.username == currentUserController.username

        if (isDiving || !isOwnWorkout) {
            // Summary data not needed for dives or for other user's workouts
            summaryStateFlow.value = loaded()
        } else {
            activityRetainedCoroutineScope.launch {
                traceSuspend("loadRecentSummary") {
                    getSummary(workoutHeader, currentSummaryPage)
                }
            }
        }
    }

    private suspend fun getSummary(workoutHeader: WorkoutHeader, currentSummaryPage: Int) {
        val loaded = withContext(IO) {
            loaded(
                RecentWorkoutSummaryData(
                    summary = createSummary(workoutHeader, workoutHeaderController),
                    currentSummaryPage = currentSummaryPage,
                    onViewMoreClicked = ::onViewMoreClicked
                )
            )
        }
        summaryStateFlow.value = loaded
    }

    private fun onViewMoreClicked() =
        navigationEventDispatcher.dispatchEvent(
            WorkoutDetailsRecentWorkoutSummaryActivityNavEvent(
                workoutHeader
            )
        )

    companion object {
        /**
         * Creates the 30-day summary.
         */
        private fun createSummary(
            workoutHeader: WorkoutHeader,
            workoutHeaderController: WorkoutHeaderController
        ): RecentWorkoutSummary {
            val referenceWorkout = workoutHeader

            val userName = referenceWorkout.username
            val activityType = referenceWorkout.activityType
            val summaryPeriod = calculateSummaryPeriod(referenceWorkout.startTime)
            val summaryForCurrentPeriod = workoutHeaderController.getUserWorkoutSummary(
                userName,
                activityType,
                summaryPeriod.first,
                summaryPeriod.second
            )
            val previousPeriodStart =
                summaryPeriod.first - (summaryPeriod.second - summaryPeriod.first)
            val previousPeriodEnd = summaryPeriod.first
            val summaryForPreviousPeriod = workoutHeaderController.getUserWorkoutSummary(
                userName,
                activityType,
                previousPeriodStart,
                previousPeriodEnd
            )
            val currentPeriodWorkouts = summaryForCurrentPeriod?.totalWorkouts ?: 0
            val previousPeriodWorkouts = summaryForPreviousPeriod?.totalWorkouts ?: 0
            val workoutsChange =
                if (previousPeriodWorkouts > 0) (currentPeriodWorkouts - previousPeriodWorkouts) * 100 / previousPeriodWorkouts else 100

            val currentPeriodDistance = summaryForCurrentPeriod?.totalDistance ?: 0.0
            val previousPeriodDistance = summaryForPreviousPeriod?.totalDistance ?: 0.0
            val distanceChange =
                if (previousPeriodDistance > 0) ((currentPeriodDistance - previousPeriodDistance) * 100.0 / previousPeriodDistance).toInt() else 100

            val currentPeriodEnergy = summaryForCurrentPeriod?.totalEnergyKCal ?: 0.0
            val previousPeriodEnergy = summaryForPreviousPeriod?.totalEnergyKCal ?: 0.0
            val energyChange =
                if (previousPeriodEnergy > 0) ((currentPeriodEnergy - previousPeriodEnergy) * 100.0 / previousPeriodEnergy).toInt() else 100

            val currentPeriodDuration = summaryForCurrentPeriod?.totalDuration?.toLong() ?: 0L
            val previousPeriodDuration = summaryForPreviousPeriod?.totalDuration?.toLong() ?: 0L
            val durationChange =
                if (previousPeriodDuration > 0) ((currentPeriodDuration - previousPeriodDuration) * 100L / previousPeriodDuration).toInt() else 100

            return RecentWorkoutSummary(
                workoutHeader,
                summaryPeriod.first,
                summaryPeriod.second,
                currentPeriodWorkouts,
                workoutsChange,
                currentPeriodDistance,
                distanceChange,
                currentPeriodEnergy,
                energyChange,
                currentPeriodDuration,
                durationChange
            )
        }

        /**
         * Calculates the trend period for the given reference time.
         *
         *
         * It tries to put the reference time to the middle of the period if possible.
         * e.g. if the reference time is today, the period is from 30 days ago till now;
         * if reference time is yesterday, the period is from 30 days ago till now.
         * if reference time is 20 days ago, the period is from 35 days ago till 5 days ago.
         *
         * @return Start and end time for the trend period.
         */
        fun calculateSummaryPeriod(
            referenceTime: Long
        ): Pair<Long, Long> {
            val forward = Math.min(
                SUMMARY_PERIOD_IN_DAYS * DateUtils.DAY_IN_MILLIS / 2L,
                System.currentTimeMillis() - referenceTime
            )
            val toTimestamp = referenceTime + forward
            val fromTimestamp =
                referenceTime - (SUMMARY_PERIOD_IN_DAYS * DateUtils.DAY_IN_MILLIS - forward)
            return fromTimestamp to toTimestamp
        }

        const val SUMMARY_PERIOD_IN_DAYS = 30
    }
}
