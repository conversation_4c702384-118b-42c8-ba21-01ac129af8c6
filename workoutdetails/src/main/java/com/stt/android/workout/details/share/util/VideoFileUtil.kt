package com.stt.android.workout.details.share.util

import android.content.Context
import android.net.Uri
import androidx.core.content.FileProvider
import com.stt.android.workout.details.share.video.WorkoutMapPlaybackGraphType
import com.stt.android.workout.details.share.video.WorkoutMapPlaybackMapType
import com.stt.android.workout.details.share.video.WorkoutMapPlaybackOptions
import java.io.File

object VideoFileUtil {
    fun getVideoOutputFile(
        context: Context,
        workoutId: Int,
        dataOptions: WorkoutMapPlaybackOptions,
        graphType: WorkoutMapPlaybackGraphType,
        mapType: WorkoutMapPlaybackMapType,
    ) = File(
        File(with(context) { externalCacheDir ?: cacheDir }, "workout_videos").apply { mkdirs() },
        buildVideoOutputFilename(workoutId, dataOptions, graphType, mapType),
    )

    fun getVideoShareUri(context: Context, file: File): Uri {
        return FileProvider.getUriForFile(context, "${context.packageName}.FileProvider", file)
    }

    private fun buildVideoOutputFilename(
        workoutId: Int,
        dataOptions: WorkoutMapPlaybackOptions,
        graphType: WorkoutMapPlaybackGraphType,
        mapType: WorkoutMapPlaybackMapType,
    ): String {
        return "workout_${workoutId}_${dataOptions.toIntString()}${graphType.ordinal}${mapType.ordinal}.mp4"
    }

    private fun WorkoutMapPlaybackOptions.toIntString(): String {
        return "${username.toInt()}${startTime.toInt()}${location.toInt()}${device.toInt()}"
    }

    private fun Boolean.toInt() = if (this) 1 else 0
}
