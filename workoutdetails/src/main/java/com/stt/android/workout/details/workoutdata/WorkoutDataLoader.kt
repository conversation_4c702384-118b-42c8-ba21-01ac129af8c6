package com.stt.android.workout.details.workoutdata

import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loading
import com.stt.android.domain.workout.WorkoutData
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.extensions.getWorkoutData
import com.stt.android.ui.controllers.WorkoutDataLoaderController
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

interface WorkoutDataLoader {
    val workoutDataStateFlow: StateFlow<ViewState<WorkoutData?>>
    fun loadWorkoutData(workoutHeader: WorkoutHeader): StateFlow<ViewState<WorkoutData?>>
    fun resetCache()
}

@ActivityRetainedScoped
class DefaultWorkoutDataLoader
@Inject constructor(
    private val workoutDataLoaderController: WorkoutDataLoaderController,
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope
) : WorkoutDataLoader {

    override val workoutDataStateFlow: MutableStateFlow<ViewState<WorkoutData?>> =
        MutableStateFlow(loading())

    override fun loadWorkoutData(workoutHeader: WorkoutHeader): StateFlow<ViewState<WorkoutData?>> {
        activityRetainedCoroutineScope.launch {
            getWorkoutData(workoutHeader)
        }
        return workoutDataStateFlow
    }

    private suspend fun getWorkoutData(workoutHeader: WorkoutHeader) {
        val workoutData = workoutHeader.getWorkoutData(workoutDataLoaderController)
        workoutDataStateFlow.value = ViewState.Loaded(workoutData)
    }

    override fun resetCache() {
        workoutDataStateFlow.value = loading()
    }
}
