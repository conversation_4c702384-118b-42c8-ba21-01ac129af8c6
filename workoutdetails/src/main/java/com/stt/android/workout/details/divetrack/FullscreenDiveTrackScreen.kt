package com.stt.android.workout.details.divetrack

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.Button
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.FloatingActionButton
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Switch
import androidx.compose.material.SwitchDefaults
import androidx.compose.material.Text
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import com.stt.android.common.viewstate.ViewState
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.header
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.divetrack.ui.DiveTrackView
import com.stt.android.workout.details.R
import com.stt.android.R as BaseR

@Composable
internal fun DiveTrackScreen(
    onShareClicked: () -> Unit,
    viewModel: FullscreenDiveTrackViewModel = hiltViewModel(),
) {
    val systemUiController = rememberSystemUiController()
    SideEffect {
        systemUiController.setStatusBarColor(
            color = Color.Transparent,
            darkIcons = false
        )
        systemUiController.setNavigationBarColor(
            color = Color.Transparent,
            darkIcons = true
        )
    }

    val viewState by viewModel.viewState.collectAsState()
    when (viewState) {
        is ViewState.Loading -> DiveTrackLoadingScreen()

        is ViewState.Loaded -> DiveTrackLoadedScreen(
            viewData = requireNotNull(viewState.data),
            onResetCameraClicked = viewModel::resetCamera,
            onCameraResetCompleted = viewModel::onCameraResetCompleted,
            onCameraMoved = viewModel::onCameraMoved,
            onShowDepthProfileChanged = viewModel::setShowBottomWall,
            onShareClicked = onShareClicked,
            onSubmitRateClicked = viewModel::sendUserRating,
        )

        is ViewState.Error -> DiveTrackErrorScreen()
    }
}

@Composable
private fun DiveTrackLoadedScreen(
    viewData: FullscreenDiveTrackViewModel.ViewData,
    onResetCameraClicked: () -> Unit,
    onCameraResetCompleted: () -> Unit,
    onCameraMoved: () -> Unit,
    onShowDepthProfileChanged: (Boolean) -> Unit,
    onShareClicked: () -> Unit,
    onSubmitRateClicked: (Int) -> Unit,
    modifier: Modifier = Modifier,
) {
    val onBackPressedDispatcher =
        LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher

    var showRateTrackBottomSheet by remember { mutableStateOf(false) }

    // We agreed to allow users to submit the rating for the same workout multiple times.
    var hasSubmittedRating by remember { mutableStateOf(false) }
    var submittedRating by remember { mutableIntStateOf(0) }

    Column(
        modifier = modifier.fillMaxSize()
    ) {
        Box(
            Modifier
                .fillMaxWidth()
                .weight(1.0F)
        ) {
            DiveTrackView(
                diveTrack = viewData.diveTrack,
                settings = viewData.diveTrackSettings,
                modifier = Modifier.fillMaxSize(),
                onCameraMoved = onCameraMoved,
                onCameraResetCompleted = onCameraResetCompleted,
            )

            BackButton(
                onClick = { onBackPressedDispatcher?.onBackPressed() },
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .padding(
                        horizontal = MaterialTheme.spacing.medium,
                        vertical = MaterialTheme.spacing.xxxlarge
                    )
            )

            Row(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(MaterialTheme.spacing.medium),
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
            ) {
                if (viewData.showResetCamera) {
                    ResetCameraButton(
                        onClick = onResetCameraClicked,
                    )
                }

                RateDiveTrackButton(
                    onClick = { showRateTrackBottomSheet = true },
                )

                ShareButton(
                    onClick = onShareClicked,
                )
            }
        }

        ShowDepthProfileToggle(
            showDepthProfile = viewData.diveTrackSettings.showBottomWall,
            onShowDepthProfileChanged = onShowDepthProfileChanged,
        )

        if (showRateTrackBottomSheet) {
            RateDiveTrackBottomSheet(
                hasSubmittedRating = hasSubmittedRating,
                submittedRating = submittedRating,
                onSubmitRate = { rate ->
                    onSubmitRateClicked(rate)
                    hasSubmittedRating = true
                    submittedRating = rate
                },
                onDismissRequest = { showRateTrackBottomSheet = false },
            )
        }
    }
}

@Composable
private fun BackButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    FloatingActionButton(
        onClick = onClick,
        modifier = modifier,
        backgroundColor = MaterialTheme.colors.onPrimary,
    ) {
        Icon(
            painter = SuuntoIcons.ActionBack.asPainter(),
            tint = MaterialTheme.colors.nearBlack,
            contentDescription = null,
            modifier = Modifier
                .size(MaterialTheme.iconSizes.small)
        )
    }
}

@Composable
private fun ResetCameraButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    FloatingActionButton(
        onClick = onClick,
        modifier = modifier,
        backgroundColor = MaterialTheme.colors.onPrimary,
    ) {
        Icon(
            painter = painterResource(id = BaseR.drawable.reset_outline),
            tint = MaterialTheme.colors.nearBlack,
            contentDescription = null,
            modifier = Modifier
                .size(MaterialTheme.iconSizes.small)
        )
    }
}

@Composable
private fun ShareButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    FloatingActionButton(
        onClick = onClick,
        modifier = modifier,
        backgroundColor = MaterialTheme.colors.onPrimary,
    ) {
        Icon(
            painter = painterResource(id = BaseR.drawable.share_outline),
            tint = MaterialTheme.colors.nearBlack,
            contentDescription = null,
            modifier = Modifier
                .size(MaterialTheme.iconSizes.small)
        )
    }
}

@Composable
private fun RateDiveTrackButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    FloatingActionButton(
        onClick = onClick,
        modifier = modifier,
        backgroundColor = MaterialTheme.colors.onPrimary,
    ) {
        Icon(
            painter = painterResource(id = R.drawable.feedback_outline),
            tint = MaterialTheme.colors.nearBlack,
            contentDescription = null,
            modifier = Modifier
                .size(MaterialTheme.iconSizes.small),
        )
    }
}

@Composable
private fun ShowDepthProfileToggle(
    showDepthProfile: Boolean,
    onShowDepthProfileChanged: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(
                start = MaterialTheme.spacing.medium,
                end = MaterialTheme.spacing.medium,
                top = MaterialTheme.spacing.medium,
                bottom = MaterialTheme.spacing.xxlarge,
            ),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = stringResource(id = R.string.dive_track_show_depth_profile),
            modifier = Modifier.weight(1.0F),
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colors.onSurface,
        )

        Switch(
            checked = showDepthProfile,
            onCheckedChange = onShowDepthProfileChanged,
            colors = SwitchDefaults.colors(
                checkedTrackColor = MaterialTheme.colors.primary,
                checkedThumbColor = MaterialTheme.colors.primary,
            ),
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun RateDiveTrackBottomSheet(
    hasSubmittedRating: Boolean,
    submittedRating: Int,
    onSubmitRate: (rate: Int) -> Unit,
    onDismissRequest: () -> Unit,
    modifier: Modifier = Modifier,
) {
    ModalBottomSheet(
        onDismissRequest = onDismissRequest,
        modifier = modifier,
        containerColor = MaterialTheme.colors.surface,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.spacing.medium),
        ) {
            Text(
                text = stringResource(id = R.string.dive_track_rate_title),
                fontSize = 18.sp,
                style = MaterialTheme.typography.header,
                color = MaterialTheme.colors.onSurface,
            )
            Text(
                text = stringResource(id = R.string.dive_track_rate_subtitle),
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colors.onSurface,
            )

            var selectedRating by remember { mutableIntStateOf(submittedRating) }
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        horizontal = MaterialTheme.spacing.small,
                        vertical = MaterialTheme.spacing.medium,
                    ),
            ) {
                repeat(5) { index ->
                    if (index > 0) {
                        Spacer(modifier = Modifier.weight(1.0F))
                    }

                    Icon(
                        painter = painterResource(
                            id = if (index < selectedRating) {
                                BaseR.drawable.star_filled
                            } else {
                                BaseR.drawable.star_outline
                            }
                        ),
                        tint = MaterialTheme.colors.primary,
                        contentDescription = null,
                        modifier = Modifier
                            .size(MaterialTheme.iconSizes.medium)
                            .clickable(
                                enabled = !hasSubmittedRating,
                            ) {
                                selectedRating = index + 1
                            },
                    )
                }
            }
            if (hasSubmittedRating) {
                Text(
                    text = stringResource(id = R.string.dive_track_rate_thanks),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            top = 16.dp,
                            bottom = 32.dp,
                        ),
                    style = MaterialTheme.typography.header,
                    color = MaterialTheme.colors.onSurface,
                    textAlign = TextAlign.Center,
                )
            } else {
                Button(
                    onClick = { onSubmitRate(selectedRating) },
                    enabled = selectedRating > 0,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            start = MaterialTheme.spacing.small,
                            end = MaterialTheme.spacing.small,
                            bottom = MaterialTheme.spacing.medium,
                        )
                ) {
                    Text(
                        text = stringResource(R.string.dive_track_rate_submit)
                    )
                }
            }
        }
    }
}

@Composable
private fun DiveTrackLoadingScreen() {
    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        CircularProgressIndicator(
            modifier = Modifier.align(Alignment.Center)
        )
    }
}

@Composable
private fun DiveTrackErrorScreen() {
    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        // TODO
        Text(
            text = stringResource(BaseR.string.error_generic),
            modifier = Modifier.align(Alignment.Center)
        )
    }
}
