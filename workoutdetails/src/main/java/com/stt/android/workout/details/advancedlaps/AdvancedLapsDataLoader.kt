package com.stt.android.workout.details.advancedlaps

import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.domain.advancedlaps.AdvancedLapsUseCase
import com.stt.android.domain.advancedlaps.LapsTable
import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.isMultisport
import com.stt.android.extensions.supportsLaps
import com.stt.android.logbook.SuuntoLogbookWindow
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.mapping.getSummaryCategoriesByStId
import com.stt.android.utils.traceSuspend
import com.stt.android.workout.details.multisport.MultisportPartActivityLoader
import com.stt.android.workout.details.sml.SmlDataLoader
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.conflate
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

interface AdvancedLapsDataLoader {
    val lapsDataStateFlow: StateFlow<ViewState<List<LapsTable>?>>
    fun loadLapsTables(workoutHeader: WorkoutHeader): StateFlow<ViewState<List<LapsTable>?>>
}

@ActivityRetainedScoped
class DefaultAdvancedLapsDataLoader
@Inject constructor(
    private val advancedLapsUseCase: AdvancedLapsUseCase,
    private val infoModelFormatter: InfoModelFormatter,
    private val smlDataLoader: SmlDataLoader,
    private val multisportPartActivityLoader: MultisportPartActivityLoader,
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope
) : AdvancedLapsDataLoader {
    override val lapsDataStateFlow: MutableStateFlow<ViewState<List<LapsTable>?>> =
        MutableStateFlow(loading())

    override fun loadLapsTables(workoutHeader: WorkoutHeader): StateFlow<ViewState<List<LapsTable>?>> {
        if (!workoutHeader.activityType.supportsLaps()) {
            // Advanced laps data not needed for scuba diving
            lapsDataStateFlow.value = loaded()
        } else {
            activityRetainedCoroutineScope.launch {
                combine(
                    smlDataLoader.smlStateFlow,
                    multisportPartActivityLoader.multisportPartActivityFlow
                ) { smlDataState, multisportPartActivityState ->
                    smlDataState to multisportPartActivityState
                }
                    .conflate()
                    .onEach { states ->
                        val smlState = states.first
                        val multisportPartActivity = states.second.data
                        when {
                            smlState.isFailure() -> lapsDataStateFlow.value = loaded()
                            smlState.isLoading() -> lapsDataStateFlow.value = loading()
                            smlState.isLoaded() -> {
                                val sml = smlState.data
                                if (sml != null) {
                                    traceSuspend("getLapsTables") {
                                        lapsDataStateFlow.value = loaded(
                                            getLapsTables(workoutHeader, sml, multisportPartActivity)
                                        )
                                    }
                                } else {
                                    lapsDataStateFlow.value = loaded()
                                }
                            }
                        }
                    }
                    .catch { t -> Timber.w(t, "Loading advanced laps data failed.") }
                    .collect()
            }
        }
        return lapsDataStateFlow
    }

    private fun getLapsTables(
        workoutHeader: WorkoutHeader,
        sml: Sml,
        multisportPartActivity: MultisportPartActivity?
    ): List<LapsTable> {
        // Processes lap tables out of the sml
        val unit = when (infoModelFormatter.unit) {
            MeasurementUnit.METRIC -> com.stt.android.core.domain.MeasurementUnit.METRIC
            else -> com.stt.android.core.domain.MeasurementUnit.IMPERIAL
        }

        val activityType = multisportPartActivity?.let { ActivityType.valueOf(it.activityType) } ?: workoutHeader.activityType

        // The summary categories control what data types can be selected to be shown in the laps table,
        // if the list is empty consider the autolap type being unsupported for the activity type
        val supportsDistanceAutoLaps = !activityType.supportsDiveProfile &&
            getSummaryCategoriesByStId(activityType.id, LapsTableType.DISTANCE_AUTO_LAP).isNotEmpty()
        val supportsDurationAutoLaps = !activityType.supportsDiveProfile &&
            getSummaryCategoriesByStId(activityType.id, LapsTableType.DURATION_AUTO_LAP).isNotEmpty()

        return advancedLapsUseCase.getLapsTableData(
            windows = getWindows(workoutHeader, sml, multisportPartActivity),
            streamData = sml.streamData,
            multisportPartActivity = multisportPartActivity,
            header = sml.summary.header,
            measurementUnit = unit,
            supportsDistanceAutoLaps = supportsDistanceAutoLaps,
            supportsDurationAutoLaps = supportsDurationAutoLaps
        ).filter { it.lapsTableRows.isNotEmpty() }
    }

    private fun getWindows(
        workoutHeader: WorkoutHeader,
        sml: Sml,
        multisportPartActivity: MultisportPartActivity?
    ): List<SuuntoLogbookWindow> {
        return if (workoutHeader.isMultisport && multisportPartActivity != null) {
            sml.getMultisportWindows(multisportPartActivity)
        } else {
            sml.summary.windows
        }
    }
}
