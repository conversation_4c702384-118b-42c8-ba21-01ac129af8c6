package com.stt.android.workout.details.comments

import android.view.View
import android.widget.TextView
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.ui.components.AddCommentEditText
import com.stt.android.ui.components.InlineTextForm
import com.stt.android.workout.details.AddCommentClickListener
import com.stt.android.workout.details.OnTextSubmittedHandler
import com.stt.android.workout.details.R

@EpoxyModelClass
abstract class AddCommentModel : EpoxyModelWithHolder<Holder>(), InlineTextForm.OnSubmitListener {
    @EpoxyAttribute
    var editMode: Boolean = false

    @EpoxyAttribute
    var enabled: Boolean = true

    @EpoxyAttribute
    lateinit var text: String

    @EpoxyAttribute
    var workoutKey: String? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var onTextSubmittedHandler: OnTextSubmittedHandler? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var addCommentClickHandler: AddCommentClickListener? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var backKeyPressImeListener: AddCommentEditText.BackKeyPressImeListener? = null

    override fun getDefaultLayout() = R.layout.add_comment_model

    override fun bind(holder: Holder) {
        super.bind(holder)
        holder.apply {
            commentForm.isEnabled = enabled
            if (commentForm.text != text) {
                commentForm.updateText(text)
            }
            if (editMode) {
                addCommentLabel.visibility = View.GONE
                commentForm.visibility = View.VISIBLE
                if (enabled) {
                    commentForm.showUp()
                }
            } else {
                commentForm.saveText()
                commentForm.visibility = View.GONE
                addCommentLabel.visibility = View.VISIBLE
            }

            commentForm.setOnSubmitListener(this@AddCommentModel)
            commentForm.setBackKeyPressListener(backKeyPressImeListener)
            addCommentLabel.setOnClickListener { addCommentClickHandler?.onAddCommentClicked() }
        }
    }

    override fun unbind(holder: Holder) {
        super.unbind(holder)
        holder.apply {
            commentForm.setOnSubmitListener(null)
            commentForm.setBackKeyPressListener(null)
            addCommentLabel.setOnClickListener(null)
        }
    }

    override fun onViewDetachedFromWindow(holder: Holder) {
        super.onViewDetachedFromWindow(holder)
        holder.commentForm.clearFocus()
    }

    override fun shouldSaveViewState(): Boolean = true

    override fun onSubmit(text: String) {
        onTextSubmittedHandler?.invoke(workoutKey ?: return, text)
    }
}

class Holder : KotlinEpoxyHolder() {
    val addCommentLabel: TextView by bind(R.id.add_comment_label)
    val commentForm: InlineTextForm by bind(R.id.comment_form)
}
