package com.stt.android.workout.details.heartrate

import com.github.mikephil.charting.data.Entry
import com.google.android.gms.maps.model.LatLng
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsPropertyValue.WorkoutAnalysisScreenSource.VIEW_ON_MAP_HR_ZONES
import com.stt.android.analytics.AnalyticsPropertyValue.WorkoutDetailsContext.WORKOUT_DETAILS_SCREEN
import com.stt.android.analytics.AnalyticsPropertyValue.WorkoutDetailsContext.WORKOUT_MULTISPORT_DETAILS_SCREEN
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loading
import com.stt.android.controllers.IntensityExtensionDataModel
import com.stt.android.controllers.UserSettingsController
import com.stt.android.core.domain.GraphType
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.user.workoutextension.IntensityExtension
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workout.WorkoutHrEvent
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.extensions.getIndexOfHighlightedRoute
import com.stt.android.intensityzone.IntensityZoneLimits
import com.stt.android.ui.extensions.hasRoute
import com.stt.android.ui.utils.averageHrEvents
import com.stt.android.ui.utils.getZoneDurationsFromHrEvents
import com.stt.android.workout.details.HeartRateData
import com.stt.android.workout.details.HrGraphData
import com.stt.android.workout.details.NavigationEventDispatcher
import com.stt.android.workout.details.WorkoutDetailsFullscreenChartNavEvent
import com.stt.android.workout.details.WorkoutDetailsMapGraphAnalysisNavEvent
import com.stt.android.workout.details.analytics.WorkoutDetailsAnalytics
import com.stt.android.workout.details.multisport.MultisportPartActivityLoader
import com.stt.android.workout.details.sml.SmlDataLoader
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import java.time.Duration
import java.util.concurrent.TimeUnit
import kotlin.math.roundToInt

abstract class BaseHeartRateDataLoader(
    private val userSettingsController: UserSettingsController,
    private val navigationEventDispatcher: NavigationEventDispatcher,
    protected val intensityExtensionDataModel: IntensityExtensionDataModel,
    protected val smlDataLoader: SmlDataLoader,
    private val workoutDetailsAnalytics: WorkoutDetailsAnalytics,
    private val multisportPartActivityLoader: MultisportPartActivityLoader
) : HeartRateDataLoader {
    protected lateinit var workoutHeader: WorkoutHeader
    override val hrGraphDataStateFlow = MutableStateFlow<ViewState<HeartRateData>>(loading())

    override suspend fun loadHeartRateData(workoutHeader: WorkoutHeader, scope: CoroutineScope): Flow<ViewState<HeartRateData>> {
        update(workoutHeader, scope)
        return hrGraphDataStateFlow
    }

    override suspend fun update(workoutHeader: WorkoutHeader, scope: CoroutineScope) {
        this.workoutHeader = workoutHeader
        scope.launch {
            getHeartRateData(workoutHeader)
        }
    }

    protected fun graphClickListener() {
        navigationEventDispatcher.dispatchEvent(
            WorkoutDetailsFullscreenChartNavEvent(
                workoutHeader,
                getGraphType(),
                AnalyticsPropertyValue.WorkoutAnalysisScreenSource.HR_ZONES_GRAPH,
                multisportPartActivityLoader.multisportPartActivityFlow.value.data
            )
        )
    }

    protected fun viewOnMapClickListener() {
        val multisportPartActivity = multisportPartActivityLoader.multisportPartActivityFlow.value.data
        navigationEventDispatcher.dispatchEvent(
            WorkoutDetailsMapGraphAnalysisNavEvent(
                workoutHeader,
                multisportPartActivity,
                workoutDetailsAnalytics,
                false,
                getGraphType(),
                VIEW_ON_MAP_HR_ZONES,
                if (multisportPartActivity == null) WORKOUT_DETAILS_SCREEN else WORKOUT_MULTISPORT_DETAILS_SCREEN
            )
        )
    }

    protected fun createGraphData(
        workoutHeader: WorkoutHeader,
        hrEvents: List<WorkoutHrEvent>,
        intensityExtension: IntensityExtension?,
        multisportPartActivity: MultisportPartActivity? = null,
        sml: Sml? = null,
        multisportRoutes: List<List<LatLng>>? = null,
    ): HrGraphData {
        val zoneLimits = getLimits(workoutHeader, intensityExtension)
        val durations = getZoneDurations(hrEvents, zoneLimits.asFloats, intensityExtension)
        val entries =
            averageHrEvents(hrEvents, TimeUnit.HOURS.toSeconds(1)).map(WorkoutHrEvent::asEntry)
        val avgHr = getAvgHr(multisportPartActivity, sml, workoutHeader, hrEvents)
        return HrGraphData(
            avgHr = avgHr,
            entries = entries.toImmutableList(),
            zoneLimits = zoneLimits,
            thresholds = emptyList(), // TODO: Give thresholds when available
            durations = durations,
            showViewOnMap = hasRoute(
                workoutHeader,
                multisportPartActivity,
                sml,
                multisportRoutes
            )
        )
    }

    private fun hasRoute(
        workoutHeader: WorkoutHeader,
        multisportPartActivity: MultisportPartActivity?,
        sml: Sml?,
        multisportRoutes: List<List<LatLng>>?
    ): Boolean {
        return if (multisportPartActivity == null) {
            workoutHeader.hasRoute
        } else {
            val activityType = ActivityType.valueOf(multisportPartActivity.activityType)
            val partHasRoute = if (sml != null && multisportRoutes != null) {
                val routeIndex = sml.getIndexOfHighlightedRoute(multisportPartActivity)
                if (routeIndex >= 0) {
                    multisportRoutes[routeIndex].isNotEmpty()
                } else {
                    false
                }
            } else {
                workoutHeader.hasRoute
            }

            partHasRoute && !activityType.isIndoor && !activityType.isDiving
        }
    }

    private fun getLimits(
        workoutHeader: WorkoutHeader,
        intensityExtension: IntensityExtension?
    ): IntensityZoneLimits {
        val heartRateMax = workoutHeader.heartRateUserSetMax.let {
            when {
                it > 0 -> it.roundToInt()
                else -> userSettingsController.settings.hrMaximum
            }
        }
        return IntensityZoneLimits.createHrIntensityZoneLimits(heartRateMax, intensityExtension?.intensityZones?.hr)
    }

    private fun getZoneDurations(
        hrEvents: List<WorkoutHrEvent>,
        limits: List<Float>,
        intensityExtension: IntensityExtension?
    ): List<Long> {
        return intensityExtension?.intensityZones?.hr?.let { zones ->
            listOf(
                zones.zone1Duration,
                zones.zone2Duration,
                zones.zone3Duration,
                zones.zone4Duration,
                zones.zone5Duration
            )
                .map { Duration.ofSeconds(it.toLong()).toMillis() }
        } ?: getZoneDurationsFromHrEvents(hrEvents, limits)
    }

    abstract fun getGraphType(): GraphType

    abstract fun getAvgHr(
        multisportPartActivity: MultisportPartActivity?,
        sml: Sml?,
        workoutHeader: WorkoutHeader,
        hrEvents: List<WorkoutHrEvent>
    ): Int

    abstract suspend fun getHeartRateData(workoutHeader: WorkoutHeader)
}
private fun WorkoutHrEvent.asEntry() = Entry(millisecondsInWorkout / 1000f, heartRate.toFloat())
