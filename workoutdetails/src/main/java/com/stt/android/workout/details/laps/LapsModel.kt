package com.stt.android.workout.details.laps

import android.content.Context
import android.content.res.Resources
import android.view.View
import android.widget.Button
import android.widget.TextView
import androidx.percentlayout.widget.PercentFrameLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.domain.user.ActivityTypeHelper
import com.stt.android.domain.user.LapSettingHelper
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workout.SpeedPaceState
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.laps.CompleteLap
import com.stt.android.laps.Laps
import com.stt.android.laps.LapsAdapter
import com.stt.android.laps.LapsPercentLayoutUtils
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.utils.ViewHelper
import com.stt.android.utils.StringUtils.capitalizeFirst
import com.stt.android.workout.details.LapsData
import com.stt.android.workout.details.R
import java.lang.ref.WeakReference
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

@EpoxyModelClass
abstract class LapsModel : EpoxyModelWithHolder<LapsViewHolder>(), View.OnClickListener {
    private val buttons: HashMap<Laps.Type, Button> = HashMap(Laps.Type.entries.size)
    private var selectedLapsType: Laps.Type? = null
    private lateinit var viewHolder: WeakReference<LapsViewHolder>
    private lateinit var lapsAdapter: LapsAdapter

    @EpoxyAttribute
    lateinit var activityType: ActivityType

    @EpoxyAttribute
    lateinit var workoutHeader: WorkoutHeader

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var lapsData: LapsData

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var infoModelFormatter: InfoModelFormatter

    override fun getDefaultLayout() = R.layout.model_laps

    override fun bind(holder: LapsViewHolder) {
        if (::viewHolder.isInitialized) viewHolder.clear()
        viewHolder = WeakReference(holder)
        buttons.clear()
        val context = holder.lapsRecyclerView.context
        val unit = infoModelFormatter.unit
        setSelectedLapsType(context, activityType, unit)
        holder.setupRecyclerView(context)
        holder.setupButtons(context, unit)
        holder.updateLabels(context, workoutHeader, unit)
        onLapsChange(if (activityType.isSlopeSki) lapsData.manualLaps else getCurrentLaps())
    }

    override fun unbind(holder: LapsViewHolder) {
        super.unbind(holder)
        viewHolder.clear()
    }

    private fun LapsViewHolder.updateLabels(
        context: Context,
        workoutHeader: WorkoutHeader,
        unit: MeasurementUnit
    ) {
        val header = workoutHeader
        updateSpeedPaceLabel(context, header, unit)
        if (activityType.isSlopeSki) {
            lapsTypeSelector.visibility = View.GONE
            lapAscentOrSkiDistanceTitle.text = capitalizeFirst(context.getString(unit.distanceUnit))
            lapDistanceOrSkiRunTitle.setText(BaseR.string.ski_run_number_label)
        } else {
            lapDistanceOrSkiRunTitle.text = capitalizeFirst(
                context.getString(
                    if (activityType.usesNauticalUnits) CR.string.nautical_mile else unit.distanceUnit
                )
            )
            lapsTypeSelector.visibility = View.VISIBLE
        }
    }

    private fun setSelectedLapsType(
        context: Context,
        activityType: ActivityType,
        unit: MeasurementUnit
    ) {
        selectedLapsType = LapSettingHelper.readLapType(context, activityType.id)
        if (unit == MeasurementUnit.IMPERIAL) {
            // 2-mile lap is always hidden
            if (selectedLapsType == Laps.Type.TWO) {
                selectedLapsType = Laps.Type.DEFAULT
            }
        } else if (unit == MeasurementUnit.METRIC) {
            // 10-km lap is hidden for foot-based activities, and 0.5-km lap is hidden for others
            if (activityType.isByFoot) {
                if (selectedLapsType == Laps.Type.TEN) {
                    selectedLapsType = Laps.Type.DEFAULT
                }
            } else {
                if (selectedLapsType == Laps.Type.HALF) {
                    selectedLapsType = Laps.Type.DEFAULT
                }
            }
        }

        if (!lapsData.hasManualLaps && selectedLapsType == Laps.Type.MANUAL) {
            selectedLapsType = Laps.Type.DEFAULT
        }
    }

    private fun LapsViewHolder.updateSpeedPaceLabel(
        context: Context,
        workoutHeader: WorkoutHeader,
        unit: MeasurementUnit
    ) {
        val label: String = when (ActivityTypeHelper.getDefaultSpeedPaceState(workoutHeader.activityType)) {
            SpeedPaceState.SPEED -> {
                context.getString(BaseR.string.speed_lap_label, context.getString(unit.speedUnit))
            }
            SpeedPaceState.SPEEDKNOTS -> {
                context.getString(BaseR.string.speed_lap_label, context.getString(CR.string.knots))
            }
            SpeedPaceState.PACE -> {
                context.getString(
                    BaseR.string.pace_lap_label,
                    context.getString(CR.string.minute) + context.getString(unit.paceUnit)
                )
            }
            else -> {
                context.getString(
                    BaseR.string.pace_lap_label,
                    context.getString(CR.string.minute) + context.getString(unit.paceUnit)
                )
            }
        }
        lapAvgSpeedTitle.text = label
    }

    private fun LapsViewHolder.setupRecyclerView(context: Context) {
        lapsRecyclerView.layoutManager = LinearLayoutManager(
            context,
            RecyclerView.VERTICAL,
            false
        )
        lapsAdapter = LapsAdapter(context, true, activityType, infoModelFormatter.unit)
        lapsRecyclerView.adapter = lapsAdapter
        // If enabled it will mess with the scrolling of the parent view
        lapsRecyclerView.isNestedScrollingEnabled = false
    }

    private fun LapsViewHolder.setupButtons(context: Context, unit: MeasurementUnit) {
        if (unit == MeasurementUnit.IMPERIAL) {
            // we always show 0.5, 1, 5 and 10-mile laps
            buttons[Laps.Type.HALF] = automaticHalf
            buttons[Laps.Type.ONE] = automaticOne
            buttons[Laps.Type.FIVE] = automaticFive
            buttons[Laps.Type.TEN] = automaticTen

            // meaning we always hide 2-mile lap
            ViewHelper.setVisibility(automaticTwo, View.GONE)
        } else {
            // for foot-based activities, we show 0.5, 1, 2 and 5-km laps
            // for others we show 1, 2, 5 and 10-km laps

            // meaning we always show 1, 2 and 5-km laps
            buttons[Laps.Type.ONE] = automaticOne
            buttons[Laps.Type.TWO] = automaticTwo
            buttons[Laps.Type.FIVE] = automaticFive
            if (activityType.isByFoot) {
                buttons[Laps.Type.HALF] = automaticHalf
                ViewHelper.setVisibility(automaticTen, View.GONE)
            } else {
                buttons[Laps.Type.TEN] = automaticTen
                ViewHelper.setVisibility(automaticHalf, View.GONE)
            }
        }

        buttons[Laps.Type.MANUAL] = manual

        val resources: Resources = context.resources
        for ((lapType, button) in buttons) {
            button.tag = lapType
            button.setOnClickListener(this@LapsModel)
            button.text = lapType.getTitle(resources, unit)
        }
        buttons[selectedLapsType]?.isEnabled = false
        if (activityType.isSlopeSki) {
            toggleSkiLapsVisibility(false)
        }

        for ((_, button) in buttons) {
            if (button.tag == Laps.Type.MANUAL && !lapsData.hasManualLaps) {
                buttons[selectedLapsType]?.isEnabled = false
                ViewHelper.setVisibility(button, View.GONE)
            } else {
                ViewHelper.setVisibility(button, View.VISIBLE)
            }
        }
    }

    override fun onClick(v: View?) {
        if (v is Button) {
            buttons[selectedLapsType]!!.isEnabled = true
            v.setEnabled(false)
            selectedLapsType = v.getTag() as Laps.Type
            LapSettingHelper.saveLapType(
                v.context,
                activityType.id,
                selectedLapsType
            )
            onLapsChange(getCurrentLaps())
        }
    }

    private fun getCurrentLaps(): Laps? {
        return when (selectedLapsType) {
            Laps.Type.MANUAL -> lapsData.manualLaps
            Laps.Type.HALF,
            Laps.Type.ONE,
            Laps.Type.TWO,
            Laps.Type.FIVE,
            Laps.Type.TEN -> lapsData.automaticLaps?.getLaps(selectedLapsType)
            else -> throw IllegalArgumentException("Unsupported lap type $selectedLapsType")
        }
    }

    private fun onLapsChange(laps: Laps?) {
        val completeLaps: MutableList<CompleteLap?>
        var fastestLapIndex: Int
        if (laps == null) {
            completeLaps = mutableListOf()
            fastestLapIndex = 0
        } else {
            // Defensive copy so we can reverse it
            completeLaps = ArrayList(laps.completeLaps)
            completeLaps.reverse()
            fastestLapIndex = laps.fastestLapIndex
            fastestLapIndex = completeLaps.size - 1 - fastestLapIndex
        }
        if (activityType.isSlopeSki) {
            toggleSkiLapsVisibility(completeLaps.isNotEmpty())
        }
        val hasHr: Boolean = lapsAdapter.setLaps(
            completeLaps,
            activityType,
            infoModelFormatter.unit,
            fastestLapIndex
        )
        updateVisibleColumns(hasHr, activityType)
    }

    private fun toggleSkiLapsVisibility(visible: Boolean) {
        viewHolder.get()?.noLapsText?.visibility = if (visible) {
            View.GONE
        } else {
            View.VISIBLE
        }
    }

    private fun updateVisibleColumns(hasHr: Boolean, activityType: ActivityType) {
        val holder = viewHolder.get() ?: return
        with(holder) {
            if (!hasHr) {
                LapsPercentLayoutUtils.hideColumnAndResize(columnTitles, lapHrTitle)
            }
            if (activityType.isIndoor) {
                LapsPercentLayoutUtils.hideColumnAndResize(
                    columnTitles,
                    lapAscentOrSkiDistanceTitle
                )
                LapsPercentLayoutUtils.hideColumnAndResize(columnTitles, lapDescentTitle)
            }
        }
    }
}

class LapsViewHolder : KotlinEpoxyHolder() {
    val lapsRecyclerView by bind<RecyclerView>(BaseR.id.laps)
    val automaticHalf by bind<Button>(BaseR.id.automaticHalf)
    val automaticOne by bind<Button>(BaseR.id.automaticOne)
    val automaticTwo by bind<Button>(BaseR.id.automaticTwo)
    val automaticFive by bind<Button>(BaseR.id.automaticFive)
    val automaticTen by bind<Button>(BaseR.id.automaticTen)
    val manual by bind<Button>(BaseR.id.manual)
    val noLapsText by bind<TextView>(BaseR.id.no_laps_text)
    val columnTitles by bind<PercentFrameLayout>(BaseR.id.lapColumnTitles)
    val lapHrTitle by bind<TextView>(BaseR.id.lapHrTitle)
    val lapAscentOrSkiDistanceTitle by bind<TextView>(BaseR.id.lapAscentOrSkiDistanceTitle)
    val lapDistanceOrSkiRunTitle by bind<TextView>(BaseR.id.lapDistanceOrSkiRunTitle)
    val lapDescentTitle by bind<TextView>(BaseR.id.lapDescentTitle)
    val lapsTypeSelector by bind<View>(R.id.lapsTypeSelector)
    val lapAvgSpeedTitle by bind<TextView>(BaseR.id.lapAvgSpeedTitle)
}
