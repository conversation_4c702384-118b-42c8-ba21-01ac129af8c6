package com.stt.android.workout.details.intensity

import android.content.Context
import com.soy.algorithms.intensity.IntensityZonesData
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.core.domain.GraphType
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.workoutextension.IntensityExtension
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.infomodel.ActivityMapping
import com.stt.android.infomodel.SummaryGraph
import com.stt.android.infomodel.SummaryItem
import com.stt.android.infomodel.getActivitySummaryForActivity
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.utils.ZoneSenseUtils
import com.stt.android.utils.firstOfType
import com.stt.android.workout.details.AerobicIqGraphData
import com.stt.android.workout.details.AnalysisDiveGasData
import com.stt.android.workout.details.DefaultHeartRateLoader
import com.stt.android.workout.details.HeartRateData
import com.stt.android.workout.details.HeartRateInThreeMinsLoader
import com.stt.android.workout.details.HrGraphData
import com.stt.android.workout.details.NavigationEventDispatcher
import com.stt.android.workout.details.WorkoutAnalysisData
import com.stt.android.workout.details.WorkoutDetailsFullscreenChartNavEvent
import com.stt.android.workout.details.WorkoutExtensionsData
import com.stt.android.workout.details.ZoneAnalysisChartData
import com.stt.android.workout.details.ZoneAnalysisData
import com.stt.android.workout.details.ZoneAnalysisGraphType
import com.stt.android.workout.details.ZonedAnalysisYAxisType
import com.stt.android.workout.details.analysis.WorkoutAnalysisDataLoader
import com.stt.android.workout.details.charts.AnalysisGraphXValueType
import com.stt.android.workout.details.charts.WorkoutChartValueFormatter
import com.stt.android.workout.details.charts.WorkoutLineChartData
import com.stt.android.workout.details.charts.WorkoutLineEntry
import com.stt.android.workout.details.charts.isSafeValue
import com.stt.android.workout.details.heartrate.HeartRateDataLoader
import com.stt.android.workout.details.multisport.MultisportPartActivityLoader
import com.stt.android.workout.details.sml.SmlDataLoader
import com.stt.android.workout.details.watch.WorkoutExtensionsDataLoader
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.conflate
import kotlinx.coroutines.flow.dropWhile
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.util.Locale
import javax.inject.Inject
import kotlin.math.max
import kotlin.math.min
import kotlin.math.roundToInt
import kotlin.time.Duration
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration.Companion.seconds
import com.stt.android.coroutines.combine as combineMulti

interface ZoneAnalysisDataLoader {
    suspend fun loadZoneAnalysisData(
        workoutHeader: WorkoutHeader
    ): Flow<ViewState<ZoneAnalysisData>>

    suspend fun update(workoutHeader: WorkoutHeader)
}

@ActivityRetainedScoped
class DefaultZoneAnalysisDataLoader @Inject constructor(
    private val workoutAnalysisDataLoader: WorkoutAnalysisDataLoader,
    private val workoutExtensionsDataLoader: WorkoutExtensionsDataLoader,
    @DefaultHeartRateLoader private val heartRateDataLoader: HeartRateDataLoader,
    @HeartRateInThreeMinsLoader private val recoveryHeartRateInThreeMinsLoader: HeartRateDataLoader,
    private val multisportPartActivityLoader: MultisportPartActivityLoader,
    private val infoModelFormatter: InfoModelFormatter,
    private val navigationEventDispatcher: NavigationEventDispatcher,
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope,
    private val measurementUnit: MeasurementUnit,
    private val getAerobicIqGraphDataUseCase: GetAerobicIqGraphDataUseCase,
    private val smlDataLoader: SmlDataLoader,
    private val zoneSenseUtils: ZoneSenseUtils,
) : ZoneAnalysisDataLoader {

    private val zoneAnalysisFlowMutable = MutableStateFlow<ViewState<ZoneAnalysisData>>(loading())

    private val selectedGraphTypesFlow = MutableStateFlow(SelectedGraphTypes.EMPTY)

    private var initialGraphTypes: Pair<GraphType, GraphType>? = null

    private lateinit var workoutHeader: WorkoutHeader

    override suspend fun loadZoneAnalysisData(
        workoutHeader: WorkoutHeader
    ): Flow<ViewState<ZoneAnalysisData>> {
        this.workoutHeader = workoutHeader
        update(workoutHeader)
        return zoneAnalysisFlowMutable
    }

    override suspend fun update(workoutHeader: WorkoutHeader) {
        activityRetainedCoroutineScope.launch {
            getZoneAnalysisData(workoutHeader)
        }
    }

    private fun getZoneAnalysisData(workoutHeader: WorkoutHeader) {
        activityRetainedCoroutineScope.launch(IO) {
            val hrDataFlow = heartRateDataLoader.hrGraphDataStateFlow
            val workoutExtensionFlow = workoutExtensionsDataLoader.workoutExtensionsStateFlow
            val workoutAnalysisDataFlow = workoutAnalysisDataLoader.loadWorkoutAnalysisData(
                workoutHeader,
                AnalysisGraphXValueType.DURATION,
                activityRetainedCoroutineScope
            )
            val multisportPartFlow = multisportPartActivityLoader.multisportPartActivityFlow
            combineMulti(
                hrDataFlow,
                workoutExtensionFlow.dropWhile { it.isLoading() },
                workoutAnalysisDataFlow.dropWhile { it.isLoading() },
                multisportPartFlow.dropWhile { it.isLoading() },
                selectedGraphTypesFlow,
                if (zoneSenseUtils.isZoneSenseEnabled(workoutHeader.id)) {
                    smlDataLoader.loadSml(workoutHeader).dropWhile { it.isLoading() }
                } else {
                    flowOf(ViewState.Loaded(null))
                },
                recoveryHeartRateInThreeMinsLoader.loadHeartRateData(workoutHeader, activityRetainedCoroutineScope).dropWhile { it.isLoading() }
            ) { hrData, extensionData, workoutAnalysisData, multisportPartData, selectedGraphTypesData, smlData, recoveryHRInThreeMinsData ->
                listOf(
                    hrData,
                    extensionData,
                    workoutAnalysisData,
                    multisportPartData,
                    selectedGraphTypesData,
                    smlData,
                    recoveryHRInThreeMinsData
                )
            }
                .conflate()
                .onEach { loadedData ->
                    processLoadedData(loadedData)
                }.collect()
        }
    }

    @Suppress("UNCHECKED_CAST")
    private suspend fun processLoadedData(loadedData: List<Any>) {
        val hrState = loadedData[0] as ViewState<HeartRateData?>
        val extensionDataState = loadedData[1] as ViewState<WorkoutExtensionsData?>
        val workoutAnalysisDataState = loadedData[2] as ViewState<WorkoutAnalysisData?>
        val multisportPartState = loadedData[3] as ViewState<MultisportPartActivity?>
        val selectedGraphTypes = loadedData[4] as SelectedGraphTypes
        val smlData = loadedData[5] as ViewState<Sml?>
        val recoveryHRInThreeMinsData = loadedData[6] as ViewState<HeartRateData?>

        val fullAnalysisData = workoutAnalysisDataState.data ?: return
        val multisportPart = multisportPartState.data
        val analysisData =
            multisportPart?.let { fullAnalysisData.multisportPartAnalysisData[it] }
                ?: fullAnalysisData

        val hrGraphData = hrState.data?.let {
            if (multisportPart != null) {
                it.multisportPartGraphData[multisportPart]
            } else {
                it.graphData
            }
        }?.takeIf { hr -> hr.hasValidData() }

        val recoveryHRInThreeMinsGraphData = recoveryHRInThreeMinsData.data?.let {
            if (multisportPart != null) {
                it.multisportPartGraphData[multisportPart]
            } else {
                it.graphData
            }
        }?.takeIf { hr -> hr.hasValidData() }

        val intensityExtension: IntensityExtension? =
            extensionDataState.data?.workoutExtensions?.firstOfType()

        val stId = multisportPart?.activityType
            ?: fullAnalysisData.pagerData.workoutHeader.activityTypeId

        val aerobicIqGraphData = getAerobicIqGraphDataUseCase(
            workoutId = workoutHeader.id,
            sml = smlData.data,
            multisportPartActivity = multisportPart,
        )

        val availableGraphTypesForWorkout = getAvailableGraphTypesForWorkout(
            graphDataList = analysisData.pagerData.graphData,
            hrGraphData = hrGraphData,
            aerobicIqGraphData = aerobicIqGraphData,
            recoveryHRInThreeMinsData = recoveryHRInThreeMinsGraphData
        ).sortedBy {
            getSupportedGraphTypesForActivityType(stId).indexOf(it)
                .takeIf { index -> index >= 0 } ?: Int.MAX_VALUE
        }

        val nonUsedAvailableGraphTypes =
            availableGraphTypesForWorkout.toMutableSet()

        val mainGraphType =
            getValidGraphType(selectedGraphTypes.mainGraphType, nonUsedAvailableGraphTypes)
        nonUsedAvailableGraphTypes.remove(mainGraphType)

        val secondaryGraphType =
            getValidGraphType(selectedGraphTypes.secondaryGraphType, nonUsedAvailableGraphTypes)
        nonUsedAvailableGraphTypes.remove(secondaryGraphType)

        if (initialGraphTypes == null) {
            initialGraphTypes = mainGraphType to secondaryGraphType
        }

        val mainGraphData = getChartAnalysisData(
            graphType = mainGraphType,
            lineChartData = analysisData.pagerData.graphData,
            hrGraphData = hrGraphData,
            aerobicIqGraphData = aerobicIqGraphData,
            recoveryHRInThreeMinsData = recoveryHRInThreeMinsGraphData,
        )
        val secondaryGraphData = getChartAnalysisData(
            graphType = secondaryGraphType,
            lineChartData = analysisData.pagerData.graphData,
            hrGraphData = hrGraphData,
            aerobicIqGraphData = aerobicIqGraphData,
            recoveryHRInThreeMinsData = recoveryHRInThreeMinsGraphData,
        )

        val mainGraphZoneLimits = mainGraphData?.let { data ->
            IntensityZoneUtils.getMainGraphZoneLimits(
                mainGraphType = mainGraphType,
                graphDataMaxYValue = data.dataMaxYValue,
                graphDataMinYValue = data.dataMinYValue,
                measurementUnit = measurementUnit,
                hrGraphData = hrGraphData,
                intensityExtension = intensityExtension
            )
        }

        val mainGraphZoneDurations = if (multisportPart == null) {
            getMainGraphZoneDurations(
                mainGraphType = mainGraphType,
                hrGraphData = hrGraphData,
                intensityExtension = intensityExtension
            )
        } else {
            null
        }

        val isSwimming = ActivityType.valueOf(stId).isSwimming

        zoneAnalysisFlowMutable.value = loaded(
            ZoneAnalysisData(
                mainGraphData = mainGraphData,
                secondaryGraphData = secondaryGraphData,
                mainGraphZoneLimits = mainGraphZoneLimits,
                mainGraphDurations = mainGraphZoneDurations,
                isSwimming = isSwimming,
                multisportPart = multisportPart,
                onGraphTypeSelected = ::onGraphTypeSelected,
                availableGraphTypes = nonUsedAvailableGraphTypes,
                onSwitchClicked = ::onSwitchClicked,
                onFullScreenAnalysisClicked = ::onFullScreenAnalysisClicked,
                infoModelFormatter = infoModelFormatter,
                vo2Max = aerobicIqGraphData?.vo2Max,
                anaerobic = aerobicIqGraphData?.anaerobic,
                aerobic = aerobicIqGraphData?.aerobic,
            )
        )
    }

    private fun getChartAnalysisData(
        graphType: GraphType,
        lineChartData: List<WorkoutLineChartData>,
        hrGraphData: HrGraphData?,
        recoveryHRInThreeMinsData: HrGraphData?,
        aerobicIqGraphData: AerobicIqGraphData?,
    ): ZoneAnalysisChartData? =
        when (graphType) {
            GraphType.Summary(SummaryGraph.HEARTRATE) -> {
                hrGraphData?.let {
                    hrGraphDataToChartAnalysisData(hrGraphData, graphType)
                }
            }
            GraphType.Summary(SummaryGraph.RECOVERYHRINTHREEMINS) -> {
                recoveryHRInThreeMinsData?.let {
                    hrGraphDataToChartAnalysisData(recoveryHRInThreeMinsData, graphType)
                }
            }
            GraphType.Summary(SummaryGraph.AEROBICZONE) -> {
                aerobicIqGraphData?.let {
                    generateAerobicIqZoneAnalysisChartData(it)
                }
            }
            else -> {
                lineChartData.firstOrNull { it.graphType == graphType }?.let {
                    workoutLineChartDataToChartAnalysisData(it)
                }
            }
        }

    private fun getValidGraphType(
        selectedGraphType: GraphType?,
        availableGraphTypes: Set<GraphType>
    ): GraphType {
        return if (selectedGraphType != null && availableGraphTypes.contains(selectedGraphType) || selectedGraphType == GraphType.NONE) {
            selectedGraphType
        } else {
            availableGraphTypes.firstOrNull() ?: GraphType.NONE
        }
    }

    private fun getMainGraphZoneDurations(
        mainGraphType: GraphType,
        hrGraphData: HrGraphData?,
        intensityExtension: IntensityExtension?,
    ): List<Duration>? = when (mainGraphType) {
        GraphType.Summary(SummaryGraph.HEARTRATE),
        GraphType.Summary(SummaryGraph.RECOVERYHRINTHREEMINS) ->
            hrGraphData?.durations?.map { it.milliseconds }
        GraphType.Summary(SummaryGraph.PACE) ->
            intensityExtension?.intensityZones?.speed?.durations()
        GraphType.Summary(SummaryGraph.POWER) ->
            intensityExtension?.intensityZones?.power?.durations()
        else -> null
    }?.takeIf { durations -> durations.any { it != Duration.ZERO } }

    private fun IntensityZonesData.durations(): List<Duration> = listOf(
        zone1Duration,
        zone2Duration,
        zone3Duration,
        zone4Duration,
        zone5Duration,
    ).map { zoneDuration -> zoneDuration.toDouble().seconds }

    data class SelectedGraphTypes(
        val mainGraphType: GraphType?,
        val secondaryGraphType: GraphType?
    ) {
        companion object {
            val EMPTY = SelectedGraphTypes(
                mainGraphType = null,
                secondaryGraphType = null
            )
        }
    }

    private fun onSwitchClicked(mainGraphType: GraphType, secondaryGraphType: GraphType) {
        selectedGraphTypesFlow.update {
            SelectedGraphTypes(
                mainGraphType = secondaryGraphType,
                secondaryGraphType = mainGraphType
            )
        }
    }

    private fun onGraphTypeSelected(graphType: GraphType, yAxis: ZonedAnalysisYAxisType) {
        selectedGraphTypesFlow.update {
            when (yAxis) {
                ZonedAnalysisYAxisType.MAIN -> {
                    val secondaryGraphType =
                        if (graphType == GraphType.Summary(SummaryGraph.RECOVERYHRINTHREEMINS)) ZoneAnalysisGraphType.EMPTY.graphType
                        else it.secondaryGraphType ?: initialGraphTypes?.second
                    SelectedGraphTypes(
                        mainGraphType = graphType,
                        secondaryGraphType = secondaryGraphType
                    )
                }

                ZonedAnalysisYAxisType.SECONDARY -> {
                    val mainGraphType = it.mainGraphType ?: initialGraphTypes?.first
                    SelectedGraphTypes(
                        mainGraphType = mainGraphType,
                        secondaryGraphType = graphType
                    )
                }
            }
        }
    }

    private fun onFullScreenAnalysisClicked(graphType: GraphType, multisportPart: MultisportPartActivity?) {
        navigationEventDispatcher.dispatchEvent(
            WorkoutDetailsFullscreenChartNavEvent(
                workoutHeader,
                graphType,
                analyticsSource = AnalyticsPropertyValue.WorkoutAnalysisScreenSource.INSIGHTS_GRAPH,
                multisportPartActivity = multisportPart,
            )
        )
    }

    private fun getAvailableGraphTypesForWorkout(
        graphDataList: List<WorkoutLineChartData>,
        hrGraphData: HrGraphData?,
        recoveryHRInThreeMinsData: HrGraphData?,
        aerobicIqGraphData: AerobicIqGraphData?
    ): Set<GraphType> {
        return buildSet {
            graphDataList
                .filter { it.data.isNotEmpty() }
                .mapTo(this) { it.graphType }
            if (hrGraphData != null) {
                add(GraphType.Summary(SummaryGraph.HEARTRATE))
            }
            if (recoveryHRInThreeMinsData != null) {
                add(GraphType.Summary(SummaryGraph.RECOVERYHRINTHREEMINS))
            }
            if (aerobicIqGraphData != null && zoneSenseUtils.isZoneSenseEnabled(workoutHeader.id)) {
                add(GraphType.Summary(SummaryGraph.AEROBICZONE))
            }
        }
    }

    private fun hrGraphDataToChartAnalysisData(hrGraphData: HrGraphData, graphType: GraphType): ZoneAnalysisChartData {
        val entries = hrGraphData.entries

        var minXValue = Float.MAX_VALUE
        var maxXValue = Float.MIN_VALUE
        var minYValue = Float.MAX_VALUE
        var maxYValue = Float.MIN_VALUE

        entries.forEach { entry ->
            minXValue = min(entry.x, minXValue)
            maxXValue = max(entry.x, maxXValue)
            minYValue = min(entry.y, minYValue)
            maxYValue = max(entry.y, maxYValue)
        }

        val workoutLineChartData = WorkoutLineChartData(
            graphType = graphType,
            xValueType = AnalysisGraphXValueType.DURATION,
            data = listOf(WorkoutLineEntry(entries = entries)),
            minAllowedValue = 0f,
            formatter = object : WorkoutChartValueFormatter {
                override fun formatXValue(value: Float, context: Context): String {
                    return when {
                        value == 0.0f -> "0"
                        value.isSafeValue -> infoModelFormatter.formatValue(SummaryItem.DURATION, value).value.orEmpty()
                        else -> ""
                    }
                }

                override fun formatConvertedYValue(valueInUserUnits: Float) =
                    valueInUserUnits.roundToInt().toString()
            },
            infoModelFormatter = infoModelFormatter
        )
        return ZoneAnalysisChartData(
            lineChartData = workoutLineChartData,
            dataMinXValue = minXValue,
            dataMaxXValue = maxXValue,
            dataMinYValue = minYValue,
            dataMaxYValue = maxYValue
        )
    }

    private fun generateAerobicIqZoneAnalysisChartData(aerobicIqGraphData: AerobicIqGraphData): ZoneAnalysisChartData {
        var minXValue = Float.MAX_VALUE
        var maxXValue = Float.MIN_VALUE
        var minYValue = Float.MAX_VALUE
        var maxYValue = Float.MIN_VALUE

        aerobicIqGraphData.entries.forEach { entry ->
            minXValue = min(entry.x, minXValue)
            maxXValue = max(entry.x, maxXValue)
            minYValue = min(entry.y, minYValue)
            maxYValue = max(entry.y, maxYValue)
        }
        return ZoneAnalysisChartData(
            lineChartData = WorkoutLineChartData(
                graphType = GraphType.Summary(SummaryGraph.AEROBICZONE),
                xValueType = AnalysisGraphXValueType.DURATION,
                data = listOf(WorkoutLineEntry(aerobicIqGraphData.entries)),
                isInverted = true,
                formatter = object : WorkoutChartValueFormatter {
                    override fun formatXValue(value: Float, context: Context): String {
                        return when {
                            value == 0.0f -> "0"
                            value.isSafeValue -> infoModelFormatter.formatValue(SummaryItem.DURATION, value).value.orEmpty()
                            else -> ""
                        }
                    }

                    override fun formatConvertedYValue(valueInUserUnits: Float): String {
                        return DecimalFormat(
                            "#.#",
                            DecimalFormatSymbols.getInstance(Locale.US)
                        ).format(valueInUserUnits)
                    }
                },
                infoModelFormatter = infoModelFormatter
            ),
            dataMinXValue = minXValue,
            dataMaxXValue = maxXValue,
            dataMinYValue = minYValue,
            dataMaxYValue = maxYValue,
        )
    }

    private fun workoutLineChartDataToChartAnalysisData(workoutLineChartData: WorkoutLineChartData): ZoneAnalysisChartData {
        var minXValue = Float.MAX_VALUE
        var maxXValue = Float.MIN_VALUE
        var minYValue = Float.MAX_VALUE
        var maxYValue = Float.MIN_VALUE
        workoutLineChartData.data.forEach { lineEntry ->
            lineEntry.entries.forEach { entry ->
                minXValue = min(entry.x, minXValue)
                maxXValue = max(entry.x, maxXValue)

                minYValue = min(entry.y, minYValue)
                maxYValue = max(entry.y, maxYValue)
            }
        }
        return ZoneAnalysisChartData(
            lineChartData = workoutLineChartData,
            diveGases = getDiveGases(workoutLineChartData),
            dataMinXValue = minXValue,
            dataMaxXValue = maxXValue,
            dataMinYValue = minYValue,
            dataMaxYValue = maxYValue
        )
    }

    private fun getDiveGases(workoutLineChartData: WorkoutLineChartData): List<AnalysisDiveGasData> {
        return if (workoutLineChartData.graphType == GraphType.Summary(SummaryGraph.TANKPRESSURE) ||
            workoutLineChartData.graphType == GraphType.Summary(SummaryGraph.GASCONSUMPTION)
        ) {
            workoutLineChartData.data.mapNotNull { workoutLineEntry ->
                workoutLineEntry.lineColor?.let { color ->
                    AnalysisDiveGasData(name = workoutLineEntry.name, color = color)
                }
            }
        } else {
            emptyList()
        }
    }

    private fun getSupportedGraphTypesForActivityType(activityTypeId: Int): List<GraphType.Summary> {
        val activityMapping: ActivityMapping? = ActivityMapping.entries
            .firstOrNull { it.stId == activityTypeId }
        return getActivitySummaryForActivity(activityMapping).analysisGraphs
            .map { GraphType.Summary(it) }
    }
}
