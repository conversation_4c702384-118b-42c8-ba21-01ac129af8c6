package com.stt.android.workout.details

import android.os.Bundle
import android.view.View
import androidx.core.os.BundleCompat
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.airbnb.epoxy.EpoxyVisibilityTracker
import com.google.android.material.snackbar.Snackbar
import com.stt.android.R
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.common.ui.SimpleProgressDialogFragment
import com.stt.android.common.ui.observeNotNull
import com.stt.android.common.viewstate.ViewState
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.extensions.combineLatest
import com.stt.android.home.explore.routes.RouteView
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.components.WeatherConditionsView
import com.stt.android.ui.extensions.requireTheme
import com.stt.android.ui.utils.WideScreenPaddingDecoration
import com.stt.android.ui.utils.setOnClickListenerThrottled
import com.stt.android.utils.permissions.DefaultEasyPermissionsHandler
import com.stt.android.utils.permissions.EasyPermissionsHandler
import com.stt.android.workout.details.laps.advanced.AdvancedLapsViewModel
import com.stt.android.workout.details.photopager.MediaPageTracker
import javax.inject.Inject

abstract class BaseWorkoutDetailsFragment :
    Fragment(),
    EasyPermissionsHandler by DefaultEasyPermissionsHandler() {

    protected abstract val workoutDetailsController: BaseWorkoutDetailsController

    @Inject
    lateinit var coverImagePagerController: WorkoutCoverImagePagerController

    @Inject
    lateinit var mediaPageTracker: MediaPageTracker

    @Inject
    lateinit var infoModelFormatter: InfoModelFormatter

    protected val viewModel: WorkoutDetailsViewModelNew by activityViewModels()

    protected val advancedLapsViewModel: AdvancedLapsViewModel by activityViewModels()

    private val epoxyVisibilityTracker: EpoxyVisibilityTracker by lazy {
        EpoxyVisibilityTracker()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        getWorkoutDetailRV().initialize()
        setControllerLifecycle()
        loadData()
        handleData()
    }

    abstract fun getRouteView(): RouteView

    abstract fun getWeatherConditionsView(): WeatherConditionsView

    abstract fun getWorkoutDetailRV(): RecyclerView

    abstract fun getWorkoutCoverImagePager(): ViewPager2

    private fun setControllerLifecycle() {
        workoutDetailsController.viewLifecycle = viewLifecycleOwner.lifecycle
        workoutDetailsController.lifecycleScope = lifecycleScope
        coverImagePagerController.lifecycleScope = lifecycleScope
        coverImagePagerController.lifecycle = viewLifecycleOwner.lifecycle
    }

    protected open fun initView() {
        initCoverImageLoaderView(getWorkoutCoverImagePager())
    }

    protected open fun loadData() {
        viewModel.showMultisportPartActivity(getMultisportPartActivity())
        advancedLapsViewModel.loadData()
    }

    private fun getMultisportPartActivity(): MultisportPartActivity? = arguments?.let {
        BundleCompat.getParcelable(it, ARGUMENT_MULTISPORT_PART_ACTIVITY, MultisportPartActivity::class.java)
    }

    private fun handleData() {
        combineLatest(
            viewModel.viewState,
            advancedLapsViewModel.viewState,
        ).observeNotNull(this) { (workoutDetailsViewState, advancedLapsViewState) ->
            handleViewStateEvent(workoutDetailsViewState, advancedLapsViewState)
        }
    }

    private fun handleViewStateEvent(
        workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>,
        advancedLapsDataViewState: ViewState<AdvancedLapsData?>,
    ) {
        if (getMultisportPartActivity() != workoutDetailsViewState.data?.multisportPartActivity?.data) {
            return // State is not for this fragment instance, ignore
        }

        handleCoverImageViewStateEvent(workoutDetailsViewState)
        workoutDetailsViewState.data?.weatherConditions?.data?.let {
            getWeatherConditionsView().setWeatherConditions(it, infoModelFormatter)
        }
        workoutDetailsController.setData(workoutDetailsViewState, advancedLapsDataViewState)

        (workoutDetailsViewState as? ViewState.Error)
            ?.errorEvent
            ?.showErrorSnackbar()

        if (workoutDetailsViewState.data?.hideBarInfo == true) {
            setSelectButton()
        }
    }

    private fun handleCoverImageViewStateEvent(state: ViewState<WorkoutDetailsViewState?>) {
        val coverImageData = state.data?.coverImageData ?: return
        if (coverImageData.data?.coverImages?.any { it is CoverImage.RouteCoverImage } == true &&
            coverImageData.data?.multisportPartActivity != state.data?.multisportPartActivity?.data
        ) {
            return // State is not for this fragment instance, ignore
        }
        coverImagePagerController.setData(coverImageData)
        // For now, hide the share button when viewing details for a multisport part activity
        if (coverImageData.data?.isShareable == true && state.data?.multisportPartActivity?.data == null) {
            setShareButtonView(coverImageData.data)
        }
        setShowWorkoutPlaybackButton(coverImageData.data)
        getRouteView().setCoverImage(coverImageData.data)
    }

    abstract fun setShareButtonView(coverImageData: CoverImageData?)

    abstract fun setShowWorkoutPlaybackButton(coverImageData: CoverImageData?)

    protected open fun setSelectButton() {}

    private fun RouteView.setCoverImage(coverImageData: CoverImageData?) {
        if (coverImageData?.showRouteView == true) {
            visibility = View.VISIBLE
            setPoints(coverImageData.routePoints)
            setOnClickListenerThrottled {
                coverImageData.onRouteViewClick()
            }
        } else {
            visibility = View.GONE
        }
    }

    protected open fun initCoverImageLoaderView(coverImagePager: ViewPager2) {
        coverImagePager.adapter = coverImagePagerController.adapter
        mediaPageTracker.attach(coverImagePager, coverImagePagerController, viewModel)
    }

    private fun RecyclerView.initialize() {
        adapter = workoutDetailsController.adapter
        // We want to keep items from being destroyed and recreated when the workout
        // details is scrolled. Increasing the amount of items kept alive outside the
        // visible area.
        setItemViewCacheSize(20)
        epoxyVisibilityTracker.attach(this)
        addItemDecoration(WideScreenPaddingDecoration(resources, requireTheme()))
    }

    private fun RecyclerView.release() {
        adapter = null
        epoxyVisibilityTracker.detach(this)
    }

    override fun onResume() {
        super.onResume()
        viewModel.updateMapType()
    }

    protected fun ErrorEvent.showErrorSnackbar() {
        Snackbar.make(
            view ?: return,
            errorStringRes,
            if (showCloseButton) Snackbar.LENGTH_INDEFINITE else Snackbar.LENGTH_LONG
        ).apply {
            if (showCloseButton) {
                setAction(R.string.close) {}
            }
        }.show()
    }

    override fun onDestroyView() {
        coverImagePagerController.lifecycleScope = null
        coverImagePagerController.mapSnapshotter = null
        workoutDetailsController.lifecycleScope = null

        getWorkoutDetailRV().release()
        getWorkoutCoverImagePager().adapter = null

        super.onDestroyView()
    }

    protected fun showLoading() {
        val dialog =
            SimpleProgressDialogFragment.newInstance(getString(R.string.creating_share_link))
        dialog.show(childFragmentManager, SimpleProgressDialogFragment.FRAGMENT_TAG)
    }

    protected fun hideLoading() {
        val fm = childFragmentManager
        val fragment =
            fm.findFragmentByTag(SimpleProgressDialogFragment.FRAGMENT_TAG) as? DialogFragment
        fragment?.dismiss()
    }

    companion object {
        const val ARGUMENT_MULTISPORT_PART_ACTIVITY = "multisportPartActivity"
    }
}
