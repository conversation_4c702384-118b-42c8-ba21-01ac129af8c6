package com.stt.android.workout.details.intensity.composables

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.stt.android.aerobiczone.AerobicZonesInfoSheet
import com.stt.android.compose.theme.spacing
import com.stt.android.core.domain.GraphType
import com.stt.android.infomodel.SummaryGraph
import com.stt.android.workout.details.ZoneAnalysisData
import com.stt.android.workout.details.ZoneAnalysisGraphType
import com.stt.android.workout.details.intensity.GraphAnalysisBottomSection

@Composable
fun ZoneAnalysisScreen(
    availableGraphTypes: Set<ZoneAnalysisGraphType>,
    mainGraphType: ZoneAnalysisGraphType,
    secondaryGraphType: ZoneAnalysisGraphType,
    zoneAnalysisData: ZoneAnalysisData,
    showFullscreenButton: Boolean,
    modifier: Modifier = Modifier,
    onAerobicZoneInfoClick: ((dest: AerobicZonesInfoSheet) -> Unit)? = null
) {
    Surface(
        modifier = modifier.fillMaxSize(),
        color = MaterialTheme.colors.surface
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.spacing.small)
        ) {
            ZoneAnalysisHeader(
                availableGraphTypes = availableGraphTypes,
                mainGraphType = mainGraphType,
                secondaryGraphType = secondaryGraphType,
                isZoneColorsEnabled = zoneAnalysisData.mainGraphZoneLimits != null,
                diveGasesList = zoneAnalysisData.mainGraphData?.diveGases,
                multisportPart = zoneAnalysisData.multisportPart,
                showFullscreenButton = showFullscreenButton,
                onGraphTypeSelected = zoneAnalysisData.onGraphTypeSelected,
                onSwitchGraphsClicked = {
                    zoneAnalysisData.onSwitchClicked(
                        mainGraphType.graphType,
                        secondaryGraphType.graphType
                    )
                },
                onFullScreenAnalysisClicked = zoneAnalysisData.onFullScreenAnalysisClicked,
            )
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
            ZoneAnalysisChart(
                modifier = Modifier
                    .height(200.dp)
                    .fillMaxWidth(),
                zoneAnalysisData = zoneAnalysisData,
            )
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))

            zoneAnalysisData.graphAnalysisBottomSection?.let {
                when (val bottomSection = it) {
                    is GraphAnalysisBottomSection.HeartRate -> if (bottomSection.zoneDurationData.isNotEmpty()) {
                        ZoneDurations(
                            zoneDurationTitle = stringResource(bottomSection.title),
                            zoneDurationData = bottomSection.zoneDurationData,
                            onInfoClick = onAerobicZoneInfoClick?.let {
                                when (zoneAnalysisData.graphType) {
                                    GraphType.Summary(SummaryGraph.HEARTRATE) -> {
                                        {
                                            onAerobicZoneInfoClick(
                                                AerobicZonesInfoSheet.HEART_RATE
                                            )
                                        }
                                    }

                                    GraphType.Summary(SummaryGraph.PACE) -> {
                                        {
                                            onAerobicZoneInfoClick(
                                                AerobicZonesInfoSheet.PACE
                                            )
                                        }
                                    }

                                    GraphType.Summary(SummaryGraph.POWER) -> {
                                        {
                                            onAerobicZoneInfoClick(
                                                AerobicZonesInfoSheet.POWER
                                            )
                                        }
                                    }

                                    else -> null
                                }
                            }
                        )
                    }

                    is GraphAnalysisBottomSection.DiveGases -> if (bottomSection.diveGases.isNotEmpty()) {
                        ZoneAnalysisDiveGases(gasData = bottomSection.diveGases)
                    }

                    is GraphAnalysisBottomSection.AerobicZone -> {
                        if (bottomSection.hasValue) {
                            AerobicZoneDurations(
                                vo2Max = bottomSection.vo2Max,
                                anaerobic = bottomSection.anaerobic,
                                aerobic = bottomSection.aerobic,
                                onInfoClick = onAerobicZoneInfoClick?.let {
                                    {
                                        onAerobicZoneInfoClick(
                                            AerobicZonesInfoSheet.HEART_RATE
                                        )
                                    }
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}
