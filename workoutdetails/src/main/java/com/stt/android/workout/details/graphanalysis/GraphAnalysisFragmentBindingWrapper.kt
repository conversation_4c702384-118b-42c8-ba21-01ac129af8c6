package com.stt.android.workout.details.graphanalysis

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ProgressBar
import android.widget.TextView
import androidx.constraintlayout.widget.Guideline
import androidx.lifecycle.LifecycleOwner
import com.airbnb.epoxy.LifecycleAwareEpoxyViewBinder
import com.airbnb.epoxy.ModelCollector
import com.stt.android.domain.workout.ActivityType
import com.stt.android.workout.details.R
import com.stt.android.workout.details.WorkoutValuesContainer
import com.stt.android.workout.details.databinding.GraphAnalysisFragmentFullBinding
import com.stt.android.workout.details.databinding.GraphAnalysisFragmentMinimalBinding
import com.stt.android.workout.details.graphanalysis.highlight.GraphAnalysisHighlightInfoView
import com.stt.android.workout.details.graphanalysis.laps.GraphAnalysisSelectedLapView
import com.stt.android.workoutdetail.workoutvalues.composables.WorkoutValuesGridType
import com.stt.android.workout.details.workoutvalues.workoutValuesNew
import com.stt.android.workoutdetail.workoutvalues.WorkoutValuesGridData
import com.stt.android.workoutdetail.workoutvalues.WorkoutValuesGridItemData

class GraphAnalysisFragmentBindingWrapper(
    val root: View,
    val graphAnalysisChart: GraphAnalysisChart,
    val loadingIndicator: ProgressBar,
    val selectGraphTypesButton: ImageButton? = null,
    val playButton: ImageButton,
    val highlightedDurationLabel: TextView,
    val highlightedDistanceLabel: TextView,
    val highlightedDistanceUnit: TextView,
    val showFullscreenGraphAnalysisButton: ImageButton?,
    val closeFullscreenGraphAnalysisButton: ImageButton?,
    val mainGraphHighlightInfo: GraphAnalysisHighlightInfoView,
    val comparisonGraphHighlightInfo: GraphAnalysisHighlightInfoView,
    val backgroundGraphHighlightInfo: GraphAnalysisHighlightInfoView,
    val invisibleWhileLoading: View,
    val workoutValuesGrid: WorkoutValueGridWrapper?,
    val initiallyVisibleContentBottomGuide: Guideline?,
    val selectedLapView: GraphAnalysisSelectedLapView,
    val selectLapButton: ImageButton?,
    val tvSelectLapLabel: TextView?,
) {
    companion object {
        fun inflate(
            layoutInflater: LayoutInflater,
            container: ViewGroup?,
            attachToParent: Boolean,
            viewLifecycleOwner: LifecycleOwner,
            displayMode: GraphAnalysisFragment.DisplayMode
        ): GraphAnalysisFragmentBindingWrapper = when (displayMode) {
            GraphAnalysisFragment.DisplayMode.MINIMAL ->
                inflateMinimalLapsSupport(
                    layoutInflater,
                    container,
                    attachToParent,
                    viewLifecycleOwner
                )
            GraphAnalysisFragment.DisplayMode.FULL ->
                inflateFullLapsSupport(
                    layoutInflater,
                    container,
                    attachToParent,
                    viewLifecycleOwner
                )
        }

        private fun inflateMinimalLapsSupport(
            layoutInflater: LayoutInflater,
            parent: ViewGroup?,
            attachToParent: Boolean,
            viewLifecycleOwner: LifecycleOwner
        ): GraphAnalysisFragmentBindingWrapper {
            val binding =
                GraphAnalysisFragmentMinimalBinding.inflate(
                    layoutInflater,
                    parent,
                    attachToParent
                )

            return GraphAnalysisFragmentBindingWrapper(
                root = binding.root,
                graphAnalysisChart = binding.graphAnalysisChart,
                loadingIndicator = binding.loadingIndicator,
                selectGraphTypesButton = null,
                playButton = binding.playButton,
                highlightedDurationLabel = binding.highlightedDurationLabel,
                highlightedDistanceLabel = binding.highlightedDistanceLabel,
                highlightedDistanceUnit = binding.highlightedDistanceUnit,
                showFullscreenGraphAnalysisButton = binding.openFullModeButton,
                closeFullscreenGraphAnalysisButton = null,
                mainGraphHighlightInfo = binding.mainGraphHighlightInfo,
                comparisonGraphHighlightInfo = binding.comparisonGraphHighlightInfo,
                backgroundGraphHighlightInfo = binding.backgroundGraphHighlightInfo,
                invisibleWhileLoading = binding.invisibleWhileLoadingGroup,
                workoutValuesGrid = WorkoutValueGridWrapper(viewLifecycleOwner, binding.root, R.id.workout_values_root),
                initiallyVisibleContentBottomGuide = binding.initiallyVisibleContentBottomGuide,
                selectedLapView = binding.analysisSelectedLap,
                selectLapButton = binding.selectLapButton,
                tvSelectLapLabel = binding.tvSelectLapLabel,
            )
        }

        private fun inflateFullLapsSupport(
            layoutInflater: LayoutInflater,
            parent: ViewGroup?,
            attachToParent: Boolean,
            viewLifecycleOwner: LifecycleOwner
        ): GraphAnalysisFragmentBindingWrapper {
            val binding =
                GraphAnalysisFragmentFullBinding.inflate(
                    layoutInflater,
                    parent,
                    attachToParent
                )

            return GraphAnalysisFragmentBindingWrapper(
                root = binding.root,
                graphAnalysisChart = binding.graphAnalysisChart,
                loadingIndicator = binding.loadingIndicator,
                selectGraphTypesButton = null,
                playButton = binding.playButton,
                highlightedDurationLabel = binding.highlightedDurationLabel,
                highlightedDistanceLabel = binding.highlightedDistanceLabel,
                highlightedDistanceUnit = binding.highlightedDistanceUnit,
                showFullscreenGraphAnalysisButton = null,
                closeFullscreenGraphAnalysisButton = binding.closeFullscreenButton,
                mainGraphHighlightInfo = binding.mainGraphHighlightInfo,
                comparisonGraphHighlightInfo = binding.comparisonGraphHighlightInfo,
                backgroundGraphHighlightInfo = binding.backgroundGraphHighlightInfo,
                invisibleWhileLoading = binding.invisibleWhileLoadingGroup,
                workoutValuesGrid = binding.workoutValuesRoot?.let {
                    WorkoutValueGridWrapper(
                        viewLifecycleOwner,
                        binding.root,
                        R.id.workout_values_root
                    )
                },
                initiallyVisibleContentBottomGuide = null,
                selectedLapView = binding.analysisSelectedLap,
                selectLapButton = null,
                tvSelectLapLabel = null,
            )
        }
    }
}

class WorkoutValueGridWrapper(
    private val viewLifecycleOwner: LifecycleOwner,
    private val rootView: View,
    viewId: Int
) {
    private val viewBinder = LifecycleAwareEpoxyViewBinder(
        lifecycleOwner = viewLifecycleOwner,
        rootView = { rootView },
        viewId = viewId,
        useVisibilityTracking = false,
        fallbackToNameLookup = false,
        modelProvider = {
            buildWorkoutValueGrid()
        }
    )

    private var valuesAreOutdated: Boolean = false

    var workoutValuesContainer: WorkoutValuesContainer? = null
        set(value) {
            if (value == field) return

            field = value
            valuesAreOutdated = false
            viewBinder.invalidate()
        }

    fun setWorkoutValuesAreOutdated(outdated: Boolean) {
        if (valuesAreOutdated == outdated) return

        valuesAreOutdated = outdated
        viewBinder.invalidate()
    }

    private fun ModelCollector.buildWorkoutValueGrid() {
        val container = workoutValuesContainer ?: return
        val activityType = ActivityType.valueOf(container.activityType)
        val activityName = activityType.getLocalizedName(rootView.context.resources)
        val items = container.workoutValues.map {
            val valueAndUnit =
                it.value?.let { value -> "$value ${it.getUnitLabel(rootView.context)}" } ?: ""
            WorkoutValuesGridItemData(
                value = valueAndUnit,
                name = it.label,
                workoutValue = it
            )
        }

        workoutValuesNew {
            id("workoutValuesNew")
            workoutValuesGridData(
                WorkoutValuesGridData(
                    showHeader = false,
                    activityName = activityName,
                    activityIcon = activityType.iconId,
                    addTopMargin = false,
                    workoutValues = items,
                    workoutValueGroups = emptyList(),
                    showDetailsButton = false,
                    workoutValuesGridType = WorkoutValuesGridType.ANALYSIS,
                    onValueClicked = { _ -> },
                    onMultisportDetailsClicked = {},
                    onViewMoreClicked = {}
                )
            )
        }
    }
}
