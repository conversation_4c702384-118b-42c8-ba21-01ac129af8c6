package com.stt.android.workout.details.photopager

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.epoxy.EpoxyVisibilityTracker
import com.airbnb.epoxy.OnModelBuildFinishedListener
import com.google.android.material.tabs.TabLayoutMediator
import com.stt.android.common.ui.observeNotNull
import com.stt.android.common.viewstate.loaded
import com.stt.android.domain.review.ReviewState
import com.stt.android.window.setFlagsAndColors
import com.stt.android.workout.details.CoverImage
import com.stt.android.workout.details.WorkoutDetailsViewModelNew
import com.stt.android.workout.details.databinding.WorkoutPhotoPagerFragmentBinding
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class WorkoutPhotoPagerFragment : Fragment() {
    @Inject
    internal lateinit var workoutPhotoPagerController: WorkoutPhotoPagerController

    @Inject
    internal lateinit var mediaPageTracker: MediaPageTracker

    private lateinit var pagerIndicator: TabLayoutMediator
    private lateinit var binding: WorkoutPhotoPagerFragmentBinding

    private val viewModel: WorkoutDetailsViewModelNew by activityViewModels()

    private val modelBuildFinishedListener: OnModelBuildFinishedListener =
        OnModelBuildFinishedListener {
            //  We show max 5 dots. If we have more than 5 images/videos, we need to scroll the
            //  tab layout to correct position when orientation change occurs and we have to do
            //  it after the models have been built for the tab layout to have the tabs.
            val position = mediaPageTracker.currentPhotoPagerPosition
            if (position >= 0 && position < binding.pagerIndicator.tabCount) {
                binding.pagerIndicator.setScrollPosition(
                    position,
                    0f,
                    true,
                    true
                )
            }
        }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        changeStatusAndNavigationBarColor(true)
        binding = WorkoutPhotoPagerFragmentBinding.inflate(inflater, container, false)
        binding.pager.adapter = workoutPhotoPagerController.adapter
        EpoxyVisibilityTracker().attach(binding.pager.getChildAt(0) as RecyclerView)
        mediaPageTracker.attach(
            binding.pager,
            workoutPhotoPagerController,
            viewModel,
            savedInstanceState != null
        )
        pagerIndicator = TabLayoutMediator(binding.pagerIndicator, binding.pager) { _, _ -> }
        pagerIndicator.attach()
        workoutPhotoPagerController.addModelBuildListener(modelBuildFinishedListener)
        return binding.root
    }

    @SuppressLint("SourceLockedOrientationActivity")
    override fun onDestroyView() {
        super.onDestroyView()
        changeStatusAndNavigationBarColor(false)
        workoutPhotoPagerController.removeModelBuildListener(modelBuildFinishedListener)
        EpoxyVisibilityTracker().detach(binding.pager.getChildAt(0) as RecyclerView)
        pagerIndicator.detach()
        mediaPageTracker.detach()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        viewModel.viewState.observeNotNull(viewLifecycleOwner) { state ->
            Timber.d("Updating photo pager view state")
            binding.state = state
            workoutPhotoPagerController.viewLifecycle = viewLifecycleOwner.lifecycle
            workoutPhotoPagerController.lifecycleScope = lifecycleScope

            // only show the video that review state is passed
            val coverImages = state.data?.coverImageData?.data?.coverImages?.filter {
                when (it) {
                    is CoverImage.PhotoCoverImage -> it.picture.reviewState == ReviewState.PASS
                    is CoverImage.VideoCoverImage -> it.video.reviewState == ReviewState.PASS
                    is CoverImage.DefaultCoverImage,
                    is CoverImage.RouteCoverImage -> true
                }
            }
            workoutPhotoPagerController.setData(loaded(coverImages))
        }
    }

    // region Status and nav bar color
    private fun changeStatusAndNavigationBarColor(darkMode: Boolean) {
        activity?.window?.setFlagsAndColors(darkMode = darkMode)
    }
    // endregion
}
