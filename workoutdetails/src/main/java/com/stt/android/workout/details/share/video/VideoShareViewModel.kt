package com.stt.android.workout.details.share.video

import android.content.Context
import androidx.core.content.edit
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workouts.GetWorkoutByIdUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.maps.colortrack.ColorTrackDescriptor
import com.stt.android.utils.STTConstants
import com.stt.android.workout.details.graphanalysis.playback.WorkoutPlaybackGeopointLoader
import com.stt.android.workout.details.multisport.MultisportPartActivityLoader
import com.stt.android.workout.details.share.colortrack.AltitudeColorTrackGenerator
import com.stt.android.workout.details.share.colortrack.HeartRateColorTrackGenerator
import com.stt.android.workout.details.share.colortrack.PaceColorTrackerGenerator
import com.stt.android.workout.details.share.util.VideoFileUtil
import com.stt.android.workout.details.sml.SmlDataLoader
import com.stt.android.workout.details.workoutdata.WorkoutDataLoader
import com.stt.android.workouts.SyncSingleWorkoutUseCase
import com.stt.android.workouts.setSharingLinkIfPrivate
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@HiltViewModel
class VideoShareViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    savedStateHandle: SavedStateHandle,
    val dispatchers: CoroutinesDispatchers,
    getWorkoutByIdUseCase: GetWorkoutByIdUseCase,
    smlDataLoader: SmlDataLoader,
    multisportPartActivityLoader: MultisportPartActivityLoader,
    workoutPlaybackGeopointLoader: WorkoutPlaybackGeopointLoader,
    private val workoutDataLoader: WorkoutDataLoader,
    private val videoShareInfoDataLoader: VideoShareInfoDataLoader,
    private val workoutHeaderController: WorkoutHeaderController,
    private val syncSingleWorkoutUseCase: SyncSingleWorkoutUseCase,
) : ViewModel() {

    val workoutId: Int = savedStateHandle[STTConstants.ExtraKeys.WORKOUT_ID]!!

    private val _workoutHeader = MutableStateFlow<WorkoutHeader?>(null)

    private val preferences = context.workoutMapPlaybackPreferences

    private val _graphType = MutableStateFlow(WorkoutMapPlaybackGraphType.from(preferences))
    val graphType = _graphType.asStateFlow()

    private val _mapType = MutableStateFlow(WorkoutMapPlaybackMapType.from(preferences))
    val mapType = _mapType.asStateFlow()

    private val _cameraBounds = MutableStateFlow<LatLngBounds?>(null)
    val cameraBounds = _cameraBounds.asStateFlow()

    private val _startEndPoints = MutableStateFlow<Pair<WorkoutGeoPoint, WorkoutGeoPoint>?>(null)
    val startEndPoints = _startEndPoints.asStateFlow()

    private val _colorTrack = MutableStateFlow<ColorTrackDescriptor?>(null)
    val colorTrack = _colorTrack.asStateFlow()

    val infoData: StateFlow<VideoShareInfoData?>
        get() = videoShareInfoDataLoader.dataFlow

    private val _dataOptions = MutableStateFlow(WorkoutMapPlaybackOptions.from(preferences))
    val dataOptions = _dataOptions.asStateFlow()

    private val _showEditIcon = MutableStateFlow(true)
    val showEditIcon = _showEditIcon.asStateFlow()

    init {
        viewModelScope.launch {
            val workoutHeader = withContext(dispatchers.io) {
                getWorkoutByIdUseCase(workoutId).header
            }
            _workoutHeader.tryEmit(workoutHeader)
            smlDataLoader.loadSml(workoutHeader)
            multisportPartActivityLoader.setMultisportPartActivity(null)
            workoutDataLoader.loadWorkoutData(workoutHeader)
            videoShareInfoDataLoader.load(workoutHeader)
        }
        viewModelScope.launch(dispatchers.io) {
            workoutPlaybackGeopointLoader.observeGeoPointForPlayback().collectLatest {
                if (it.isEmpty()) {
                    _cameraBounds.tryEmit(null)
                    _startEndPoints.tryEmit(null)
                } else {
                    _cameraBounds.tryEmit(calcCameraBounds(it))
                    _startEndPoints.tryEmit(it.first() to it.last())
                }
            }
        }
        viewModelScope.launch(dispatchers.io) {
            combine(
                _graphType,
                workoutPlaybackGeopointLoader.observeGeoPointForPlayback(),
                smlDataLoader.smlStateFlow.filter { it.isLoaded() },
            ) { graphType, geoPoints, smlState ->
                Triple(graphType, geoPoints, smlState.data)
            }.collectLatest {
                _colorTrack.tryEmit(loadColorTrack(it.first, it.second, it.third))
            }
        }
    }

    fun setGraphType(graphType: WorkoutMapPlaybackGraphType) {
        _graphType.tryEmit(graphType)
        preferences.edit {
            putInt(STTConstants.WorkoutMapPlaybackPreferences.GRAPH_TYPE, graphType.ordinal)
        }
    }

    fun setMapType(mapType: WorkoutMapPlaybackMapType) {
        _mapType.tryEmit(mapType)
        preferences.edit {
            putInt(STTConstants.WorkoutMapPlaybackPreferences.MAP_TYPE, mapType.ordinal)
        }
    }

    fun setShowEditIcon(showEditIcon: Boolean) {
        _showEditIcon.tryEmit(showEditIcon)
    }

    fun updateDataOptions() {
        _dataOptions.tryEmit(WorkoutMapPlaybackOptions.from(preferences))
    }

    fun getOutputFile() = VideoFileUtil.getVideoOutputFile(
        context,
        workoutId,
        dataOptions.value,
        graphType.value,
        mapType.value,
    )

    private fun calcCameraBounds(geoPoints: List<WorkoutGeoPoint>): LatLngBounds {
        return LatLngBounds.Builder().apply {
            geoPoints.forEach { include(LatLng(it.latitude, it.longitude)) }
        }.build()
    }

    private suspend fun loadColorTrack(
        graphType: WorkoutMapPlaybackGraphType,
        geoPoints: List<WorkoutGeoPoint>,
        sml: Sml?,
    ): ColorTrackDescriptor {
        val generator = when (graphType) {
            WorkoutMapPlaybackGraphType.HEART_RATE -> {
                val state = workoutDataLoader.workoutDataStateFlow.first { it.isLoaded() }
                HeartRateColorTrackGenerator(state.data?.heartRateEvents ?: emptyList())
            }

            WorkoutMapPlaybackGraphType.ALTITUDE -> AltitudeColorTrackGenerator()

            WorkoutMapPlaybackGraphType.PACE -> PaceColorTrackerGenerator()
        }
        return generator.generate(geoPoints, sml)
    }

    suspend fun prepareShareLink(): WorkoutHeader? = _workoutHeader.value?.also {
        setSharingLinkIfPrivate(it, workoutHeaderController, syncSingleWorkoutUseCase)
    }
}
