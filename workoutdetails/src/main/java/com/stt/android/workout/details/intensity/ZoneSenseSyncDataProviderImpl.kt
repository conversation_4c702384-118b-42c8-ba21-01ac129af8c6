package com.stt.android.workout.details.intensity

import com.stt.android.controllers.CurrentUserController
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutDataSource
import com.stt.android.domain.workouts.ZoneSenseSyncWorkoutHeader
import com.suunto.connectivity.zonesense.ZoneSenseBaselineValuesSyncData
import com.suunto.connectivity.zonesense.ZoneSenseSyncData
import com.suunto.connectivity.zonesense.ZoneSenseSyncDataProvider
import kotlinx.coroutines.flow.firstOrNull
import javax.inject.Inject

class ZoneSenseSyncDataProviderImpl @Inject constructor(
    private val currentUserController: CurrentUserController,
    private val workoutDataSource: WorkoutDataSource,
) : ZoneSenseSyncDataProvider {
    override suspend fun getZoneSenseSyncData(): ZoneSenseSyncData {
        val workouts = workoutDataSource.getAllWorkoutsForZoneSenseSync(currentUserController.username)

        val runningWorkouts = mutableListOf<ZoneSenseSyncWorkoutHeader>()
        val cyclingWorkouts = mutableListOf<ZoneSenseSyncWorkoutHeader>()
        val swimmingWorkouts = mutableListOf<ZoneSenseSyncWorkoutHeader>()
        val skiingWorkouts = mutableListOf<ZoneSenseSyncWorkoutHeader>()
        val rowingWorkouts = mutableListOf<ZoneSenseSyncWorkoutHeader>()

        workouts.forEach { workout ->
            when (workout.activityTypeId) {
                in RUNNING_GROUP -> runningWorkouts.add(workout)
                in CYCLING_GROUP -> cyclingWorkouts.add(workout)
                in SWIMMING_GROUP -> swimmingWorkouts.add(workout)
                in SKIING_GROUP -> skiingWorkouts.add(workout)
                in ROWING_GROUP -> rowingWorkouts.add(workout)
            }
        }

        return ZoneSenseSyncData(
            baselineValues = ZoneSenseBaselineValuesSyncData(
                running = runningWorkouts.calculateCumulativeBaseline().toFloat(),
                cycling = cyclingWorkouts.calculateCumulativeBaseline().toFloat(),
                swimming = swimmingWorkouts.calculateCumulativeBaseline().toFloat(),
                skiing = skiingWorkouts.calculateCumulativeBaseline().toFloat(),
                rowing = rowingWorkouts.calculateCumulativeBaseline().toFloat(),
            )
        )
    }

    override suspend fun getPreviousBaseline(workoutId: Int, activityTypeId: Int): Double? {
        val workoutHeader = workoutDataSource.fetchWorkoutHeader(workoutId)
            .firstOrNull()
            ?: return null
        return getPreviousBaseline(
            username = workoutHeader.username,
            activityTypeId = activityTypeId,
            untilInMillsSinceEpoch = workoutHeader.startTime,
        )
    }

    override suspend fun getPreviousBaseline(
        username: String,
        activityTypeId: Int,
        untilInMillsSinceEpoch: Long,
    ): Double? {
        val activityTypeIds = when (activityTypeId) {
            in RUNNING_GROUP -> RUNNING_GROUP
            in CYCLING_GROUP -> CYCLING_GROUP
            in SWIMMING_GROUP -> SWIMMING_GROUP
            in SKIING_GROUP -> SKIING_GROUP
            in ROWING_GROUP -> ROWING_GROUP
            else -> return null
        }

        return workoutDataSource.getWorkoutsForZoneSense(
            username = username,
            activityTypeIds = activityTypeIds,
            untilInMillsSinceEpoch = untilInMillsSinceEpoch,
        ).calculateCumulativeBaseline()
            // calculateCumulativeBaseline() returns -100 if previous baseline doesn't exist.
            // The value was needed for the watch, but here this is used for the app, so need to filter.
            .takeIf { it in VALID_BASELINE_RANGE }
    }

    private companion object {
        const val INVALID_BASELINE_VALUE = -100.0
        val VALID_BASELINE_RANGE = -1.0..1.0
        const val OLD_BASELINE_WEIGHT = 0.75
        const val NEW_BASELINE_WEIGHT = 0.25

        val RUNNING_GROUP = setOf(
            ActivityType.RUNNING.id,
            ActivityType.TRAIL_RUNNING.id,
            ActivityType.TREADMILL.id,
            ActivityType.TRACK_RUNNING.id,
            ActivityType.VERTICAL_RUN.id,
        )
        val CYCLING_GROUP = setOf(
            ActivityType.CYCLING.id,
            ActivityType.INDOOR_CYCLING.id,
            ActivityType.GRAVEL_CYCLING.id,
            ActivityType.MOUNTAIN_BIKING.id,
            ActivityType.E_BIKING.id,
            ActivityType.E_MTB.id,
            ActivityType.CYCLOCROSS.id,
            ActivityType.HANDCYCLING.id,
        )
        val SWIMMING_GROUP = setOf(
            ActivityType.SWIMMING.id,
            ActivityType.OPENWATER_SWIMMING.id,
        )
        val SKIING_GROUP = setOf(
            ActivityType.CROSS_COUNTRY_SKIING.id,
            ActivityType.SKI_TOURING.id,
            ActivityType.ROLLER_SKIING.id,
            ActivityType.BACKCOUNTRY_SKIING.id,
            ActivityType.SPLITBOARDING.id,
            ActivityType.BIATHLON.id,
            ActivityType.SKI_MOUNTAINEERING.id,
            ActivityType.SKATE_SKIING.id,
            ActivityType.CLASSIC_SKIING.id,
            ActivityType.ROLLER_SKATING.id,
        )
        val ROWING_GROUP = setOf(
            ActivityType.ROWING.id,
            ActivityType.INDOOR_ROWING.id,
        )

        fun List<ZoneSenseSyncWorkoutHeader>.calculateCumulativeBaseline(): Double {
            val baselines = mapNotNull { header ->
                header.zoneSenseAerobicBaseline
                    ?.takeIf { it in VALID_BASELINE_RANGE }
            }.takeUnless(List<*>::isEmpty)
                ?: return INVALID_BASELINE_VALUE

            val baseline = baselines
                .reduce { cumulativeBaseline, baseline ->
                    cumulativeBaseline * OLD_BASELINE_WEIGHT + baseline * NEW_BASELINE_WEIGHT
                }

            return if (baseline in VALID_BASELINE_RANGE) return baseline else INVALID_BASELINE_VALUE
        }
    }
}
