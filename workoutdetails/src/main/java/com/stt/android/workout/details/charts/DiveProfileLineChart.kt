package com.stt.android.workout.details.charts

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import androidx.core.content.ContextCompat
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineDataSet
import com.stt.android.domain.sml.DiveEvent
import com.stt.android.domain.sml.StateEvent
import com.stt.android.domain.sml.StateMarkType
import com.stt.android.domain.sml.withModifiedElapsed
import com.stt.android.ui.extensions.smallIcon
import dagger.hilt.android.AndroidEntryPoint
import java.util.concurrent.TimeUnit

@AndroidEntryPoint
class DiveProfileLineChart @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : AnalysisWorkoutLineChart(context, attrs, defStyle) {

    fun setDiveChartData(workoutLineChartData: WorkoutLineChartData, showEvents: Boolean) {
        setWorkoutLineChartData(workoutLineChartData)
        if (showEvents) {
            // avoid icon overlapping axis line
            axisRight.axisMaximum /= 0.9f
            val events = workoutLineChartData.events.filterIsInstance<DiveEvent>()
            drawDiveEvents(events)
        }
    }

    private fun drawDiveEvents(events: List<DiveEvent>) {
        // drawStreamPointChart needs to be called and finished first to have the data entries
        if (data.dataSetCount == 0) return

        val dataSet = data.getDataSetByIndex(0)

        val entries = events.mapIndexedNotNull { index, event ->
            when {
                event.data.elapsed != null -> event
                index == 0 && event is StateEvent && event.type == StateMarkType.DIVE_ACTIVE ->
                    event.copy(data = event.data.withModifiedElapsed(elapsed = 0L))
                else -> null // skip
            }
        }.map { event ->
            val xValue = TimeUnit.MILLISECONDS.toSeconds(event.data.elapsed!!).toFloat()
            val yValue = dataSet.getEntryForXValue(xValue, 0f).y
            Entry(xValue, yValue, ContextCompat.getDrawable(context, event.smallIcon), event)
        }

        val eventDataSet = LineDataSet(entries, "").apply {
            enableDashedLine(0f, 1f, 0f) // disables the line connecting the entries
            axisDependency = YAxis.AxisDependency.RIGHT
            // draw only the icons
            setDrawValues(false)
            setDrawCircles(false)
            setDrawIcons(true)
            highLightColor = Color.BLACK
            highlightLineWidth = 1.0f
            setDrawHighlightIndicators(true)
            setDrawHorizontalHighlightIndicator(false)
        }
        data.addDataSet(eventDataSet)

        // The renderer will not draw values or icons if the number of entries exceed this.
        setMaxVisibleValueCount(Int.MAX_VALUE)
    }
}
