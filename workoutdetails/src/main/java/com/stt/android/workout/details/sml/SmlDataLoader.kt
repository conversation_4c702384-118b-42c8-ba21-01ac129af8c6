package com.stt.android.workout.details.sml

import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loading
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.sml.FetchSmlUseCase
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.sml.reader.SmlFactory
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.utils.traceSuspend
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

interface SmlDataLoader {
    val smlStateFlow: StateFlow<ViewState<Sml?>>
    fun loadSml(workoutHeader: WorkoutHeader): StateFlow<ViewState<Sml?>>
    fun resetCache()
    suspend fun getSml(workoutHeader: WorkoutHeader): Sml?
}

@ActivityRetainedScoped
class DefaultSmlDataLoader @Inject constructor(
    private val fetchSmlUseCase: FetchSmlUseCase,
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : SmlDataLoader {
    override val smlStateFlow: MutableStateFlow<ViewState<Sml?>> =
        MutableStateFlow(loading())

    override fun loadSml(workoutHeader: WorkoutHeader): StateFlow<ViewState<Sml?>> {
        activityRetainedCoroutineScope.launch {
            traceSuspend("loadSml") {
                smlStateFlow.value = ViewState.Loaded(getSml(workoutHeader))
            }
        }
        return smlStateFlow
    }

    override suspend fun getSml(workoutHeader: WorkoutHeader): Sml? = withContext(coroutinesDispatchers.io) {
        runSuspendCatching {
            fetchSmlUseCase.fetchSml(workoutHeader.id, workoutHeader.key)
        }.getOrElse { e ->
            Timber.w(e, "Sml loading failed for workoutId: ${workoutHeader.id}")
            SmlFactory.EMPTY
        }
    }

    override fun resetCache() {
        smlStateFlow.value = loading()
    }
}
