package com.stt.android.workout.details.charts

import android.content.Context
import com.github.mikephil.charting.data.Entry
import com.stt.android.core.domain.GraphType
import com.stt.android.domain.sml.SmlEvent
import com.stt.android.mapping.InfoModelFormatter

data class WorkoutLineChartData(
    val graphType: GraphType,
    val xValueType: AnalysisGraphXValueType,
    val data: List<WorkoutLineEntry>,
    val events: List<SmlEvent> = emptyList(),
    /**
     * If set, chart should always show this as the minimum
     */
    val minValueStrict: Float? = null,
    /**
     * If set, chart can show a larger value as the minimum to fit the data better but should
     * not go lower in any situation. minValueStrict should take priority.
     */
    val minAllowedValue: Float? = null,
    /**
     * If set, chart should always show this as the maximum
     */
    val maxValueStrict: Float? = null,
    val minRange: Float? = null,
    val isFilled: Boolean = false,
    val isInverted: Boolean = false,
    val isFillInverted: Boolean = false,
    val enableLegend: Boolean = false,
    val averageLineValue: Float? = null,
    val formatter: WorkoutChartValueFormatter,
    val infoModelFormatter: InfoModelFormatter
) {
    //region Overriding equals/hashCode since LineData does not provide them.
    private val hashCode: Int = createHashCode()

    override fun equals(other: Any?): Boolean = hashCode == other.hashCode()

    override fun hashCode(): Int {
        return hashCode
    }

    private fun createHashCode(): Int {
        var result = graphType.hashCode()
        result = 31 * result + data.size.hashCode()
        result = 31 * result + events.size.hashCode()
        result = 31 * result + (averageLineValue?.hashCode() ?: 0)
        result = 31 * result + (minValueStrict?.hashCode() ?: 0)
        result = 31 * result + (maxValueStrict?.hashCode() ?: 0)
        return result
    }
    //endregion
}

interface WorkoutChartValueFormatter {
    fun formatXValue(value: Float, context: Context): String
    fun formatConvertedYValue(valueInUserUnits: Float): String
}

data class WorkoutLineEntry(
    val entries: List<Entry>,
    val name: String = "",
    val lineColor: Int? = null,
)
