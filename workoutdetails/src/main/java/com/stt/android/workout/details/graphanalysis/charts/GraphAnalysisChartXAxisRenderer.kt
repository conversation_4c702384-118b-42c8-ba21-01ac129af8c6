package com.stt.android.workout.details.graphanalysis.charts

import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import androidx.core.graphics.withClip
import androidx.core.graphics.withSave
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.renderer.XAxisRenderer
import com.github.mikephil.charting.utils.MPPointD
import com.github.mikephil.charting.utils.Transformer
import com.github.mikephil.charting.utils.Utils
import com.github.mikephil.charting.utils.ViewPortHandler
import kotlin.math.abs

/**
 * Allows drawing more grid lines than just the default ones at label positions
 *
 * Also draws short tick marks at the bottom of the chart at grid line positions if grid is enabled.
 * The way they are drawn inspired heavily by this fork of MPAndroidChart: https://github.com/spirit0fcom/MPAndroidChart,
 * linked in this issue in the original repo: https://github.com/PhilJay/MPAndroidChart/issues/4600
 */
class GraphAnalysisChartXAxisRenderer(
    viewPortHandler: ViewPortHandler,
    xAxis: XAxi<PERSON>,
    transformer: Transformer
) : XAxisRenderer(viewPortHandler, xAxis, transformer) {
    var numInBetweenGridLines: Int = 4
        set(value) {
            field = maxOf(value, 0)
        }

    var drawOnlyTickMarks: Boolean = false

    private val inBetweenGridLinePaint = Paint(mGridPaint)
    private val labelClippingRect = RectF()

    fun setTransformer(transformer: Transformer) {
        mTrans = transformer
    }

    private fun getTransformedGridPositions(): FloatArray {
        val numGridLinesPerLabel = numInBetweenGridLines + 1
        val bufferItemsPerLabel = 2 * numGridLinesPerLabel

        // We insert the in-between lines in before the actual label lines in the loop below,
        // to make sure that the right side of the chart always has the in-between lines we pretend
        // that there's two more labels. One fake would be enough if didn't limit the label value
        // range in [computeAxisValues]
        val drawnEntryCount = if (mXAxis.mEntryCount > 0) mXAxis.mEntryCount + 4 else 0
        val drawnEntries = if (drawnEntryCount > 0) floatArrayOf(
            0f,
            0f,
            *mXAxis.mEntries,
            0f,
            0f
        ) else floatArrayOf()
        // If there's any labels to draw, we always draw at least 5 due to the inserted fake labels
        // so count 0-4 can be ignored
        if (drawnEntryCount == 5) {
            // Only one real label that is expected to be centered, draw the fake lines at the end and start
            drawnEntries[0] = -drawnEntries[2]
            drawnEntries[1] = 0f
            drawnEntries[1] = drawnEntries[2] * 2
            drawnEntries[2] = drawnEntries[2] * 3
        } else if (drawnEntryCount > 5) {
            // Take the distance between the first two labels, and insert the fakes the same distance backwards
            drawnEntries[1] = drawnEntries[2] -
                (drawnEntries[3] - drawnEntries[2])
            drawnEntries[0] = drawnEntries[1] -
                (drawnEntries[2] - drawnEntries[1])

            // Take the distance between the last two labels, and insert the fakes the same distance forwards
            drawnEntries[drawnEntryCount - 2] = drawnEntries[drawnEntryCount - 3] +
                (drawnEntries[drawnEntryCount - 3] - drawnEntries[drawnEntryCount - 4])
            drawnEntries[drawnEntryCount - 1] = drawnEntries[drawnEntryCount - 2] +
                (drawnEntries[drawnEntryCount - 2] - drawnEntries[drawnEntryCount - 3])
        }

        if (mRenderGridLinesBuffer.size != drawnEntryCount * bufferItemsPerLabel) {
            mRenderGridLinesBuffer = FloatArray(drawnEntryCount * bufferItemsPerLabel)
        }

        for (i in mRenderGridLinesBuffer.indices step bufferItemsPerLabel) {
            val entryPosition = drawnEntries[i / bufferItemsPerLabel]
            // For the first entry estimate distance to potential previous label with the next label
            val distanceComparisonEntryPosition = if (i == 0) {
                drawnEntries[1]
            } else {
                drawnEntries[(i - bufferItemsPerLabel) / bufferItemsPerLabel]
            }

            val entryDistance = abs(entryPosition - distanceComparisonEntryPosition)
            val prevEntryPosition = entryPosition - entryDistance
            val distanceBetweenLines = entryDistance / numGridLinesPerLabel
            // Starting from where the previous label was, move forwards one distance between lines
            // and insert position to array. So the last line added is the actual label line
            for (j in 0 until numGridLinesPerLabel) {
                val bufferIndex = i + (j * 2)
                val bufferValue = prevEntryPosition + (distanceBetweenLines * (j + 1))
                mRenderGridLinesBuffer[bufferIndex] = bufferValue
                mRenderGridLinesBuffer[bufferIndex + 1] = bufferValue
            }
        }

        mTrans.pointValuesToPixel(mRenderGridLinesBuffer)
        return mRenderGridLinesBuffer
    }

    override fun renderGridLines(c: Canvas) {
        // Original XAxisRenderer had hardcoded the amount & positions of grid lines to match the
        // label entries, so we had to copy and slightly modify the code form MPAndroidChart 3.1.0
        // The modifications are related to calculating values of the the positions array,
        // which has been moved to getTransformedGridPositions so it can be reused with the tickmarks,
        // and using separate paint for the custom lines

        if (!mXAxis.isDrawGridLinesEnabled || !mXAxis.isEnabled || drawOnlyTickMarks) return

        c.withClip(gridClippingRect) {
            val positions = getTransformedGridPositions()
            setupGridPaint()
            setupInBetweenGridLinePaint()

            val gridLinePath = mRenderGridLinesPath
            gridLinePath.reset()

            val numEntriesPerLabel = (numInBetweenGridLines + 1) * 2
            val moduloForLabelLines = numEntriesPerLabel - 2
            for (i in positions.indices step 2) {
                val paint = if (i != 0 && i % numEntriesPerLabel == moduloForLabelLines) {
                    mGridPaint
                } else {
                    inBetweenGridLinePaint
                }
                val x = positions[i]

                // This part is taken from [drawGridLine] that was called here,
                // it uses hardcoded mGridPaint but we need to use different paints for different lines
                gridLinePath.moveTo(x, mViewPortHandler.contentBottom())
                gridLinePath.lineTo(x, mViewPortHandler.contentTop())
                c.drawPath(gridLinePath, paint)

                gridLinePath.reset()
                // Part from [drawGridLine] end
            }
        }
    }

    override fun renderAxisLabels(c: Canvas) {
        // Normally X-axis labels are fully visible until the grid line its associated with goes
        // out of bounds, which would cause overlapping with out custom Y-axis labels that are drawn
        // lower than the defaults. Clip the X-axis labels to be visible only below the chart.
        c.withSave {
            labelClippingRect.apply {
                set(mViewPortHandler.contentRect)
                bottom = mViewPortHandler.chartHeight
            }
            c.clipRect(labelClippingRect)

            super.renderAxisLabels(c)
        }

        // labels are drawn on top of the ticks if they are drawn with the grid itself,
        // so we need to draw them separately after the labels
        renderTickMarks(c)
    }

    override fun computeAxisValues(min: Float, max: Float) {
        // In default MPAndroidChart graphs the X-axis and Y-axis labels don't
        // overlap, but with our customizations there's a chance for that and to prevent
        // that the X-axis labels are clipped if they extend beyond graph area to where
        // the Y-labels are. To avoid the clipping, we adjust the range of values considered
        // to be used as labels so that the values near the graph's edges are taken out.
        // The adjustment is calculated by calculating how much of the label would be clipped,
        // and move that much inwards. This is repeated if the new label is longer than the first one
        // until we find a label that doesn't clip, or the adjustment has reached the midpoint of
        // available value range.
        val rangeMidpoint = min + (max - min) * 0.5f
        val output = MPPointD.getInstance(0.0, 0.0)

        var adjustedMin = min
        var adjustedMinPixelX = mViewPortHandler.contentLeft()
        while (adjustedMin < rangeMidpoint) {
            val halfLabelWidth = Utils.calcTextWidth(
                mAxisLabelPaint,
                mXAxis.valueFormatter.getFormattedValue(adjustedMin)
            ) / 2f
            val labelLeft = adjustedMinPixelX - halfLabelWidth
            if (labelLeft >= mViewPortHandler.contentLeft()) {
                break
            }

            adjustedMinPixelX += mViewPortHandler.contentLeft() - labelLeft
            transformer.getValuesByTouchPoint(adjustedMinPixelX, 0f, output)
            adjustedMin = output.x.toFloat()
        }

        var adjustedMax = max
        var adjustedMaxPixelX = mViewPortHandler.contentRight()
        while (adjustedMax > rangeMidpoint) {
            val halfLabelWidth = Utils.calcTextWidth(
                mAxisLabelPaint,
                mXAxis.valueFormatter.getFormattedValue(adjustedMax)
            ) / 2f
            val labelRight = adjustedMaxPixelX + halfLabelWidth
            if (labelRight <= mViewPortHandler.contentRight()) {
                break
            }

            adjustedMaxPixelX -= labelRight - mViewPortHandler.contentRight()
            transformer.getValuesByTouchPoint(adjustedMaxPixelX, 0f, output)
            adjustedMax = output.x.toFloat()
        }

        super.computeAxisValues(
            adjustedMin.coerceAtMost(rangeMidpoint),
            adjustedMax.coerceAtLeast(rangeMidpoint)
        )
    }

    private fun renderTickMarks(c: Canvas) {
        if (!mXAxis.isDrawGridLinesEnabled || !mXAxis.isEnabled) return
        val positions = getTransformedGridPositions()
        val yStart = mViewPortHandler.contentBottom()
        val length = mXAxis.yOffset * 0.9f
        for (i in positions.indices step 2) {
            val xPos = positions[i]
            if (xPos in mViewPortHandler.contentLeft()..mViewPortHandler.contentRight()) {
                c.drawLine(xPos, yStart, xPos, yStart + length, mGridPaint)
            }
        }
    }

    private fun setupInBetweenGridLinePaint() {
        inBetweenGridLinePaint.color = mXAxis.gridColor
        inBetweenGridLinePaint.strokeWidth = mXAxis.gridLineWidth
        inBetweenGridLinePaint.pathEffect = mXAxis.gridDashPathEffect
        inBetweenGridLinePaint.alpha = 64
    }
}
