package com.stt.android.workout.details.graphanalysis.playback

import com.google.android.gms.maps.model.LatLng

sealed class WorkoutPlaybackCameraConfig

data class Workout2DPlaybackCameraConfig(
    val markerPosition: LatLng,
    val cameraPosition: LatLng? = null
) : WorkoutPlaybackCameraConfig()

data class Workout3DPlaybackCameraConfig(
    val markerPosition: LatLng,
    val markerAltitude: Double,
    val markerDistance: Double,
    val cameraPosition: LatLng? = null,
    val cameraAltitude: Double = 0.0,
    val cameraPitch: Double = 0.0,
    val cameraBearing: Double = 0.0
) : WorkoutPlaybackCameraConfig()
