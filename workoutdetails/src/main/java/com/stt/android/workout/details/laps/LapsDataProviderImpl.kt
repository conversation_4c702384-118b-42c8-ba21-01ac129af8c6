package com.stt.android.workout.details.laps

import android.content.Context
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.advancedlaps.LapsTable
import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.extensions.stringRes
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.activities.competition.LapsDataProvider
import com.stt.android.ui.activities.competition.LapsValueItem
import com.stt.android.workout.details.advancedlaps.AdvancedLapsDataLoader
import com.stt.android.workout.details.multisport.MultisportPartActivityLoader
import com.stt.android.workout.details.sml.SmlDataLoader
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import timber.log.Timber
import javax.inject.Inject

@ActivityRetainedScoped
class LapsDataProviderImpl @Inject constructor(
    @ApplicationContext private val appContext: Context,
    private val smlDataLoader: SmlDataLoader,
    private val multisportPartActivityLoader: MultisportPartActivityLoader,
    private val advancedLapsDataLoader: AdvancedLapsDataLoader,
    private val infoModelFormatter: InfoModelFormatter,
    private val userSettingsController: UserSettingsController,
) : LapsDataProvider {
    private lateinit var currentData: ViewState<List<LapsTable>?>
    private lateinit var otherDataFlow: Flow<ViewState<List<LapsTable>?>>

    private val isMetric: Boolean
        get() = userSettingsController.settings.measurementUnit == MeasurementUnit.METRIC

    override suspend fun loadLapsComparisonData(
        currentHeader: WorkoutHeader,
        currentLapsTable: LapsTable,
        otherHeader: WorkoutHeader,
        summaryItem: SummaryItem
    ): Flow<ViewState<List<Pair<LapsValueItem?, LapsValueItem?>>>> {
        smlDataLoader.resetCache()
        otherDataFlow = loadData(otherHeader)

        return flow {
            emitAll(
                compareCurrentAndOtherData(currentHeader, currentLapsTable, summaryItem)
            )
        }
    }

    override suspend fun loadCurrentDataAndGetDistanceFilters(currentHeader: WorkoutHeader): Map<LapsTable, String>? {
        ensureCurrentDataInitialized(currentHeader)

        return currentData.data?.associate {
            it to when (it.lapsType) {
                LapsTableType.ONE_KM_AUTO_LAP,
                LapsTableType.FIVE_KM_AUTO_LAP,
                LapsTableType.TEN_KM_AUTO_LAP,
                LapsTableType.ONE_MILE_AUTO_LAP,
                LapsTableType.FIVE_MILE_AUTO_LAP,
                LapsTableType.TEN_MILE_AUTO_LAP,
                LapsTableType.DISTANCE_AUTO_LAP ->
                    infoModelFormatter.formatDistanceAutoLapTitle(
                        it.autoLapLength.toDouble(),
                        it.lapsType
                    )

                LapsTableType.DURATION_AUTO_LAP ->
                    infoModelFormatter.formatDurationAutoLapTitle(
                        it.autoLapLength.toDouble(),
                        it.lapsType
                    )

                LapsTableType.MANUAL,
                LapsTableType.INTERVAL,
                LapsTableType.DOWNHILL,
                LapsTableType.DIVE -> appContext.getString(it.lapsType.stringRes())
            }
        }
    }

    override suspend fun compareCurrentAndOtherData(
        currentHeader: WorkoutHeader,
        currentLapsTable: LapsTable,
        summaryItem: SummaryItem
    ): Flow<ViewState<List<Pair<LapsValueItem?, LapsValueItem?>>>> {
        ensureCurrentDataInitialized(currentHeader)
        return otherDataFlow.map { otherData ->
            processLapsComparison(currentData, otherData, currentLapsTable, summaryItem)
        }.catch { t ->
            Timber.w(t, "Loading laps comparison data failed.")
        }
    }

    private suspend fun ensureCurrentDataInitialized(currentHeader: WorkoutHeader) {
        if (!::currentData.isInitialized) {
            currentData = loadData(currentHeader).first()
        }
    }

    private fun processLapsComparison(
        currentData: ViewState<List<LapsTable>?>,
        otherData: ViewState<List<LapsTable>?>,
        currentLapsTable: LapsTable,
        summaryItem: SummaryItem
    ): ViewState<List<Pair<LapsValueItem?, LapsValueItem?>>> =
        if (currentData.isLoaded() && otherData.isLoaded()) {
            val currentLaps = currentData.data?.let {
                convertTablesToItems(it, currentLapsTable, summaryItem)
            }.orEmpty()
            val otherLaps = otherData.data?.let {
                convertTablesToItems(it, currentLapsTable, summaryItem)
            }.orEmpty()
            val lapsPairs = mergeLapsComparison(currentLaps, otherLaps)
            loaded(highlightLaps(lapsPairs, summaryItem))
        } else {
            loading()
        }

    private fun mergeLapsComparison(
        currentLaps: List<LapsValueItem?>,
        otherLaps: List<LapsValueItem?>
    ): List<Pair<LapsValueItem?, LapsValueItem?>> = when {
        currentLaps.size > otherLaps.size -> {
            currentLaps.zip(otherLaps + List(currentLaps.size - otherLaps.size) { null })
        }

        currentLaps.size < otherLaps.size -> {
            currentLaps.zip(otherLaps.take(currentLaps.size))
        }

        else -> {
            currentLaps.zip(otherLaps)
        }
    }

    private fun loadData(workoutHeader: WorkoutHeader): Flow<ViewState<List<LapsTable>?>> {
        return combine(
            smlDataLoader.loadSml(workoutHeader),
            multisportPartActivityLoader.setMultisportPartActivity(null),
            advancedLapsDataLoader.loadLapsTables(workoutHeader)
        ) { _, _, lapsState -> lapsState }
            .filter { it.isLoaded() }
            .distinctUntilChanged()
    }

    private fun highlightLaps(
        lapsPairs: List<Pair<LapsValueItem?, LapsValueItem?>>,
        summaryItem: SummaryItem
    ): List<Pair<LapsValueItem?, LapsValueItem?>> {
        return lapsPairs.map { (current, other) ->
            when (summaryItem) {
                SummaryItem.DURATION, SummaryItem.AVGHEARTRATE -> highlightSmaller(current, other)
                SummaryItem.AVGPACE -> highlightLarger(current, other)
                else -> current to other
            }
        }
    }

    private fun highlightSmaller(
        current: LapsValueItem?,
        other: LapsValueItem?
    ): Pair<LapsValueItem?, LapsValueItem?> {
        when {
            current != null && (other == null || current.value < other.value) -> current.highlighted =
                true

            other != null && (current == null || other.value < current.value) -> other.highlighted =
                true
        }
        return Pair(current, other)
    }

    private fun highlightLarger(
        current: LapsValueItem?,
        other: LapsValueItem?
    ): Pair<LapsValueItem?, LapsValueItem?> {
        when {
            current != null && (other == null || current.value > other.value) -> current.highlighted =
                true

            other != null && (current == null || other.value > current.value) -> other.highlighted =
                true
        }
        return Pair(current, other)
    }

    private fun convertTablesToItems(
        lapsTables: List<LapsTable>,
        currentLapsTable: LapsTable,
        summaryItem: SummaryItem
    ): List<LapsValueItem?> {
        return matchDistanceAutoLap(
            lapsTables,
            currentLapsTable
        )?.lapsTableRows?.mapNotNull { row ->
            when (summaryItem) {
                SummaryItem.DURATION -> row.duration?.let {
                    createLapValueItem(summaryItem, it)
                }

                SummaryItem.AVGPACE -> row.avgSpeed?.let {
                    createLapValueItem(summaryItem, it)
                }

                SummaryItem.AVGHEARTRATE -> row.avgHR?.let {
                    createLapValueItem(summaryItem, it)
                }

                else -> null
            }
        } ?: emptyList()
    }

    private fun matchDistanceAutoLap(
        lapsTables: List<LapsTable>,
        currentLapsTable: LapsTable
    ): LapsTable? {
        lapsTables.firstOrNull { it.lapsType == currentLapsTable.lapsType }?.let { return it }

        if (currentLapsTable.lapsType != LapsTableType.DISTANCE_AUTO_LAP) return null

        return mapDistanceToLapsType(currentLapsTable.autoLapLength)?.let { lapsType ->
            lapsTables.firstOrNull { it.lapsType == lapsType }
        }
    }

    private fun mapDistanceToLapsType(length: Float): LapsTableType? = when (length) {
        1000f -> if (isMetric) LapsTableType.ONE_KM_AUTO_LAP else LapsTableType.ONE_MILE_AUTO_LAP
        5000f -> if (isMetric) LapsTableType.FIVE_KM_AUTO_LAP else LapsTableType.FIVE_MILE_AUTO_LAP
        10000f -> if (isMetric) LapsTableType.TEN_KM_AUTO_LAP else LapsTableType.TEN_MILE_AUTO_LAP
        else -> null
    }

    private fun createLapValueItem(summaryItem: SummaryItem, value: Float): LapsValueItem =
        LapsValueItem(
            valueFormatted = infoModelFormatter.formatValueAsString(summaryItem, value),
            value = value
        )
}
