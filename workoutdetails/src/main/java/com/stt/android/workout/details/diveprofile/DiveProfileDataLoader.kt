package com.stt.android.workout.details.diveprofile

import androidx.lifecycle.LiveData
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.controllers.SummaryExtensionDataModel
import com.stt.android.core.domain.GraphType
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.extensions.loadExtension
import com.stt.android.infomodel.SummaryGraph
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.workout.details.DiveProfileData
import com.stt.android.workout.details.NavigationEventDispatcher
import com.stt.android.workout.details.WorkoutDetailsFullscreenChartNavEvent
import com.stt.android.workout.details.analytics.WorkoutDetailsAnalytics
import com.stt.android.workout.details.charts.GenerateAnalysisGraphDataUseCase
import com.stt.android.workout.details.extensions.DiveExtensionDataLoader
import com.stt.android.workout.details.multisport.MultisportPartActivityLoader
import com.stt.android.workout.details.sml.SmlDataLoader
import com.stt.android.workouts.details.values.isCadenceProcessingRequiredForSuuntoRun
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.conflate
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

interface DiveProfileDataLoader {
    val diveProfileStateFlow: StateFlow<ViewState<DiveProfileData?>>
    val navigationEvent: LiveData<DiveProfileData?>
    suspend fun loadDiveProfile(workoutHeader: WorkoutHeader): Flow<ViewState<DiveProfileData?>>
}

@FlowPreview
@ActivityRetainedScoped
class DefaultDiveProfileDataLoader
@Inject constructor(
    private val smlDataLoader: SmlDataLoader,
    private val diveExtensionDataLoader: DiveExtensionDataLoader,
    private val navigationEventDispatcher: NavigationEventDispatcher,
    private val workoutDetailsAnalytics: WorkoutDetailsAnalytics,
    private val multisportPartActivityLoader: MultisportPartActivityLoader,
    private val generateAnalysisGraphDataUseCase: GenerateAnalysisGraphDataUseCase,
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope,
    private val summaryExtensionDataModel: SummaryExtensionDataModel,
) : DiveProfileDataLoader {
    lateinit var workoutHeader: WorkoutHeader

    override val diveProfileStateFlow: MutableStateFlow<ViewState<DiveProfileData?>> =
        MutableStateFlow(loading())

    private val _navigationEvent = SingleLiveEvent<DiveProfileData?>()
    override val navigationEvent: LiveData<DiveProfileData?> = _navigationEvent

    override suspend fun loadDiveProfile(workoutHeader: WorkoutHeader): StateFlow<ViewState<DiveProfileData?>> {
        this.workoutHeader = workoutHeader
        val supportsDiveProfile = workoutHeader.activityType.supportsDiveProfile
        if (!supportsDiveProfile) {
            diveProfileStateFlow.value = loaded()
        } else {
            activityRetainedCoroutineScope.launch {
                combine(
                    smlDataLoader.smlStateFlow,
                    diveExtensionDataLoader.diveExtensionStateFlow
                ) { smlDataState, diveExtensionDataState ->
                    smlDataState to diveExtensionDataState
                }
                    .conflate()
                    .collect { (smlDataState, diveExtensionDataState) ->
                        if (smlDataState.isLoading() || diveExtensionDataState.isLoading()) {
                            diveProfileStateFlow.value =
                                loading(
                                    DiveProfileData(
                                        smlDataState.data,
                                        diveExtensionDataState.data,
                                        ::onDiveProfileTapped,
                                        ::onShowEventsTapped
                                    )
                                )
                        } else {
                            val graphData = generateAnalysisGraphDataUseCase.generate(
                                GraphType.Summary(SummaryGraph.DEPTH),
                                workoutHeader = workoutHeader,
                                sml = smlDataState.data,
                                workoutData = null,
                                activityWindow = null,
                                multisportPartActivity = null,
                                diveExtension = diveExtensionDataState.data,
                                isCadenceProcessingRequired = isCadenceProcessingRequiredForSuuntoRun(
                                    workoutHeader,
                                    summaryExtensionDataModel.loadExtension(workoutHeader)
                                )
                            )
                            diveProfileStateFlow.value =
                                loaded(
                                    DiveProfileData(
                                        smlDataState.data,
                                        diveExtensionDataState.data,
                                        ::onDiveProfileTapped,
                                        ::onShowEventsTapped,
                                        graphData
                                    )
                                )
                        }
                    }
            }
        }
        return diveProfileStateFlow
    }

    private fun onDiveProfileTapped() {
        navigationEventDispatcher.dispatchEvent(
            WorkoutDetailsFullscreenChartNavEvent(
                workoutHeader,
                GraphType.Summary(SummaryGraph.DEPTH),
                AnalyticsPropertyValue.WorkoutAnalysisScreenSource.INSIGHTS_GRAPH,
                multisportPartActivityLoader.multisportPartActivityFlow.value.data
            )
        )
    }

    private fun onShowEventsTapped() {
        val diveProfileData = diveProfileStateFlow.value.data
        trackShowEvents(diveProfileData)
        _navigationEvent.postValue(diveProfileData)
    }

    private fun trackShowEvents(diveProfileData: DiveProfileData?) {
        GlobalScope.launch {
            runSuspendCatching {
                workoutDetailsAnalytics.trackShowDiveEvents(
                    diveProfileData?.sml
                )
            }.onFailure { Timber.w(it, "Failed to track show dive events analytics") }
        }
    }
}
