package com.stt.android.workout.details.competition

import android.content.Context
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.activities.competition.CompetitionResultEnum
import com.stt.android.workout.details.R
import kotlin.math.abs
import com.stt.android.core.R as CoreR

fun ImageView.setCompetitionIcon(result: Int?) {
    alpha = when (result) {
        CompetitionResultEnum.WINNER.value -> 1f
        CompetitionResultEnum.LOSER.value -> 0.5f
        else -> 0.5f
    }
}

fun TextView.setCompetitionTitleColor(context: Context, result: Int?) {
    val colorRes = when (result) {
        CompetitionResultEnum.WINNER.value -> CoreR.color.comparison_color_increase
        CompetitionResultEnum.LOSER.value -> CoreR.color.comparison_color_decrease
        else -> com.stt.android.R.color.medium_grey
    }
    setTextColor(ContextCompat.getColor(context, colorRes))
}

fun TextView.setCompetitionTitleText(
    diff: Long,
    infoModelFormatter: InfoModelFormatter,
    summaryItem: SummaryItem
) {
    val prefix = when {
        diff > 0L -> "+"
        diff < 0L -> "-"
        else -> ""
    }
    val formattedText = infoModelFormatter.formatValueAsString(
        summaryItem,
        abs(diff)
    )
    text = "$prefix$formattedText"
}

fun TextView.setCompetitionSubtitleText(result: Int?, context: Context) {
    text = when (result) {
        CompetitionResultEnum.UNFINISHED.value -> context.getString(R.string.competition_summary_subtitle_not_completed)
        CompetitionResultEnum.WINNER.value -> context.getString(R.string.competition_summary_subtitle_faster)
        CompetitionResultEnum.LOSER.value -> context.getString(R.string.competition_summary_subtitle_slower)
        else -> ""
    }
}
