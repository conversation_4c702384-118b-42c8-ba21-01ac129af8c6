package com.stt.android.workout.details.graphanalysis.map

import androidx.lifecycle.ViewModel
import com.stt.android.core.domain.GraphType
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

class MainGraphTypeChangeViewModel : ViewModel() {

    private val _mainGraphTypeStateFlow = MutableStateFlow<GraphType>(GraphType.NONE)
    val mainGraphTypeStateFlow = _mainGraphTypeStateFlow.asStateFlow()

    fun setMainGraphType(type: GraphType) {
        _mainGraphTypeStateFlow.value = type
    }
}
