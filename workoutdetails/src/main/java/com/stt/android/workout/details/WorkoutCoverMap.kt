package com.stt.android.workout.details

import android.content.Context
import android.util.AttributeSet
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.widget.FrameLayout
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import com.airbnb.epoxy.AfterPropsSet
import com.airbnb.epoxy.CallbackProp
import com.airbnb.epoxy.ModelProp
import com.airbnb.epoxy.ModelView
import com.airbnb.epoxy.OnViewRecycled
import com.stt.android.cardlist.MultisportMapCard
import com.stt.android.cardlist.TraverseMapCard
import com.stt.android.domain.workout.ActivityType
import com.stt.android.extensions.activityTypeChangeCoordinates
import com.stt.android.maps.MapType
import com.stt.android.maps.SuuntoMapOptions
import com.stt.android.workout.details.graphanalysis.map.GraphAnalysisWorkoutMapView

@ModelView(autoLayout = ModelView.Size.MATCH_WIDTH_MATCH_HEIGHT)
class WorkoutCoverMap @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr),
    DefaultLifecycleObserver {

    private var observingLifecycle = false
    private var lifecycle: Lifecycle? = null

    @set:[
    ModelProp(ModelProp.Option.IgnoreRequireHashCode)
    ]
    lateinit var coverImage: CoverImage

    @ModelProp
    lateinit var mapType: MapType

    @set:[CallbackProp]
    var onMapClickListener: OnClickListener? = null

    private lateinit var graphAnalysisWorkoutMapView: GraphAnalysisWorkoutMapView

    @AfterPropsSet
    fun bind() {
        graphAnalysisWorkoutMapView.setOnClickListener(onMapClickListener)

        (coverImage as? CoverImage.RouteCoverImage)
            ?.let { updateMap(mapType, it) }
    }

    @OnViewRecycled
    fun clear() {
        lifecycle?.removeObserver(this)
        observingLifecycle = false
    }

    private fun updateMap(mapType: MapType, cover: CoverImage.RouteCoverImage) {
        graphAnalysisWorkoutMapView.setAllGesturesEnabled(false)
        graphAnalysisWorkoutMapView.changeMapType(mapType)
        val activityType = ActivityType.valueOf(cover.activityType)
        val route = cover.mapCard.route
        when {
            activityType.isSlopeSki && cover.runsOrLifts.isNotEmpty() -> {
                graphAnalysisWorkoutMapView.showSkiWorkout(
                    activityRoutes = cover.mapCard.activityRoutes.orEmpty(),
                    nonActivityRoutes = cover.mapCard.nonActivityRoutes.orEmpty(),
                    bounds = cover.mapCard.bounds,
                    animate = false,
                    runsOrLifts = cover.runsOrLifts,
                )
            }
            cover.mapCard is MultisportMapCard && route != null -> {
                val mapCard = cover.mapCard
                val currentMultisportPartRouteIndex = mapCard.indexOfHighlightedRoute
                val (currentMultisportPartRoute, inactivePartRoutes) = if (currentMultisportPartRouteIndex != -1) {
                    val currentRoute = mapCard.routes[currentMultisportPartRouteIndex]
                    val otherRoutes =
                        mapCard.routes.filterIndexed { idx, _ -> idx != currentMultisportPartRouteIndex }
                    currentRoute to otherRoutes
                } else {
                    route to emptyList()
                }
                val activityTypeChanges = cover.mapCard.routes.activityTypeChangeCoordinates

                graphAnalysisWorkoutMapView.showMultisportWorkout(
                    activityRoutes = mapCard.activityRoutes.orEmpty(),
                    nonActivityRoutes = mapCard.nonActivityRoutes.orEmpty(),
                    points = currentMultisportPartRoute,
                    bounds = cover.mapCard.bounds,
                    animate = false,
                    inactiveMultisportPartRoutes = inactivePartRoutes,
                    activityTypeChangeLocations = activityTypeChanges,
                )
            }
            cover.mapCard is TraverseMapCard -> {
                graphAnalysisWorkoutMapView.showHuntingOrFishingWorkout(
                    activityRoutes = cover.mapCard.activityRoutes.orEmpty(),
                    nonActivityRoutes = cover.mapCard.nonActivityRoutes.orEmpty(),
                    points = route.orEmpty(),
                    bounds = cover.mapCard.bounds,
                    animate = false,
                    traverseEvents = cover.mapCard.traverseEvents,
                )
            }
            else -> {
                graphAnalysisWorkoutMapView.showWorkout(
                    activityRoutes = cover.mapCard.activityRoutes.orEmpty(),
                    nonActivityRoutes = cover.mapCard.nonActivityRoutes.orEmpty(),
                    points = route.orEmpty(),
                    bounds = cover.mapCard.bounds,
                    animate = false,
                    isDiving = activityType.isDiving,
                )
            }
        }
    }

    override fun onCreate(owner: LifecycleOwner) {
        val options = SuuntoMapOptions(
            mapType = mapType.name,
            textureMode = true,
        )
        graphAnalysisWorkoutMapView = GraphAnalysisWorkoutMapView(context, options, null)
            .also {
                addView(
                    it,
                    LayoutParams(
                        MATCH_PARENT,
                        resources.getDimensionPixelSize(R.dimen.cover_image_height)
                    )
                )
            }

        graphAnalysisWorkoutMapView.onCreate(null)
    }

    override fun onStart(owner: LifecycleOwner) {
        graphAnalysisWorkoutMapView.onStart()
    }

    override fun onResume(owner: LifecycleOwner) {
        graphAnalysisWorkoutMapView.onResume()
    }

    override fun onPause(owner: LifecycleOwner) {
        graphAnalysisWorkoutMapView.onPause()
    }

    override fun onStop(owner: LifecycleOwner) {
        graphAnalysisWorkoutMapView.onStop()
    }

    override fun onDestroy(owner: LifecycleOwner) {
        clear()
        graphAnalysisWorkoutMapView.onDestroy()
    }

    fun onBind(lifecycle: Lifecycle?) {
        if (this.lifecycle?.currentState == Lifecycle.State.DESTROYED) {
            this.lifecycle?.removeObserver(this)
            observingLifecycle = false
        }
        if (!observingLifecycle && lifecycle != null && lifecycle.currentState != Lifecycle.State.DESTROYED) {
            this.lifecycle = lifecycle

            lifecycle.addObserver(this)
            observingLifecycle = true
        }
    }
}
