package com.stt.android.workout.details.graphanalysis.laps

import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageButton
import androidx.annotation.AttrRes
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.children
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import com.stt.android.domain.advancedlaps.LapsTableRow
import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.domain.user.KILOMETERS_TO_METERS
import com.stt.android.domain.user.MILES_TO_METERS
import com.stt.android.extensions.stringRes
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.workout.details.R
import com.stt.android.workout.details.databinding.GraphAnalysisSelectedLapBinding
import com.stt.android.workout.details.graphanalysis.AnalysisLapsData
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import com.stt.android.R as BR
import com.stt.android.core.R as CR

@AndroidEntryPoint
class GraphAnalysisSelectedLapView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet?,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr, defStyleRes) {

    @Inject
    internal lateinit var infoModelFormatter: InfoModelFormatter

    private val paddingHorizontalExtra: Int

    var onClearLapClickListener: (() -> Unit)? = null
    var onOpenLapSelectionDialogClickListener: (() -> Unit)? = null
    var onPreviousLapClickListener: (() -> Unit)? = null
    var onNextLapClickListener: (() -> Unit)? = null

    var canSelectLap: Boolean = true
        set(value) {
            if (value == field) return
            field = value
            val res = if (value) R.color.laps_cell_text_selected else CR.color.near_black
            binding.selectedLapDescription.setTextColor(ContextCompat.getColor(context, res))
            updateViews()
        }

    var lapsData: AnalysisLapsData? = null
        set(value) {
            if (value == field) return
            field = value
            updateViews()
        }

    private val binding: GraphAnalysisSelectedLapBinding = GraphAnalysisSelectedLapBinding.inflate(
        LayoutInflater.from(context),
        this
    )

    init {
        val array = resources.obtainAttributes(attrs, R.styleable.GraphAnalysisSelectedLapView)
        paddingHorizontalExtra = array.getDimensionPixelSize(
            R.styleable.GraphAnalysisSelectedLapView_gaslPaddingHorizontalExtra,
            resources.getDimensionPixelSize(BR.dimen.size_spacing_small),
        )
        array.recycle()

        binding.clearLapSelectionButton.setOnClickListener {
            onClearLapClickListener?.invoke()
        }

        // Make the button's text end to align with this parent view's end, and let the ripple
        // area overflow. Its expected that this view's parent uses clipChildren = false to allow
        // overflowing ripple to be shown
        binding.clearLapSelectionButton.updateLayoutParams<LayoutParams> {
            marginEnd = -binding.clearLapSelectionButton.paddingEnd
        }

        updateViews()
    }

    private fun updateViews() {
        val data = lapsData
        if (data != null) {
            if (
                data.lapStartSecondsInWorkout != null &&
                data.lapEndSecondsInWorkout != null
            ) {
                // If there's a need to put the selected lap description on two lines, we want to make
                // sure the line break is between the lap's name and the window start time. All other
                // spaces and the hyphen are their non-breaking variants to ensure this, and
                // have been written in the "\u" format for clarity

                val windowStart = infoModelFormatter
                    .formatValue(SummaryItem.DURATION, data.lapStartSecondsInWorkout)
                    .value
                    .orEmpty()
                val windowEnd = infoModelFormatter
                    .formatValue(SummaryItem.DURATION, data.lapEndSecondsInWorkout)
                    .value
                    .orEmpty()

                val lapName = if (data.lapsTableType != null && data.selectedLap != null) {
                    val autoLapLength = data.lapsTables
                        .firstOrNull { it.lapsType == data.lapsTableType }
                        ?.autoLapLength
                        ?.takeIf { it > 0f }
                    val tableTitle = getLapsTableTitle(data.lapsTableType, data.selectedLap, autoLapLength)
                    val lapPrefix = context.getString(BR.string.lap)
                    "$tableTitle/$lapPrefix\u00A0${data.selectedLap.lapNumber}"
                } else ""
                binding.clearLapSelectionButton.visibility = View.VISIBLE
                binding.selectedLapDescription.text = "$lapName $windowStart\u00A0\u2011\u00A0$windowEnd".trim()
                if (data.lapsTableType != null && data.selectedLap != null) {
                    val selectLaps =
                        data.lapsTables.firstOrNull { it.lapsType == data.lapsTableType }
                    val index = selectLaps?.lapsTableRows?.indexOf(data.selectedLap)
                    if (index == 0) {
                        binding.previousLap.setImageResource(com.stt.android.R.drawable.icon_arrow_left_gray)
                        binding.previousLap.removeOnClickListenerAndRippleEffect(true)
                    } else {
                        binding.previousLap.setImageResource(com.stt.android.R.drawable.icon_arrow_left_black)
                        binding.previousLap.setOnClickListenerWithRippleEffect({
                            onPreviousLapClickListener?.invoke()
                        }, android.R.attr.actionBarItemBackground)
                    }
                    if (index == selectLaps?.lapsTableRows?.size?.minus(1)) {
                        binding.nextLap.setImageResource(com.stt.android.R.drawable.icon_arrow_right_gray)
                        binding.nextLap.removeOnClickListenerAndRippleEffect(true)
                    } else {
                        binding.nextLap.setImageResource(com.stt.android.R.drawable.icon_arrow_right_black)
                        binding.nextLap.setOnClickListenerWithRippleEffect({
                            onNextLapClickListener?.invoke()
                        }, android.R.attr.actionBarItemBackground)
                    }
                    binding.previousLap.isVisible = true
                    binding.nextLap.isVisible = true
                } else {
                    binding.previousLap.isVisible = false
                    binding.nextLap.isVisible = false
                }
            } else {
                binding.clearLapSelectionButton.visibility = View.GONE
                binding.selectedLapDescription.setText(R.string.entire_workout)
                binding.previousLap.isVisible = false
                binding.nextLap.isVisible = false
            }
        } else {
            binding.clearLapSelectionButton.visibility = View.GONE
            binding.selectedLapDescription.setText(R.string.entire_workout)
            binding.previousLap.isVisible = false
            binding.nextLap.isVisible = false
        }

        if (canSelectLap) {
            this.setOnClickListenerWithRippleEffect({
                onOpenLapSelectionDialogClickListener?.invoke()
            }, android.R.attr.selectableItemBackground)
        } else {
            this.removeOnClickListenerAndRippleEffect()
        }

        val firstChildIsButton = children.firstOrNull { it.isVisible } is ImageButton
        val lastChildIsButton = children.lastOrNull { it.isVisible } is ImageButton
        setPadding(
            (if (firstChildIsButton) 0 else context.resources.getDimensionPixelSize(BR.dimen.size_spacing_medium)) + paddingHorizontalExtra,
            0,
            (if (lastChildIsButton) 0 else context.resources.getDimensionPixelSize(BR.dimen.size_spacing_medium)) + paddingHorizontalExtra,
            0,
        )
    }

    private fun getLapsTableTitle(
        lapsTableType: LapsTableType,
        lap: LapsTableRow,
        autoLapLength: Float?
    ): String = when (lapsTableType) {
        LapsTableType.ONE_KM_AUTO_LAP ->
            infoModelFormatter.formatDistanceAutoLapTitle(KILOMETERS_TO_METERS, lapsTableType)
        LapsTableType.ONE_MILE_AUTO_LAP ->
            infoModelFormatter.formatDistanceAutoLapTitle(MILES_TO_METERS, lapsTableType)
        LapsTableType.FIVE_KM_AUTO_LAP ->
            infoModelFormatter.formatDistanceAutoLapTitle(5 * KILOMETERS_TO_METERS, lapsTableType)
        LapsTableType.FIVE_MILE_AUTO_LAP ->
            infoModelFormatter.formatDistanceAutoLapTitle(5 * MILES_TO_METERS, lapsTableType)
        LapsTableType.TEN_KM_AUTO_LAP ->
            infoModelFormatter.formatDistanceAutoLapTitle(10 * KILOMETERS_TO_METERS, lapsTableType)
        LapsTableType.TEN_MILE_AUTO_LAP ->
            infoModelFormatter.formatDistanceAutoLapTitle(10 * MILES_TO_METERS, lapsTableType)
        LapsTableType.DISTANCE_AUTO_LAP ->
            infoModelFormatter.formatDistanceAutoLapTitle((autoLapLength ?: lap.distance)?.toDouble(), lapsTableType)
        LapsTableType.DURATION_AUTO_LAP ->
            infoModelFormatter.formatDurationAutoLapTitle(lap.duration?.toDouble(), lapsTableType)
        LapsTableType.MANUAL,
        LapsTableType.INTERVAL,
        LapsTableType.DOWNHILL,
        LapsTableType.DIVE -> context.getString(lapsTableType.stringRes())
    }

    private fun View.setOnClickListenerWithRippleEffect(
        listener: OnClickListener,
        @AttrRes rippleAttrRes: Int,
    ) {
        this.setOnClickListener(listener)
        TypedValue().apply {
            context.theme.resolveAttribute(rippleAttrRes, this, true)
        }.resourceId.takeIf { it > 0 }?.let {
            this.setBackgroundResource(it)
        }
    }

    private fun View.removeOnClickListenerAndRippleEffect(dummy: Boolean = false) {
        if (dummy) {
            this.setOnClickListener { }
        } else {
            this.setOnClickListener(null)
            this.isClickable = false
        }
        this.setBackgroundResource(0)
    }
}
