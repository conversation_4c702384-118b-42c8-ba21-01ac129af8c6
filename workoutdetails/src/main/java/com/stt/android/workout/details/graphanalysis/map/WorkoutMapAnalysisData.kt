package com.stt.android.workout.details.graphanalysis.map

import com.stt.android.domain.sml.Sml
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.workout.details.graphanalysis.playback.PlaybackTimeWindow

data class WorkoutMapAnalysisData(
    val header: WorkoutHeader,
    val sml: Sml?,
    val geoPoints: List<WorkoutGeoPoint>,
    val fullWorkoutMapRouteData: WorkoutMapRouteData?,
    val timeWindow: PlaybackTimeWindow?,
)
