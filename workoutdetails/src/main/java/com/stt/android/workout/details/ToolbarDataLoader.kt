package com.stt.android.workout.details

import androidx.lifecycle.LiveData
import com.google.maps.android.PolyUtil
import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.failure
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.session.CurrentUser
import com.stt.android.domain.mapbox.FetchLocationNameUseCase
import com.stt.android.domain.user.GetUserByUsernameUseCase
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.ui.utils.SingleLiveEvent
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

@ActivityRetainedScoped
class ToolbarDataLoader
@Inject constructor(
    private val currentUser: CurrentUser,
    private val navigationEventDispatcher: NavigationEventDispatcher,
    private val getUserByUsernameUseCase: GetUserByUsernameUseCase,
    private val fetchLocationNameUseCase: FetchLocationNameUseCase,
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope,
    private val isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase
) {
    lateinit var workoutHeader: WorkoutHeader

    private val _menuClickEvent = SingleLiveEvent<Int>()
    val onMenuClickEvent: LiveData<Int> = _menuClickEvent

    private val toolbarDataStateFlow = MutableStateFlow<ViewState<ToolbarData?>>(loading())

    suspend fun loadToolbarData(workoutHeader: WorkoutHeader): StateFlow<ViewState<ToolbarData?>> {
        update(workoutHeader)
        return toolbarDataStateFlow
    }

    suspend fun update(workoutHeader: WorkoutHeader): Job {
        this.workoutHeader = workoutHeader
        return activityRetainedCoroutineScope.launch(IO) {
            Timber.d("Loading toolbar data")

            toolbarDataStateFlow.value = runSuspendCatching {
                val showPremiumRequiredNotes = async {
                    !isSubscribedToPremiumUseCase.invoke().first()
                }

                val realNameOrUserName = getRealNameOrUserName(workoutHeader)
                val locationPlaceName = workoutHeader.startPosition?.let {
                    fetchLocationNameUseCase(
                        FetchLocationNameUseCase.Params(
                            it.latitude,
                            it.longitude,
                            useCoarseAccuracy = true
                        )
                    )?.city
                }

                loaded(
                    ToolbarData(
                        name = realNameOrUserName,
                        workoutStartTime = workoutHeader.startTime,
                        sharingFlags = workoutHeader.sharingFlags,
                        hasRoute = workoutHeader.hasRoute(),
                        isOwnWorkout = workoutHeader.username == currentUser.getUsername(),
                        isFieldTester = currentUser.isFieldTester(),
                        locationPlaceName = locationPlaceName,
                        onTitleClicked = ::handleTitleClick,
                        onMenuItemClicked = ::handleMenuClick,
                        showPremiumRequiredNotes = showPremiumRequiredNotes.await()
                    )
                )
            }.getOrElse { e ->
                Timber.w(e, "Failed to load toolbar data")
                failure(ErrorEvent.get(e::class))
            }
        }
    }

    private suspend fun getRealNameOrUserName(workoutHeader: WorkoutHeader): String =
        withContext(IO) {
            runSuspendCatching {
                getUserByUsernameUseCase.getUserByUsername(
                    workoutHeader.username,
                    queryRemoteIfNeeded = true
                )
                    .realNameOrUsername
            }.getOrElse { e ->
                Timber.w(e, "Failed to load real name or username")
                ""
            }
        }

    private fun navigateToProfile(username: String) {
        navigationEventDispatcher.dispatchEvent(WorkoutDetailsProfileNavEvent(username))
    }

    private fun handleTitleClick() = navigateToProfile(workoutHeader.username)

    private fun handleMenuClick(menuId: Int): Boolean {
        _menuClickEvent.postValue(menuId)
        return true
    }

    private companion object {
        fun WorkoutHeader.hasRoute(): Boolean =
            !isPolylineEmpty && totalDistance != 0.0 && PolyUtil.decode(polyline).size > 1
    }
}
