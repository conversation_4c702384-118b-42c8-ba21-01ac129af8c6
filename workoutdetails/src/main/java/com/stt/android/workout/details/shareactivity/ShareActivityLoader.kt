package com.stt.android.workout.details.shareactivity

import androidx.work.WorkManager
import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.failure
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.sync.SyncRequest.push
import com.stt.android.domain.sync.SyncRequestHandlerWorker
import com.stt.android.domain.workout.SharingOption
import com.stt.android.domain.workouts.SaveWorkoutHeaderUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.workout.details.ShareActivityData
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@ActivityRetainedScoped
class ShareActivityLoader
@Inject constructor(
    private val currentUserController: CurrentUserController,
    private val saveWorkoutHeaderUseCase: SaveWorkoutHeaderUseCase,
    private val workManager: WorkManager,
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope
) {
    private val shareActivityFlow = MutableStateFlow<ViewState<ShareActivityData>>(loading())
    private lateinit var workoutHeader: WorkoutHeader

    fun loadShareActivityData(workoutHeader: WorkoutHeader): Flow<ViewState<ShareActivityData>> {
        this.workoutHeader = workoutHeader
        activityRetainedCoroutineScope.launch {
            refreshShareActivityData()
        }
        return shareActivityFlow
    }

    fun update(workoutHeader: WorkoutHeader) {
        this.workoutHeader = workoutHeader
        refreshShareActivityData()
    }

    private fun refreshShareActivityData(
        initialState: ViewState<ShareActivityData> = createState(workoutHeader.sharingFlags)
    ) {
        shareActivityFlow.update { initialState }
    }

    private fun createState(sharingFlags: Int): ViewState<ShareActivityData> {
        val derivedSharingFlags = if (sharingFlags.hasPublic()) {
            // "share to public" is selected, make sure "share to follower" is also selected
            sharingFlags or followers
        } else {
            sharingFlags
        }
        return loaded(
            ShareActivityData(
                sharingFlags = derivedSharingFlags,
                shareActivityClickHandler = ::handleShareActivityClick,
                isOwnWorkout = workoutHeader.username == currentUserController.username,
            )
        )
    }

    private fun handleShareActivityClick(sharingFlag: Int) {
        activityRetainedCoroutineScope.launch {
            val previousState = shareActivityFlow.first()
            val previousSharingFlags = previousState.data?.sharingFlags ?: return@launch
            val derivedSharingFlags = if (sharingFlag.isPrivate()) {
                // Private button selected, clear all sharing flags
                sharingFlag
            } else if (sharingFlag.hasPublic()) {
                // Public button selected, toggle public and followers on/off
                previousSharingFlags xor sharingFlag or followers
            } else if (sharingFlag.hasFollowers() && previousSharingFlags.hasPublic()) {
                // Followers button selected while public was already toggled on, do nothing
                previousSharingFlags
            } else {
                // Other button was selected, like Facebook or Twitter, toggle it on/off
                previousSharingFlags xor sharingFlag
            }

            // Update the UI
            refreshShareActivityData(createState(derivedSharingFlags))

            // Persist to DB and backend
            runSuspendCatching {
                saveWorkoutHeaderUseCase(workoutHeader.copy(sharingFlags = derivedSharingFlags))
                SyncRequestHandlerWorker.enqueue(workManager, push())
            }.onFailure { e ->
                Timber.w(e, "Unable to persist sharing flags")
                // Notify the error so we can show it in the UI later
                shareActivityFlow.update { failure(ErrorEvent.get(e::class), previousState.data) }
            }
        }
    }
}

private fun Int.hasPublic(): Boolean = (this and SharingOption.EVERYONE.backendId) != 0
private fun Int.hasFollowers(): Boolean = (this and followers) != 0
private fun Int.isPrivate(): Boolean = this == private
private val private = SharingOption.NOT_SHARED.backendId
private val followers = SharingOption.FOLLOWERS.backendId
