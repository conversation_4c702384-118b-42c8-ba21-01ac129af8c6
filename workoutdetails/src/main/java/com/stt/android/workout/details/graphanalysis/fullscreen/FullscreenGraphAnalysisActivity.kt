package com.stt.android.workout.details.graphanalysis.fullscreen

import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.os.Bundle
import android.widget.FrameLayout
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.doOnLayout
import androidx.fragment.app.commit
import androidx.navigation.navArgs
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.core.domain.GraphType
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.premium.PremiumRequiredToAccessHandler
import com.stt.android.workout.details.R
import com.stt.android.workout.details.WorkoutDetailsConstants.RESULT_UPDATED_GRAPH_ANALYSIS_SELECTIONS
import com.stt.android.workout.details.analytics.WorkoutDetailsAnalytics
import com.stt.android.workout.details.graphanalysis.GraphAnalysisFragment
import com.stt.android.workout.details.graphanalysis.GraphAnalysisSelections
import com.stt.android.workout.details.graphanalysis.playback.WorkoutPlaybackPauseReason
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.flowOf
import javax.inject.Inject

@AndroidEntryPoint
class FullscreenGraphAnalysisActivity :
    AppCompatActivity(R.layout.fullscreen_graph_analysis_activity),
    GraphAnalysisFragment.Listener {

    @Inject
    lateinit var workoutDetailsAnalytics: WorkoutDetailsAnalytics

    @Inject
    lateinit var premiumRequiredToAccessHandler: PremiumRequiredToAccessHandler

    private val viewModel: FullscreenGraphAnalysisViewModel by viewModels()

    private val lockLandscape: Boolean by lazy {
        navArgs<FullscreenGraphAnalysisActivityArgs>().value.lockLandscape
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        workoutDetailsAnalytics.setIsFullscreenGraphAnalysis(true)

        if (lockLandscape) {
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
        }

        val arguments = navArgs<FullscreenGraphAnalysisActivityArgs>().value
        viewModel.loadData(arguments.workoutHeader, arguments.multisportPartActivity)

        if (savedInstanceState == null) {
            val fragmentArgs = GraphAnalysisFragment.createArguments(
                displayMode = GraphAnalysisFragment.DisplayMode.FULL,
                initialMainGraphType = arguments.initialMainGraphType,
                initialSelections = arguments.initialSelections
            )

            supportFragmentManager.commit {
                add(
                    R.id.graph_analysis_fragment_container,
                    GraphAnalysisFragment::class.java,
                    fragmentArgs,
                )
            }
        }

        val rootView = findViewById<FrameLayout>(R.id.fullscreen_graph_analysis_root)
        rootView.doOnLayout {
            (supportFragmentManager.findFragmentById(R.id.graph_analysis_fragment_container) as GraphAnalysisFragment)
                .setInitiallyVisibleContentHeight(it.height)
        }

        premiumRequiredToAccessHandler.onCreate(this)
        premiumRequiredToAccessHandler.startCheckingForPremiumAccess(
            viewLifecycleOwner = this,
            containerView = rootView,
            description = getString(R.string.buy_premium_popup_fullscreen_analysis_description),
            premiumRequired = flowOf(lockLandscape),
            onCloseClickListener = { closeFullscreenGraphAnalysis() },
            analyticsSource = AnalyticsPropertyValue.BuyPremiumPopupShownSource.LANDSCAPE_LOCKED_FULLSCREEN_GRAPH_ANALYSIS,
            closeFromButton = true
        )
    }

    override fun onResume() {
        super.onResume()
        (supportFragmentManager.findFragmentById(R.id.graph_analysis_fragment_container) as GraphAnalysisFragment).listener = this
    }

    override fun onDestroy() {
        (supportFragmentManager.findFragmentById(R.id.graph_analysis_fragment_container) as GraphAnalysisFragment).listener = null
        super.onDestroy()
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        // By changing back to default requestedOrientation, we get the orientation change animation
        // if the device is in portrait but Activity is locked to landscape. This nicely mirrors
        // the animation we get when opening this Activity in landscape lock if opened when
        // device is in portrait.
        // Updating the ActivityResult needs to be done before finishing this Activity (in super.onBackPressed()),
        // otherwise the result isn't delivered to previous Activity properly.
        updateActivityResult()
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
        super.onBackPressed()
    }

    override fun onGraphAnalysisChartReady(): Pair<Boolean, Boolean> {
        viewModel.onChartReadyForPlayback()
        return Pair(true, false)
    }

    override fun showFullscreenGraphAnalysis(initialMainGraphType: GraphType?) {
        // No-op, already in fullscreen
    }

    override fun closeFullscreenGraphAnalysis() {
        // Same as with onBackPressed, update result and switch locked orientation back to default
        updateActivityResult()
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
        finish()
    }

    override fun onPlaybackClicked(): Boolean = false

    override fun onPause() {
        super.onPause()

        if (!isChangingConfigurations) {
            viewModel.onScreenExited()
        }

        viewModel.pausePlayback(WorkoutPlaybackPauseReason.ScreenExit)
    }

    private fun updateActivityResult() {
        val graphAnalysisFragment =
            supportFragmentManager.findFragmentById(R.id.graph_analysis_fragment_container) as? GraphAnalysisFragment
        val selections = graphAnalysisFragment?.getGraphAnalysisSelections()
            ?: GraphAnalysisSelections(0L, null)

        setResult(
            RESULT_OK,
            Intent().apply {
                putExtra(
                    RESULT_UPDATED_GRAPH_ANALYSIS_SELECTIONS,
                    selections
                )
            }
        )
    }

    companion object {
        fun newStartIntent(
            context: Context,
            analyticsSource: String,
            lockLandscape: Boolean = false,
            autoPlayback: Boolean = false,
            initialSelections: GraphAnalysisSelections = GraphAnalysisSelections(0L, null),
            initialMainGraphType: GraphType? = null,
            trackWorkoutAnalysisScreenEvent: Boolean = false,
            workoutHeader: WorkoutHeader,
            multisportPartActivity: MultisportPartActivity?
        ) = Intent(context, FullscreenGraphAnalysisActivity::class.java).apply {
            putExtras(
                FullscreenGraphAnalysisActivityArgs(
                    autoPlayback = autoPlayback,
                    lockLandscape = lockLandscape,
                    initialSelections = initialSelections,
                    initialMainGraphType = initialMainGraphType,
                    trackWorkoutAnalysisScreenEvent = trackWorkoutAnalysisScreenEvent,
                    analyticsSource = analyticsSource,
                    workoutHeader = workoutHeader,
                    multisportPartActivity = multisportPartActivity
                ).toBundle()
            )
        }
    }
}
