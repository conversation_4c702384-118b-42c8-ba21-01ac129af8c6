package com.stt.android.workout.details.share.colortrack

import kotlin.collections.getOrNull
import kotlin.collections.windowed

data class Bucket(
    val fraction: Double,
    val timestamp: Long,
) {
    companion object {
        private const val BUCKET_COUNT = 20

        private fun generateBucketRange(
            minValue: Double,
            maxValue: Double,
            bucketCount: Int,
        ): DoubleArray {
            val bucketSize = (maxValue - minValue) / (bucketCount + 1)
            return DoubleArray(bucketCount) { minValue + (it + 1) * bucketSize }
        }

        private fun findBucketIndex(value: Double, buckets: DoubleArray): Int {
            buckets.forEachIndexed { index, bucket ->
                if (value < bucket) {
                    return index
                }
            }
            return buckets.size
        }

        fun <T> createBucketsFromList(
            list: List<T>,
            value: T.() -> Double,
            timestamp: T.() -> Long,
            pauseResumeTimes: List<Pair<Long, Long>>? = null,
            bucketCount: Int = BUCKET_COUNT,
        ): List<Bucket> {
            var minValue = Double.MAX_VALUE
            var maxValue = -Double.MAX_VALUE
            list.forEach {
                val bucketValue = it.value()
                minValue = minValue.coerceAtMost(bucketValue)
                maxValue = maxValue.coerceAtLeast(bucketValue)
            }
            val bucketRange = generateBucketRange(minValue, maxValue, bucketCount)
            val buckets = list.windowed(2, partialWindows = true).mapNotNull {
                val start = it[0]
                val end = it.getOrNull(1)

                val startBucketIndex = findBucketIndex(start.value(), bucketRange)
                val matched = if (end != null) {
                    val endBucketIndex = findBucketIndex(end.value(), bucketRange)
                    startBucketIndex != endBucketIndex
                } else true
                if (matched) {
                    val fraction = startBucketIndex.toDouble() / bucketCount.toDouble()
                    Bucket(fraction, start.timestamp())
                } else null
            }
            if (pauseResumeTimes.isNullOrEmpty()) return buckets

            return buckets.toMutableList().apply {
                pauseResumeTimes.forEach { (pauseTime, resumeTime) ->
                    val pauseIndex = indexOfFirst { it.timestamp > pauseTime }
                    val resumeIndex = indexOfFirst { it.timestamp > resumeTime }
                    if (pauseIndex != -1) {
                        subList(pauseIndex, if (resumeIndex != -1) resumeIndex else size).clear()
                        add(pauseIndex, Bucket(0.0, pauseTime))
                        add(pauseIndex + 1, Bucket(0.0, resumeTime))
                    }
                }
            }
        }
    }
}
