package com.stt.android.workout.details.diveprofile

import android.view.View
import android.widget.TextView
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.github.mikephil.charting.utils.Utils
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.workout.details.OnDiveProfileTapped
import com.stt.android.workout.details.OnShowEventsTapped
import com.stt.android.workout.details.R
import com.stt.android.workout.details.charts.DiveProfileLineChart
import com.stt.android.workout.details.charts.WorkoutLineChartData
import java.util.Locale
import com.stt.android.core.R as CR

@EpoxyModelClass
abstract class DiveProfileModel : EpoxyModelWithHolder<DiveProfileViewHolder>() {

    @EpoxyAttribute
    lateinit var chartData: WorkoutLineChartData

    @EpoxyAttribute
    var diveExtension: DiveExtension? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var infoModelFormatter: InfoModelFormatter

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var onChartClicked: OnDiveProfileTapped

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var supportsDiveEvents: Boolean = false

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var onShowEventsClicked: OnShowEventsTapped

    override fun getDefaultLayout() = R.layout.model_dive_profile

    override fun bind(holder: DiveProfileViewHolder) {
        with(holder.workoutLineChart) {
            val extraOffset =
                Utils.convertPixelsToDp(resources.getDimension(com.stt.android.R.dimen.size_spacing_medium))
            setExtraOffsets(
                extraOffset,
                extraOffset,
                extraOffset,
                extraOffset
            )

            setDiveChartData(chartData, supportsDiveEvents)

            setOnClickListener { onChartClicked() }
            setInfo(holder)
        }
        if (supportsDiveEvents) {
            holder.root.isEnabled = true
            holder.root.setOnClickListener { onShowEventsClicked() }
            holder.showEventsButton.visibility = View.VISIBLE
        } else {
            // can't click when it is longScreenshot
            holder.root.isEnabled = false
            holder.showEventsButton.visibility = View.GONE
        }
    }

    private fun setInfo(holder: DiveProfileViewHolder) {
        val context = holder.info.context
        diveExtension?.maxDepth?.let { maxDepth ->
            val workoutValue = infoModelFormatter.formatValue(SummaryItem.MAXDEPTH, maxDepth)
            holder.info.text = String.format(
                Locale.US,
                context.getString(CR.string.max_depth_with_value_and_unit),
                workoutValue.value ?: "",
                workoutValue.unit?.let { context.getString(it) } ?: ""
            )
        }
    }
}

class DiveProfileViewHolder : KotlinEpoxyHolder() {
    val root by bind<View>(R.id.container)
    val workoutLineChart by bind<DiveProfileLineChart>(R.id.workoutLineChart)
    val showEventsButton by bind<TextView>(R.id.diveProfileViewEvents)
    val info by bind<TextView>(R.id.diveProfileInfo)
}
