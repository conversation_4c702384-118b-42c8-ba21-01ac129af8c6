package com.stt.android.workout.details.intensity.composables

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material.DropdownMenu
import androidx.compose.material.DropdownMenuItem
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import com.stt.android.core.domain.GraphType
import com.stt.android.workout.details.AnalysisDiveGasData
import com.stt.android.workout.details.R
import com.stt.android.workout.details.ZoneAnalysisGraphType
import com.stt.android.workout.details.ZonedAnalysisYAxisType

@Composable
fun ZoneAnalysisDropdownMenu(
    availableGraphTypes: Set<ZoneAnalysisGraphType>,
    graphType: ZoneAnalysisGraphType,
    yAxisType: ZonedAnalysisYAxisType,
    onGraphTypeSelected: (item: GraphType, yAxis: ZonedAnalysisYAxisType) -> Unit,
    isZoneColorsEnabled: Boolean,
    diveGasesList: List<AnalysisDiveGasData>?,
    modifier: Modifier = Modifier
) {
    var expanded by remember { mutableStateOf(false) }
    val hasAerobicIqGraphs by remember(availableGraphTypes) { mutableStateOf(availableGraphTypes.any { it.isAerobicIqGraph }) }

    Box(
        modifier = modifier
            .wrapContentSize(Alignment.TopStart)
    ) {
        if (yAxisType == ZonedAnalysisYAxisType.MAIN) {
            ZoneAnalysisButton(
                graphTitle = graphType.graphTitle,
                graphUnitTitle = graphType.graphUnit,
                isZoneColorsEnabled = isZoneColorsEnabled,
                buttonEnabled = availableGraphTypes.isNotEmpty(),
                diveGasesList = diveGasesList,
                onClick = { expanded = true },
                modifier = Modifier.fillMaxWidth(),
            )
        } else {
            ZoneAnalysisButtonSecondary(
                graphTitle = graphType.graphTitle.takeIf { it.isNotEmpty() }
                    ?: stringResource(id = R.string.zone_analysis_graph_type_none),
                graphUnitTitle = graphType.graphUnit,
                showColorBar = graphType != ZoneAnalysisGraphType.EMPTY,
                onClick = { expanded = true },
                modifier = Modifier.fillMaxWidth(),
            )
        }

        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false }
        ) {
            availableGraphTypes.forEach {
                DropdownMenuItem(
                    onClick = {
                        onGraphTypeSelected.invoke(it.graphType, yAxisType)
                        expanded = false
                    }
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = it.graphTitle.takeIf { it.isNotEmpty() }
                                ?: stringResource(id = R.string.zone_analysis_graph_type_none),
                            modifier = Modifier.weight(1f)
                        )
                        if (hasAerobicIqGraphs) {
                            if (it.isAerobicIqGraph) {
                                Spacer(modifier = Modifier.width(MaterialTheme.spacing.smaller))
                                Icon(
                                    painter = painterResource(com.stt.android.R.drawable.heart_belt_outline),
                                    contentDescription = null,
                                    modifier = Modifier.size(MaterialTheme.iconSizes.medium)
                                )
                            } else {
                                Spacer(modifier = Modifier.size(MaterialTheme.iconSizes.medium))
                            }
                        }
                    }
                }
            }
        }
    }
}

@Preview(showBackground = true, widthDp = 145)
@Composable
private fun ZoneAnalysisDropdownMenuPreview() {
    AppTheme {
        ZoneAnalysisDropdownMenu(
            availableGraphTypes = emptySet(),
            graphType = ZoneAnalysisGraphType(GraphType.NONE, "Heart rate", "bpm"),
            yAxisType = ZonedAnalysisYAxisType.MAIN,
            isZoneColorsEnabled = false,
            diveGasesList = null,
            onGraphTypeSelected = { _, _ -> }
        )
    }
}
