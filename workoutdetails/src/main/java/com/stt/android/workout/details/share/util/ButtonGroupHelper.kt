package com.stt.android.workout.details.share.util

import android.view.View
import com.google.android.material.animation.MotionSpec
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
import com.stt.android.workout.details.R

class ButtonGroupHelper<T> {

    private lateinit var mainButton: ExtendedFloatingActionButton

    private lateinit var subButtons: List<ExtendedFloatingActionButton>

    private lateinit var iconCallback: (T) -> Int

    private lateinit var values: List<T>

    private var selectedValue: T? = null

    private var isExpanded = false

    private val subValues: List<T>
        get() = values.filter { it != selectedValue }

    fun setup(
        mainButton: ExtendedFloatingActionButton,
        subButtons: List<ExtendedFloatingActionButton>,
        values: List<T>,
        iconCallback: (T) -> Int,
        onSelected: (T) -> Unit,
    ) {
        this.mainButton = mainButton
        this.subButtons = subButtons
        this.values = values
        this.iconCallback = iconCallback

        mainButton.setOnClickListener {
            if (isExpanded) {
                collapse()
            } else {
                updateSubIcons()
                expand()
            }
        }

        subButtons.forEachIndexed { index, button ->
            button.setupMotionSpecs()
            // work around: animation not work if fab isn't laid out
            button.visibility = View.INVISIBLE
            button.setOnClickListener {
                onSelected(subValues[index])
            }
        }

        selectedValue = values[0]
        updateMainIcon()
        updateSubIcons()
    }

    fun setSelected(value: T) {
        selectedValue = value
        updateMainIcon()
        collapse()
    }

    private fun updateMainIcon() {
        mainButton.setIconResource(iconCallback.invoke(selectedValue!!))
    }

    private fun updateSubIcons() {
        val subValues = subValues
        subButtons.forEachIndexed { index, button ->
            button.setIconResource(iconCallback.invoke(subValues[index]))
        }
    }

    private fun expand() {
        isExpanded = true
        subButtons.forEach { it.show() }
    }

    private fun collapse() {
        isExpanded = false
        subButtons.forEach { it.hide() }
    }

    private fun ExtendedFloatingActionButton.setupMotionSpecs() {
        showMotionSpec = MotionSpec.createFromResource(context, R.animator.fab_show_video_share)
        hideMotionSpec = MotionSpec.createFromResource(context, R.animator.fab_hide_video_share)
    }
}
