package com.stt.android.workout.details.charts

import android.content.Context
import com.amersports.formatter.SourceUnit
import com.amersports.formatter.Success
import com.stt.android.core.domain.GraphType
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.ActivityType
import com.stt.android.infomodel.SummaryGraph
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.util.Locale

internal class WorkoutChartValueFormatterImpl(
    private val graphType: GraphType,
    activityType: ActivityType,
    private val xValueFormatter: (Float, Context) -> String,
    private val infoModelFormatter: InfoModelFormatter
) : WorkoutChartValueFormatter {
    // non-null for SummaryGraph type
    private val summaryItem = (graphType as? GraphType.Summary)?.summaryGraph
        ?.summaryItemForFormatting(activityType)

    private val formatType = (graphType as? GraphType.SuuntoPlus)?.suuntoPlusChannel?.formatStyleForSIM

    private val toSIUnit = (graphType as? GraphType.Summary)?.getToSIUnitConverter(activityType)

    override fun formatXValue(value: Float, context: Context) = xValueFormatter(value, context)

    override fun formatConvertedYValue(valueInUserUnits: Float): String {
        // Chart entries use converted units for the values. Convert back to SI units in order
        // to be able to use SIM formatter. This is not used for SuuntoPlus charts.
        val valueInBaseUnit = toSIUnit?.let { infoModelFormatter.unit.it(valueInUserUnits.toDouble()) }
        return when {
            summaryItem != null && PACE_ITEMS.contains(summaryItem) && valueInBaseUnit == 0.0 -> {
                // formatValue would error as 0 isn't a valid pace value, but its fine as a chart
                // label. Use another method that gives us the 0 properly formatted
                infoModelFormatter.formatDurationAsPace(valueInBaseUnit)
            }
            summaryItem != null && valueInBaseUnit != null -> {
                infoModelFormatter.formatValue(summaryItem, valueInBaseUnit).value ?: ""
            }
            graphType is GraphType.SuuntoPlus -> {
                // for SuuntoPlus charts we format directly from the range unit used in the chart

                // The 'StiffnessTwodigits' format type values are already converted to kN/m in the GenerateAnalysisGraphDataUseCase.
                // Using `formatValue` here would incorrectly divide the value by 1000 again.
                // TODO: Remove 'StiffnessTwodigits' from this if statement when simFormatter is updated to convert joules to kN/m.
                if (formatType != null && formatType != "StiffnessTwodigits") {
                    val result = infoModelFormatter.formatValue(
                        formatType,
                        valueInUserUnits,
                        withStyle = true,
                        sourceUnit = SourceUnit.RANGE_UNIT,
                        forceRangeUnitOutput = true
                    )
                    (result as? Success)?.value ?: ""
                } else {
                    DecimalFormat(
                        "#.##",
                        DecimalFormatSymbols.getInstance(Locale.US)
                    ).format(valueInUserUnits)
                }
            }
            else -> "-"
        }
    }

    // Use a relevant SummaryItem for formatting Y axis values depending on graph type
    private fun SummaryGraph.summaryItemForFormatting(activityType: ActivityType): SummaryItem =
        when (this) {
            SummaryGraph.SPEED -> SummaryItem.AVGSPEED
            SummaryGraph.HEARTRATE -> SummaryItem.AVGHEARTRATE
            SummaryGraph.PACE -> if (activityType.isSwimming) {
                SummaryItem.AVGSWIMPACE
            } else {
                SummaryItem.AVGPACE
            }
            SummaryGraph.ALTITUDE -> SummaryItem.LOWALTITUDE
            SummaryGraph.CADENCE -> SummaryItem.AVGCADENCE
            SummaryGraph.EPOC -> SummaryItem.PEAKEPOC
            SummaryGraph.TEMPERATURE -> SummaryItem.AVGTEMPERATURE
            SummaryGraph.POWER -> SummaryItem.AVGPOWER
            SummaryGraph.SEALEVELPRESSURE -> SummaryItem.AVGSEALEVELPRESSURE
            SummaryGraph.BIKECADENCE -> SummaryItem.AVGCADENCE
            SummaryGraph.SWIMSTROKERATE -> SummaryItem.AVGSWIMSTROKERATE
            SummaryGraph.SWIMPACE -> SummaryItem.AVGSWIMPACE
            SummaryGraph.SWOLF -> SummaryItem.AVGSWOLF
            SummaryGraph.SPEEDKNOTS -> SummaryItem.AVGNAUTICALSPEED
            SummaryGraph.DEPTH -> SummaryItem.AVGDEPTH
            SummaryGraph.VERTICALSPEED -> SummaryItem.AVGVERTICALSPEED
            SummaryGraph.GASCONSUMPTION -> SummaryItem.GASCONSUMPTION
            SummaryGraph.TANKPRESSURE -> SummaryItem.DIVEGASPRESSURE
            SummaryGraph.RECOVERYHRINTHREEMINS -> SummaryItem.AVGHEARTRATE
            SummaryGraph.VERTICALOSCILLATION -> SummaryItem.AVGVERTICALOSCILLATION
            SummaryGraph.GROUNDCONTACTTIME -> SummaryItem.AVGGROUNDCONTACTTIME
            SummaryGraph.BREATHINGRATE -> SummaryItem.BREATHINGRATE
            SummaryGraph.AVGBREASTSTROKEBREATHANGLE  -> SummaryItem.AVGBREASTSTROKEBREATHANGLE
            SummaryGraph.AVGFREESTYLEBREATHANGLE -> SummaryItem.AVGFREESTYLEBREATHANGLE
            SummaryGraph.DURATION -> SummaryItem.DURATION
            SummaryGraph.BREASTSTROKEHEADANGLE -> SummaryItem.BREASTSTROKEHEADANGLE
            SummaryGraph.FREESTYLEPITCHANGLE -> SummaryItem.FREESTYLEPITCHANGLE
            SummaryGraph.BREASTSTROKEGLIDETIME -> SummaryItem.BREASTSTROKEGLIDETIME
            SummaryGraph.AVGSKIPSRATE -> SummaryItem.AVGCADENCE
            SummaryGraph.AVGSKIPSPERROUND -> SummaryItem.AVGSKIPSPERROUND
            SummaryGraph.AEROBICZONE,
            SummaryGraph.AEROBICHRTHRESHOLDS,
            SummaryGraph.AEROBICPOWERTHRESHOLDS -> throw IllegalStateException("Formatter is not defined for $this")

            SummaryGraph.NONE -> SummaryItem.NONE
        }

    private fun GraphType.Summary.getToSIUnitConverter(
        activityType: ActivityType
    ): MeasurementUnit.(Double) -> Double =
        when (summaryGraph) {
            SummaryGraph.SWIMSTROKERATE -> {
                MeasurementUnit::fromRpmToHz
            }
            else -> summaryGraph.reverseValueUnitConverter(activityType)
        }

    companion object {
        private val PACE_ITEMS = setOf(SummaryItem.AVGPACE, SummaryItem.AVGSWIMPACE)
    }
}
