package com.stt.android.workout.details.graphanalysis.charts

import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.DataSet
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineDataSet

/**
 * Extends LineDataSet by adding graphAnalysisYAxisDependency field that splits the default
 * option to depend on the right Y axis to depend on the first or the second right Y Axis.
 *
 * When default axisDependency is read, both right Y axis are treated as the default right axis
 * When default axisDependency is written, the default right axis is treated as the first one
 */
class GraphAnalysisLineDataSet(yVals: List<Entry?>, label: String) : LineDataSet(yVals, label) {
    var graphAnalysisYAxisDependency: GraphAnalysisYAxisDependency = GraphAnalysisYAxisDependency.LEFT
        set(value) {
            field = value
            mAxisDependency = if (value == GraphAnalysisYAxisDependency.LEFT) {
                YAxis.AxisDependency.LEFT
            } else {
                YAxis.AxisDependency.RIGHT
            }
        }

    override fun setAxisDependency(dependency: YAxis.AxisDependency) {
        graphAnalysisYAxisDependency = if (dependency == YAxis.AxisDependency.LEFT) {
            GraphAnalysisYAxisDependency.LEFT
        } else {
            GraphAnalysisYAxisDependency.RIGHT_FIRST
        }
    }

    override fun getAxisDependency(): YAxis.AxisDependency {
        return if (graphAnalysisYAxisDependency == GraphAnalysisYAxisDependency.LEFT) {
            YAxis.AxisDependency.LEFT
        } else {
            YAxis.AxisDependency.RIGHT
        }
    }

    override fun copy(): DataSet<Entry> {
        val entries = mValues.map { it.copy() }
        val copied = GraphAnalysisLineDataSet(entries, label)
        copied.graphAnalysisYAxisDependency = this.graphAnalysisYAxisDependency
        copy(copied)
        return copied
    }
}

enum class GraphAnalysisYAxisDependency {
    LEFT,
    RIGHT_FIRST,
    RIGHT_SECOND,
}
