package com.stt.android.workout.details.intensity

// This code is used in backend, it has some logic to filter the ibi values
// I used the same exact class name so we don't waste time checking what is what
class IBIExtensionBuilder {
    private val timestampIbisMap: MutableMap<Long, List<Int>> = hashMapOf()

    private var firstTimestamp: Long? = null
    private var firstHeartBeatTimestamp: Long? = null
    private var timePeriod: TimePeriod? = null

    fun addIBI(timestamp: Long, ibi: List<Int>) {
        if (ibi.isEmpty()) {
            return
        }
        val localFirstTimestamp: Long? = firstTimestamp // for cast safety
        if (localFirstTimestamp == null || timestamp < localFirstTimestamp) {
            val ibiSum = ibi.sumOf { it }
            firstTimestamp = timestamp
            firstHeartBeatTimestamp = timestamp - ibiSum
        }

        // There can be multiple ibi samples on same timestamp
        // so the ibis are either added to timestampIbisMap or appended to existing value.
        timestampIbisMap[timestamp] = timestampIbisMap[timestamp].orEmpty() + ibi
    }

    fun setTimePeriod(timePeriod: TimePeriod?) {
        this.timePeriod = timePeriod
    }

    fun build(): List<WorkoutExtensionPoint> {
        val points: MutableList<WorkoutExtensionPoint> = ArrayList()

        // Create extensions points. Timestamp for each ibi found by adding the ibi value to a running timestamp
        var timestamp: Long = firstHeartBeatTimestamp ?: 0
        val ibisList: List<List<Int>> = timestampIbisMap.entries
            .sortedBy { it.key }
            .map { (_, value) -> value }
            .toList()
        for (ibis in ibisList) {
            for (ibi in ibis) {
                timestamp += ibi
                if (TimePeriod.filter(timePeriod, timestamp)) {
                    points.add(
                        WorkoutExtensionPoint(
                            ibi = ibi / 1000f,
                            timestamp = timestamp
                        )
                    )
                }
            }
        }

        if (points.isEmpty()) {
            return emptyList()
        }

        return points
    }
}

data class TimePeriod(
    val start: Long,
    val end: Long
) {
    operator fun contains(timestamp: Long): Boolean {
        return timestamp in start..end
    }

    companion object {
        fun filter(timePeriod: TimePeriod?, timestamp: Long): Boolean {
            return timePeriod == null || timePeriod.contains(timestamp)
        }
    }
}

data class WorkoutExtensionPoint(
    val ibi: Float,
    val timestamp: Long,
)
