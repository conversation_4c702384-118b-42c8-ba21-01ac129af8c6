package com.stt.android.workout.details.reactions

import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.stt.android.workout.details.reactionUserItem
import javax.inject.Inject

class ReactionUserListController
@Inject constructor() : ViewStateEpoxyController<ReactionUserListViewState>() {
    override fun buildModels(viewState: ViewState<ReactionUserListViewState?>) {
        val data = viewState.data
        data?.userFollowStatusList?.forEach { userFollowStatus ->
            reactionUserItem {
                id(userFollowStatus.username)
                workoutKey(data.workoutKey)
                userFollowStatus(userFollowStatus)
                showFollowButton(data.shouldShowFollowButtonForUser(userFollowStatus.username))
                onFollowClickListener { model, _, _, _ ->
                    data.onFollowButtonClicked.invoke(model.workoutKey(), model.userFollowStatus())
                }
                onUserClickListener { model, _, _, _ ->
                    data.onUserClicked.invoke(model.workoutKey(), model.userFollowStatus())
                }
            }
        }
        super.buildModels(viewState)
    }
}
