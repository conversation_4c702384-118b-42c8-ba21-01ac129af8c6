package com.stt.android.workout.details.laps.advanced.table

import android.content.Context
import android.util.TypedValue
import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.core.graphics.ColorUtils
import com.airbnb.epoxy.EpoxyAsyncUtil
import com.airbnb.epoxy.TypedEpoxyController
import com.stt.android.ThemeColors
import com.stt.android.core.domain.LapsTableDataType
import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.domain.advancedlaps.rowValue
import com.stt.android.extensions.icon
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.utils.LiveEvent
import com.stt.android.ui.utils.ThrottlingOnModelClickListener
import com.stt.android.utils.takeIfNotNaN
import com.stt.android.workout.details.R
import com.stt.android.workout.details.advancedLapsTableHeader
import com.stt.android.workout.details.advancedLapsTableRow
import com.stt.android.workout.details.databinding.ViewholderAdvancedLapsTableRowBinding
import com.stt.android.workout.details.laps.advanced.AdvancedLapsRowType
import com.stt.android.workout.details.laps.advanced.getSummaryRowValueForCalculations
import com.stt.android.workout.details.laps.advanced.getSuuntoPlusRowValueForCalculations
import timber.log.Timber
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.util.Locale
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

class AdvancedLapsTableController(
    private val lapsTableType: LapsTableType,
    private val lapSelectionEnabled: Boolean,
    private val lapsTableColouringEnabled: Boolean,
    private val onSelectColumnRequested: OnSelectColumnRequested?,
    private val infoModelFormatter: InfoModelFormatter,
    context: Context
) : TypedEpoxyController<AdvancedLapsTableItems>(
    EpoxyAsyncUtil.getAsyncBackgroundHandler(),
    EpoxyAsyncUtil.getAsyncBackgroundHandler()
) {

    private val lapCellWhite = ContextCompat.getColor(context, CR.color.white)
    private val lapCellBlue = ContextCompat.getColor(context, R.color.laps_cell_blue)
    private val lapCellRed = ContextCompat.getColor(context, R.color.laps_cell_red)

    private val lapCellTextColorSelect = ContextCompat.getColor(context, R.color.laps_cell_text_selected)
    private val lapCellTextColorDefault = ContextCompat.getColor(context, BaseR.color.advanced_laps_tab_row_color)

    override fun buildModels(tableItems: AdvancedLapsTableItems) {
        createAdvancedLapsTableHeaderModel(
            tableItems.headerItem,
            onSelectColumnRequested
        )
        tableItems.items.forEach { item ->
            createAdvancedLapsTableRowModel(tableItems.lapsTableType, item)
            if (item.isExpanded) {
                item.subRows.forEach { createAdvancedLapsTableRowModel(tableItems.lapsTableType, it) }
            }
        }
    }

    /**
     * Creates the first row containing the floating action buttons to change the columns.
     */
    private fun createAdvancedLapsTableHeaderModel(
        item: AdvancedLapsTableHeaderItem,
        onSelectColumnRequested: OnSelectColumnRequested?
    ) {
        if (item.selectedColumns.isEmpty()) return

        val isDownhill = lapsTableType == LapsTableType.DOWNHILL
        val columnIcons = item.selectedColumns.map { it.icon(isDownhill) }

        advancedLapsTableHeader {
            id(item.id)
            item(item)
            column1DrawableResId(columnIcons.elementAt(0))
            column2DrawableResId(columnIcons.elementAtOrElse(1) { columnIcons.elementAt(0) })
            column3DrawableResId(columnIcons.elementAtOrElse(2) { columnIcons.elementAt(0) })
            column4DrawableResId(columnIcons.elementAtOrElse(3) { columnIcons.elementAt(0) })
            if (onSelectColumnRequested != null) {
                onColumn1HeaderClicked(ThrottlingOnModelClickListener { model, _, _, _ ->
                    onSelectColumnRequested(
                        LiveEvent(
                            AdvancedLapsSelectColumnRequest(
                                stId = model.item().stId,
                                lapsTableType = <EMAIL>,
                                columnIndex = 0,
                                column = model.item().selectedColumns.elementAt(0)
                            )
                        )
                    )
                })
                onColumn2HeaderClicked(ThrottlingOnModelClickListener { model, _, _, _ ->
                    val selectedColumns = model.item().selectedColumns
                    onSelectColumnRequested(
                        LiveEvent(
                            AdvancedLapsSelectColumnRequest(
                                stId = model.item().stId,
                                lapsTableType = <EMAIL>,
                                columnIndex = 1,
                                column = selectedColumns.elementAtOrElse(1) {
                                    selectedColumns.elementAt(0)
                                }
                            )
                        )
                    )
                })
                onColumn3HeaderClicked(ThrottlingOnModelClickListener { model, _, _, _ ->
                    val selectedColumns = model.item().selectedColumns
                    onSelectColumnRequested(
                        LiveEvent(
                            AdvancedLapsSelectColumnRequest(
                                stId = model.item().stId,
                                lapsTableType = <EMAIL>,
                                columnIndex = 2,
                                column = selectedColumns.elementAtOrElse(2) {
                                    selectedColumns.elementAt(0)
                                }
                            )
                        )
                    )
                })
                onColumn4HeaderClicked(ThrottlingOnModelClickListener { model, _, _, _ ->
                    val selectedColumns = model.item().selectedColumns
                    onSelectColumnRequested(
                        LiveEvent(
                            AdvancedLapsSelectColumnRequest(
                                stId = model.item().stId,
                                lapsTableType = <EMAIL>,
                                columnIndex = 3,
                                column = selectedColumns.elementAtOrElse(3) {
                                    selectedColumns.elementAt(0)
                                }
                            )
                        )
                    )
                })
            }
        }
    }

    /**
     * Creates a lap row containing values for the selected columns.
     */
    private fun createAdvancedLapsTableRowModel(
        tableType: LapsTableType,
        item: AdvancedLapsTableRowItem
    ) {
        if (item.selectedColumns.isEmpty()) return

        val showColors = lapsTableColouringEnabled && item.type != AdvancedLapsRowType.SubRow
        val lapCellDataList = getLapCellData(item, showColors)

        advancedLapsTableRow {
            id(item.id)
            item(item)
            lapCell1(lapCellDataList.elementAt(0))
            lapCell2(lapCellDataList.elementAtOrElse(1) { lapCellDataList.elementAt(0) })
            lapCell3(lapCellDataList.elementAtOrElse(2) { lapCellDataList.elementAt(0) })
            lapCell4(lapCellDataList.elementAtOrElse(3) { lapCellDataList.elementAt(0) })
            onBind { model, view, _ ->
                val binding = view.dataBinding
                if (binding is ViewholderAdvancedLapsTableRowBinding) {
                    <EMAIL>(model.item().type, binding)
                }
            }
            onRowClicked { model, _, _, _ ->
                val modelItem = model.item()
                if (lapSelectionEnabled) {
                    modelItem.onLapSelected(tableType, modelItem.row)
                } else if (modelItem.type == AdvancedLapsRowType.Expandable) {
                    modelItem.onExpandToggled(modelItem)
                }
            }

            if (lapSelectionEnabled) {
                onExpandClicked { model, _, _, _ ->
                    val modelItem = model.item()
                    if (modelItem is AdvancedLapsTableRowItem && modelItem.type == AdvancedLapsRowType.Expandable) {
                        modelItem.onExpandToggled(modelItem)
                    }
                }
            }
        }
    }

    private fun bindRowBackground(
        type: AdvancedLapsRowType,
        viewBinding: ViewholderAdvancedLapsTableRowBinding
    ) {
        val container = viewBinding.lapRowLayout
        val expandRowBackground = viewBinding.expandRowBackground
        val context = viewBinding.root.context

        if (lapSelectionEnabled) {
            with(TypedValue()) {
                context.theme.resolveAttribute(android.R.attr.selectableItemBackground, this, true)
                container.foreground = ContextCompat.getDrawable(context, resourceId)
                expandRowBackground.setBackgroundResource(resourceId)
                expandRowBackground.visibility = View.VISIBLE
            }
        } else {
            when (type) {
                AdvancedLapsRowType.SubRow -> container.setBackgroundColor(
                    ThemeColors.resolveColor(context, BaseR.attr.suuntoBackground)
                )
                AdvancedLapsRowType.Expandable -> with(TypedValue()) {
                    context.theme.resolveAttribute(android.R.attr.selectableItemBackground, this, true)
                    container.foreground = ResourcesCompat.getDrawable(container.resources, resourceId, context.theme)
                }
                else -> {
                    container.background = null
                }
            }

            expandRowBackground.background = null
            expandRowBackground.visibility = View.GONE
        }
    }
    private fun getLapCellData(item: AdvancedLapsTableRowItem, colouringEnabled: Boolean): List<LapCellData> {
        return item.selectedColumns.map { columnData ->
            val column = columnData.column
            column.rowValue(item.row)?.let { rowValue ->
                val cellColor = columnData.percentiles?.let { getCellColor(it, rowValue, column, colouringEnabled) }
                    ?: 0
                runCatching {
                    val text = when (column) {
                        is LapsTableDataType.Summary ->
                            infoModelFormatter.formatValueAsString(column.summaryItem, rowValue)

                        is LapsTableDataType.SuuntoPlus -> {
                            val formatStyle = column.suuntoPlusChannel.formatStyleForSIM
                            if (formatStyle != null) {
                                infoModelFormatter.formatValueAsString(
                                    formatName = formatStyle,
                                    value = rowValue,
                                    withStyle = true
                                )
                            } else {
                                DecimalFormat(
                                    "#.##",
                                    DecimalFormatSymbols.getInstance(Locale.US)
                                ).format(rowValue)
                            }
                        }
                    }
                    LapCellData(
                        text = text,
                        backgroundColor = if (text.isNotBlank()) cellColor else 0,
                        textColor = if (item.isSelected) lapCellTextColorSelect else lapCellTextColorDefault,
                        bold = item.isSelected,
                    )
                }.onFailure { Timber.w(it) }.getOrNull()
            } ?: LapCellData(
                text = "",
                backgroundColor = 0,
                textColor = lapCellTextColorDefault,
                bold = false,
            )
        }
    }

    private fun getCellColor(
        columnPercentiles: LapColumnPercentiles,
        rowValue: Any?,
        column: LapsTableDataType,
        colouringEnabled: Boolean
    ): Int {
        if (!colouringEnabled || rowValue !is Number) {
            return 0
        }

        val highColor = if (column.useInvertedColors) lapCellBlue else lapCellRed
        val lowColor = if (column.useInvertedColors) lapCellRed else lapCellBlue

        val rowValueForCalculations =
            when (column) {
                is LapsTableDataType.Summary -> {
                    if (columnPercentiles.areFormattedValuesUsedForCalculation) {
                        getSummaryRowValueForCalculations(
                            infoModelFormatter,
                            rowValue,
                            column.summaryItem
                        )
                    } else {
                        rowValue.toDouble()
                    }
                }

                is LapsTableDataType.SuuntoPlus -> {
                    if (columnPercentiles.areFormattedValuesUsedForCalculation) {
                        val formatStyle = column.suuntoPlusChannel.formatStyleForSIM
                        if (formatStyle != null) {
                            getSuuntoPlusRowValueForCalculations(
                                infoModelFormatter,
                                rowValue,
                                formatStyle
                            )
                        } else {
                            null
                        }
                    } else {
                        rowValue.toDouble()
                    }
                }
            }

        return if (rowValueForCalculations != null) {
            columnPercentiles.let {
                when {
                    rowValueForCalculations <= it.percentile10 -> lowColor
                    rowValueForCalculations >= it.percentile90 -> highColor
                    rowValueForCalculations in it.percentile45..it.percentile55 -> lapCellWhite
                    rowValueForCalculations > it.percentile55 && rowValueForCalculations < it.percentile90 -> getColorBetween(
                        value = rowValueForCalculations,
                        rangeStart = it.percentile55,
                        rangeEnd = it.percentile90,
                        color = highColor
                    )

                    rowValueForCalculations > it.percentile10 && rowValueForCalculations < it.percentile45 -> getColorBetween(
                        value = rowValueForCalculations,
                        rangeStart = it.percentile45,
                        rangeEnd = it.percentile10,
                        color = lowColor
                    )

                    else -> lapCellWhite
                }
            }
        } else {
            lapCellWhite
        }
    }

    private fun getColorBetween(
        value: Double,
        rangeStart: Double,
        rangeEnd: Double,
        color: Int
    ): Int {
        val ratio = calculateRatio(rangeStart, rangeEnd, value)
        return ColorUtils.blendARGB(lapCellWhite, color, ratio)
    }

    private fun calculateRatio(
        rangeStart: Double,
        rangeEnd: Double,
        rowValue: Double
    ): Float {
        val range = rangeEnd - rangeStart
        val startValue = rowValue - rangeStart
        val result = (startValue / range).toFloat().takeIfNotNaN() ?: 0f
        return result.coerceIn(0f, 1f)
    }
}
