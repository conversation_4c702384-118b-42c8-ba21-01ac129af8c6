package com.stt.android.workout.details.divetrack

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.spacing
import com.stt.android.workout.details.R
import java.util.Locale

@Composable
fun DiveTrackHeader(
    onShowFullscreenClicked: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .fillMaxWidth()
            .height(48.dp),
    ) {
        Text(
            text = stringResource(id = R.string.dive_track_title).uppercase(Locale.getDefault()),
            style = MaterialTheme.typography.bodyBold,
            modifier = Modifier.padding(start = MaterialTheme.spacing.medium),
        )
        IconButton(
            onClick = onShowFullscreenClicked,
            modifier = Modifier.padding(end = MaterialTheme.spacing.small),
        ) {
            Icon(
                painter = painterResource(id = R.drawable.fullscreen_icon),
                contentDescription = null,
                tint = Color.Unspecified,
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewDiveTrackHeaderWithFullscreen() {
    AppTheme {
        DiveTrackHeader({})
    }
}
