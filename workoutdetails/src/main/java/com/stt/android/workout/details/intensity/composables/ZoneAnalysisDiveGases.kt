package com.stt.android.workout.details.intensity.composables

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.workout.details.AnalysisDiveGasData
import com.stt.android.workout.details.R
import com.stt.android.R as BaseR

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun ZoneAnalysisDiveGases(gasData: List<AnalysisDiveGasData>, modifier: Modifier = Modifier) {
    Column(
        modifier = modifier
            .background(MaterialTheme.colors.surface)
            .padding(top = MaterialTheme.spacing.small, bottom = MaterialTheme.spacing.smaller)
    ) {
        Text(
            text = stringResource(id = R.string.zone_analysis_dive_gases),
            style = MaterialTheme.typography.body1,
            modifier = Modifier.padding(start = MaterialTheme.spacing.small),
            color = MaterialTheme.colors.nearBlack
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        FlowRow(
            modifier = Modifier.padding(MaterialTheme.spacing.small),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.large),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall)
        ) {
            gasData.forEach {
                ZoneAnalysisDiveGas(
                    gasName = it.name,
                    gasColor = it.color
                )
            }
        }
    }
}

@Composable
fun ZoneAnalysisDiveGas(gasName: String, gasColor: Int, modifier: Modifier = Modifier) {
    Row(
        modifier = modifier
            .background(MaterialTheme.colors.surface)
    ) {
        Text(
            modifier = Modifier.padding(end = MaterialTheme.spacing.small),
            text = gasName,
            style = MaterialTheme.typography.bodyBold,
            color = MaterialTheme.colors.nearBlack,
        )
        Box(
            modifier = Modifier
                .background(Color(gasColor))
                .height(MaterialTheme.spacing.xsmall)
                .width(MaterialTheme.spacing.large)
                .align(Alignment.CenterVertically)
        )
    }
}

@Preview(widthDp = 350)
@Composable
private fun ZoneAnalysisDiveGasesPreview() {
    AppTheme {
        ZoneAnalysisDiveGases(previewGasData())
    }
}

@Preview
@Composable
private fun ZoneAnalysisDiveGasPreview() {
    AppTheme {
        ZoneAnalysisDiveGas("NX50", colorResource(id = BaseR.color.chart_color_1).toArgb())
    }
}

@Composable
private fun previewGasData() = listOf(
    AnalysisDiveGasData("Air", colorResource(id = BaseR.color.chart_color_1).toArgb()),
    AnalysisDiveGasData("NX50", colorResource(id = BaseR.color.chart_color_2).toArgb()),
    AnalysisDiveGasData("TX 21/33", colorResource(id = BaseR.color.chart_color_3).toArgb()),
    AnalysisDiveGasData("TX 12/65", colorResource(id = BaseR.color.chart_color_4).toArgb()),
    AnalysisDiveGasData("TX 6/45", colorResource(id = BaseR.color.chart_color_5).toArgb()),
    AnalysisDiveGasData("TX 30/30", colorResource(id = BaseR.color.chart_color_6).toArgb()),
)
