package com.stt.android.workout.details.divelocation

import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.fragment.app.FragmentManager
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.workout.details.DiveLocationData
import com.stt.android.workout.details.R
import com.stt.android.workoutdetail.location.WorkoutLocationClickListener
import com.stt.android.workoutdetail.location.WorkoutLocationFragment

@EpoxyModelClass
abstract class DiveLocationModel : EpoxyModelWithHolder<Holder>() {
    @EpoxyAttribute
    lateinit var diveLocationData: DiveLocationData

    @EpoxyAttribute
    var workoutHeader: WorkoutHeader? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var fragmentManager: FragmentManager

    override fun getDefaultLayout() = R.layout.model_dive_location

    private val locationClickListener = object : WorkoutLocationClickListener {
        override fun onWorkoutLocationClicked() {
            diveLocationData.onClick()
        }
    }

    override fun bind(holder: Holder) {
        super.bind(holder)

        var fragment =
            fragmentManager.findFragmentByTag(WorkoutLocationFragment.FRAGMENT_TAG)
        if (fragment == null) {
            fragment = WorkoutLocationFragment.newInstance(
                diveLocationData.latLng,
                diveLocationData.isUnconfirmed,
                workoutHeader
            ).apply {
                setWorkoutLocationClickListener(locationClickListener)
            }

            fragmentManager
                .beginTransaction()
                .replace(
                    R.id.workoutLocationContainer,
                    fragment,
                    WorkoutLocationFragment.FRAGMENT_TAG
                )
                .commit()
        } else {
            // This workaround to handle java.lang.IllegalArgumentException: No view found for id
            // When using a fragment inside a recyclerview,
            // when the fragment has been restored, it may be under the wrong parent,
            // or may doesn't have a parent at all, to ensure the fragment's view has a proper
            // container as parent by re-parenting to this view
            val fragmentView = fragment.view
            val fragmentViewParent = fragmentView?.parent as? ViewGroup
            if (fragmentViewParent != this) {
                fragmentViewParent?.removeView(fragmentView) // Remove from previous parent
                // It is important to remove the view from the parent, else addView will throw exception
                // The specified child already has a parent. You must call removeView() on the child's parent first.
                holder.container.addView(
                    fragmentView,
                    FrameLayout.LayoutParams(
                        FrameLayout.LayoutParams.MATCH_PARENT,
                        FrameLayout.LayoutParams.WRAP_CONTENT
                    )
                )
            }
            if (fragment is WorkoutLocationFragment) {
                fragment.setWorkoutLocationClickListener(locationClickListener)
            }
        }
    }
}

class Holder : KotlinEpoxyHolder() {
    val container by bind<FrameLayout>(R.id.workoutLocationContainer)
}
