package com.stt.android.workout.details.graphanalysis

import com.stt.android.domain.sml.RecordingStatusEvent
import com.stt.android.domain.sml.SmlStreamData
import com.stt.android.domain.sml.SmlStreamSamplePoint
import com.stt.android.domain.workout.WorkoutGeoPoint
import kotlin.math.absoluteValue

fun timeInWorkoutToDistance(
    secondsInWorkout: Float,
    smlStreamData: SmlStreamData?,
    geoPoints: List<WorkoutGeoPoint>
) = if (smlStreamData != null) {
    timeInWorkoutToDistance(secondsInWorkout, smlStreamData)
} else {
    timeInWorkoutToDistance(secondsInWorkout, geoPoints)
}

fun timeInWorkoutToDistance(
    secondsInWorkout: Float,
    smlStreamData: SmlStreamData
): Float? {
    val timestamp = smlStreamData.getTimestampForSecondsInWorkout(secondsInWorkout)
        ?: return null

    val samplePoints = smlStreamData.samplePoint
    val indexOfMatchingOrNextSample = samplePoints
        .binarySearch { it.timestamp.compareTo(timestamp) }
        .let { if (it < 0) it.absoluteValue - 1 else it }

    if (samplePoints.getOrNull(indexOfMatchingOrNextSample)?.cumulativeDistance != null) {
        return samplePoints[indexOfMatchingOrNextSample].cumulativeDistance
    }

    var previousWithDistance: SmlStreamSamplePoint? = null
    var nextWithDistance: SmlStreamSamplePoint? = null

    if (indexOfMatchingOrNextSample == -1) {
        previousWithDistance = samplePoints.firstOrNull()?.copy(cumulativeDistance = 0f)
        nextWithDistance = samplePoints.firstOrNull { it.cumulativeDistance != null }
    } else {
        val iteratorForNext = samplePoints
            .listIterator(indexOfMatchingOrNextSample)
        while (iteratorForNext.hasNext() && nextWithDistance == null) {
            nextWithDistance = iteratorForNext.next().takeIf { it.cumulativeDistance != null }
        }

        val iteratorForPrevious = samplePoints
            .listIterator((indexOfMatchingOrNextSample - 1).coerceAtLeast(0))
        while (iteratorForPrevious.hasPrevious() && previousWithDistance == null) {
            previousWithDistance = iteratorForPrevious.previous().takeIf { it.cumulativeDistance != null }
        }
    }

    return if (previousWithDistance != null && nextWithDistance != null) {
        val nextDistance = nextWithDistance.cumulativeDistance!!
        val previousDistance = previousWithDistance.cumulativeDistance!!
        val distanceDiff = nextDistance - previousDistance
        val timeDiff = nextWithDistance.timestamp - previousWithDistance.timestamp
        if (timeDiff == 0L) {
            previousDistance
        } else {
            val timeSincePrevious = timestamp - previousWithDistance.timestamp
            previousDistance + (distanceDiff * (timeSincePrevious / timeDiff))
        }
    } else if (previousWithDistance != null) {
        previousWithDistance.cumulativeDistance
    } else {
        nextWithDistance?.cumulativeDistance
    }
}

private fun SmlStreamData.getTimestampForSecondsInWorkout(secondsInWorkouts: Float): Long? {
    val millisInWorkout = secondsInWorkouts.times(1000).toLong()
    val lastEventBeforeOrAtTargetTimestamp = events
        .filterIsInstance<RecordingStatusEvent>()
        .lastOrNull { it.data.duration != null && it.data.duration!! <= millisInWorkout }

    return if (lastEventBeforeOrAtTargetTimestamp != null) {
        val millisPastEvent = millisInWorkout - lastEventBeforeOrAtTargetTimestamp.duration!!
        lastEventBeforeOrAtTargetTimestamp.timestamp + millisPastEvent
    } else {
        null
    }
}

fun timeInWorkoutToDistance(
    secondsInWorkouts: Float,
    geoPoints: List<WorkoutGeoPoint>
): Float? {
    if (geoPoints.isEmpty()) return null

    val millisInWorkout = (secondsInWorkouts * 1000).toInt()
    val indexOrInsertionPoint = geoPoints.binarySearch { it.millisecondsInWorkout.compareTo(millisInWorkout) }
    if (indexOrInsertionPoint == -1) {
        return 0f // time is before first geoPoint
    }

    val indexOfMatchingOrNextGeoPoint = if (indexOrInsertionPoint < 0) {
        indexOrInsertionPoint.absoluteValue - 1
    } else {
        indexOrInsertionPoint
    }

    if (indexOfMatchingOrNextGeoPoint >= geoPoints.lastIndex) {
        return geoPoints.last().totalDistance.toFloat() // time is past last geoPoint
    }

    val nextGeoPoint = geoPoints[indexOfMatchingOrNextGeoPoint]
    if (nextGeoPoint.millisecondsInWorkout == millisInWorkout ||
        indexOfMatchingOrNextGeoPoint == 0
    ) {
        // exact match or can't interpolate with previous, use this geoPoint's distance
        return nextGeoPoint.totalDistance.toFloat()
    }

    val previousGeoPoint = geoPoints[indexOfMatchingOrNextGeoPoint - 1]

    val timeDiffBetweenGeoPoints = nextGeoPoint.millisecondsInWorkout - previousGeoPoint.millisecondsInWorkout

    if (timeDiffBetweenGeoPoints == 0) {
        return previousGeoPoint.totalDistance.toFloat()
    }

    val timePastPreviousGeoPoint = millisInWorkout - previousGeoPoint.millisecondsInWorkout

    return (previousGeoPoint.totalDistance + nextGeoPoint.distance * (timePastPreviousGeoPoint / timeDiffBetweenGeoPoints.toFloat())).toFloat()
}

fun canBeUsedAsDistanceData(smlStreamData: SmlStreamData): Boolean {
    val hasValidRecordingEvents = smlStreamData.events
        .filterIsInstance<RecordingStatusEvent>()
        .any { it.data.duration != null }
    val hasDistanceInSamplePoints = smlStreamData.samplePoint.any { it.cumulativeDistance != null }
    return hasValidRecordingEvents && hasDistanceInSamplePoints
}
