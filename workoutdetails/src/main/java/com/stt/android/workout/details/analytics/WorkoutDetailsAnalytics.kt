package com.stt.android.workout.details.analytics

import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty.ACTIVITY_DISTANCE
import com.stt.android.analytics.AnalyticsEventProperty.ACTIVITY_DURATION
import com.stt.android.analytics.AnalyticsEventProperty.ACTIVITY_TYPE
import com.stt.android.analytics.AnalyticsEventProperty.AMOUNT_OF_EVENTS
import com.stt.android.analytics.AnalyticsEventProperty.CONTENT_TYPE
import com.stt.android.analytics.AnalyticsEventProperty.CONTEXT
import com.stt.android.analytics.AnalyticsEventProperty.CUSTOM_TAGS_ADDED
import com.stt.android.analytics.AnalyticsEventProperty.DESCRIPTION
import com.stt.android.analytics.AnalyticsEventProperty.DISTANCE_IN_METERS
import com.stt.android.analytics.AnalyticsEventProperty.DURATION_IN_MINUTES
import com.stt.android.analytics.AnalyticsEventProperty.EXPORT_TYPE
import com.stt.android.analytics.AnalyticsEventProperty.HAS_DESCRIPTION
import com.stt.android.analytics.AnalyticsEventProperty.MAP_3D_MODE
import com.stt.android.analytics.AnalyticsEventProperty.MAP_HEATMAP_TYPE
import com.stt.android.analytics.AnalyticsEventProperty.MAP_TYPE
import com.stt.android.analytics.AnalyticsEventProperty.NEW_INTERVAL
import com.stt.android.analytics.AnalyticsEventProperty.NUMBER_OF_ACHIEVEMENTS
import com.stt.android.analytics.AnalyticsEventProperty.NUMBER_OF_PHOTOS
import com.stt.android.analytics.AnalyticsEventProperty.NUMBER_OF_VIDEOS
import com.stt.android.analytics.AnalyticsEventProperty.NUM_ACTIVITIES
import com.stt.android.analytics.AnalyticsEventProperty.NUM_COMMENTS
import com.stt.android.analytics.AnalyticsEventProperty.NUM_LIKES
import com.stt.android.analytics.AnalyticsEventProperty.NUM_PHOTOS
import com.stt.android.analytics.AnalyticsEventProperty.PART_OF_MULTISPORT_WORKOUT
import com.stt.android.analytics.AnalyticsEventProperty.POSITION_IN_MULTISPORT_WORKOUT
import com.stt.android.analytics.AnalyticsEventProperty.PUBLIC_WORKOUT_TAGS
import com.stt.android.analytics.AnalyticsEventProperty.REPORTED_USER_ID
import com.stt.android.analytics.AnalyticsEventProperty.REPORTED_WORKOUT_ID
import com.stt.android.analytics.AnalyticsEventProperty.RESULT
import com.stt.android.analytics.AnalyticsEventProperty.SELF
import com.stt.android.analytics.AnalyticsEventProperty.SOURCE
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_DEPTH_AVG
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_DEPTH_MAX
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_NUMBER_IN_SERIES
import com.stt.android.analytics.AnalyticsEventProperty.TARGET_ACCOUNT_TYPE
import com.stt.android.analytics.AnalyticsEventProperty.TARGET_RELATIONSHIP
import com.stt.android.analytics.AnalyticsEventProperty.TARGET_WORKOUT_VISIBILITY
import com.stt.android.analytics.AnalyticsEventProperty.TOTAL_DISTANCE
import com.stt.android.analytics.AnalyticsEventProperty.TOTAL_DURATION
import com.stt.android.analytics.AnalyticsEventProperty.VALUE_NAME
import com.stt.android.analytics.AnalyticsEventProperty.WORKOUT_ANALYSIS_BACKGROUND_GRAPH_TYPE
import com.stt.android.analytics.AnalyticsEventProperty.WORKOUT_ANALYSIS_COMPARISON_GRAPH_TYPE
import com.stt.android.analytics.AnalyticsEventProperty.WORKOUT_ANALYSIS_FULL_SCREEN_MODE
import com.stt.android.analytics.AnalyticsEventProperty.WORKOUT_ANALYSIS_IS_LAP_CHOSEN
import com.stt.android.analytics.AnalyticsEventProperty.WORKOUT_ANALYSIS_LAP_COUNT
import com.stt.android.analytics.AnalyticsEventProperty.WORKOUT_ANALYSIS_LAP_DISTANCE
import com.stt.android.analytics.AnalyticsEventProperty.WORKOUT_ANALYSIS_LAP_DURATION
import com.stt.android.analytics.AnalyticsEventProperty.WORKOUT_ANALYSIS_LAP_NUMBER
import com.stt.android.analytics.AnalyticsEventProperty.WORKOUT_ANALYSIS_LAP_SELECTION_TYPE
import com.stt.android.analytics.AnalyticsEventProperty.WORKOUT_ANALYSIS_LAP_TYPE
import com.stt.android.analytics.AnalyticsEventProperty.WORKOUT_ANALYSIS_MAIN_GRAPH_TYPE
import com.stt.android.analytics.AnalyticsEventProperty.WORKOUT_ANALYSIS_WORKOUT_DISTANCE
import com.stt.android.analytics.AnalyticsEventProperty.WORKOUT_ANALYSIS_WORKOUT_DURATION
import com.stt.android.analytics.AnalyticsEventProperty.WORKOUT_DURATION
import com.stt.android.analytics.AnalyticsEventProperty.WORKOUT_PLAYBACK_DIMENSION
import com.stt.android.analytics.AnalyticsEventProperty.WORKOUT_PLAYBACK_INITIATED_FROM
import com.stt.android.analytics.AnalyticsEventProperty.WORKOUT_PLAYBACK_NEW_DIMENSION
import com.stt.android.analytics.AnalyticsEventProperty.WORKOUT_PLAYBACK_PAUSE_REASON
import com.stt.android.analytics.AnalyticsEventProperty.WORKOUT_PLAYBACK_PLAYBACK_TYPE
import com.stt.android.analytics.AnalyticsEventProperty.WORKOUT_PLAYBACK_TIMELINE_POINT
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsPropertyValue.MAP_NO_HEATMAP
import com.stt.android.analytics.AnalyticsPropertyValue.ReportProperty
import com.stt.android.analytics.AnalyticsPropertyValue.SourceProperty.WORKOUT_DETAILS
import com.stt.android.analytics.AnalyticsPropertyValue.TargetAccountType
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.analytics.FirebaseAnalyticsTracker
import com.stt.android.analytics.toOnOff
import com.stt.android.analytics.toYesNo
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.VideoModel
import com.stt.android.core.domain.GraphType
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.achievements.GetAchievementUseCase
import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.user.GetUserByUsernameUseCase
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.people.PeopleController
import com.stt.android.models.MapSelectionModel
import com.stt.android.ui.extensions.targetWorkoutVisibility
import com.stt.android.workout.details.graphanalysis.AnalysisLapsData
import com.stt.android.workout.details.graphanalysis.playback.PlaybackType
import com.stt.android.workout.details.graphanalysis.playback.WorkoutPlaybackPauseReason
import com.stt.android.workout.details.workoutheader.WorkoutHeaderLoader
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.roundToInt

interface WorkoutDetailsAnalytics {
    fun setWorkoutAnalysisGraphTypes(
        main: GraphType,
        comparison: GraphType,
        background: GraphType
    )

    fun multisportPartChanged()

    fun setPlaybackType(playbackType: PlaybackType)

    fun setIsFullscreenGraphAnalysis(isFullscreenGraphAnalysis: Boolean)

    suspend fun trackWorkoutDetailsScreenEvent(
        analyticsSource: String?
    )

    suspend fun trackWorkoutAnalysisScreenEvent(
        analyticsSource: String?,
        analyticsContext: String?
    )

    /**
     * Should be triggered every time fullscreen analytics is opened, even if
     * other similar events are triggered
     */
    suspend fun trackWorkoutAnalysisFullScreenEvent()

    suspend fun trackShowDiveEvents(
        sml: Sml?
    )

    suspend fun trackReportWorkoutEvent()

    suspend fun trackSaveRouteEvent()

    suspend fun trackSendCommentEvent()

    suspend fun trackLikeWorkoutEvent()

    suspend fun trackWorkoutValuesGridExpandedEvent()

    suspend fun trackWorkoutMultisportDetailsScreenEvent(
        multisportPartActivity: MultisportPartActivity?,
        sml: Sml
    )

    suspend fun trackWorkoutValueDescriptionOpenedEvent(
        valueName: String,
        analyticsContext: String?
    )

    suspend fun trackWorkoutExportEvent(
        exportType: String,
        activityType: Int
    )

    suspend fun trackWorkoutExportError(
        exportType: String,
        errorCode: Int? = null
    )

    suspend fun trackLapTableChangeIntervalEvent(
        newInterval: LapsTableType,
        activityName: String,
        isPartOfMultisport: Boolean
    )

    suspend fun trackWorkoutPlaybackInitialPlay(
        fromTimelineStart: Boolean,
        isLapChosen: Boolean,
        initiatedFrom: String,
    )

    suspend fun trackWorkoutPlaybackResume(
        pauseReason: WorkoutPlaybackPauseReason,
        isLapChosen: Boolean,
        initiatedFrom: String
    )

    suspend fun trackWorkoutPlaybackEnd(initiatedFrom: String, isLapChosen: Boolean)

    fun trackMap3dModeChangedDuringPlayback(map3dEnabled: Boolean)

    suspend fun trackAnalysisBottomSheetOpened(
        openingContext: String
    )

    suspend fun trackAnalysisCustomizeGraphs(
        main: GraphType,
        comparison: GraphType,
        background: GraphType
    )

    suspend fun trackAnalysisMapModeChanged()

    suspend fun trackAnalysisFullscreenMode()

    suspend fun trackAnalysisLapSelectionChanged(
        lapsData: AnalysisLapsData,
        lapCount: Int,
        lapDistanceMeters: Double
    )

    fun trackAnalysisThirtyDaySummaryGraphSwipe()
}

@ActivityRetainedScoped
class DefaultWorkoutDetailsAnalytics
@Inject constructor(
    private val videoModel: VideoModel,
    private val peopleController: PeopleController,
    private val currentUserController: CurrentUserController,
    private val emarsysAnalytics: EmarsysAnalytics,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val getAchievementUseCase: GetAchievementUseCase,
    private val mapSelectionModel: MapSelectionModel,
    private val workoutHeaderLoader: WorkoutHeaderLoader,
    private val firebaseAnalyticsTracker: FirebaseAnalyticsTracker,
    private val getUserByUsernameUseCase: GetUserByUsernameUseCase,
) : WorkoutDetailsAnalytics {
    // Cache of the selected analysis graph types is kept in a SharedFlow to let events
    // that are fired before the graph data is loaded to wait for the loading to finish
    // so they can add the types as properties. Nullable so it can be reset when multisport
    // part changes to one with possibly different graph types
    private val graphAnalysisInfoFlow =
        MutableStateFlow<Triple<GraphType, GraphType, GraphType>?>(null)

    private var playbackType = PlaybackType.STATIC_CAMERA
    private var isFullscreenGraphAnalysis = false

    override fun setWorkoutAnalysisGraphTypes(
        main: GraphType,
        comparison: GraphType,
        background: GraphType
    ) {
        graphAnalysisInfoFlow.tryEmit(Triple(main, comparison, background))
    }

    override fun multisportPartChanged() {
        graphAnalysisInfoFlow.tryEmit(null)
    }

    override fun setPlaybackType(playbackType: PlaybackType) {
        this.playbackType = playbackType
    }

    override fun setIsFullscreenGraphAnalysis(isFullscreenGraphAnalysis: Boolean) {
        this.isFullscreenGraphAnalysis = isFullscreenGraphAnalysis
    }

    override suspend fun trackWorkoutDetailsScreenEvent(
        analyticsSource: String?
    ) {
        withContext(IO) {
            runSuspendCatching {
                val header = getHeader()
                val videoCount = videoModel.count(header.id)
                val relationship = getRelationship(header)
                val achievementsCount =
                    header.key?.run { getAchievementUseCase(this)?.count } ?: 0
                AnalyticsProperties().apply {
                    put(SOURCE, analyticsSource ?: AnalyticsPropertyValue.WorkoutDetailsSourceProperty.OTHER)
                    put(ACTIVITY_TYPE, header.activityType.simpleName)
                    put(DISTANCE_IN_METERS, header.totalDistance)
                    put(DURATION_IN_MINUTES, (header.totalTime / 60.0).roundToInt())
                    put(HAS_DESCRIPTION, header.description?.isNotEmpty() ?: false)
                    put(NUM_LIKES, header.reactionCount)
                    put(NUM_COMMENTS, header.commentCount)
                    put(NUM_PHOTOS, header.pictureCount)
                    put(NUMBER_OF_VIDEOS, videoCount)
                    put(TARGET_ACCOUNT_TYPE, TargetAccountType.NORMAL)
                    put(TARGET_RELATIONSHIP, relationship)
                    put(TARGET_WORKOUT_VISIBILITY, header.targetWorkoutVisibility())
                    put(NUMBER_OF_ACHIEVEMENTS, achievementsCount)
                    if (currentUserController.username == header.username) {
                        val suuntoTagsString = header.suuntoTags.joinToString { it.analyticsName }
                        if (suuntoTagsString.isNotBlank()) {
                            put(PUBLIC_WORKOUT_TAGS, suuntoTagsString)
                        }
                        put(CUSTOM_TAGS_ADDED, header.userTags.isNotEmpty().toYesNo())
                    }
                    amplitudeAnalyticsTracker.trackEvent(
                        AnalyticsEvent.WORKOUT_DETAILS_SCREEN,
                        this
                    )
                }
            }.onFailure { e ->
                Timber.w(e, "Failed to send WorkoutDetailsMapScreenEvent")
            }
        }
    }

    override suspend fun trackWorkoutAnalysisScreenEvent(
        analyticsSource: String?,
        analyticsContext: String?
    ) {
        withContext(IO) {
            runSuspendCatching {
                val header = getHeader()
                val videoCount = videoModel.count(header.id)
                val relationship = getRelationship(header)

                AnalyticsProperties().apply {
                    put(SOURCE, analyticsSource)
                    put(CONTEXT, analyticsContext)
                    put(ACTIVITY_TYPE, header.activityType.simpleName)
                    put(DISTANCE_IN_METERS, header.totalDistance)
                    put(DURATION_IN_MINUTES, (header.totalTime / 60.0).roundToInt())
                    put(HAS_DESCRIPTION, header.description?.isNotEmpty() ?: false)
                    put(NUM_PHOTOS, header.pictureCount)
                    put(NUMBER_OF_VIDEOS, videoCount)
                    put(TARGET_ACCOUNT_TYPE, TargetAccountType.NORMAL)
                    put(TARGET_RELATIONSHIP, relationship)
                    put(TARGET_WORKOUT_VISIBILITY, header.targetWorkoutVisibility())
                    if (!isFullscreenGraphAnalysis) {
                        put(MAP_TYPE, mapSelectionModel.selectedMapType.analyticsName)
                        put(
                            MAP_HEATMAP_TYPE,
                            mapSelectionModel.selectedHeatmap?.analyticsName ?: MAP_NO_HEATMAP
                        )
                        put(MAP_3D_MODE, mapSelectionModel.map3dEnabled.toOnOff())
                    }
                    amplitudeAnalyticsTracker.trackEvent(
                        AnalyticsEvent.WORKOUT_ANALYSIS_SCREEN,
                        this
                    )
                }
            }.onFailure { e ->
                Timber.w(e, "Failed to send WorkoutAnalysisScreen event")
            }
        }
    }

    override suspend fun trackWorkoutAnalysisFullScreenEvent() {
        withContext(IO) {
            val header = getHeader()
            val relationship = getRelationship(header)

            val properties = AnalyticsProperties().apply {
                put(ACTIVITY_TYPE, header.activityType.simpleName)
                put(DURATION_IN_MINUTES, (header.totalTime / 60.0).roundToInt())
                put(DISTANCE_IN_METERS, header.totalDistance.roundToInt())
                // TODO - Update if support for passing lap to fullscreen from map is implemented
                put(
                    WORKOUT_ANALYSIS_LAP_SELECTION_TYPE,
                    AnalyticsPropertyValue.WorkoutAnalysisLapSelectionType.WHOLE_EXERCISE
                )
                put(TARGET_RELATIONSHIP, relationship)
                put(TARGET_WORKOUT_VISIBILITY, header.targetWorkoutVisibility())
            }

            amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.WORKOUT_ANALYSIS_FULLSCREEN, properties)
        }
    }

    override suspend fun trackShowDiveEvents(sml: Sml?) {
        withContext(IO) {
            runSuspendCatching {
                val header = getHeader()
                val eventCount = sml?.streamData?.getDiveEvents(sml.summary.isDiveAfterTissueReset)
                    ?.count { it.data.elapsed != null } ?: 0

                val relationship = getRelationship(header)

                AnalyticsProperties().apply {
                    put(AMOUNT_OF_EVENTS, eventCount)
                    put(ACTIVITY_TYPE, header.activityType.simpleName)
                    put(WORKOUT_DURATION, header.totalTime.roundToInt())
                    put(SUUNTO_DIVE_DEPTH_MAX, sml?.summary?.header?.depth?.max ?: 0)
                    put(SUUNTO_DIVE_DEPTH_AVG, sml?.summary?.header?.depth?.avg ?: 0)
                    put(
                        SUUNTO_DIVE_NUMBER_IN_SERIES,
                        sml?.summary?.header?.diving?.numberInSeries ?: 1
                    )
                    put(TARGET_RELATIONSHIP, relationship)
                    put(TARGET_WORKOUT_VISIBILITY, header.targetWorkoutVisibility())
                    amplitudeAnalyticsTracker.trackEvent(
                        AnalyticsEvent.SUUNTO_WORKOUT_DETAILS_DIVE_SHOW_EVENTS,
                        this
                    )
                }
            }.onFailure { e ->
                Timber.w(e, "Failed to send WorkoutDetailsDiveShowEvents")
            }
        }
    }

    override suspend fun trackReportWorkoutEvent() {
        withContext(IO) {
            runSuspendCatching {
                val header = getHeader()
                val videoCount = videoModel.count(header.id)

                val relationship = getRelationship(header)

                AnalyticsProperties().apply {
                    put(CONTENT_TYPE, ReportProperty.WORKOUT)
                    put(TARGET_RELATIONSHIP, relationship)
                    put(TARGET_WORKOUT_VISIBILITY, header.targetWorkoutVisibility())
                    put(NUMBER_OF_PHOTOS, header.pictureCount)
                    put(NUMBER_OF_VIDEOS, videoCount)
                    put(REPORTED_WORKOUT_ID, header.key)
                    put(REPORTED_USER_ID, header.username)
                    put(HAS_DESCRIPTION, header.description?.isNotEmpty() ?: false)
                    put(DESCRIPTION, header.description)
                    amplitudeAnalyticsTracker.trackEvent(
                        AnalyticsEvent.REPORT_CONTENT,
                        this
                    )
                }
            }.onFailure { e ->
                Timber.w(e, "Failed to send ReportContent")
            }
        }
    }

    override suspend fun trackSaveRouteEvent() {
        withContext(IO) {
            runSuspendCatching {
                val header = getHeader()
                val relationship = getRelationship(header)

                AnalyticsProperties().apply {
                    put(TARGET_ACCOUNT_TYPE, TargetAccountType.NORMAL)
                    put(TARGET_RELATIONSHIP, relationship)
                    put(DISTANCE_IN_METERS, header.totalDistance)
                    put(ACTIVITY_TYPE, header.activityType.simpleName)
                    amplitudeAnalyticsTracker.trackEvent(
                        AnalyticsEvent.ROUTE_FROM_ACTIVITY,
                        this
                    )
                }
            }.onFailure { e ->
                Timber.w(e, "Failed to send RouteRouteFromActivity")
            }
        }
    }

    override suspend fun trackSendCommentEvent() {
        withContext(IO) {
            runSuspendCatching {
                val header = getHeader()
                val videoCount = videoModel.count(header.id)
                val activityType: String = header.activityType.simpleName
                val relationship = getRelationship(header)
                val achievementsCount =
                    header.key?.run { getAchievementUseCase(this)?.count } ?: 0

                AnalyticsProperties().apply {
                    put(TARGET_ACCOUNT_TYPE, TargetAccountType.NORMAL)
                    put(TARGET_RELATIONSHIP, relationship)
                    put(TARGET_WORKOUT_VISIBILITY, header.targetWorkoutVisibility())
                    put(ACTIVITY_TYPE, activityType)
                    put(DURATION_IN_MINUTES, (header.totalTime / 60.0).roundToInt())
                    put(DISTANCE_IN_METERS, header.totalDistance)
                    put(NUM_LIKES, header.reactionCount)
                    put(NUM_COMMENTS, header.commentCount + 1)
                    put(NUM_PHOTOS, header.pictureCount)
                    put(NUMBER_OF_VIDEOS, videoCount)
                    put(HAS_DESCRIPTION, header.description?.isNotEmpty() ?: false)
                    put(NUMBER_OF_ACHIEVEMENTS, achievementsCount)
                    amplitudeAnalyticsTracker.trackEvent(
                        AnalyticsEvent.COMMENT_WORKOUT,
                        this
                    )
                    emarsysAnalytics.trackEventWithProperties(
                        AnalyticsEvent.COMMENT_WORKOUT,
                        this.map
                    )
                    firebaseAnalyticsTracker.trackEvent(
                        AnalyticsEvent.COMMENT_WORKOUT,
                        this
                    )
                }
            }.onFailure { e ->
                Timber.w(e, "Failed to send CommentWorkout")
            }
        }
    }

    override suspend fun trackLikeWorkoutEvent() {
        withContext(IO) {
            runSuspendCatching {
                val header = getHeader()
                val videoCount = videoModel.count(header.id)
                val activityType: String = header.activityType.simpleName
                val achievementsCount =
                    header.key?.run { getAchievementUseCase(this)?.count } ?: 0

                AnalyticsProperties().apply {
                    put(SOURCE, WORKOUT_DETAILS)
                    put(TARGET_ACCOUNT_TYPE, TargetAccountType.NORMAL)
                    put(TARGET_WORKOUT_VISIBILITY, header.targetWorkoutVisibility())
                    put(ACTIVITY_TYPE, activityType)
                    put(DURATION_IN_MINUTES, (header.totalTime / 60.0).roundToInt())
                    put(DISTANCE_IN_METERS, header.totalDistance)
                    put(NUM_LIKES, header.reactionCount)
                    put(NUM_COMMENTS, header.commentCount)
                    put(NUM_PHOTOS, header.pictureCount)
                    put(NUMBER_OF_VIDEOS, videoCount)
                    put(NUMBER_OF_ACHIEVEMENTS, achievementsCount)
                    put(HAS_DESCRIPTION, header.description?.isNotEmpty() ?: false)
                    amplitudeAnalyticsTracker.trackEvent(
                        AnalyticsEvent.LIKE_WORKOUT,
                        this
                    )
                }
            }.onFailure { e ->
                Timber.w(e, "Failed to send CommentWorkout")
            }
        }
    }

    override suspend fun trackWorkoutValuesGridExpandedEvent() {
        withContext(IO) {
            val header = getHeader()
            val activityType: String = header.activityType.simpleName
            AnalyticsProperties().apply {
                put(ACTIVITY_TYPE, activityType)
                amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.WORKOUT_DETAILS_GRID_VIEW_MORE, this)
            }
        }
    }

    override suspend fun trackWorkoutMultisportDetailsScreenEvent(
        multisportPartActivity: MultisportPartActivity?,
        sml: Sml
    ) {
        withContext(IO) {
            runSuspendCatching {
                val header = getHeader()
                val stId = multisportPartActivity?.activityType ?: return@withContext
                val activityWindow = sml.getActivityWindow(multisportPartActivity) ?: return@withContext
                val position = sml.summary.activityWindows.indexOf(activityWindow) + 1
                val activityType: String = ActivityType.valueOf(stId).simpleName
                val relationship = getRelationship(header)

                AnalyticsProperties().apply {
                    put(ACTIVITY_TYPE, activityType)
                    put(POSITION_IN_MULTISPORT_WORKOUT, position)
                    put(NUM_ACTIVITIES, sml.streamData.multisportPartActivities.size)
                    put(ACTIVITY_DURATION, ((activityWindow.duration ?: 0f) / 60.0).roundToInt())
                    put(TOTAL_DURATION, (header.totalTime / 60.0).roundToInt())
                    put(ACTIVITY_DISTANCE, activityWindow.distance ?: 0f)
                    put(TOTAL_DISTANCE, header.totalDistance)
                    put(TARGET_RELATIONSHIP, relationship)
                    put(TARGET_WORKOUT_VISIBILITY, header.targetWorkoutVisibility())
                    amplitudeAnalyticsTracker.trackEvent(
                        AnalyticsEvent.WORKOUT_MULTISPORT_DETAILS_SCREEN,
                        this
                    )
                }
            }.onFailure { e ->
                Timber.w(e, "Failed to send WorkoutMultisportDetailsScreen event")
            }
        }
    }

    override suspend fun trackWorkoutValueDescriptionOpenedEvent(
        valueName: String,
        analyticsContext: String?
    ) {
        withContext(IO) {
            runSuspendCatching {
                val header = getHeader()
                val activityType: String = header.activityType.simpleName
                val relationship = getRelationship(header)

                AnalyticsProperties().apply {
                    put(CONTEXT, analyticsContext)
                    put(ACTIVITY_TYPE, activityType)
                    put(TARGET_WORKOUT_VISIBILITY, header.targetWorkoutVisibility())
                    put(VALUE_NAME, valueName)
                    put(TARGET_RELATIONSHIP, relationship)
                    amplitudeAnalyticsTracker.trackEvent(
                        AnalyticsEvent.WORKOUT_DETAILS_VALUE_DESCRIPTION_OPENED,
                        this
                    )
                }
            }.onFailure { e ->
                Timber.w(e, "Failed to send workout details value description opened event")
            }
        }
    }

    override suspend fun trackWorkoutExportEvent(exportType: String, activityType: Int) {
        withContext(IO) {
            try {
                AnalyticsProperties().apply {
                    put(EXPORT_TYPE, exportType)
                    put(SOURCE, AnalyticsPropertyValue.ExportSource.WORKOUT_DETAILS)
                    put(ACTIVITY_TYPE, ActivityType.valueOf(activityType).simpleName)
                    put(RESULT, AnalyticsPropertyValue.ExportResult.OK)
                    amplitudeAnalyticsTracker.trackEvent(
                        AnalyticsEvent.EXPORT_FILE,
                        this
                    )
                }
            } catch (e: Exception) {
                Timber.w(e, "Failed to send track export event")
            }
        }
    }

    override suspend fun trackWorkoutExportError(exportType: String, errorCode: Int?) {
        withContext(IO) {
            try {
                AnalyticsProperties().apply {
                    put(EXPORT_TYPE, exportType)
                    put(SOURCE, AnalyticsPropertyValue.ExportSource.WORKOUT_DETAILS)
                    if (errorCode != null) {
                        put(RESULT, "${AnalyticsPropertyValue.ExportResult.ERROR}: $errorCode")
                    } else {
                        put(RESULT, AnalyticsPropertyValue.ExportResult.ERROR)
                    }

                    amplitudeAnalyticsTracker.trackEvent(
                        AnalyticsEvent.EXPORT_FILE,
                        this
                    )
                }
            } catch (e: Exception) {
                Timber.w(e, "Failed to send workout export error")
            }
        }
    }

    private suspend fun getRelationship(workoutHeader: WorkoutHeader): String {
        val isCurrentUserWorkout =
            currentUserController.username == workoutHeader.username
        return if (isCurrentUserWorkout) {
            SELF
        } else {
            val user = getUserByUsernameUseCase.getUserByUsername(
                username = workoutHeader.username,
                queryRemoteIfNeeded = true,
            )
            peopleController.getFollowRelationshipValueForAnalytics(user)
        }
    }

    override suspend fun trackLapTableChangeIntervalEvent(
        newInterval: LapsTableType,
        activityName: String,
        isPartOfMultisport: Boolean
    ) {
        withContext(IO) {
            try {
                AnalyticsProperties().apply {
                    put(ACTIVITY_TYPE, activityName)
                    putYesNo(PART_OF_MULTISPORT_WORKOUT, isPartOfMultisport)
                    put(
                        NEW_INTERVAL,
                        when (newInterval) {
                            LapsTableType.ONE_KM_AUTO_LAP -> AnalyticsPropertyValue.LapsTableType.ONE_KM_AUTO_LAP
                            LapsTableType.FIVE_KM_AUTO_LAP -> AnalyticsPropertyValue.LapsTableType.FIVE_KM_AUTO_LAP
                            LapsTableType.TEN_KM_AUTO_LAP -> AnalyticsPropertyValue.LapsTableType.TEN_KM_AUTO_LAP
                            LapsTableType.ONE_MILE_AUTO_LAP -> AnalyticsPropertyValue.LapsTableType.ONE_MILE_AUTO_LAP
                            LapsTableType.FIVE_MILE_AUTO_LAP -> AnalyticsPropertyValue.LapsTableType.FIVE_MILE_AUTO_LAP
                            LapsTableType.TEN_MILE_AUTO_LAP -> AnalyticsPropertyValue.LapsTableType.TEN_MILE_AUTO_LAP
                            LapsTableType.DISTANCE_AUTO_LAP -> AnalyticsPropertyValue.LapsTableType.DISTANCE_AUTO_LAP
                            LapsTableType.DURATION_AUTO_LAP -> AnalyticsPropertyValue.LapsTableType.DURATION_AUTO_LAP
                            LapsTableType.MANUAL -> AnalyticsPropertyValue.LapsTableType.MANUAL
                            LapsTableType.INTERVAL -> AnalyticsPropertyValue.LapsTableType.INTERVAL
                            LapsTableType.DOWNHILL -> AnalyticsPropertyValue.LapsTableType.DOWNHILL
                            LapsTableType.DIVE -> AnalyticsPropertyValue.LapsTableType.DIVE
                        }
                    )
                    amplitudeAnalyticsTracker.trackEvent(
                        AnalyticsEvent.WORKOUT_DETAILS_LAP_TABLE_CHANGE_INTERVAL,
                        this
                    )
                }
            } catch (exception: Exception) {
                Timber.w(exception, "Failed to send track lap table change interval event")
            }
        }
    }

    override suspend fun trackWorkoutPlaybackInitialPlay(
        fromTimelineStart: Boolean,
        isLapChosen: Boolean,
        initiatedFrom: String
    ) {
        withContext(IO) {
            runSuspendCatching {
                val header = getHeader()
                val relationship = getRelationship(header)
                val properties = AnalyticsProperties().apply {
                    if (!isFullscreenGraphAnalysis) {
                        put(WORKOUT_PLAYBACK_DIMENSION, mapSelectionModel.map3dEnabled.toMapDimensionProperty())
                        put(WORKOUT_PLAYBACK_PLAYBACK_TYPE, playbackType.toAnalyticsProperty())
                    }
                    put(
                        WORKOUT_PLAYBACK_TIMELINE_POINT,
                        fromTimelineStart.toTimelinePointProperty()
                    )
                    put(WORKOUT_PLAYBACK_INITIATED_FROM, initiatedFrom)
                    put(WORKOUT_ANALYSIS_FULL_SCREEN_MODE, isFullscreenGraphAnalysis.toYesNo())
                    put(WORKOUT_ANALYSIS_IS_LAP_CHOSEN, isLapChosen.toYesNo())
                    put(TARGET_RELATIONSHIP, relationship)
                    put(TARGET_WORKOUT_VISIBILITY, header.targetWorkoutVisibility())
                }
                amplitudeAnalyticsTracker.trackEvent(
                    AnalyticsEvent.WORKOUT_PLAYBACK_START,
                    properties
                )
            }.onFailure { e ->
                Timber.w(e, "Failed to send playback initial start event")
            }
        }
    }

    override suspend fun trackWorkoutPlaybackResume(
        pauseReason: WorkoutPlaybackPauseReason,
        isLapChosen: Boolean,
        initiatedFrom: String
    ) {
        withContext(IO) {
            runSuspendCatching {
                val header = getHeader()
                val relationship = getRelationship(header)
                val properties = AnalyticsProperties().apply {
                    if (!isFullscreenGraphAnalysis) {
                        put(WORKOUT_PLAYBACK_PLAYBACK_TYPE, playbackType.toAnalyticsProperty())
                    }
                    put(WORKOUT_PLAYBACK_PAUSE_REASON, pauseReason.toAnalyticsProperty())
                    put(WORKOUT_PLAYBACK_INITIATED_FROM, initiatedFrom)
                    put(WORKOUT_ANALYSIS_FULL_SCREEN_MODE, isFullscreenGraphAnalysis.toYesNo())
                    put(WORKOUT_ANALYSIS_IS_LAP_CHOSEN, isLapChosen.toYesNo())
                    put(TARGET_RELATIONSHIP, relationship)
                    put(TARGET_WORKOUT_VISIBILITY, header.targetWorkoutVisibility())
                }

                amplitudeAnalyticsTracker.trackEvent(
                    AnalyticsEvent.WORKOUT_PLAYBACK_RESUME,
                    properties
                )
            }.onFailure { e ->
                Timber.w(e, "Failed to send playback resume event")
            }
        }
    }

    override suspend fun trackWorkoutPlaybackEnd(initiatedFrom: String, isLapChosen: Boolean) {
        withContext(IO) {
            runSuspendCatching {
                val header = getHeader()
                val relationship = getRelationship(header)
                val properties = AnalyticsProperties().apply {
                    put(WORKOUT_PLAYBACK_INITIATED_FROM, initiatedFrom)
                    put(WORKOUT_ANALYSIS_FULL_SCREEN_MODE, isFullscreenGraphAnalysis.toYesNo())
                    put(WORKOUT_ANALYSIS_IS_LAP_CHOSEN, isLapChosen.toYesNo())
                    put(TARGET_RELATIONSHIP, relationship)
                    put(TARGET_WORKOUT_VISIBILITY, header.targetWorkoutVisibility())
                }

                amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.WORKOUT_PLAYBACK_END, properties)
            }.onFailure { e ->
                Timber.w(e, "Failed to send playback ended event")
            }
        }
    }

    override fun trackMap3dModeChangedDuringPlayback(map3dEnabled: Boolean) {
        try {
            val properties = AnalyticsProperties().apply {
                put(WORKOUT_PLAYBACK_NEW_DIMENSION, map3dEnabled.toMapDimensionProperty())
            }
            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.WORKOUT_PLAYBACK_CHANGE_DIMENSION,
                properties
            )
        } catch (e: Exception) {
            Timber.w(e, "Failed to send playback change map 3d mode event")
        }
    }

    override suspend fun trackAnalysisBottomSheetOpened(
        openingContext: String
    ) {
        withContext(IO) {
            val header = getHeader()
            graphAnalysisInfoFlow
                .filterNotNull()
                .take(1)
                .collect { graphTypes ->
                    val properties = createWorkoutAnalysisEventProperties(
                        header,
                        mapSelectionModel,
                        graphTypes,
                        isFullscreenGraphAnalysis
                    ).apply {
                        put(CONTEXT, openingContext)
                    }

                    amplitudeAnalyticsTracker.trackEvent(
                        AnalyticsEvent.WORKOUT_ANALYSIS_BOTTOM_SHEET_OPENED,
                        properties
                    )
                }
        }
    }

    override suspend fun trackAnalysisCustomizeGraphs(
        main: GraphType,
        comparison: GraphType,
        background: GraphType
    ) {
        withContext(IO) {
            val header = getHeader()
            val relationship = getRelationship(header)
            setWorkoutAnalysisGraphTypes(main, comparison, background)
            val properties = createWorkoutAnalysisEventProperties(
                header,
                mapSelectionModel,
                Triple(main, comparison, background),
                isFullscreenGraphAnalysis
            ).apply {
                put(
                    CONTEXT,
                    if (isFullscreenGraphAnalysis) {
                        AnalyticsPropertyValue.WorkoutAnalysisLapSelectionContext.FULLSCREEN
                    } else {
                        AnalyticsPropertyValue.WorkoutAnalysisLapSelectionContext.DEFAULT_VIEW
                    }
                )
                put(TARGET_RELATIONSHIP, relationship)
                put(TARGET_WORKOUT_VISIBILITY, header.targetWorkoutVisibility())
            }
            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.WORKOUT_ANALYSIS_CUSTOMIZE_GRAPHS,
                properties
            )
        }
    }

    override suspend fun trackAnalysisMapModeChanged() {
        withContext(IO) {
            val header = getHeader()
            graphAnalysisInfoFlow
                .filterNotNull()
                .take(1)
                .collect { graphTypes ->
                    val properties = createWorkoutAnalysisEventProperties(
                        header,
                        mapSelectionModel,
                        graphTypes,
                        isFullscreenGraphAnalysis
                    )
                    amplitudeAnalyticsTracker.trackEvent(
                        AnalyticsEvent.WORKOUT_ANALYSIS_MAP_MODE_CHANGED,
                        properties
                    )
                }
        }
    }

    override suspend fun trackAnalysisFullscreenMode() {
        withContext(IO) {
            val header = getHeader()
            graphAnalysisInfoFlow
                .filterNotNull()
                .take(1)
                .collect { graphTypes ->
                    val properties = createWorkoutAnalysisEventProperties(
                        header,
                        mapSelectionModel,
                        graphTypes,
                        isFullscreenGraphAnalysis
                    )
                    amplitudeAnalyticsTracker.trackEvent(
                        AnalyticsEvent.WORKOUT_ANALYSIS_FULLSCREEN_MODE,
                        properties
                    )
                }
        }
    }

    override suspend fun trackAnalysisLapSelectionChanged(
        lapsData: AnalysisLapsData,
        lapCount: Int,
        lapDistanceMeters: Double
    ) {
        withContext(IO) {
            val header = getHeader()
            val relationship = getRelationship(header)

            val properties = AnalyticsProperties().apply {
                put(ACTIVITY_TYPE, header.activityType.simpleName)
                put(
                    CONTEXT,
                    if (isFullscreenGraphAnalysis) {
                        AnalyticsPropertyValue.WorkoutAnalysisLapSelectionContext.FULLSCREEN
                    } else {
                        AnalyticsPropertyValue.WorkoutAnalysisLapSelectionContext.DEFAULT_VIEW
                    }
                )
                put(WORKOUT_ANALYSIS_LAP_SELECTION_TYPE, lapsData.getLapSelectionType())

                if (lapsData.lapsTableType != null) {
                    put(
                        WORKOUT_ANALYSIS_LAP_TYPE,
                        if (lapsData.lapsTableType == LapsTableType.MANUAL) {
                            AnalyticsPropertyValue.WorkoutAnalysisLapType.MANUAL
                        } else {
                            AnalyticsPropertyValue.WorkoutAnalysisLapType.AUTOMATIC
                        }
                    )
                }

                put(WORKOUT_ANALYSIS_LAP_COUNT, lapCount)
                put(WORKOUT_ANALYSIS_LAP_NUMBER, lapsData.selectedLap?.lapNumber ?: 1)

                val workoutDurationMinutes = (header.totalTime / 60.0).roundToInt()
                val lapDurationMinutes = if (lapsData.lapDurationSeconds != null) {
                    (lapsData.lapDurationSeconds / 60.0).roundToInt()
                } else {
                    workoutDurationMinutes
                }
                put(WORKOUT_ANALYSIS_WORKOUT_DURATION, workoutDurationMinutes)
                put(WORKOUT_ANALYSIS_LAP_DURATION, lapDurationMinutes)
                put(WORKOUT_ANALYSIS_WORKOUT_DISTANCE, header.totalDistance.roundToInt())
                put(WORKOUT_ANALYSIS_LAP_DISTANCE, lapDistanceMeters.roundToInt())
                put(TARGET_RELATIONSHIP, relationship)
                put(TARGET_WORKOUT_VISIBILITY, header.targetWorkoutVisibility())
            }

            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.WORKOUT_ANALYSIS_LAP_SELECTION_CHANGED,
                properties
            )
        }
    }

    private suspend fun getHeader(): WorkoutHeader = workoutHeaderLoader
        .workoutHeaderFlow
        .mapNotNull { it.data }
        .first()

    override fun trackAnalysisThirtyDaySummaryGraphSwipe() {
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.THIRTY_DAY_SUMMARY_GRAPH_SWIPE)
    }
}

private fun Boolean.toMapDimensionProperty() =
    if (this) {
        AnalyticsPropertyValue.WorkoutPlaybackDimension.THREE_DIMENSIONAL
    } else {
        AnalyticsPropertyValue.WorkoutPlaybackDimension.TWO_DIMENSIONAL
    }

private fun Boolean.toTimelinePointProperty() =
    if (this) {
        AnalyticsPropertyValue.WorkoutPlaybackTimelinePoint.START
    } else {
        AnalyticsPropertyValue.WorkoutPlaybackTimelinePoint.MIDDLE
    }

private fun WorkoutPlaybackPauseReason.toAnalyticsProperty() = when (this) {
    WorkoutPlaybackPauseReason.PauseButton -> AnalyticsPropertyValue.WorkoutPlaybackPauseReason.PAUSE_BUTTON
    WorkoutPlaybackPauseReason.MoveTimelinePoint -> AnalyticsPropertyValue.WorkoutPlaybackPauseReason.MOVE_TIMELINE_POINT
    WorkoutPlaybackPauseReason.ScreenExit -> AnalyticsPropertyValue.WorkoutPlaybackPauseReason.SCREEN_EXIT
    WorkoutPlaybackPauseReason.BottomSheetStateChanged -> AnalyticsPropertyValue.WorkoutPlaybackPauseReason.BOTTOM_SHEET_STATE_CHANGE
    WorkoutPlaybackPauseReason.SelectedLapChanged -> AnalyticsPropertyValue.WorkoutPlaybackPauseReason.SELECTED_LAP_CHANGE
}

private fun PlaybackType.toAnalyticsProperty() = when (this) {
    PlaybackType.MOVING_CAMERA_2D -> AnalyticsPropertyValue.WorkoutPlaybackType.MOVING_CAMERA_2D
    PlaybackType.MOVING_CAMERA_3D -> AnalyticsPropertyValue.WorkoutPlaybackType.MOVING_CAMERA_3D
    PlaybackType.STATIC_CAMERA -> AnalyticsPropertyValue.WorkoutPlaybackType.STATIC_CAMERA
}

private fun AnalysisLapsData.getLapSelectionType(): String {
    return if (lapEndSecondsInWorkout == null || lapStartSecondsInWorkout == null) {
        AnalyticsPropertyValue.WorkoutAnalysisLapSelectionType.WHOLE_EXERCISE
    } else if (isCustomLapSelected) {
        AnalyticsPropertyValue.WorkoutAnalysisLapSelectionType.MANUAL_ZOOM
    } else {
        AnalyticsPropertyValue.WorkoutAnalysisLapSelectionType.LAP
    }
}

private fun createWorkoutAnalysisEventProperties(
    header: WorkoutHeader,
    mapSelectionModel: MapSelectionModel,
    graphTypes: Triple<GraphType, GraphType, GraphType>,
    isFullscreenGraphAnalysis: Boolean
) = AnalyticsProperties().apply {
    put(ACTIVITY_TYPE, header.activityType.simpleName)
    put(DURATION_IN_MINUTES, (header.totalTime / 60.0).roundToInt())
    put(DISTANCE_IN_METERS, header.totalDistance)
    put(WORKOUT_ANALYSIS_MAIN_GRAPH_TYPE, graphTypes.first.key)
    put(WORKOUT_ANALYSIS_COMPARISON_GRAPH_TYPE, graphTypes.second.key)
    put(WORKOUT_ANALYSIS_BACKGROUND_GRAPH_TYPE, graphTypes.third.key)

    if (!isFullscreenGraphAnalysis) {
        put(MAP_TYPE, mapSelectionModel.selectedMapType.analyticsName)
        put(MAP_HEATMAP_TYPE, mapSelectionModel.selectedHeatmap?.analyticsName ?: MAP_NO_HEATMAP)
        put(MAP_3D_MODE, mapSelectionModel.map3dEnabled.toOnOff())
    }
}

fun navigatedFromValueToPlaybackInitiatedFrom(navigatedFrom: String?) = when (navigatedFrom) {
    AnalyticsPropertyValue.WorkoutAnalysisScreenSource.PLAY_BUTTON_FEED -> AnalyticsPropertyValue.WorkoutPlaybackInitiatedFrom.FEED
    AnalyticsPropertyValue.WorkoutAnalysisScreenSource.NO_MAP_ANALYSIS_BUTTON,
    AnalyticsPropertyValue.WorkoutAnalysisScreenSource.PLAY_BUTTON_WORKOUT_DETAILS,
    AnalyticsPropertyValue.WorkoutAnalysisScreenSource.MAP_OVERVIEW,
    AnalyticsPropertyValue.WorkoutAnalysisScreenSource.MAP_THUMBNAIL,
    AnalyticsPropertyValue.WorkoutAnalysisScreenSource.VIEW_ON_MAP_HR_ZONES,
    AnalyticsPropertyValue.WorkoutAnalysisScreenSource.VIEW_ON_MAP_INSIGHTS_GRAPH,
    AnalyticsPropertyValue.WorkoutAnalysisScreenSource.INSIGHTS_GRAPH,
    AnalyticsPropertyValue.WorkoutAnalysisScreenSource.MAP_ANALYSIS_OPEN_FULLSCREEN_BUTTON -> AnalyticsPropertyValue.WorkoutPlaybackInitiatedFrom.WORKOUT_DETAILS_SCREEN
    else -> AnalyticsPropertyValue.WorkoutPlaybackInitiatedFrom.UNKNOWN
}
