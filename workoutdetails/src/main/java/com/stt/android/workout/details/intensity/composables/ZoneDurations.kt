package com.stt.android.workout.details.intensity.composables

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.stt.android.compose.component.SuuntoLinearProgressBar
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.aerobic
import com.stt.android.compose.theme.anaerobic
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.theme.vo2Max
import com.stt.android.ui.components.ZoneDurationsView
import com.stt.android.utils.ZoneDurationData
import com.stt.android.workout.details.R
import kotlin.time.Duration.Companion.hours
import kotlin.time.Duration.Companion.seconds

@Composable
fun ZoneDurations(
    zoneDurationTitle: String?,
    zoneDurationData: List<ZoneDurationData>,
    modifier: Modifier = Modifier,
    onInfoClick: (() -> Unit)? = null,
) {
    Column(modifier = modifier.padding(top = MaterialTheme.spacing.medium)) {
        zoneDurationTitle?.let {
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = MaterialTheme.spacing.medium),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = zoneDurationTitle,
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier.padding(bottom = MaterialTheme.spacing.xsmall)
                )
                onInfoClick?.let {
                    IconButton(onClick = onInfoClick) {
                        Icon(
                            painter = painterResource(com.stt.android.R.drawable.ic_info_outline),
                            contentDescription = null,
                            tint = MaterialTheme.colors.primary,
                            modifier = Modifier.size(MaterialTheme.iconSizes.small)
                        )
                    }
                }
            }
        }
        AndroidView(factory = {
            ZoneDurationsView(it)
        }, modifier = Modifier.fillMaxWidth()) { zoneDurationsView ->
            zoneDurationsView.setZoneDurations(zoneDurationData)
        }
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
    }
}

@Composable
fun AerobicZoneDurations(
    vo2Max: ZoneDurationData,
    anaerobic: ZoneDurationData,
    aerobic: ZoneDurationData,
    modifier: Modifier = Modifier,
    onInfoClick: (() -> Unit)? = null
) {
    Column(
        modifier = modifier.padding(
            vertical = MaterialTheme.spacing.medium,
            horizontal = MaterialTheme.spacing.small
        )
    ) {
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = MaterialTheme.spacing.medium),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = stringResource(R.string.aerobic_zone_duration_title),
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier.padding(bottom = MaterialTheme.spacing.xsmall)
                )
                Text(
                    text = stringResource(com.stt.android.R.string.powered_by_zone_sense),
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colors.darkGrey
                )
            }
            onInfoClick?.let {
                IconButton(onClick = onInfoClick) {
                    Icon(
                        painter = painterResource(com.stt.android.R.drawable.ic_info_outline),
                        contentDescription = null,
                        tint = MaterialTheme.colors.primary,
                        modifier = Modifier.size(MaterialTheme.iconSizes.small)
                    )
                }
            }
        }
        AerobicZoneDurationItem(
            label = stringResource(R.string.aerobic_zone_duration_vo2max),
            color = MaterialTheme.colors.vo2Max,
            formattedDuration = vo2Max.formattedDuration.orEmpty(),
            percentage = vo2Max.percentage,
        )
        TextWithDashedLineOnRight(text = stringResource(R.string.aerobic_zone_duration_anaerobic_threshold))
        AerobicZoneDurationItem(
            label = stringResource(R.string.aerobic_zone_duration_anaerobic),
            color = MaterialTheme.colors.anaerobic,
            formattedDuration = anaerobic.formattedDuration.orEmpty(),
            percentage = anaerobic.percentage,
        )
        TextWithDashedLineOnRight(text = stringResource(R.string.aerobic_zone_duration_aerobic_threshold))
        AerobicZoneDurationItem(
            label = stringResource(R.string.aerobic_zone_duration_aerobic),
            color = MaterialTheme.colors.aerobic,
            formattedDuration = aerobic.formattedDuration.orEmpty(),
            percentage = aerobic.percentage,
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
        Row(verticalAlignment = Alignment.CenterVertically) {
            Icon(
                painter = painterResource(com.stt.android.R.drawable.heart_belt_outline),
                contentDescription = null,
                tint = MaterialTheme.colors.darkGrey
            )
            Text(
                text = stringResource(R.string.aerobic_zone_duration_description),
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium),
                color = MaterialTheme.colors.darkGrey
            )
        }
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
    }
}

@Composable
private fun AerobicZoneDurationItem(
    label: String,
    color: Color,
    formattedDuration: String,
    percentage: Int,
    modifier: Modifier = Modifier
) {
    val progress = percentage.toFloat() / 100
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = MaterialTheme.spacing.xsmall)
    ) {
        ZoneLabel(
            text = label,
            color = color,
            modifier = Modifier.weight(2f)
        )
        ZoneProgress(
            progress = progress,
            color = color,
            modifier = Modifier.weight(2f)
        )
        ZoneValue(
            text = "$percentage %",
            modifier = Modifier.weight(1f)
        )

        ZoneValue(
            text = formattedDuration,
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
private fun ZoneLabel(
    text: String,
    color: Color,
    modifier: Modifier = Modifier
) {
    Text(
        text = text,
        style = MaterialTheme.typography.bodyBold,
        color = color,
        modifier = modifier
    )
}

@Composable
private fun ZoneProgress(
    progress: Float,
    color: Color,
    modifier: Modifier = Modifier,
) {
    SuuntoLinearProgressBar(
        progress = progress,
        modifier = modifier.clip(RoundedCornerShape(25.dp)),
        color = color,
    )
}

@Composable
private fun ZoneValue(
    text: String,
    modifier: Modifier = Modifier
) {
    Text(
        text = text,
        style = MaterialTheme.typography.bodyBold,
        modifier = modifier,
        textAlign = TextAlign.Center,
        color = MaterialTheme.colors.nearBlack
    )
}

@Composable
fun TextWithDashedLineOnRight(text: String, modifier: Modifier = Modifier) {
    val nearBlack = MaterialTheme.colors.nearBlack
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = MaterialTheme.spacing.small),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.body,
            color = MaterialTheme.colors.nearBlack
        )

        Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))

        Canvas(
            modifier = Modifier
                .weight(1f)
                .height(1.dp)
        ) {
            val pathEffect = PathEffect.dashPathEffect(floatArrayOf(10f, 10f), 0f)
            drawLine(
                color = nearBlack,
                start = Offset.Zero,
                end = Offset(size.width, 0f),
                pathEffect = pathEffect
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun ZoneDurationsPreview() {
    AppTheme {
        ZoneDurations(
            zoneDurationTitle = "Heart rate zones",
            zoneDurationData = listOf(
                ZoneDurationData(
                    duration = 1.hours,
                    formattedDuration = "01:00.0",
                    percentage = 100,
                ),
            ),
            onInfoClick = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun AerobicZoneDurationsPreview() {
    AppTheme {
        AerobicZoneDurations(
            vo2Max = ZoneDurationData(
                duration = 121.seconds,
                formattedDuration = "2'01",
                percentage = 3,
            ),
            anaerobic = ZoneDurationData(
                duration = 1774.seconds,
                formattedDuration = "29'34",
                percentage = 45,
            ),
            aerobic = ZoneDurationData(
                duration = 2114.seconds,
                formattedDuration = "35’14",
                percentage = 52,
            ),
            onInfoClick = {},
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun AerobicZoneDurationItemPreview() {
    AppTheme {
        AerobicZoneDurationItem(
            label = "VO2 Max",
            color = Color.Red,
            formattedDuration = "2'01",
            percentage = 60,
        )
    }
}
