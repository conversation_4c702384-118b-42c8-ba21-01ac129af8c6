package com.stt.android.workout.details.graphanalysis.laps

import com.stt.android.data.toEpochMilli
import com.stt.android.domain.advancedlaps.AdvancedLapsUtils.calculateAscentDescentThreshold
import com.stt.android.domain.advancedlaps.AdvancedOngoingLap
import com.stt.android.domain.advancedlaps.LapsTableRow
import com.stt.android.domain.advancedlaps.Statistics
import com.stt.android.domain.advancedlaps.WindowType
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.sml.SmlStreamSamplePoint
import com.stt.android.domain.sml.SmlTimedStreamSamplePoint
import com.stt.android.domain.sml.addPauseTimeToMillisInWorkout
import com.stt.android.domain.sml.avgMinMaxOrNull
import com.stt.android.domain.sml.dataPointsWithoutPauses
import com.stt.android.domain.sml.filterSamplePoint
import com.stt.android.domain.sml.filterStreamPoint
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workouts.AvgMinMax
import com.stt.android.domain.workouts.DomainWindow
import com.stt.android.domain.workouts.toDomainWindowWithFallback
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.mapping.getActivitySummaryForActivityId
import com.stt.android.utils.STTConstants
import com.stt.android.utils.filterRepeated
import com.stt.android.workout.details.HrGraphData
import com.stt.android.workout.details.WorkoutValuesContainer
import com.stt.android.workouts.AltitudeChangeCalculator
import com.stt.android.workouts.details.values.WorkoutValue
import com.stt.android.workouts.details.values.WorkoutValueFactory
import com.suunto.algorithms.data.HeartRate.Companion.bpm
import javax.inject.Inject
import kotlin.math.absoluteValue

class GenerateAnalysisWorkoutValuesContainerUseCase @Inject constructor(
    private val infoModelFormatter: InfoModelFormatter
) {
    operator fun invoke(
        activityType: Int,
        hrGraphData: HrGraphData?,
        recoveryHRInThreeMinsData: HrGraphData?,
        geoPoints: List<WorkoutGeoPoint>,
        workoutDurationMillis: Long,
        lapsTableRow: LapsTableRow?,
        sml: Sml?,
        timeWindowStartSeconds: Float?,
        timeWindowEndSeconds: Float?,
        multisportPartActivity: MultisportPartActivity?,
        hasDistanceData: Boolean,
        padValueCountToAtleast: Int? = null
    ): WorkoutValuesContainer {
        val smlWorkoutStartTimestamp = multisportPartActivity?.startTime
            ?: sml?.summary?.header?.zonedDateTime?.toEpochMilli()
            ?: sml?.streamData?.events?.firstOrNull()?.data?.timestamp

        val originalWindow = when {
            lapsTableRow != null -> {
                generateFromLapsTableRow(activityType, lapsTableRow)
            }
            sml != null && smlWorkoutStartTimestamp != null -> {
                generateFromSml(
                    activityType,
                    sml,
                    smlWorkoutStartTimestamp,
                    workoutDurationMillis,
                    timeWindowStartSeconds,
                    timeWindowEndSeconds,
                    multisportPartActivity
                )
            }
            else -> {
                generateFromGeoPointsAndHrGraphData(
                    activityType,
                    geoPoints,
                    hrGraphData,
                    recoveryHRInThreeMinsData,
                    workoutDurationMillis,
                    timeWindowStartSeconds,
                    timeWindowEndSeconds,
                )
            }
        }

        // Remove data points we can't calculate for arbitrary time ranges
        val windowWithCustomLapCompatibleValues = originalWindow.copy(
            distance = originalWindow.distance.takeIf { hasDistanceData },
            ascentTime = null,
            descentTime = null,
            descentMax = null,
            distanceMax = null,
            downhillGrade = null,
            energy = null,
            recoveryTime = null,
            strokes = null,
            swimStyle = null,
            swimmingStyle = null,
        )

        val activityItems = getActivitySummaryForActivityId(activityType).items
        val factory = WorkoutValueFactory(
            workoutExtensions = emptyList(),
            workoutHeader = null,
            infoModelFormatter = infoModelFormatter,
            activityWindow = windowWithCustomLapCompatibleValues,
        )

        val valueList = factory.getValueForItems(activityItems).map {
            it.copy(
                descriptionTextResId = null,
                descriptionImageResId = null,
                descriptionUrl = null,
            )
        }
        val paddedValueList = if (padValueCountToAtleast != null && valueList.size < padValueCountToAtleast) {
            valueList + List(padValueCountToAtleast - valueList.size) { WorkoutValue.createEmpty() }
        } else {
            valueList
        }

        return WorkoutValuesContainer(activityType, multisportPartActivity, paddedValueList)
    }

    // TODO - Does resource heavy SML data filtering & pause removal every time when a DomainWindow
    //   for an arbitrary time range is needed. We need to add some way to get SML stream data
    //   for a part of workout that doesn't involve caching duplicate of the whole SML with the
    //   pauses removed.
    private fun generateFromSml(
        activityType: Int,
        sml: Sml,
        startTimestamp: Long,
        workoutDurationMillis: Long,
        timeWindowStartSeconds: Float?,
        timeWindowEndSeconds: Float?,
        multisportPartActivity: MultisportPartActivity?
    ): DomainWindow {
        if (timeWindowStartSeconds == null && timeWindowEndSeconds == null) {
            val smlWindow = sml.getActivityWindow(multisportPartActivity)?.toDomainWindowWithFallback()
            if (smlWindow != null) {
                // The whole workout's window's duration can be off from other stored duration
                // values, so overwrite it with the provided duration for consistency.
                // Rounding the seconds down is consistent with LogbookWindow to DomainWindow conversion.
                return if (multisportPartActivity == null) {
                    smlWindow.copy(
                        duration = workoutDurationMillis / 1000.0,
                        temperature = sml.streamData.temperature.avgMinMaxOrNull()
                    )
                } else {
                    smlWindow
                }
            }
        }

        val windowStartTimestamp = timeWindowStartSeconds?.let {
            startTimestamp + sml.streamData.addPauseTimeToMillisInWorkout(it.times(1000).toLong())
        }
        val windowEndTimestamp = timeWindowEndSeconds?.let {
            startTimestamp + sml.streamData.addPauseTimeToMillisInWorkout(it.times(1000).toLong())
        }

        val samplePoints = sml.streamData.samplePoint.filterSamplePoint(windowStartTimestamp, windowEndTimestamp)
        val samplePointsWithoutPauses = sml.streamData.dataPointsWithoutPauses(
            timestampedDataPoints = samplePoints,
            startTimestamp = windowStartTimestamp,
            startDistance = 0f
        )
            .asSequence()
            .filterRepeated() // Some SML files may have repeated samples with identical timestamp
            .filter { sample -> sample.samplePoint.cumulativeDistance != null || sample.samplePoint.heartrate != null }

        val lap = AdvancedOngoingLap(
            samplePointsWithoutPauses.firstOrNull()?.time ?: 0L,
            samplePoints
                .firstOrNull { it.cumulativeDistance != null }
                ?.cumulativeDistance ?: 0f,
            calculateAscentDescentThreshold(sml.summary.header)
        )

        samplePointsWithoutPauses.forEach { lap.updateOngoingLap(it) }

        val ascent = lap.ascentDescentStatistics.deltaUp.toFloat().takeIf { lap.ascentDescentStatistics.count > 0 }
        val descent = (-lap.ascentDescentStatistics.deltaDown.toFloat()).takeIf { lap.ascentDescentStatistics.count > 0 }

        val (swolftStatistics, strokeRateStatistics) = if (ActivityType.valueOf(activityType).isSwimming) {
            val swolfStatistics = Statistics()
            sml.streamData.dataPointsWithoutPauses(
                sml.streamData.swolf.filterStreamPoint(windowStartTimestamp, windowEndTimestamp),
                startTimestamp = windowStartTimestamp,
                startDistance = 0f
            ).forEach {
                swolfStatistics.addSample(it.value.toDouble())
            }

            val strokeRateStatistics = Statistics()
            sml.streamData.dataPointsWithoutPauses(
                sml.streamData.strokeRate.filterStreamPoint(windowStartTimestamp, windowEndTimestamp),
                startTimestamp = windowStartTimestamp,
                startDistance = 0f
            ).forEach {
                strokeRateStatistics.addSample(it.value / 60.0)
            }

            swolfStatistics to strokeRateStatistics
        } else {
            null to null
        }

        return generateFromOngoingLap(
            activityType,
            timeWindowEndSeconds,
            lap,
            (lap.cumulativeDuration - lap.workoutDurationOnStart).coerceAtLeast(0L),
            ascent,
            descent,
            swolftStatistics,
            strokeRateStatistics
        )
    }

    private fun generateFromGeoPointsAndHrGraphData(
        activityType: Int,
        geoPoints: List<WorkoutGeoPoint>,
        hrGraphData: HrGraphData?,
        recoveryHRInThreeMinsData: HrGraphData?,
        workoutDurationMillis: Long,
        timeWindowStartSeconds: Float?,
        timeWindowEndSeconds: Float?,
    ): DomainWindow {
        val windowStartMillis = timeWindowStartSeconds?.times(1000)?.toLong() ?: 0L
        val windowEndMillis = timeWindowEndSeconds?.times(1000)?.toInt() ?: workoutDurationMillis.toInt()

        val firstGeoPointIndex = geoPoints
            .binarySearch { it.millisecondsInWorkout.compareTo(windowStartMillis) }
            .let { if (it < 0) it.absoluteValue - 1 else it }

        val lap = AdvancedOngoingLap(
            windowStartMillis,
            geoPoints.getOrNull(firstGeoPointIndex)?.totalDistance?.toFloat() ?: 0f,
            STTConstants.AltitudeCalculations.MINIMUM_ALTITUDE_DIFF.toFloat()
        )
        val altitudeChangeCalculator = AltitudeChangeCalculator()
        var altitudesRecorded = false

        for (geoPoint in geoPoints.subList(firstGeoPointIndex, geoPoints.size)) {
            if (geoPoint.millisecondsInWorkout > windowEndMillis) {
                break
            }

            if (geoPoint.hasAltitude()) {
                altitudesRecorded = true
                altitudeChangeCalculator.addAltitude(geoPoint.altitude)
            }

            lap.updateOngoingLap(
                SmlTimedStreamSamplePoint(
                    geoPoint.millisecondsInWorkout.toLong(),
                    geoPoint.totalDistance.toFloat(),
                    SmlStreamSamplePoint(
                        timestamp = geoPoint.timestamp,
                        cumulativeDistance = geoPoint.totalDistance.toFloat(),
                        heartrate = null,
                        speed = geoPoint.speedMetersPerSecond,
                        altitude = geoPoint.altitude.toFloat(),
                        temperature = null,
                        verticalSpeed = null,
                        power = null,
                        cadence = null,
                        latitude = geoPoint.latitude,
                        longitude = geoPoint.longitude,
                        suuntoPlusSample = null,
                        rawIbiSuuntoLogbookSample = null,
                        recoveryHeartrateInThreeMins = null,
                        null,
                        null,
                    )
                )
            )
        }

        if (hrGraphData != null) {
            val entries = hrGraphData.entries
            val firstHrEntryIndex = if (timeWindowStartSeconds != null) {
                entries
                    .binarySearch { it.x.compareTo(timeWindowStartSeconds) }
                    .let { if (it < 0) it.absoluteValue - 1 else it }
            } else {
                0
            }

            for (hrEntry in entries.subList(firstHrEntryIndex, hrGraphData.entries.size)) {
                val timestampMillis = hrEntry.x.times(1000).toLong()
                lap.updateOngoingLap(
                    SmlTimedStreamSamplePoint(
                        timestampMillis,
                        0f,
                        SmlStreamSamplePoint(
                            timestamp = timestampMillis,
                            cumulativeDistance = null,
                            heartrate = hrEntry.y.bpm.inHz.toFloat(),
                            speed = null,
                            altitude = null,
                            temperature = null,
                            verticalSpeed = null,
                            power = null,
                            cadence = null,
                            latitude = null,
                            longitude = null,
                            suuntoPlusSample = null,
                            rawIbiSuuntoLogbookSample = null,
                            recoveryHeartrateInThreeMins = null,
                            verticalOscillation = null,
                            groundContactTime = null,
                        )
                    )
                )
            }

            recoveryHRInThreeMinsData?.let { list ->
                for (hrEntry in list.entries) {
                    val timestampMillis = hrEntry.x.times(1000).toLong()
                    lap.updateOngoingLap(
                        SmlTimedStreamSamplePoint(
                            timestampMillis,
                            0f,
                            SmlStreamSamplePoint(
                                timestamp = timestampMillis,
                                cumulativeDistance = null,
                                heartrate = null,
                                speed = null,
                                altitude = null,
                                temperature = null,
                                verticalSpeed = null,
                                power = null,
                                cadence = null,
                                latitude = null,
                                longitude = null,
                                suuntoPlusSample = null,
                                rawIbiSuuntoLogbookSample = null,
                                recoveryHeartrateInThreeMins = hrEntry.y.bpm.inHz.toFloat(),
                                verticalOscillation = null,
                                groundContactTime = null,
                            )
                        )
                    )
                }
            }
        }

        return generateFromOngoingLap(
            activityType,
            timeWindowEndSeconds,
            lap,
            windowEndMillis.toLong() - windowStartMillis,
            altitudeChangeCalculator.totalAscent.toFloat().takeIf { altitudesRecorded },
            altitudeChangeCalculator.totalDescent.toFloat().takeIf { altitudesRecorded }
        )
    }

    private fun generateFromOngoingLap(
        activityType: Int,
        timeWindowEndSeconds: Float?,
        lap: AdvancedOngoingLap,
        lapDuration: Long,
        ascent: Float?,
        descent: Float?,
        swolfStatistics: Statistics? = null,
        strokeRateStatistics: Statistics? = null
    ): DomainWindow {
        val lapDistance = (lap.cumulativeDistance - lap.workoutDistanceOnStart)
            .coerceAtLeast(0f)
        val lapTemperature = lap.temperatureStatistics
        val lapsTableRow = LapsTableRow(
            rowid = 0,
            lapNumber = 0,
            minAltitude = lap.altitudeStatistics.min.toFloat()
                .takeIf { lap.altitudeStatistics.count > 0 },
            maxAltitude = lap.altitudeStatistics.max.toFloat()
                .takeIf { lap.altitudeStatistics.count > 0 },
            avgAltitude = lap.altitudeStatistics.avg.toFloat()
                .takeIf { lap.altitudeStatistics.count > 0 },
            ascent = ascent,
            ascentTime = null,
            descent = descent,
            descentTime = null,
            maxDescent = null,
            minCadence = lap.cadenceStatistics.min.toFloat()
                .takeIf { lap.cadenceStatistics.count > 0 },
            maxCadence = lap.cadenceStatistics.max.toFloat()
                .takeIf { lap.cadenceStatistics.count > 0 },
            avgCadence = lap.cadenceStatistics.avg.toFloat()
                .takeIf { lap.cadenceStatistics.count > 0 },
            distance = lapDistance,
            distanceMax = null,
            minDownhillGrade = null,
            maxDownhillGrade = null,
            avgDownhillGrade = null,
            duration = lapDuration.toFloat() / 1000, // ms to s
            energy = null,
            minHR = lap.heartRateStatistics.min.toFloat()
                .takeIf { lap.heartRateStatistics.count > 0 },
            maxHR = lap.heartRateStatistics.max.toFloat()
                .takeIf { lap.heartRateStatistics.count > 0 },
            avgHR = lap.heartRateStatistics.avg.toFloat()
                .takeIf { lap.heartRateStatistics.count > 0 },
            minPower = lap.powerStatistics.min.toFloat()
                .takeIf { lap.powerStatistics.count > 0 },
            maxPower = lap.powerStatistics.max.toFloat()
                .takeIf { lap.powerStatistics.count > 0 },
            avgPower = lap.powerStatistics.avg.toFloat()
                .takeIf { lap.powerStatistics.count > 0 },
            recoveryTime = null,
            minSpeed = lap.speedStatistics.min.toFloat()
                .takeIf { lap.speedStatistics.count > 0 },
            maxSpeed = lap.speedStatistics.max.toFloat()
                .takeIf { lap.speedStatistics.count > 0 },
            avgSpeed = if (lap.speedStatistics.count > 0) {
                lap.speedStatistics.avg.toFloat()
            } else if (lapDistance > 0 && lapDuration > 0) {
                lapDistance / lapDuration * 1000f
            } else {
                0f
            },
            minStrokeRate = strokeRateStatistics?.min?.toFloat()
                ?.takeIf { strokeRateStatistics.count > 0 },
            maxStrokeRate = strokeRateStatistics?.max?.toFloat()
                ?.takeIf { strokeRateStatistics.count > 0 },
            avgStrokeRate = strokeRateStatistics?.avg?.toFloat()
                ?.takeIf { strokeRateStatistics.count > 0 },
            minStrokes = null,
            maxStrokes = null,
            avgStrokes = null,
            swimStyle = null,
            minSwolf = swolfStatistics?.min?.toFloat()
                ?.takeIf { swolfStatistics.count > 0 },
            maxSwolf = swolfStatistics?.max?.toFloat()
                ?.takeIf { swolfStatistics.count > 0 },
            avgSwolf = swolfStatistics?.avg?.toFloat()
                ?.takeIf { swolfStatistics.count > 0 },
            minTemperature = lapTemperature?.min?.toFloat()
                ?.takeIf { lapTemperature.count > 0 },
            maxTemperature = lapTemperature?.max?.toFloat()
                ?.takeIf { lapTemperature.count > 0 },
            avgTemperature = lapTemperature?.avg?.toFloat()
                ?.takeIf { lapTemperature.count > 0 },
            type = WindowType.UNKNOWN,
            minVerticalSpeed = lap.verticalSpeedStatistics.min.toFloat()
                .takeIf { lap.verticalSpeedStatistics.count > 0 },
            maxVerticalSpeed = lap.verticalSpeedStatistics.max.toFloat()
                .takeIf { lap.verticalSpeedStatistics.count > 0 },
            avgVerticalSpeed = lap.verticalSpeedStatistics.avg.toFloat()
                .takeIf { lap.verticalSpeedStatistics.count > 0 },
            minDepth = null,
            maxDepth = null,
            avgDepth = null,
            diveTime = null,
            diveRecoveryTime = null,
            diveTimeMax = null,
            diveInWorkout = null,
            cumulatedDistance = lap.cumulativeDistance,
            cumulatedDuration = timeWindowEndSeconds,
            suuntoPlusData = lap.getSuuntoPlusData(),
            repetitionCount = lap.repetitionCount.takeIf { lap.repetitionCount > 0 },
            isIntervalRecoveryLap = false,
            aerobicHrThreshold = null,
            anaerobicHrThreshold = null,
            aerobicPowerThreshold = null,
            anaerobicPowerThreshold = null,
            aerobicPaceThreshold = null,
            anaerobicPaceThreshold = null,
            aerobicDuration = null,
            anaerobicDuration = null,
            vo2MaxDuration = null,
            avgStrideLength = lap.strideStatistics.avg.toFloat()
                .takeIf { lap.strideStatistics.count > 0 },
            minStrideLength = lap.strideStatistics.min.toFloat()
                .takeIf { lap.strideStatistics.count > 0 },
            maxStrideLength = lap.strideStatistics.max.toFloat()
                .takeIf { lap.strideStatistics.count > 0 },
            fatConsumption = lap.fatConsumption,
            carbohydrateConsumption = lap.carbohydrateConsumption,
            avgGroundContactTime = lap.groundContactTimeStatistics.avg.toFloat()
                .takeIf { lap.groundContactTimeStatistics.count > 0 },
            minGroundContactTime = lap.groundContactTimeStatistics.min.toFloat()
                .takeIf { lap.groundContactTimeStatistics.count > 0 },
            maxGroundContactTime = lap.groundContactTimeStatistics.max.toFloat()
                .takeIf { lap.groundContactTimeStatistics.count > 0 },
            avgVerticalOscillation = lap.verticalOscillationStatistics.avg.toFloat()
                .takeIf { lap.verticalOscillationStatistics.count > 0 },
            minVerticalOscillation = lap.verticalOscillationStatistics.min.toFloat()
                .takeIf { lap.verticalOscillationStatistics.count > 0 },
            maxVerticalOscillation = lap.verticalOscillationStatistics.max.toFloat()
                .takeIf { lap.verticalOscillationStatistics.count > 0 },
            avgLeftGroundContactBalance = lap.leftGroundContactBalanceStatistics.avg.toFloat()
                .takeIf { lap.leftGroundContactBalanceStatistics.count > 0 },
            minLeftGroundContactBalance = lap.leftGroundContactBalanceStatistics.min.toFloat()
                .takeIf { lap.leftGroundContactBalanceStatistics.count > 0 },
            maxLeftGroundContactBalance = lap.leftGroundContactBalanceStatistics.max.toFloat()
                .takeIf { lap.leftGroundContactBalanceStatistics.count > 0 },
            avgRightGroundContactBalance = lap.rightGroundContactBalanceStatistics.avg.toFloat()
                .takeIf { lap.rightGroundContactBalanceStatistics.count > 0 },
            minRightGroundContactBalance = lap.rightGroundContactBalanceStatistics.min.toFloat()
                .takeIf { lap.rightGroundContactBalanceStatistics.count > 0 },
            maxRightGroundContactBalance = lap.rightGroundContactBalanceStatistics.max.toFloat()
                .takeIf { lap.rightGroundContactBalanceStatistics.count > 0 },
            avgAscentSpeed = lap.ascentSpeed.avg.toFloat(),
            minAscentSpeed = lap.ascentSpeed.min.toFloat(),
            maxAscentSpeed = lap.ascentSpeed.max.toFloat(),
            avgDescentSpeed = lap.descentSpeed.avg.toFloat(),
            minDescentSpeed = lap.descentSpeed.min.toFloat(),
            maxDescentSpeed = lap.descentSpeed.max.toFloat(),
            avgDistancePerStroke = lap.distancePerStroke.avg.toFloat(),
            minDistancePerStroke = lap.distancePerStroke.min.toFloat(),
            maxDistancePerStroke = lap.distancePerStroke.max.toFloat(),
        )

        return generateFromLapsTableRow(activityType, lapsTableRow)
    }

    private fun generateFromLapsTableRow(
        activityType: Int,
        lapsTableRow: LapsTableRow
    ): DomainWindow {
        val altitudeAvgMinMax = AvgMinMax(lapsTableRow.avgAltitude?.toDouble(), lapsTableRow.minAltitude?.toDouble(), lapsTableRow.maxAltitude?.toDouble())
        val cadenceAvgMinMax = getAvgMinMaxIfAllNotNull(lapsTableRow.avgCadence, lapsTableRow.minCadence, lapsTableRow.maxCadence)
        val downHillGradeMinMaxAvg = getAvgMinMaxIfAllNotNull(lapsTableRow.avgDownhillGrade, lapsTableRow.minDownhillGrade, lapsTableRow.maxDownhillGrade)
        val hrMinMaxAvg = getAvgMinMaxIfAllNotNull(lapsTableRow.avgHR, lapsTableRow.minHR, lapsTableRow.maxHR)
        val powerAvgMinMax = getAvgMinMaxIfAllNotNull(lapsTableRow.avgPower, lapsTableRow.minPower, lapsTableRow.maxPower)
        val speedAvgMinMax = AvgMinMax(lapsTableRow.avgSpeed?.toDouble(), lapsTableRow.minSpeed?.toDouble(), lapsTableRow.maxSpeed?.toDouble())
        val stokeRateAvgMinMax = getAvgMinMaxIfAllNotNull(lapsTableRow.avgStrokeRate, lapsTableRow.minStrokeRate, lapsTableRow.maxStrokeRate)
        val swolfAvgMinMax = getAvgMinMaxIfAllNotNull(lapsTableRow.avgSwolf, lapsTableRow.minSwolf, lapsTableRow.maxSwolf)
        val temperatureAvgMinMax = getAvgMinMaxIfAllNotNull(lapsTableRow.avgTemperature, lapsTableRow.minTemperature, lapsTableRow.maxTemperature)
        val verticalSpeedAvgMinMax = getAvgMinMaxIfAllNotNull(lapsTableRow.avgVerticalSpeed, lapsTableRow.minVerticalSpeed, lapsTableRow.maxVerticalSpeed)
        val depthAvgMinMax = getAvgMinMaxIfAllNotNull(lapsTableRow.avgDepth, lapsTableRow.minDepth, lapsTableRow.maxDepth)
        val strideAvgMinMax = AvgMinMax(lapsTableRow.avgStrideLength?.toDouble(), lapsTableRow.minStrideLength?.toDouble(), lapsTableRow.maxStrideLength?.toDouble())
        val groundContactTimeAvgMinMax = AvgMinMax(lapsTableRow.avgGroundContactTime?.toDouble(), lapsTableRow.minGroundContactTime?.toDouble(), lapsTableRow.maxGroundContactTime?.toDouble())
        val verticalOscillationAvgMinMax = AvgMinMax(lapsTableRow.avgVerticalOscillation?.toDouble(), lapsTableRow.minVerticalOscillation?.toDouble(), lapsTableRow.maxVerticalOscillation?.toDouble())
        val leftGroundContactBalanceAvgMinMax = AvgMinMax(lapsTableRow.avgLeftGroundContactBalance?.toDouble(), lapsTableRow.minLeftGroundContactBalance?.toDouble(), lapsTableRow.maxLeftGroundContactBalance?.toDouble())
        val rightGroundContactBalanceAvgMinMax = AvgMinMax(lapsTableRow.avgRightGroundContactBalance?.toDouble(), lapsTableRow.minRightGroundContactBalance?.toDouble(), lapsTableRow.maxRightGroundContactBalance?.toDouble())
        val ascentSpeedAvgMinMax = AvgMinMax(lapsTableRow.avgAscentSpeed?.toDouble(), lapsTableRow.minAscentSpeed?.toDouble(), lapsTableRow.maxAscentSpeed?.toDouble())
        val descentSpeedAvgMinMax = AvgMinMax(lapsTableRow.avgDescentSpeed?.toDouble(), lapsTableRow.minDescentSpeed?.toDouble(), lapsTableRow.maxDescentSpeed?.toDouble())
        val distancePerStrokeAvgMinMax = AvgMinMax(lapsTableRow.avgDistancePerStroke?.toDouble(), lapsTableRow.minDistancePerStroke?.toDouble(), lapsTableRow.maxDistancePerStroke?.toDouble())
        return DomainWindow(
            activityId = activityType,
            type = WindowType.UNKNOWN.type,
            altitudeRange = altitudeAvgMinMax,
            ascent = lapsTableRow.ascent?.toDouble(),
            ascentTime = lapsTableRow.ascentTime?.toDouble(),
            cadence = cadenceAvgMinMax,
            descent = lapsTableRow.descent?.toDouble(),
            descentTime = lapsTableRow.descentTime?.toDouble(),
            descentMax = lapsTableRow.maxDescent?.toDouble(),
            distance = lapsTableRow.distance?.toDouble(),
            distanceMax = lapsTableRow.distanceMax?.toDouble(),
            downhillGrade = downHillGradeMinMaxAvg,
            duration = lapsTableRow.duration?.toDouble(),
            energy = lapsTableRow.energy?.toDouble(),
            hr = hrMinMaxAvg,
            power = powerAvgMinMax,
            recoveryTime = null,
            speed = speedAvgMinMax,
            strokeRate = stokeRateAvgMinMax,
            strokes = null,
            swimStyle = null,
            swimmingStyle = null,
            swolf = swolfAvgMinMax,
            temperature = temperatureAvgMinMax,
            verticalSpeed = verticalSpeedAvgMinMax,
            depth = depthAvgMinMax,
            diveTime = lapsTableRow.diveTime?.toDouble(),
            diveRecoveryTime = lapsTableRow.diveRecoveryTime?.toDouble(),
            diveTimeMax = lapsTableRow.diveTimeMax?.toDouble(),
            diveInWorkout = lapsTableRow.diveInWorkout,
            repetitionCount = lapsTableRow.repetitionCount,
            depthAverage = null,
            maxDepthAverage = null,
            stride = strideAvgMinMax,
            fatConsumption = lapsTableRow.fatConsumption,
            carbohydrateConsumption = lapsTableRow.carbohydrateConsumption,
            groundContactTime = groundContactTimeAvgMinMax,
            verticalOscillation = verticalOscillationAvgMinMax,
            leftGroundContactBalance = leftGroundContactBalanceAvgMinMax,
            rightGroundContactBalance = rightGroundContactBalanceAvgMinMax,
            ascentSpeed = ascentSpeedAvgMinMax,
            descentSpeed = descentSpeedAvgMinMax,
            distancePerStroke = distancePerStrokeAvgMinMax,
        )
    }

    private fun getAvgMinMaxIfAllNotNull(avg: Float?, min: Float?, max: Float?): AvgMinMax? = if (avg != null && min != null && max != null) {
        AvgMinMax(avg.toDouble(), min.toDouble(), max.toDouble())
    } else {
        null
    }
}
