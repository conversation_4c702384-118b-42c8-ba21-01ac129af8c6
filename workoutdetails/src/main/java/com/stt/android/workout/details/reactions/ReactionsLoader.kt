package com.stt.android.workout.details.reactions

import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.failure
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.ReactionModel
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.user.GetUserByUsernameUseCase
import com.stt.android.domain.user.ReactionSummary
import com.stt.android.domain.workouts.SaveWorkoutHeaderUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.reactions.SyncReactionsUseCase
import com.stt.android.workout.details.NavigationEventDispatcher
import com.stt.android.workout.details.ReactionsData
import com.stt.android.workout.details.WorkoutDetailsFollowersListNavEvent
import com.stt.android.workout.details.analytics.WorkoutDetailsAnalytics
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

@ActivityRetainedScoped
class ReactionsLoader
@Inject constructor(
    private val currentUserController: CurrentUserController,
    private val getUserByUsernameUseCase: GetUserByUsernameUseCase,
    private val reactionModel: ReactionModel,
    private val navigationEventDispatcher: NavigationEventDispatcher,
    private val syncReactionsUseCase: SyncReactionsUseCase,
    private val saveWorkoutHeaderUseCase: SaveWorkoutHeaderUseCase,
    private val workoutDetailsAnalytics: WorkoutDetailsAnalytics,
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope
) {
    private val reactionsViewState = MutableStateFlow<ViewState<ReactionsData?>>(loading(null))
    private lateinit var workoutHeader: WorkoutHeader

    private suspend fun refreshReactionsData(
        workoutId: Int,
        workoutKey: String?,
        localOnly: Boolean = false,
        initialState: ReactionsData = createInitialState(workoutId)
    ) = withContext(IO) {
        reactionsViewState.update { loading(initialState) }
        if (workoutKey == null) {
            Timber.d("workout is not synced yet, emitting empty state")
            reactionsViewState.update { loaded(initialState) }
            return@withContext
        }

        //region TODO Replace below DB access with use cases once reactions are migrated to Room
        val likes = runSuspendCatching {
            if (localOnly) {
                reactionModel.loadReactionsFromLocal(
                    workoutKey = workoutKey,
                    reactionType = ReactionSummary.REACTION_LIKE,
                )
            } else {
                reactionModel.loadReactionsFromRemote(
                    workoutKey = workoutKey,
                    reactionType = ReactionSummary.REACTION_LIKE,
                )
            }
        }.getOrElse { e ->
            Timber.w(e, "Failed to load reaction")
            emptyList()
        }

        val reactionSummary = try {
            reactionModel.findSummary(workoutKey, ReactionSummary.REACTION_LIKE)
        } catch (e: Exception) {
            Timber.w(e, "Failed to find reaction summary")
            null
        } ?: ReactionSummary.local(workoutKey, ReactionSummary.REACTION_LIKE)
            .run {
                // Apparently, ReactionSummary can be out of sync with Reactions (go figure)
                toBuilder()
                    .count(likes.size)
                    .userReacted(likes.any { it.userName == currentUserController.username })
                    .build()
            }
        //endregion

        val avatarUrls = likes.take(3).map { it.userProfilePictureUrl }

        // Emit what we've already got
        reactionsViewState.update {
            loaded(
                ReactionsData(
                    workoutId = workoutId,
                    reactionSummary = reactionSummary,
                    userAvatarsUrls = avatarUrls,
                    likeClickHandler = ::onLikeClicked,
                    avatarClickHandler = ::onAvatarClicked
                )
            )
        }

        if (likes.isEmpty()) return@withContext

        // Then update user avatars
        val updatedUserAvatarsUrls = withContext(IO) {
            likes.take(3)
                .map { reaction ->
                    runSuspendCatching {
                        getUserByUsernameUseCase.getUserByUsername(
                            reaction.userName,
                            queryRemoteIfNeeded = true
                        ).profileImageUrl
                    }.getOrElse { e ->
                        Timber.w(e, "Failed to load user")
                        reaction.userProfilePictureUrl
                    }
                }
        }
        reactionsViewState.update {
            loaded(
                ReactionsData(
                    workoutId = workoutId,
                    reactionSummary = reactionSummary,
                    userAvatarsUrls = updatedUserAvatarsUrls,
                    likeClickHandler = ::onLikeClicked,
                    avatarClickHandler = ::onAvatarClicked
                )
            )
        }
    }

    private fun createInitialState(workoutId: Int): ReactionsData {
        return ReactionsData(workoutId = workoutId)
    }

    suspend fun loadReactionsData(
        workoutHeader: WorkoutHeader
    ): Flow<ViewState<ReactionsData?>> {
        <EMAIL> = workoutHeader
        activityRetainedCoroutineScope.launch {
            refreshReactionsData(workoutHeader.id, workoutHeader.key)
        }

        return reactionsViewState
    }

    private fun onLikeClicked(workoutId: Int, reactionSummary: ReactionSummary) {
        activityRetainedCoroutineScope.launch {
            runSuspendCatching {
                persistReactionUpdate(reactionSummary)
            }.onFailure { e ->
                Timber.w(e, "Error persisting reaction")
                reactionsViewState.update { failure(ErrorEvent.get(e::class)) }
                return@launch
            }

            val newState = runSuspendCatching {
                reactionsViewState.first().data
            }.onFailure {
                Timber.w(it, "Failed to get reactions viewState.")
            }.getOrNull() ?: createInitialState(workoutId)
                .copy(reactionSummary = reactionSummary)

            refreshReactionsData(
                workoutId = workoutId,
                workoutKey = reactionSummary.workoutKey,
                localOnly = true,
                initialState = newState
            )

            syncReactionsUseCase()
            if (!reactionSummary.isUserReacted) {
                workoutDetailsAnalytics.trackLikeWorkoutEvent()
            }
        }
    }

    private suspend fun persistReactionUpdate(reactionSummary: ReactionSummary) = withContext(IO) {
        val updatedReactionSummary = if (reactionSummary.isUserReacted) {
            Timber.d("Adding reaction")
            reactionModel.addReactionBlocking(reactionSummary)
        } else {
            Timber.d("Removing reaction")
            reactionModel.removeReactionBlocking(reactionSummary)
        }
        val updatedWorkoutHeader = workoutHeader.copy(
            reactionCount = updatedReactionSummary.count
        )
        Timber.d("Updating workout")
        saveWorkoutHeaderUseCase(updatedWorkoutHeader)
    }

    private fun onAvatarClicked(workoutKey: String) {
        navigationEventDispatcher.dispatchEvent(WorkoutDetailsFollowersListNavEvent(workoutKey))
    }
}
