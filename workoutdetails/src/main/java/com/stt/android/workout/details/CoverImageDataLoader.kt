package com.stt.android.workout.details

import androidx.lifecycle.LiveData
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.google.maps.android.PolyUtil
import com.google.maps.android.SphericalUtil
import com.stt.android.analytics.AnalyticsPropertyValue.WorkoutAnalysisScreenSource.MAP_OVERVIEW
import com.stt.android.analytics.AnalyticsPropertyValue.WorkoutAnalysisScreenSource.MAP_THUMBNAIL
import com.stt.android.analytics.AnalyticsPropertyValue.WorkoutAnalysisScreenSource.NO_MAP_ANALYSIS_BUTTON
import com.stt.android.analytics.AnalyticsPropertyValue.WorkoutAnalysisScreenSource.PLAY_BUTTON_WORKOUT_DETAILS
import com.stt.android.analytics.AnalyticsPropertyValue.WorkoutDetailsContext.WORKOUT_DETAILS_SCREEN
import com.stt.android.analytics.AnalyticsPropertyValue.WorkoutDetailsContext.WORKOUT_MULTISPORT_DETAILS_SCREEN
import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.review.ReviewState
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.sml.SmlStreamData
import com.stt.android.domain.sync.SyncRequest
import com.stt.android.domain.sync.SyncRequestHandler
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workout.SharingOption
import com.stt.android.domain.workout.WorkoutData
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workouts.DomainWorkout
import com.stt.android.domain.workouts.SaveWorkoutHeaderUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.domain.workouts.getSingleValidLocation
import com.stt.android.domain.workouts.hasValidStartAndStopPositions
import com.stt.android.domain.workouts.isHuntingOrFishing
import com.stt.android.domain.workouts.isMultisport
import com.stt.android.extensions.getIndexOfHighlightedRoute
import com.stt.android.extensions.multisportRoutes
import com.stt.android.extensions.traverseEvents
import com.stt.android.extensions.useSkiMapStyle
import com.stt.android.maps.MAP_TYPE_SKI
import com.stt.android.maps.MapType
import com.stt.android.maps.MapTypeHelper
import com.stt.android.models.MapSelectionModel
import com.stt.android.multimedia.sportie.SportieShareSource
import com.stt.android.remote.UserAgent
import com.stt.android.routes.toLatLng
import com.stt.android.ski.SlopeSkiUtils
import com.stt.android.ui.extensions.supportWorkoutAnalysisOnMap
import com.stt.android.ui.extensions.supportsWorkoutAnalysisWithoutMap
import com.stt.android.ui.map.MapHelper
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.workout.details.analytics.WorkoutDetailsAnalytics
import com.stt.android.workout.details.multisport.MultisportPartActivityLoader
import com.stt.android.workout.details.sml.SmlDataLoader
import com.stt.android.workout.details.workoutdata.WorkoutDataLoader
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareHelper
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.conflate
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.transformWhile
import kotlinx.coroutines.flow.zip
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.max

@FlowPreview
@ExperimentalCoroutinesApi
@ActivityRetainedScoped
class CoverImageDataLoader @Inject constructor(
    @UserAgent private val userAgent: String,
    private val mapCardCreator: MapCardCreator,
    private val navigationEventDispatcher: NavigationEventDispatcher,
    private val workoutDetailsAnalytics: WorkoutDetailsAnalytics,
    private val currentUserController: CurrentUserController,
    private val saveWorkoutHeaderUseCase: SaveWorkoutHeaderUseCase,
    private val syncRequestHandler: SyncRequestHandler,
    private val mapSelectionModel: MapSelectionModel,
    private val smlDataLoader: SmlDataLoader,
    private val workoutDataLoader: WorkoutDataLoader,
    private val multisportPartActivityLoader: MultisportPartActivityLoader,
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope,
    private val workoutShareHelper: WorkoutShareHelper,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) {
    private var smlJob: Job? = null
    private var workoutDataJob: Job? = null
    private var multisportPartActivityJob: Job? = null
    private val coverImagesStateFlow: MutableStateFlow<ViewState<CoverImageData?>> =
        MutableStateFlow(loading())
    private var workout: DomainWorkout? = null

    private val _onShareLink = SingleLiveEvent<WorkoutHeader>()
    val onShareLink: LiveData<WorkoutHeader> = _onShareLink

    // when it is longScreenshotLayout, filter video
    private var longScreenshotLayout = false

    fun setLongScreenshotLayout(longScreenshotLayout: Boolean) {
        this.longScreenshotLayout = longScreenshotLayout
    }

    fun loadCoverImagesData(workout: DomainWorkout): Flow<ViewState<CoverImageData?>> {
        update(workout)
        return coverImagesStateFlow
    }

    fun update(workout: DomainWorkout): Job {
        this.workout = workout
        this.workoutDataJob?.cancel()
        this.workoutDataJob = null
        this.smlJob?.cancel()
        this.smlJob = null
        this.multisportPartActivityJob?.cancel()
        this.multisportPartActivityJob = null
        return activityRetainedCoroutineScope.launch(coroutinesDispatchers.io) {
            runSuspendCatching {
                Timber.d("Loading cover images data")

                val isOwnWorkout = workout.header.username == currentUserController.username
                // remove videos, when longScreenshotLayout = ture
                val videos = if (longScreenshotLayout) {
                    emptyList()
                } else {
                    workout.videos
                        ?.mapNotNull { video ->
                            // For own videos, show all of them. For others' videos, only show if
                            // passed review, but backend doesn't filter.
                            if (isOwnWorkout || video.reviewState == ReviewState.PASS) {
                                CoverImage.VideoCoverImage(
                                    video = video,
                                    isMuted = false,
                                    userAgent = userAgent,
                                    onMuteClickHandler = ::onMuteButtonClicked,
                                    onClickHandler = ::navigateToPhotoPager,
                                )
                            } else {
                                null
                            }
                        }
                        ?: emptyList()
                }

                val pictures = workout.pictures
                    ?.mapNotNull { picture ->
                        // For own images, show all of them. For others' images, only show if
                        // passed review, but backend doesn't filter.
                        if (isOwnWorkout || picture.reviewState == ReviewState.PASS) {
                            CoverImage.PhotoCoverImage(
                                picture = picture,
                                onClickHandler = ::navigateToPhotoPager,
                            )
                        } else {
                            null
                        }
                    }
                    ?: emptyList()

                val latLngs = getRoute(workout)
                val activityType = ActivityType.valueOf(workout.header.activityTypeId)
                val supportWorkoutAnalysisWithoutMap = getSupportWorkoutAnalysisWithoutMap(workout)
                val supportsWorkoutAnalysisOnMap = getSupportWorkoutAnalysisOnMap(workout, latLngs, null)

                val coverImages = getCoverImages(videos, pictures, workout)
                if (coverImages.isNotEmpty()) {
                    setCoverData(
                        activityType = activityType,
                        coverImages = coverImages,
                        routePoints = latLngs,
                        totalDistance = workout.header.totalDistance,
                        isShareable = isOwnWorkout,
                        showWorkoutPlaybackButton = supportsWorkoutAnalysisOnMap || supportWorkoutAnalysisWithoutMap,
                        supportsWorkoutAnalysisOnMap = supportsWorkoutAnalysisOnMap
                    )
                } else if (!coverImagesStateFlow.value.isLoaded()) {
                    setLoadingStateWithData(
                        activityType = activityType,
                        latLngs = latLngs,
                        totalDistance = workout.header.totalDistance,
                        isShareable = isOwnWorkout,
                        showWorkoutPlaybackButton = supportsWorkoutAnalysisOnMap || supportWorkoutAnalysisWithoutMap,
                        supportsWorkoutAnalysisOnMap = supportsWorkoutAnalysisOnMap
                    )
                }

                // No need to observe for additional data if workout analysis support is true already,
                // otherwise redo the checks with both SML and WorkoutData available. Separate
                // checks on both loaded events to get the button visible ASAP
                if (!supportsWorkoutAnalysisOnMap && !supportWorkoutAnalysisWithoutMap) {
                    waitForWorkoutData(onlyOnce = true) { updateAnalysisAvailability(workout) }
                    waitForSml(onlyOnce = true) { updateAnalysisAvailability(workout) }
                }
            }.onFailure {
                Timber.w(it, "Updating cover images failed.")
                coverImagesStateFlow.value = loaded()
            }
        }
    }

    private fun updateAnalysisAvailability(workout: DomainWorkout) {
        val currentCoverImageData = coverImagesStateFlow.value.data ?: return
        val supportsWorkoutAnalysisWithoutMap = getSupportWorkoutAnalysisWithoutMap(workout)

        val updatedCoverImages = if (supportsWorkoutAnalysisWithoutMap) {
            currentCoverImageData.coverImages
                .firstOrNull { it is CoverImage.DefaultCoverImage }
                ?.let { it as CoverImage.DefaultCoverImage }
                ?.copy(onClickHandler = ::onOpenFullscreenGraphAnalysisClicked)
                ?.let(::listOf)
                ?: currentCoverImageData.coverImages
        } else {
            currentCoverImageData.coverImages
        }

        val updated = loaded(
            currentCoverImageData.copy(
                showWorkoutPlaybackButton = supportsWorkoutAnalysisWithoutMap,
                coverImages = updatedCoverImages,
            )
        )
        coverImagesStateFlow.value = updated
    }

    private fun ActivityType.getMapType() = if (useSkiMapStyle) {
        MapTypeHelper.getOrDefault(
            mapTypeName = MAP_TYPE_SKI,
            defaultValue = mapSelectionModel.selectedMapTypeUserHasAccessTo,
        )
    } else {
        mapSelectionModel.selectedMapTypeUserHasAccessTo
    }

    private fun setCoverData(
        activityType: ActivityType,
        coverImages: List<CoverImage>,
        routePoints: List<LatLng>,
        totalDistance: Double,
        isShareable: Boolean,
        showWorkoutPlaybackButton: Boolean,
        supportsWorkoutAnalysisOnMap: Boolean
    ) {
        // longScreenshot hide routeView
        val showRouteView = if (longScreenshotLayout) {
            false
        } else {
            routePoints.isNotEmpty() &&
                coverImages.any { it is CoverImage.PhotoCoverImage || it is CoverImage.VideoCoverImage }
        }

        coverImagesStateFlow.value = loaded(
            CoverImageData(
                activityType = activityType,
                showRouteView = showRouteView,
                routePoints = routePoints,
                totalDistance = totalDistance,
                showWorkoutPlaybackButton = showWorkoutPlaybackButton,
                supportsWorkoutPlaybackOnMapView = supportsWorkoutAnalysisOnMap,
                isShareable = isShareable,
                onShareClicked = ::onShareClicked,
                onRouteViewClick = ::onRouteViewClicked,
                onOpenGraphAnalysisClick = ::onOpenGraphAnalysisClickHandler,
                coverImages = coverImages,
                mapType = activityType.getMapType()
            )
        )
        Timber.d("Cover images data loaded")
    }

    private fun setLoadingStateWithData(
        activityType: ActivityType,
        latLngs: List<LatLng>,
        totalDistance: Double,
        isShareable: Boolean,
        showWorkoutPlaybackButton: Boolean,
        supportsWorkoutAnalysisOnMap: Boolean
    ) {
        coverImagesStateFlow.value = loading(
            CoverImageData(
                activityType = activityType,
                showRouteView = false,
                routePoints = latLngs,
                totalDistance = totalDistance,
                showWorkoutPlaybackButton = showWorkoutPlaybackButton,
                supportsWorkoutPlaybackOnMapView = supportsWorkoutAnalysisOnMap,
                isShareable = isShareable,
                onShareClicked = ::onShareClicked,
                onRouteViewClick = ::onRouteViewClicked,
                onOpenGraphAnalysisClick = ::onOpenGraphAnalysisClickHandler,
                coverImages = emptyList(),
                mapType = activityType.getMapType()
            )
        )
        Timber.d("Loading multisport cover images.")
    }

    fun update(mapType: MapType) {
        coverImagesStateFlow.value.data?.let { data ->
            coverImagesStateFlow.value = loaded(data.copy(mapType = mapType))
        }
    }

    private suspend fun getCoverImages(
        videos: List<CoverImage.VideoCoverImage>,
        pictures: List<CoverImage.PhotoCoverImage>,
        workout: DomainWorkout,
    ): List<CoverImage> = withContext(coroutinesDispatchers.io) {
        (videos + pictures).ifEmpty {
            workout.header.getSingleValidLocation()?.let { singleLocation ->
                getSingleLocationRouteCoverImage(workout, singleLocation.toLatLng())
            } ?: run {
                val polyline = workout.header.polyline
                if (polyline != null && workout.header.hasValidStartAndStopPositions()) {
                    getRouteCoverImage(workout, polyline)
                    emptyList()
                } else {
                    getDefaultCoverImage(workout)
                }
            }
        }
    }

    private fun getDefaultCoverImage(workout: DomainWorkout): List<CoverImage> {
        multisportPartActivityJob = activityRetainedCoroutineScope.launch {
            multisportPartActivityLoader.multisportPartActivityFlow
                .onEach {
                    val supportsWorkoutAnalysisWithoutMap =
                        getSupportWorkoutAnalysisWithoutMap(workout)
                    val updated = loaded(
                        coverImagesStateFlow.value.data?.copy(
                            coverImages = getDefaultCoverImage(
                                workout.header,
                                it.data,
                                supportsWorkoutAnalysisWithoutMap
                            ),
                            multisportPartActivity = it.data,
                            showWorkoutPlaybackButton = supportsWorkoutAnalysisWithoutMap,
                            supportsWorkoutPlaybackOnMapView = false,
                        )
                    )
                    coverImagesStateFlow.value = updated
                }
                .catch { Timber.w(it, "Getting default cover image failed.") }
                .collect()
        }

        return getDefaultCoverImage(
            workout.header,
            multisportPartActivityLoader.multisportPartActivityFlow.value.data,
            getSupportWorkoutAnalysisWithoutMap(workout)
        )
    }

    private fun getDefaultCoverImage(
        workoutHeader: WorkoutHeader,
        multisportPartActivity: MultisportPartActivity?,
        openAnalysisWithoutMapOnClick: Boolean
    ): List<CoverImage> = listOf(
        CoverImage.DefaultCoverImage(
            activityCoverPicture = ActivityType.valueOf(
                multisportPartActivity?.activityType ?: workoutHeader.activityTypeId
            ).placeholderImageId,
            onClickHandler = (::onOpenFullscreenGraphAnalysisClicked).takeIf { openAnalysisWithoutMapOnClick }
        )
    )

    private suspend fun getSingleLocationRouteCoverImage(
        workout: DomainWorkout,
        location: LatLng,
    ): List<CoverImage> = listOf(
        CoverImage.RouteCoverImage(
            workoutHeaderId = workout.header.id,
            mapCard = mapCardCreator.create(location),
            activityType = workout.header.activityTypeId,
            onClickHandler = ::navigateToMapGraphAnalysis
        )
    )

    private fun getRouteCoverImage(
        workout: DomainWorkout,
        polyline: String,
    ) {
        val workoutHeader = workout.header
        val activityType = workoutHeader.activityType
        when {
            workoutHeader.isMultisport -> {
                // TODO load activity & non-activity routes
                getMultisportRouteCoverImage(
                    workout,
                    polyline
                ) // SML needed for multisport route cover image
            }
            activityType.isSlopeSki -> {
                // TODO load activity & non-activity routes
                workoutDataJob = waitForWorkoutData {
                    val current = coverImagesStateFlow.value.data ?: return@waitForWorkoutData

                    val runsOrLifts = getRunsOrLifts(activityType.id, it)
                    val coverImage = CoverImage.RouteCoverImage(
                        workoutHeaderId = workout.header.id,
                        mapCard = mapCardCreator.create(polyline),
                        activityType = activityType.id,
                        runsOrLifts = runsOrLifts,
                        onClickHandler = ::navigateToMapGraphAnalysis
                    )

                    coverImagesStateFlow.value = loaded(
                        current.copy(
                            coverImages = listOf(coverImage),
                            multisportPartActivity = null,
                            mapType = MapTypeHelper.getOrDefault(
                                mapTypeName = MAP_TYPE_SKI,
                                defaultValue = mapSelectionModel.selectedMapTypeUserHasAccessTo,
                            ),
                        )
                    )
                }
            }
            workoutHeader.isHuntingOrFishing -> {
                // TODO load activity & non-activity routes
                waitForSmlTraverseData(polyline)
            }
            else -> {
                workoutDataLoader.workoutDataStateFlow
                    .filter(ViewState<*>::isLoaded)
                    .zip(smlDataLoader.smlStateFlow.filter(ViewState<*>::isLoaded)) { workoutData, sml ->
                        val current = coverImagesStateFlow.value.data ?: return@zip

                        val (activityRoutes, nonActivityRoutes) = calculateRoutes(
                            workoutHeader = workoutHeader,
                            workoutGeoPoints = workoutData.data?.routePoints,
                            smlStreamData = sml.data?.streamData,
                        )

                        coverImagesStateFlow.value = loaded(
                            current.copy(
                                coverImages = listOf(
                                    CoverImage.RouteCoverImage(
                                        workoutHeaderId = workoutHeader.id,
                                        mapCard = mapCardCreator.create(polyline, activityRoutes, nonActivityRoutes),
                                        activityType = workoutHeader.activityTypeId,
                                        onClickHandler = ::navigateToMapGraphAnalysis,
                                    )
                                ),
                                mapType = activityType.getMapType(),
                            )
                        )
                    }
                    .launchIn(activityRetainedCoroutineScope)
            }
        }
    }

    // Returns a pair of activity routes and non-activity routes
    private fun calculateRoutes(
        workoutHeader: WorkoutHeader,
        workoutGeoPoints: List<WorkoutGeoPoint>?,
        smlStreamData: SmlStreamData?,
    ): Pair<List<List<LatLng>>, List<List<LatLng>>> {
        if (!workoutGeoPoints.isNullOrEmpty() && smlStreamData != null) {
            MapHelper.filterGeoPointsByActivity(workoutGeoPoints, smlStreamData)
                ?.let { return it }
        }

        val route = workoutGeoPoints?.map(WorkoutGeoPoint::getLatLng)
            ?.takeUnless(List<*>::isEmpty)
            ?: workoutHeader.polyline?.let(PolyUtil::decode)
            ?: emptyList()
        return listOf(route) to emptyList()
    }

    private fun waitForWorkoutData(
        onlyOnce: Boolean = false,
        onWorkoutDataReady: suspend (workoutData: WorkoutData?) -> Unit,
    ): Job = workoutDataLoader.workoutDataStateFlow
        .filter { it.isLoaded() }
        .transformWhile { state ->
            emit(state)
            !onlyOnce
        }.onEach {
            onWorkoutDataReady(it.data)
        }
        .launchIn(activityRetainedCoroutineScope)

    private fun getMultisportRouteCoverImage(workout: DomainWorkout, polyline: String) {
        smlJob = waitForSml {
            // Start listening the SML state for multisport data
            combine(
                smlDataLoader.smlStateFlow,
                multisportPartActivityLoader.multisportPartActivityFlow,
                workoutDataLoader.workoutDataStateFlow,
                ::States
            )
                .conflate()
                .onEach {
                    if (!it.sml.isLoading() && !it.multisportPartActivity.isLoading()) {
                        val multisportPartActivity = it.multisportPartActivity.data
                        val activityType = multisportPartActivity?.activityType
                            ?.let(ActivityType::valueOf)
                        if (activityType?.isIndoor == true) {
                            // Show default cover image for indoor multisport part activities
                            getDefaultMultisportCoverImage(workout, multisportPartActivity)
                        } else {
                            getMultisportRouteCoverImage(
                                workout,
                                it.sml.data,
                                it.workoutData.data,
                                multisportPartActivity,
                                polyline
                            )
                        }

                        Timber.d("Multisport cover image loaded")
                    }
                }
                .catch { Timber.w(it, "Getting multisport route cover image failed.") }
                .collect()
        }
    }

    private fun waitForSml(
        onlyOnce: Boolean = false,
        onSmlReady: suspend (sml: Sml?) -> Unit,
    ): Job = smlDataLoader.smlStateFlow
        .filter { it.isLoaded() }
        .transformWhile { state ->
            emit(state)
            !onlyOnce
        }
        .onEach { onSmlReady(it.data) }
        .launchIn(activityRetainedCoroutineScope)

    private fun onOpenGraphAnalysisClickHandler(coverImageData: CoverImageData) {
        if (coverImageData.supportsWorkoutPlaybackOnMapView) {
            onOpenWorkoutMapGraphAnalysisClicked()
        } else {
            onOpenFullscreenGraphAnalysisClicked()
        }
    }

    private fun waitForSmlTraverseData(polyline: String) {
        this.smlJob = activityRetainedCoroutineScope.launch {
            // Start listening the SML state for traverse data
            smlDataLoader.smlStateFlow
                .onEach { smlState ->
                    if (!smlState.isLoading()) {
                        getHuntingFishingRouteCoverImage(
                            smlState.data,
                            polyline
                        )
                    }
                }
                .catch { Timber.w(it, "Getting traverse cover image failed.") }
                .collect()
        }
    }

    private suspend fun getHuntingFishingRouteCoverImage(
        sml: Sml?,
        polyline: String
    ) {
        val current = coverImagesStateFlow.value.data ?: return
        if (sml != null) {
            val traverseEvents = sml.traverseEvents
            val coverImage = CoverImage.RouteCoverImage(
                workoutHeaderId = workout?.header?.id ?: 0,
                mapCard = mapCardCreator.create(
                    polyline,
                    traverseEvents
                ),
                onClickHandler = ::navigateToMapGraphAnalysis
            )

            coverImagesStateFlow.value = loaded(
                current.copy(
                    coverImages = listOf(coverImage),
                )
            )
        } else {
            val coverImage = CoverImage.RouteCoverImage(
                workoutHeaderId = workout?.header?.id ?: 0,
                mapCard = mapCardCreator.create(polyline),
                onClickHandler = ::navigateToMapGraphAnalysis
            )

            coverImagesStateFlow.value = loaded(
                current.copy(coverImages = listOf(coverImage))
            )
        }
    }

    private suspend fun getMultisportRouteCoverImage(
        workout: DomainWorkout,
        sml: Sml?,
        workoutData: WorkoutData?,
        multisportPartActivity: MultisportPartActivity?,
        polyline: String
    ) {
        val current = coverImagesStateFlow.value.data ?: return
        val activityType =
            multisportPartActivity?.activityType ?: workout.header.activityTypeId
        val runsOrLifts = getRunsOrLifts(activityType, workoutData)

        val supportsWorkoutAnalysisWithoutMap = getSupportWorkoutAnalysisWithoutMap(
            workout,
            workoutData,
            sml,
            multisportPartActivity
        )

        if (sml != null) {
            val routes = sml.multisportRoutes
            val routeIndex = sml.getIndexOfHighlightedRoute(multisportPartActivity)
            val partRoute = if (routeIndex == -1) {
                routes.flatten()
            } else {
                routes.getOrNull(routeIndex) ?: emptyList()
            }

            if (partRoute.isNotEmpty()) {
                val (activityRoutes, nonActivityRoutes) = calculateRoutes(
                    workoutHeader = workout.header,
                    workoutGeoPoints = workoutData?.routePoints,
                    smlStreamData = sml.streamData,
                )
                val coverImage = CoverImage.RouteCoverImage(
                    workoutHeaderId = workout.header.id,
                    mapCard = mapCardCreator.create(
                        polyline,
                        routes,
                        routeIndex,
                        activityRoutes,
                        nonActivityRoutes
                    ),
                    activityType = activityType,
                    runsOrLifts = runsOrLifts,
                    onClickHandler = ::navigateToMapGraphAnalysis
                )

                val supportsWorkoutAnalysisOnMap = getSupportWorkoutAnalysisOnMap(
                    workout,
                    partRoute,
                    multisportPartActivity
                )
                coverImagesStateFlow.value = loaded(
                    current.copy(
                        coverImages = listOf(coverImage),
                        multisportPartActivity = multisportPartActivity,
                        showWorkoutPlaybackButton = supportsWorkoutAnalysisOnMap || supportsWorkoutAnalysisWithoutMap,
                        supportsWorkoutPlaybackOnMapView = supportsWorkoutAnalysisOnMap,
                        mapType = mapSelectionModel.selectedMapTypeUserHasAccessTo,
                    )
                )
            } else {
                // No route for multisport part, show default cover image
                getDefaultMultisportCoverImage(workout, multisportPartActivity)
            }
        } else {
            val mapCard = mapCardCreator.create(polyline)
            val coverImage = CoverImage.RouteCoverImage(
                workoutHeaderId = workout.header.id,
                mapCard = mapCard,
                activityType = workout.header.activityTypeId,
                runsOrLifts = runsOrLifts,
                onClickHandler = ::navigateToMapGraphAnalysis
            )
            val supportsWorkoutAnalysisOnMap = getSupportWorkoutAnalysisOnMap(
                workout,
                mapCard.route ?: emptyList(),
                multisportPartActivity
            )
            coverImagesStateFlow.value = loaded(
                current.copy(
                    coverImages = listOf(coverImage),
                    multisportPartActivity = multisportPartActivity,
                    showWorkoutPlaybackButton = supportsWorkoutAnalysisOnMap || supportsWorkoutAnalysisWithoutMap,
                    supportsWorkoutPlaybackOnMapView = supportsWorkoutAnalysisOnMap,
                )
            )
        }
    }

    private fun getDefaultMultisportCoverImage(
        workout: DomainWorkout,
        multisportPartActivity: MultisportPartActivity?,
    ) {
        val activityTypeId = multisportPartActivity?.activityType ?: workout.header.activityTypeId
        val activityType = activityTypeId.run(ActivityType::valueOf)

        coverImagesStateFlow.value.data?.let { current ->
            val updated = loaded(
                current.copy(
                    coverImages = listOf(
                        CoverImage.DefaultCoverImage(
                            activityType.placeholderImageId
                        )
                    ),
                    showWorkoutPlaybackButton = getSupportWorkoutAnalysisWithoutMap(workout),
                    supportsWorkoutPlaybackOnMapView = false,
                    multisportPartActivity = multisportPartActivity,
                )
            )
            coverImagesStateFlow.value = updated
        }
    }

    private suspend fun getRunsOrLifts(
        activityType: Int,
        workoutData: WorkoutData?
    ): List<MutableList<LatLng>> = withContext(coroutinesDispatchers.computation) {
        if (workoutData == null || !ActivityType.valueOf(activityType).isSlopeSki) {
            return@withContext emptyList()
        }

        SlopeSkiUtils.getRuns(workoutData.routePoints)
            .let { runs ->
                SlopeSkiUtils.splitPointsIntoRunsOrLifts(runs, workoutData.routePoints)
            }
    }

    private suspend fun getRoute(workout: DomainWorkout): List<LatLng> = withContext(coroutinesDispatchers.computation) {
        runSuspendCatching {
            val polyline = workout.header.polyline ?: return@runSuspendCatching emptyList()
            val decoded = PolyUtil.decode(polyline)
            val tolerance =
                with(
                    LatLngBounds.builder().apply { decoded.forEach { point -> include(point) } }
                        .build()
                ) {
                    // This is an overtly naive calculation of tolerance and the results depend on the
                    // nature of the route. The previous solution was too aggressive with certain routes,
                    // like laps around a stadium track. The result was just 2 points i.e. a straight line.
                    SphericalUtil.computeDistanceBetween(this.southwest, this.northeast) / 50.0
                }
            PolyUtil.simplify(decoded, max(1.0, tolerance))
        }.onFailure {
            Timber.w(it, "Failed to get route for cover image route view.")
        }.getOrDefault(emptyList())
    }

    internal fun onScreenshot(coverIndex: Int) {
        onShareClicked(coverIndex, byScreenshot = true)
    }

    private fun onShareClicked(position: Int, byScreenshot: Boolean = false) {
        val workoutHeader = workout?.header ?: return
        val data = coverImagesStateFlow.value.data ?: return
        val isDiving = workoutHeader.activityType.isDiving
        val hasShareableImage =
            data.coverImages.any { it is CoverImage.RouteCoverImage || it is CoverImage.PhotoCoverImage }
        val hasGpsTrack = workoutHeader.polyline != null
        val showMultipleShareWays = workoutShareHelper.showMultipleWorkoutShareWays()
        if (hasShareableImage || isDiving || hasGpsTrack || showMultipleShareWays) {
            // Adjust index.
            // +1 if there is a route and images. Share preview shows the map as the first item
            // -1 per video. Share preview does not show videos
            // -1 if it is a dive and there are no images. Default image is shown in preview.
            val videoCount = data.coverImages.count { it is CoverImage.VideoCoverImage }
            val imageCount = data.coverImages.count { it is CoverImage.PhotoCoverImage }
            val mapOffset = if (data.routePoints.isNotEmpty() && imageCount > 0) 1 else 0
            val diveOffset = if (isDiving && imageCount == 0) 1 else 0
            val index = position + mapOffset - videoCount - diveOffset
            navigationEventDispatcher.dispatchEvent(
                WorkoutDetailsSharePhotoNavEvent(
                    workoutHeader,
                    index.coerceAtLeast(0),
                    SportieShareSource.WORKOUT_DETAILS,
                    workout?.extensions?.get(SummaryExtension::class),
                    byScreenshot
                )
            )
        } else {
            activityRetainedCoroutineScope.launch {
                updateSharingFlagsIfNeeded()
            }
            _onShareLink.postValue(workoutHeader)
        }
    }

    private suspend fun updateSharingFlagsIfNeeded() = withContext(coroutinesDispatchers.io) {
        val workoutHeader = workout?.header ?: return@withContext
        val currentSharingFlags = workoutHeader.sharingFlags
        if (currentSharingFlags == SharingOption.NOT_SHARED.backendId || currentSharingFlags == SharingOption.FOLLOWERS.backendId) {
            val newSharingFlags =
                if (currentSharingFlags == SharingOption.NOT_SHARED.backendId) {
                    SharingOption.flagOf(listOf(SharingOption.LINK))
                } else {
                    SharingOption.flagOf(listOf(SharingOption.LINK, SharingOption.FOLLOWERS))
                }
            runSuspendCatching {
                saveWorkoutHeaderUseCase(workoutHeader.copy(sharingFlags = newSharingFlags))
                syncRequestHandler.runRequestInQueue(SyncRequest.push())
            }.onFailure { Timber.w(it, "Updating the header with SharingOption.LINK flag failed.") }
        }
    }

    private fun getSupportWorkoutAnalysisWithoutMap(
        workout: DomainWorkout,
        workoutData: WorkoutData? = workoutDataLoader.workoutDataStateFlow.value.data,
        sml: Sml? = smlDataLoader.smlStateFlow.value.data,
        multisportPartActivity: MultisportPartActivity? = multisportPartActivityLoader.multisportPartActivityFlow.value.data
    ): Boolean = workout.header.supportsWorkoutAnalysisWithoutMap(
        workoutData,
        sml,
        multisportPartActivity,
    )

    private fun getSupportWorkoutAnalysisOnMap(
        workout: DomainWorkout,
        latLngs: List<LatLng>,
        multisportPartActivity: MultisportPartActivity? = null
    ): Boolean = workout.header.supportWorkoutAnalysisOnMap(latLngs, multisportPartActivity)

    private fun onRouteViewClicked() {
        navigateToMapGraphAnalysis(navigationSource = MAP_THUMBNAIL)
    }

    private fun onOpenWorkoutMapGraphAnalysisClicked() {
        navigateToMapGraphAnalysis(
            navigationSource = PLAY_BUTTON_WORKOUT_DETAILS,
            autoPlayback = true
        )
    }

    private fun onOpenFullscreenGraphAnalysisClicked() {
        workout?.run {
            navigationEventDispatcher.dispatchEvent(
                WorkoutDetailsFullscreenGraphAnalysisNavEvent(
                    lockLandscape = false,
                    autoPlayback = false,
                    initialSelectedMillisInWorkout = 0L,
                    trackWorkoutAnalysisScreenEvent = true,
                    analyticsSource = NO_MAP_ANALYSIS_BUTTON,
                    workoutHeader = this.header,
                    multisportPartActivity = multisportPartActivityLoader.multisportPartActivityFlow.value.data
                )
            )
        }
    }

    private fun onMuteButtonClicked(
        videoCoverImage: CoverImage.VideoCoverImage,
        position: Int
    ) {
        Timber.d("Mute button was pressed for position: $position")

        val previousState = coverImagesStateFlow.value
        val updatedVideoCoverImage =
            videoCoverImage.copy(isMuted = !videoCoverImage.isMuted)
        val updatedCoverImages =
            previousState.data?.coverImages?.mapIndexed { index, coverImage ->
                if (index == position) {
                    updatedVideoCoverImage
                } else {
                    coverImage
                }
            }
        if (updatedCoverImages != null) {
            coverImagesStateFlow.value =
                loaded(previousState.data?.copy(coverImages = updatedCoverImages))
        }
    }

    private fun navigateToMapGraphAnalysis(
        navigationSource: String = MAP_OVERVIEW,
        autoPlayback: Boolean = false
    ) {
        workout?.run {
            val multisportPartActivity = multisportPartActivityLoader.multisportPartActivityFlow.value.data
            navigationEventDispatcher.dispatchEvent(
                WorkoutDetailsMapGraphAnalysisNavEvent(
                    this.header,
                    multisportPartActivity,
                    workoutDetailsAnalytics,
                    autoPlayback,
                    null,
                    navigationSource,
                    if (multisportPartActivity == null) WORKOUT_DETAILS_SCREEN else WORKOUT_MULTISPORT_DETAILS_SCREEN
                )
            )
        }
    }

    private fun navigateToPhotoPager() {
        navigationEventDispatcher.dispatchEvent(WorkoutDetailsPhotoPagerNavEvent())
    }

    data class States(
        val sml: ViewState<Sml?>,
        val multisportPartActivity: ViewState<MultisportPartActivity?>,
        val workoutData: ViewState<WorkoutData?>,
    )
}
