package com.stt.android.workout.details.charts

import android.content.Context
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Shader
import android.graphics.Typeface
import android.graphics.drawable.ShapeDrawable
import android.graphics.drawable.shapes.RectShape
import android.util.AttributeSet
import androidx.annotation.ColorInt
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.doOnLayout
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.ValueFormatter
import com.github.mikephil.charting.utils.EntryXComparator
import com.stt.android.FontRefs
import com.stt.android.ThemeColors
import com.stt.android.core.domain.GraphType
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.components.charts.EnlargedGraphMarkerView
import kotlin.math.min
import com.stt.android.R as BaseR

abstract class WorkoutLineChart @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : LineChart(context, attrs, defStyle) {

    @ColorInt
    protected var lineColor: Int = 0

    @ColorInt
    protected var fillColorStart: Int = 0

    @ColorInt
    protected var fillColorEnd: Int = 0

    @ColorInt
    protected var labelColor: Int = 0
    protected var chartTypeface: Typeface = Typeface.DEFAULT
    var lineWidth = 1.0f

    init {
        description.text = ""
        setNoDataText("")
        isHighlightPerDragEnabled = false
        isHighlightPerTapEnabled = false
        setDrawGridBackground(false)
        setDrawBorders(false)
        setScaleEnabled(false)
        isDoubleTapToZoomEnabled = false
        setTouchEnabled(false)
        minOffset = 0.0f
        extraTopOffset = 8.0f
        extraBottomOffset = 2.0f
        setHardwareAccelerationEnabled(true)

        axisLeft.isEnabled = false

        with(legend) {
            textColor = labelColor
            typeface = chartTypeface
            isWordWrapEnabled = true
        }
    }

    protected abstract fun addAvgLine(avgValue: Float)

    open fun setWorkoutLineChartData(workoutLineChartData: WorkoutLineChartData) {
        setupHighlighter(workoutLineChartData.graphType, workoutLineChartData.infoModelFormatter)
        val lineDataSet = workoutLineChartData.data.map {
            createLineDataSet(
                it.entries,
                color = it.lineColor ?: lineColor,
                label = it.name,
                isFilled = workoutLineChartData.isFilled,
                invertFillColors = workoutLineChartData.isFilled
            )
        }

        val lineData = LineData(lineDataSet)

        with(xAxis) {
            // Force 5 labels on x-axis when x-axis scaling (zooming) is not enabled and entry count is over 4
            if (lineData.entryCount > 0 && !isScaleXEnabled) {
                setLabelCount(min(lineData.entryCount, 5), true)
            }
            valueFormatter = object : ValueFormatter() {
                override fun getFormattedValue(value: Float) =
                    workoutLineChartData.formatter.formatXValue(value, context)
            }
        }
        with(axisRight) {
            setLabelCount(4, true)
            if (workoutLineChartData.minValueStrict != null) {
                axisMinimum = workoutLineChartData.minValueStrict
            } else {
                val rawGranularity = ((lineData.yMax - lineData.yMin) / 4).ceil()
                // granularities such as 6 or 7 are bumped to 10 by the library which can make
                // charts not have enough axis lines. The problem remains for other magnitude orders
                // but it is less prominent.
                granularity =
                    if (rawGranularity > 5 && rawGranularity < 10) 5f else rawGranularity
                // If the yMin == yMax we need to set the min a tenth lower
                axisMinimum = if (lineData.isMinMaxTheSame()) {
                    lineData.yMin.lowerByATenth()
                } else {
                    lineData.yMin
                }
            }

            isInverted = workoutLineChartData.isInverted
            this.valueFormatter = object : ValueFormatter() {
                override fun getFormattedValue(value: Float) =
                    workoutLineChartData.formatter.formatConvertedYValue(value)
            }

            if (workoutLineChartData.minRange == null) {
                axisMaximum = when {
                    workoutLineChartData.isInverted -> lineData.yMax
                    lineData.isMinMaxTheSame() -> lineData.yMax.increaseByATenth()
                    else -> lineData.yMax.roundUpToEven()
                }
            } else {
                axisMaximum = getMaxFromMinRange(
                    workoutLineChartData.minValueStrict,
                    lineData,
                    workoutLineChartData.isInverted,
                    workoutLineChartData.maxValueStrict,
                    workoutLineChartData.minRange
                )
                spaceMin = 0.5f
            }
        }

        data = lineData
        if (workoutLineChartData.enableLegend) {
            legend.isEnabled = true
            legend.textColor = ThemeColors.primaryTextColor(context)
            ResourcesCompat.getFont(context, FontRefs.CHART_FONT_REF)?.let {
                legend.typeface = it
            }
        } else {
            legend.isEnabled = false
        }

        if (workoutLineChartData.averageLineValue != null) {
            // Wait for layout before calling addAvgLine to make sure possible calculations of
            // the line's position in the View give correct results.
            doOnLayout {
                axisRight.removeAllLimitLines()
                addAvgLine(workoutLineChartData.averageLineValue)
            }
        } else {
            axisRight.removeAllLimitLines()
        }

        invalidate()
    }

    private fun createLineDataSet(
        entries: List<Entry?>,
        color: Int,
        isFilled: Boolean = false,
        label: String = "",
        invertFillColors: Boolean = false
    ): LineDataSet {
        val sortedEntries = entries.sortedWith(EntryXComparator())
        val (colorStart, colorEnd) = if (invertFillColors) {
            fillColorEnd to fillColorStart
        } else {
            fillColorStart to fillColorEnd
        }
        return LineDataSet(sortedEntries, label).apply {
            this.color = color
            setDrawValues(false)
            setDrawCircles(false)
            setDrawFilled(isFilled)
            if (isFilled) {
                fillDrawable = ShapeDrawable(RectShape()).apply {
                    shaderFactory = object : ShapeDrawable.ShaderFactory() {
                        override fun resize(width: Int, height: Int): Shader {
                            return LinearGradient(
                                0f,
                                0f,
                                0f,
                                height.toFloat(),
                                colorStart,
                                colorEnd,
                                Shader.TileMode.REPEAT
                            )
                        }
                    }
                }
            }
            mode = LineDataSet.Mode.LINEAR
            setDrawHighlightIndicators(true)
            highLightColor = Color.BLACK
            highlightLineWidth = 1.0f
            setDrawHorizontalHighlightIndicator(false)
            axisDependency = YAxis.AxisDependency.RIGHT
            lineWidth = 1.0f
        }
    }

    private fun setupHighlighter(graphType: GraphType, infoModelFormatter: InfoModelFormatter) {
        if (isScaleXEnabled) {
            isHighlightPerTapEnabled = true
            isHighlightPerDragEnabled = true
            setDrawMarkers(true)
            marker = EnlargedGraphMarkerView(
                context,
                BaseR.layout.enlarged_graph_markerview,
                graphType,
                infoModelFormatter
            )
        }
    }
}
