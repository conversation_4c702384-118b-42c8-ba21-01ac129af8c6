package com.stt.android.workout.details.diveprofile

import android.content.Context
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.stt.android.diveEvent
import com.stt.android.infomodel.SummaryItem.DURATION
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.extensions.getDescription
import com.stt.android.ui.extensions.getText
import com.stt.android.ui.extensions.icon
import com.stt.android.ui.fragments.workout.dive.DiveProfileShowEventsContainer
import com.stt.android.ui.utils.TextFormatter
import dagger.hilt.android.qualifiers.ActivityContext
import javax.inject.Inject

class DiveEventsController
@Inject constructor(
    @ActivityContext private val context: Context,
    private val infoModelFormatter: InfoModelFormatter
) : ViewStateEpoxyController<DiveProfileShowEventsContainer?>() {
    override fun buildModels(viewState: ViewState<DiveProfileShowEventsContainer?>) {
        viewState.data?.let { data ->
            data.items.forEach { item ->
                val event = item.event
                val text = event.getText(context)
                val description = event.getDescription(context, infoModelFormatter)
                val drawable = event.icon

                diveEvent {
                    id(event.hashCode())
                    text(text)
                    description(description)
                    showDescription(description.isNotEmpty())
                    drawable(drawable)
                    elapsed(
                        infoModelFormatter.formatValueAsString(
                            DURATION,
                            item.elapsed / 1000f // Formatter needs the fractions as well
                        )
                    )
                    val unit = context.getString(infoModelFormatter.unit.altitudeUnit)
                    val depthString = String.format(
                        "%s %s",
                        TextFormatter.formatDepth(item.depth.toDouble(), infoModelFormatter.unit),
                        unit
                    )
                    depth(depthString)
                }
            }
        }
    }
}
