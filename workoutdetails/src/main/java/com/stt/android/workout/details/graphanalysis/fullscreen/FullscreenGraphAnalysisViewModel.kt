package com.stt.android.workout.details.graphanalysis.fullscreen

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.workout.details.analytics.WorkoutDetailsAnalytics
import com.stt.android.workout.details.analytics.navigatedFromValueToPlaybackInitiatedFrom
import com.stt.android.workout.details.extensions.DiveExtensionDataLoader
import com.stt.android.workout.details.graphanalysis.GraphAnalysisSelections
import com.stt.android.workout.details.graphanalysis.laps.AnalysisLapsLoader
import com.stt.android.workout.details.graphanalysis.playback.PlaybackStateModel
import com.stt.android.workout.details.graphanalysis.playback.WorkoutPlaybackPauseReason
import com.stt.android.workout.details.multisport.MultisportPartActivityLoader
import com.stt.android.workout.details.sml.SmlDataLoader
import com.stt.android.workout.details.watch.WorkoutExtensionsDataLoader
import com.stt.android.workout.details.workoutdata.WorkoutDataLoader
import com.stt.android.workout.details.workoutheader.WorkoutHeaderLoader
import com.stt.android.workout.details.workoutvalues.WorkoutValuesLoader
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class FullscreenGraphAnalysisViewModel @Inject constructor(
    private val handle: SavedStateHandle,
    private val workoutValuesLoader: WorkoutValuesLoader,
    private val workoutDataLoader: WorkoutDataLoader,
    private val smlDataLoader: SmlDataLoader,
    private val diveExtensionDataLoader: DiveExtensionDataLoader,
    private val workoutHeaderLoader: WorkoutHeaderLoader,
    private val multisportPartActivityLoader: MultisportPartActivityLoader,
    private val analysisLapsLoader: AnalysisLapsLoader,
    private val workoutExtensionsLoader: WorkoutExtensionsDataLoader,
    private val playbackStateModel: PlaybackStateModel,
    private val workoutDetailsAnalytics: WorkoutDetailsAnalytics,
    private val coroutinesDispatchers: CoroutinesDispatchers
) : ViewModel() {
    private var loadJob: Job? = null

    init {
        playbackStateModel.setReadyForAnimation(CHART_READY_FOR_ANIMATION_KEY, false)
        trackWorkoutAnalysisFullScreenEvent()
    }

    /**
     * Loads data that GraphAnalysisViewModel wants to listen to but not trigger loading for
     */
    fun loadData(
        workoutHeader: WorkoutHeader,
        multisportPartActivity: MultisportPartActivity?
    ) {
        if (handle.get<Boolean>(ARG_TRACK_WORKOUT_ANALYSIS_SCREEN_EVENT) == true) {
            handle.remove<Boolean>(ARG_TRACK_WORKOUT_ANALYSIS_SCREEN_EVENT)
            trackWorkoutAnalysisScreenEvent(handle.get(ARG_ANALYTICS_SOURCE))
        }

        loadJob?.cancel()
        loadJob = viewModelScope.launch(coroutinesDispatchers.io) {
            playbackStateModel.setPlaybackDuration(15_000L)

            workoutHeaderLoader.setWorkoutHeader(workoutHeader)
            multisportPartActivityLoader.setMultisportPartActivity(multisportPartActivity)

            launch { workoutValuesLoader.loadWorkoutValuesData(workoutHeader).collect() }
            launch { workoutDataLoader.loadWorkoutData(workoutHeader).collect() }
            launch { smlDataLoader.loadSml(workoutHeader).collect() }
            launch { diveExtensionDataLoader.loadDiveExtension(workoutHeader).collect() }
            launch { analysisLapsLoader.loadLapsTables(workoutHeader).collect() }
            launch { workoutExtensionsLoader.loadWorkoutExtensions(workoutHeader).collect() }

            val autoPlayback = handle.get<Boolean>(ARG_AUTO_PLAYBACK) == true
            handle.remove<Boolean>(ARG_AUTO_PLAYBACK)

            launch(coroutinesDispatchers.main) {
                playbackStateModel.playbackProgressFlow
                    .filter { it.workoutDurationMillis != PlaybackStateModel.DURATION_NOT_SET }
                    .take(1)
                    .collect {
                        if (autoPlayback) {
                            playbackStateModel.resumePlayback(
                                navigatedFromValueToPlaybackInitiatedFrom(handle.get(ARG_ANALYTICS_SOURCE))
                            )
                        }
                    }
            }
        }
    }

    fun pausePlayback(pauseReason: WorkoutPlaybackPauseReason) {
        playbackStateModel.pausePlayback(pauseReason)
    }

    fun onChartReadyForPlayback() {
        playbackStateModel.setReadyForAnimation(CHART_READY_FOR_ANIMATION_KEY, true)
    }

    fun onScreenExited() {
        handle.set(
            ARG_INITIAL_SELECTIONS,
            GraphAnalysisSelections(
                playbackStateModel.playbackProgress.timeInWorkoutMillis,
                null
            )
        )
        handle.set(ARG_AUTO_PLAYBACK, playbackStateModel.isRunning)
    }

    private fun trackWorkoutAnalysisScreenEvent(analyticsSource: String?) {
        viewModelScope.launch(NonCancellable) {
            workoutDetailsAnalytics.trackWorkoutAnalysisScreenEvent(
                analyticsSource,
                null
            )
        }
    }

    private fun trackWorkoutAnalysisFullScreenEvent() {
        viewModelScope.launch(NonCancellable) {
            workoutDetailsAnalytics.trackWorkoutAnalysisFullScreenEvent()
        }
    }

    companion object {
        private const val ARG_AUTO_PLAYBACK = "autoPlayback"
        private const val ARG_INITIAL_SELECTIONS = "initialSelections"
        private const val ARG_TRACK_WORKOUT_ANALYSIS_SCREEN_EVENT = "trackWorkoutAnalysisScreenEvent"
        private const val ARG_ANALYTICS_SOURCE = "analyticsSource"

        private const val CHART_READY_FOR_ANIMATION_KEY =
            "WorkoutMapGraphAnalysisViewModel_CHART_READY_FOR_ANIMATION_KEY"
    }
}
