package com.stt.android.workout.details.graphanalysis.charts

import android.annotation.SuppressLint
import android.content.Context
import android.widget.TextView
import com.github.mikephil.charting.components.MarkerView
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.formatter.ValueFormatter
import com.github.mikephil.charting.highlight.Highlight
import com.github.mikephil.charting.utils.MPPointF
import com.stt.android.workout.details.R

@SuppressLint("ViewConstructor")
class GraphAnalysisHighlightMarker(
    context: Context,
    private val xValueFormatter: ValueFormatter
) : MarkerView(context, R.layout.graph_analysis_highlight_marker) {
    private val timestampTextView: TextView
        get() = rootView.findViewById(R.id.graph_analysis_highlight_marker_value_text)

    fun showTimestampText(show: <PERSON><PERSON><PERSON>) {
        timestampTextView.visibility = if (show) VISIBLE else GONE
    }

    override fun getOffsetForDrawingAtPoint(posX: Float, posY: Float): MPPointF {
        return MPPointF(offset.x + -measuredWidth * 0.5f, offset.y - posY)
    }

    override fun refreshContent(e: Entry, highlight: Highlight) {
        timestampTextView.text = xValueFormatter.getFormattedValue(e.x)
        super.refreshContent(e, highlight)
    }
}
