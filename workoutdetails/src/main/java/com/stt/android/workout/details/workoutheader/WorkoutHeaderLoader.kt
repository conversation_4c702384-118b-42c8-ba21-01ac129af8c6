package com.stt.android.workout.details.workoutheader

import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.domain.workouts.WorkoutHeader
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject

interface WorkoutHeaderLoader {
    val workoutHeaderFlow: StateFlow<ViewState<WorkoutHeader?>>
    fun setWorkoutHeader(workoutHeader: WorkoutHeader?): StateFlow<ViewState<WorkoutHeader?>>
}

@ActivityRetainedScoped
class DefaultWorkoutHeaderLoader
@Inject constructor() : WorkoutHeaderLoader {
    override val workoutHeaderFlow: MutableStateFlow<ViewState<WorkoutHeader?>> =
        MutableStateFlow(loading())

    override fun setWorkoutHeader(workoutHeader: WorkoutHeader?): StateFlow<ViewState<WorkoutHeader?>> {
        return workoutHeaderFlow.apply {
            value = loaded(workoutHeader)
        }
    }
}
