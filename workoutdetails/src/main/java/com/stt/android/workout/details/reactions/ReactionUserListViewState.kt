package com.stt.android.workout.details.reactions

import com.stt.android.follow.UserFollowStatus

typealias OnItemClickHandler = (String, UserFollowStatus) -> Unit

data class ReactionUserListViewState(
    val onUserClicked: OnItem<PERSON><PERSON><PERSON><PERSON><PERSON>,
    val onFollowButtonClicked: On<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    val userFollowStatusList: List<UserFollowStatus>,
    val workoutKey: String,
    val currentUsername: String
) {
    fun shouldShowFollowButtonForUser(username: String): Boolean = currentUsername != username
}
