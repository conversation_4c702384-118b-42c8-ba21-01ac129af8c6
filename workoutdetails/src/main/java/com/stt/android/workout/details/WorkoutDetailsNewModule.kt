package com.stt.android.workout.details

import android.content.Context
import com.soy.algorithms.camerapath.MmlBounds
import com.stt.android.aerobiczone.AerobicZoneDialogFragmentCreator
import com.stt.android.di.Forced2dPlaybackMode
import com.stt.android.elevationdata.di.ElevationModule
import com.stt.android.hr.HeartRateDeviceManager
import com.stt.android.maps.SuuntoMaps
import com.stt.android.maps.mapbox.domain.DemSourceUseCase
import com.stt.android.remote.di.BaseUrlConfiguration
import com.stt.android.ui.activities.competition.LapsDataProvider
import com.stt.android.ui.map.MapHelper
import com.stt.android.workout.details.achievements.AchievementsDataLoader
import com.stt.android.workout.details.achievements.DefaultAchievementsDataLoader
import com.stt.android.workout.details.ads.hrbelt.DefaultHrBeltAdDataLoader
import com.stt.android.workout.details.ads.hrbelt.HrBeltAdDataLoader
import com.stt.android.workout.details.advancedlaps.AdvancedLapsDataLoader
import com.stt.android.workout.details.advancedlaps.DefaultAdvancedLapsDataLoader
import com.stt.android.workout.details.aerobiczone.AerobicZoneDialogFragmentCreatorImpl
import com.stt.android.workout.details.analysis.DefaultWorkoutAnalysisDataLoader
import com.stt.android.workout.details.analysis.WorkoutAnalysisDataLoader
import com.stt.android.workout.details.analytics.DefaultWorkoutDetailsAnalytics
import com.stt.android.workout.details.analytics.WorkoutDetailsAnalytics
import com.stt.android.workout.details.competition.CompetitionWorkoutDataLoader
import com.stt.android.workout.details.competition.DefaultCompetitionWorkoutDataLoader
import com.stt.android.workout.details.divelocation.DefaultDiveLocationDataLoader
import com.stt.android.workout.details.divelocation.DiveLocationDataLoader
import com.stt.android.workout.details.diveprofile.DefaultDiveProfileDataLoader
import com.stt.android.workout.details.diveprofile.DiveProfileDataLoader
import com.stt.android.workout.details.divetrack.DefaultDiveTrackDataLoader
import com.stt.android.workout.details.divetrack.DiveTrackDataLoader
import com.stt.android.workout.details.extensions.DefaultDiveExtensionDataLoader
import com.stt.android.workout.details.extensions.DiveExtensionDataLoader
import com.stt.android.workout.details.heartrate.DefaultHeartRateDataLoader
import com.stt.android.workout.details.heartrate.HeartRateDataLoader
import com.stt.android.workout.details.heartrate.RecoveryHeartRateInThreeMinsDataLoader
import com.stt.android.workout.details.intensity.DefaultZoneAnalysisDataLoader
import com.stt.android.workout.details.intensity.ZoneAnalysisDataLoader
import com.stt.android.workout.details.laps.DefaultLapsDataLoader
import com.stt.android.workout.details.laps.LapsDataLoader
import com.stt.android.workout.details.laps.LapsDataProviderImpl
import com.stt.android.workout.details.laps.advanced.AdvancedLapsModule
import com.stt.android.workout.details.mml.MmlUtils
import com.stt.android.workout.details.multisport.DefaultMultisportPartActivityLoader
import com.stt.android.workout.details.multisport.MultisportPartActivityLoader
import com.stt.android.workout.details.share.video.VideoShareModule
import com.stt.android.workout.details.sml.DefaultSmlDataLoader
import com.stt.android.workout.details.sml.SmlDataLoader
import com.stt.android.workout.details.summary.DefaultRecentWorkoutSummaryDataLoader
import com.stt.android.workout.details.summary.RecentWorkoutSummaryDataLoader
import com.stt.android.workout.details.trend.DefaultRecentTrendDataLoader
import com.stt.android.workout.details.trend.RecentTrendDataLoader
import com.stt.android.workout.details.watch.DefaultWorkoutExtensionsDataLoader
import com.stt.android.workout.details.watch.WorkoutExtensionsDataLoader
import com.stt.android.workout.details.weather.DefaultWeatherConditionsLoader
import com.stt.android.workout.details.weather.WeatherConditionsLoader
import com.stt.android.workout.details.workoutdata.DefaultWorkoutDataLoader
import com.stt.android.workout.details.workoutdata.WorkoutDataLoader
import com.stt.android.workout.details.workoutheader.DefaultWorkoutHeaderLoader
import com.stt.android.workout.details.workoutheader.WorkoutHeaderLoader
import com.stt.android.workout.details.workoutvalues.DefaultWorkoutValuesLoader
import com.stt.android.workout.details.workoutvalues.WorkoutValuesLoader
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ActivityRetainedComponent
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.FlowPreview
import javax.inject.Named
import com.stt.android.R as BaseR

@Module(includes = [AdvancedLapsModule::class, ElevationModule::class, VideoShareModule::class])
@InstallIn(ActivityRetainedComponent::class)
@OptIn(FlowPreview::class)
abstract class WorkoutDetailsActivityRetainedModule {
    @Binds
    abstract fun bindWorkoutValuesLoader(defaultWorkoutValuesLoader: DefaultWorkoutValuesLoader): WorkoutValuesLoader

    @Binds
    @DefaultHeartRateLoader
    abstract fun bindHeartRateLoader(loader: DefaultHeartRateDataLoader): HeartRateDataLoader

    @Binds
    @HeartRateInThreeMinsLoader
    abstract fun bindRecoveryHeartRateInThreeMinsLoader(loader: RecoveryHeartRateInThreeMinsDataLoader): HeartRateDataLoader

    @Binds
    abstract fun bindWorkoutDataLoader(loader: DefaultWorkoutDataLoader): WorkoutDataLoader

    @Binds
    abstract fun bindSmlDataLoader(loader: DefaultSmlDataLoader): SmlDataLoader

    @Binds
    abstract fun bindWorkoutAnalysisLoader(loader: DefaultWorkoutAnalysisDataLoader): WorkoutAnalysisDataLoader

    @Binds
    abstract fun bindDiveExtensionDataLoader(loader: DefaultDiveExtensionDataLoader): DiveExtensionDataLoader

    @Binds
    abstract fun bindDiveProfileDataLoader(loader: DefaultDiveProfileDataLoader): DiveProfileDataLoader

    @Binds
    abstract fun bindRecentTrendDataLoader(loader: DefaultRecentTrendDataLoader): RecentTrendDataLoader

    @Binds
    abstract fun bindCompetitionWorkoutDataLoader(loader: DefaultCompetitionWorkoutDataLoader): CompetitionWorkoutDataLoader

    @Binds
    abstract fun bindRecentWorkoutSummaryLoader(loader: DefaultRecentWorkoutSummaryDataLoader): RecentWorkoutSummaryDataLoader

    @Binds
    abstract fun bindAdvancedLapsDataLoader(loader: DefaultAdvancedLapsDataLoader): AdvancedLapsDataLoader

    @Binds
    abstract fun bindLapsDataLoader(loader: DefaultLapsDataLoader): LapsDataLoader

    @Binds
    abstract fun bindHrBeltAdDataLoader(loader: DefaultHrBeltAdDataLoader): HrBeltAdDataLoader

    @Binds
    abstract fun bindAchievementsDataLoader(loader: DefaultAchievementsDataLoader): AchievementsDataLoader

    @Binds
    abstract fun bindDiveLocationDataLoader(loader: DefaultDiveLocationDataLoader): DiveLocationDataLoader

    @Binds
    abstract fun bindWeatherConditionsLoader(loader: DefaultWeatherConditionsLoader): WeatherConditionsLoader

    @Binds
    abstract fun bindMultisportPartActivityLoader(loader: DefaultMultisportPartActivityLoader): MultisportPartActivityLoader

    @Binds
    abstract fun bindWorkoutHeaderLoader(loader: DefaultWorkoutHeaderLoader): WorkoutHeaderLoader

    @Binds
    abstract fun bindWorkoutDetailsAnalytics(analytics: DefaultWorkoutDetailsAnalytics): WorkoutDetailsAnalytics

    @Binds
    abstract fun bindMmlBounds(mmlUtil: MmlUtils): MmlBounds

    @Binds
    abstract fun bindDefaultWorkoutExtensionsDataLoader(loader: DefaultWorkoutExtensionsDataLoader): WorkoutExtensionsDataLoader

    @Binds
    abstract fun bindZoneAnalysisDataLoader(loader: DefaultZoneAnalysisDataLoader): ZoneAnalysisDataLoader

    @Binds
    abstract fun bindDiveTrackDataLoader(loader: DefaultDiveTrackDataLoader): DiveTrackDataLoader

    @Binds
    abstract fun bindAerobicZoneDialogFragmentCreator(loader: AerobicZoneDialogFragmentCreatorImpl): AerobicZoneDialogFragmentCreator

    @Binds
    abstract fun bindLapsDataProvider(provider: LapsDataProviderImpl): LapsDataProvider

    companion object {
        @Provides
        @Forced2dPlaybackMode
        fun provideForced2dMode(suuntoMaps: SuuntoMaps): Boolean {
            // Don't put to Singleton module as ST Premium subscription can change the
            // default maps provider
            return MapHelper.provideForced2dMode(suuntoMaps.defaultProvider?.name)
        }
    }
}

@Module
@InstallIn(SingletonComponent::class)
object WorkoutDetailsSingletonModule {
    @Provides
    @Named("HideAdFlag")
    fun provideHideAds(context: Context): Boolean {
        return context.resources.getBoolean(BaseR.bool.hideAds)
    }

    @Provides
    @Named("HasBeltPaired")
    fun provideHasBeltPaired(context: Context): Boolean {
        return HeartRateDeviceManager.hasPairedDevice(context)
    }

    @Provides
    fun provideDemSourceUseCase(
        baseUrlConfiguration: BaseUrlConfiguration,
    ): DemSourceUseCase = DemSourceUseCase(
        demSourceMmlTemplate = "${baseUrlConfiguration.tileServerUrl}mml-dem/{z}/{x}/{y}.png",
    )
}
