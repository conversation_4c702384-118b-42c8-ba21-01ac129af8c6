package com.stt.android.workout.details.summary

import android.content.res.Resources
import android.text.format.DateUtils
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.viewpager.widget.ViewPager
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.domain.workout.ActivityType
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.adapters.RecentWorkoutSummaryPagerAdapter
import com.stt.android.ui.components.RecentWorkoutSummaryView
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.utils.CalendarProvider
import com.stt.android.utils.DateUtils.DAY_IN_MILLIS
import com.stt.android.utils.DateUtils.isSameDay
import com.stt.android.workout.details.R
import com.stt.android.workout.details.RecentWorkoutSummary
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import me.relex.circleindicator.CircleIndicator
import com.stt.android.R as BaseR

@EpoxyModelClass
abstract class RecentWorkoutSummaryModel :
    BaseRecentWorkoutSummaryModel<RecentWorkoutSummaryViewHolder>(),
    ViewPager.OnPageChangeListener {

    private lateinit var recentWorkoutSummaryPagerAdapter: RecentWorkoutSummaryPagerAdapter

    @EpoxyAttribute
    lateinit var recentWorkoutSummary: RecentWorkoutSummary

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var infoModelFormatter: InfoModelFormatter

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var unitConverter: JScienceUnitConverter

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var workoutHeaderController: WorkoutHeaderController

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var calendarProvider: CalendarProvider

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var lifecycleScope: CoroutineScope? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var onViewMoreClicked: View.OnClickListener

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var currentPage: Int = 0

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var onSummaryPageSelected: ((Int) -> Unit)? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker

    override fun getDefaultLayout() = R.layout.model_recent_workout_summary

    override fun bind(holder: RecentWorkoutSummaryViewHolder) {
        initColor(holder.root.context)
        initViewHolder(holder)
        val activityType: ActivityType = recentWorkoutSummary.referenceWorkout.activityType
        holder.activityIcon.setImageResource(activityType.iconId)
        holder.viewMoreButton.setOnClickListener(onViewMoreClicked)
        showSummary(holder)
    }

    override fun unbind(holder: RecentWorkoutSummaryViewHolder) {
        holder.viewPager.removeOnPageChangeListener(this)
        viewHolder.clear()
    }

    private fun showSummary(
        holder: RecentWorkoutSummaryViewHolder
    ) {
        lifecycleScope?.launch {
            createSummary(recentWorkoutSummary, workoutHeaderController, infoModelFormatter)?.run {
                val summary = this.toOldModel()
                holder.summaryView.setRecentWorkoutSummary(summary, infoModelFormatter.unit)
                with(holder.viewPager) {
                    recentWorkoutSummaryPagerAdapter = RecentWorkoutSummaryPagerAdapter(
                        context,
                        summary,
                        recentWorkoutSummary.referenceWorkout,
                        infoModelFormatter,
                        unitConverter,
                        amplitudeAnalyticsTracker,
                    )
                    adapter = recentWorkoutSummaryPagerAdapter
                    setCurrentItem(currentPage, false)
                    holder.dataType.text =
                        recentWorkoutSummaryPagerAdapter.getPageTitle(holder.viewPager.currentItem)
                    holder.pagerIndicator.setViewPager(this)
                    setupPageOnChangedListener(holder)
                    recentWorkoutSummaryPagerAdapter.registerDataSetObserver(holder.pagerIndicator.dataSetObserver)
                }

                // as per design, if the period ends today, we show the start time as e.g. "30 days ago"
                // otherwise we show it as e.g. "1 April 2015"
                val resources: Resources = holder.root.context.resources
                val now = System.currentTimeMillis()
                val startTime: Long = recentWorkoutSummary.startTime
                val endTime: Long = recentWorkoutSummary.endTime
                if (isSameDay(endTime, now, calendarProvider)) {
                    val dayGap = ((now - startTime) / DateUtils.DAY_IN_MILLIS).toInt()
                    holder.startDate.text = resources.getQuantityString(
                        BaseR.plurals.days_ago,
                        dayGap,
                        dayGap
                    )
                } else {
                    holder.startDate.text = TextFormatter.formatRelativeDateSpan(
                        resources,
                        startTime
                    )
                }
                holder.endDate.text = TextFormatter.formatRelativeDateSpan(resources, endTime)
                holder.title.text = resources.getString(
                    BaseR.string.days_summary,
                    (endTime - startTime) / DAY_IN_MILLIS
                )
            }
        }
    }

    private fun setupPageOnChangedListener(holder: RecentWorkoutSummaryViewHolder) {
        holder.viewPager.removeOnPageChangeListener(this)
        holder.viewPager.addOnPageChangeListener(this)
    }

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
        // do nothing
    }

    override fun onPageSelected(position: Int) {
        viewHolder.get()?.run {
            dataType.text = recentWorkoutSummaryPagerAdapter.getPageTitle(position)
        }
        onSummaryPageSelected?.invoke(position)
    }

    override fun onPageScrollStateChanged(state: Int) {
        // do nothing
    }
}

class RecentWorkoutSummaryViewHolder : KotlinEpoxyHolder() {
    val root by bind<View>(R.id.root)
    val activityIcon by bind<ImageView>(R.id.activityIcon)
    val viewMoreButton by bind<Button>(R.id.viewMoreButton)
    val summaryView by bind<RecentWorkoutSummaryView>(R.id.recentWorkoutSummaryView)
    val viewPager by bind<ViewPager>(R.id.summaryViewPager)
    val title by bind<TextView>(R.id.title)
    val dataType by bind<TextView>(R.id.dataType)
    val startDate by bind<TextView>(R.id.startDate)
    val endDate by bind<TextView>(R.id.endDate)
    val pagerIndicator by bind<CircleIndicator>(R.id.pagerIndicator)
}
