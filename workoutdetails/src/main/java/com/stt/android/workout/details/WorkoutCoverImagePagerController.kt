package com.stt.android.workout.details

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleCoroutineScope
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.stt.android.maps.MapSnapshotter
import timber.log.Timber
import javax.inject.Inject

class WorkoutCoverImagePagerController @Inject constructor() :
    ViewStateEpoxyController<CoverImageData>() {
    var lifecycleScope: LifecycleCoroutineScope? = null
    var lifecycle: Lifecycle? = null
    var mapSnapshotter: MapSnapshotter? = null

    // longScreenshot need allowImageHardware = false,because it need draw image to bitmap
    var longScreenshotLayout = false

    override fun buildModels(viewState: ViewState<CoverImageData?>) {
        if (viewState.isLoading()) {
            workoutDetailsCoverImageLoading {
                id("coverImagesLoading")
            }
        } else {
            val data = viewState.data ?: return
            data.coverImages.forEachIndexed { index, coverImage ->
                val id = when (coverImage) {
                    is CoverImage.DefaultCoverImage -> "DefaultCoverImage"
                    is CoverImage.PhotoCoverImage -> "PhotoCoverImage_${coverImage.picture.id}"
                    is CoverImage.VideoCoverImage -> "VideoCoverImage_${coverImage.video.id}"
                    is CoverImage.RouteCoverImage -> "RouteCoverImage_${data.multisportPartActivity ?: ""}"
                }

                Timber.i("Adding a coverImage with id: ${id}_$index")
                // MapView can't take screenshot,so need imageView
                if (!longScreenshotLayout && coverImage is CoverImage.RouteCoverImage) {
                    workoutCoverMap {
                        id("${id}_$index")
                        coverImage(coverImage)
                        mapType(data.mapType)
                        onMapClickListener { model, _, _, _ ->
                            model.coverImage().onClickHandler?.invoke()
                        }
                        onBind { _, view, _ -> view.onBind(lifecycle) }
                    }
                } else {
                    workoutCoverImage {
                        id("${id}_$index")
                        mapSnapshotter(mapSnapshotter)
                        scope(lifecycleScope)
                        coverImage(coverImage)
                        showPlayButton(coverImage.showPlayButton)
                        mapType(data.mapType)
                        onClickListener { model, _, _, _ ->
                            model.coverImage().onClickHandler?.invoke()
                        }
                        allowHardware(!longScreenshotLayout)
                    }
                }
            }
        }
    }
}
