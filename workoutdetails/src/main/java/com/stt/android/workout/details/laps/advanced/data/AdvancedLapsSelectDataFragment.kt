package com.stt.android.workout.details.laps.advanced.data

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.RecyclerView
import com.stt.android.R
import com.stt.android.ThemeColors
import com.stt.android.common.ui.observeNotNull
import com.stt.android.common.viewstate.ViewStateListDialogFragment2
import com.stt.android.core.domain.LapsTableDataType
import com.stt.android.databinding.FragmentLapsSelectDataBinding
import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.ui.extensions.requireTheme
import com.stt.android.ui.utils.EpoxyConditionalDividerItemDecoration
import com.stt.android.ui.utils.WideScreenPaddingDecoration
import com.stt.android.workout.details.AdvancedLapsSelectDataSummaryItemBindingModel_
import com.stt.android.workout.details.laps.advanced.AdvancedLapsViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class AdvancedLapsSelectDataFragment :
    ViewStateListDialogFragment2<
        AdvancedLapsSelectDataContainer,
        AdvancedLapsSelectDataViewModel
        >() {
    private val advancedLapsViewModel: AdvancedLapsViewModel by activityViewModels()

    private val binding: FragmentLapsSelectDataBinding get() = requireBinding()

    private val dismissHandler = Handler(Looper.getMainLooper())

    override val viewModelClass = AdvancedLapsSelectDataViewModel::class.java

    override val layoutId = R.layout.fragment_laps_select_data

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.loadData()
        advancedLapsViewModel.loadData()
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return super.onCreateView(inflater, container, savedInstanceState).apply {
            binding.toolbar.setNavigationOnClickListener { dismiss() }
            addItemDecoration(binding.list)
        }
    }

    override fun getTheme(): Int {
        return R.style.FullscreenDialog_Laps
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.onDataTypeSelected.observeNotNull(viewLifecycleOwner) { selectedData ->
            advancedLapsViewModel.onSelectDataType(
                viewModel.stId,
                viewModel.lapsTableType,
                selectedData.columnIndex,
                selectedData.dataType,
                selectedData.previousDataType
            )
            // Delayed dismiss to give time for 1) ripple effect for the selected item 2) lap table to update itself
            context?.let {
                val animTime = it.resources.getInteger(android.R.integer.config_shortAnimTime).toLong()
                dismissHandler.postDelayed({ dismiss() }, animTime)
            }
        }
    }

    override fun onDestroyView() {
        dismissHandler.removeCallbacksAndMessages(null)
        super.onDestroyView()
    }

    private fun addItemDecoration(recyclerView: RecyclerView) {
        val resources = recyclerView.context.resources

        val dividerSize = resources.getDimensionPixelSize(R.dimen.size_divider)
        val dividerColor = ThemeColors.resolveColor(recyclerView.context, R.attr.suuntoDividerColor)
        recyclerView.addItemDecoration(
            EpoxyConditionalDividerItemDecoration(dividerColor) { item, nextItem ->
                if (item is AdvancedLapsSelectDataSummaryItemBindingModel_ &&
                    nextItem is AdvancedLapsSelectDataSummaryItemBindingModel_
                ) {
                    dividerSize
                } else {
                    null
                }
            }
        )
        recyclerView.addItemDecoration(WideScreenPaddingDecoration(resources, requireTheme()))
    }

    companion object {
        const val FRAGMENT_TAG = "AdvancedLapsSelectDataFragment"

        @JvmStatic
        fun newInstance(
            stId: Int,
            selected: LapsTableDataType,
            available: List<LapsTableDataType>,
            lapTableType: LapsTableType,
            columnIndex: Int
        ) =
            AdvancedLapsSelectDataFragment().apply {
                arguments = Bundle().apply {
                    putInt(AdvancedLapsSelectDataViewModel.KEY_ST_ID, stId)
                    putParcelable(AdvancedLapsSelectDataViewModel.KEY_DATA_TYPE, selected)
                    putParcelableArrayList(AdvancedLapsSelectDataViewModel.KEY_AVAILABLE_DATA_TYPES, ArrayList(available))
                    putSerializable(AdvancedLapsSelectDataViewModel.KEY_TABLE_TYPE, lapTableType)
                    putInt(AdvancedLapsSelectDataViewModel.KEY_COLUMN_INDEX, columnIndex)
                }
            }
    }
}
