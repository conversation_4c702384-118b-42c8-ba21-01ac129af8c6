package com.stt.android.workout.details.laps.advanced.data

import androidx.annotation.VisibleForTesting
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.viewstate.LoadingStateViewModel
import com.stt.android.core.domain.AdvancedLapsSelectDataCategoryItem
import com.stt.android.core.domain.LapsTableDataType
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.infomodel.SummaryCategory
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.getSummaryCategoriesByStId
import com.stt.android.ui.utils.SingleLiveEvent
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class AdvancedLapsSelectDataViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
) : LoadingStateViewModel<AdvancedLapsSelectDataContainer>(ioThread, mainThread, coroutinesDispatchers) {
    val stId: Int = savedStateHandle.get(KEY_ST_ID)
        ?: throw RuntimeException("Missing stId")
    private val selected: LapsTableDataType = savedStateHandle.get(KEY_DATA_TYPE)
        ?: throw RuntimeException("Missing selected data type")
    private val availableItems: List<LapsTableDataType> = savedStateHandle
        .get<List<LapsTableDataType>>(KEY_AVAILABLE_DATA_TYPES)
        ?: throw RuntimeException("Missing available data types")
    private val columnIndex: Int = savedStateHandle.get(KEY_COLUMN_INDEX)
        ?: throw RuntimeException("Missing column index")
    val lapsTableType: LapsTableType = savedStateHandle.get(KEY_TABLE_TYPE)
        ?: throw RuntimeException("Missing lap table type")

    @VisibleForTesting
    val onSelectSummaryItem = { columnIndex: Int, dataType: LapsTableDataType ->
        onDataTypeSelected.postValue(SelectedSummaryItemData(columnIndex, dataType, selected))
    }

    val onDataTypeSelected = SingleLiveEvent<SelectedSummaryItemData>()

    override fun retryLoading() {
        // This method is left blank in this example. It is called by the host Fragment when we want
        // to recover from an error. If you're using RxJava2, a good practice would be to have an RxRelay,
        // which the loading stream will be observing and when this method is called, you simply call the relay.
        // With coroutines this is even easier, you just call the method that does the loading.
    }

    fun loadData() {
        notifyLoading()

        viewModelScope.launch(coroutinesDispatchers.computation) {
            runSuspendCatching {
                val items = createItems(getSummaryCategoriesByStId(stId, lapsTableType))
                val container = AdvancedLapsSelectDataContainer(items)
                notifyDataLoaded(container)
            }.onFailure { e ->
                notifyError(e)
                Timber.w(e)
            }
        }
    }

    private fun createItems(
        map: Map<SummaryCategory, List<SummaryItem>>
    ): Map<AdvancedLapsSelectDataCategoryItem, List<AdvancedLapsSelectDataType>> {
        val items =
            mutableMapOf<AdvancedLapsSelectDataCategoryItem, List<AdvancedLapsSelectDataType>>()
        map.keys
            .forEach { category: SummaryCategory ->
                // Only summary items with data are needed. Intersect the result with available items.
                val summaryItemTypesInMap = map[category]?.map { LapsTableDataType.Summary(it) }
                val intersection = summaryItemTypesInMap?.intersect(availableItems.toSet()) ?: emptySet()

                if (intersection.isNotEmpty()) {
                    val categoryItem = AdvancedLapsSelectDataCategoryItem.Summary(category)
                    items[categoryItem] = createDataTypeItems(intersection)
                }
            }
        // add suuntoplus category if needed
        val suuntoPlusItems = availableItems.filterIsInstance<LapsTableDataType.SuuntoPlus>()
        if (suuntoPlusItems.isNotEmpty()) {
            items[AdvancedLapsSelectDataCategoryItem.SuuntoPlus] = createDataTypeItems(suuntoPlusItems)
        }
        return items
    }

    private fun createDataTypeItems(dataTypes: Iterable<LapsTableDataType>): List<AdvancedLapsSelectDataType> {
        return dataTypes
            .map { summaryItem ->
                AdvancedLapsSelectDataType(
                    summaryItem,
                    summaryItem == selected,
                    columnIndex,
                    lapsTableType,
                    onSelectSummaryItem
                )
            }
    }

    companion object {
        const val KEY_ST_ID = "com.stt.android.laps.advanced.data.ST_ID"
        const val KEY_TABLE_TYPE = "com.stt.android.laps.advanced.data.TABLE_TYPE"
        const val KEY_COLUMN_INDEX = "com.stt.android.laps.advanced.data.COLUMN_INDEX"
        const val KEY_DATA_TYPE = "com.stt.android.laps.advanced.data.DATA_TYPE"
        const val KEY_AVAILABLE_DATA_TYPES =
            "com.stt.android.laps.advanced.data.AVAILABLE_DATA_TYPES"
    }
}
