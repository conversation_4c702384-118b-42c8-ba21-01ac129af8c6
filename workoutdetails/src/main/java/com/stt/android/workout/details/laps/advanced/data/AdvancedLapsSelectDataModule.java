package com.stt.android.workout.details.laps.advanced.data;

import com.stt.android.common.viewstate.ViewStateEpoxyController;
import dagger.Binds;
import dagger.Module;
import dagger.hilt.InstallIn;
import dagger.hilt.android.components.FragmentComponent;

@Module
@InstallIn(FragmentComponent.class)
public abstract class AdvancedLapsSelectDataModule {
    @Binds
    public abstract ViewStateEpoxyController<AdvancedLapsSelectDataContainer>
    bindAdvancedLapsSelectDataController(
        AdvancedLapsSelectDataController advancedLapsSelectDataController
    );
}
