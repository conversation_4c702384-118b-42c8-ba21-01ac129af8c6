package com.stt.android.workout.details.graphanalysis.highlight

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.annotation.StringRes
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import com.stt.android.workout.details.R
import com.stt.android.workout.details.databinding.GraphAnalysisHighlightInfoBinding
import com.stt.android.core.R as CR
import com.stt.android.R as BR

class GraphAnalysisHighlightInfoView(
    context: Context,
    attrs: AttributeSet?,
) : ConstraintLayout(context, attrs) {
    private val binding: GraphAnalysisHighlightInfoBinding
    private var defaultColor: Int = ContextCompat.getColor(context, CR.color.near_black)

    init {
        val attrsGraphColor = if (attrs != null) {
            val typedArray = context.resources.obtainAttributes(
                attrs,
                R.styleable.GraphAnalysisHighlightInfoViewAttrs
            )

            val graphColorAttr = typedArray.getColor(
                R.styleable.GraphAnalysisHighlightInfoViewAttrs_graphColor,
                defaultColor
            )
            typedArray.recycle()
            graphColorAttr
        } else {
            defaultColor
        }
        defaultColor = attrsGraphColor
        val inflater = LayoutInflater.from(context)
        binding = GraphAnalysisHighlightInfoBinding.inflate(inflater, this)

        val paddingHorizontal = resources.getDimensionPixelSize(BR.dimen.size_spacing_medium)
        val paddingVertical = resources.getDimensionPixelSize(BR.dimen.size_spacing_smaller)
        setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, paddingVertical)

        setCurrentValueTextColor(defaultColor)
        setCurrentValueText(null)
        setGraphNameString(null)
        setGraphUnitStringRes(0)
    }

    /**
     * @param text - text to be shown, if null defaults to "-"
     */
    fun setCurrentValueText(text: String?) {
        binding.currentValue.text = text ?: "-"
    }

    fun setCurrentValueTextColor(color: Int) {
        binding.currentValue.setTextColor(color)
    }

    fun setDefaultColor() {
        setCurrentValueTextColor(defaultColor)
    }

    /**
     * @param string - Graph name to be shown. If blank or null, defaults to showing "-"
     */
    fun setGraphNameString(string: String?) {
        binding.graphName.text = if (string.isNullOrBlank()) {
            "-"
        } else {
            string
        }
    }

    /**
     * @param stringRes - String resource ID, if 0 then [defaultTextForZeroStringRes] is used
     * @param defaultTextForZeroStringRes - Text to use if [stringRes] is 0
     */
    fun setGraphUnitStringRes(
        @StringRes stringRes: Int,
        defaultTextForZeroStringRes: String = "-",
    ) {
        binding.graphUnit.text = if (stringRes != 0) {
            context.getString(stringRes)
        } else {
            defaultTextForZeroStringRes
        }
    }
}
