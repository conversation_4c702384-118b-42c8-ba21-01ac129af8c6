package com.stt.android.workout.details.reactions

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.ui.setupActionBarWithNavController
import com.google.android.material.snackbar.Snackbar
import com.stt.android.common.ui.observeNotNull
import com.stt.android.common.viewstate.ViewState
import com.stt.android.social.userprofile.UserProfileNavigator
import com.stt.android.ui.extensions.requireTheme
import com.stt.android.ui.utils.WideScreenPaddingDecoration
import com.stt.android.workout.details.R
import com.stt.android.workout.details.databinding.ReactionUserListFragmentBinding
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class ReactionUserListFragment : Fragment() {
    @Inject
    internal lateinit var controller: ReactionUserListController

    @Inject
    internal lateinit var userProfileNavigator: UserProfileNavigator

    private val viewModel: ReactionUserListViewModel by viewModels()

    private val navController by lazy { findNavController() }

    private val args: ReactionUserListFragmentArgs by navArgs()

    private var _binding: ReactionUserListFragmentBinding? = null
    private val binding get() = _binding!!

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.loadData(args.workoutKey)
        viewModel.navigationEvent.observeNotNull(this) { username ->
            userProfileNavigator.openUserProfile(requireContext(), username)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = DataBindingUtil.inflate(inflater, R.layout.reaction_user_list_fragment, container, false)

        (requireActivity() as AppCompatActivity).apply {
            setSupportActionBar(binding.toolbar)
            setupActionBarWithNavController(navController)
            supportActionBar?.setDisplayHomeAsUpEnabled(true)
            supportActionBar?.setDisplayShowTitleEnabled(true)
        }

        binding.userList.adapter = controller.adapter

        binding.userList.addItemDecoration(WideScreenPaddingDecoration(resources, requireTheme()))

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        viewModel.viewState.observeNotNull(viewLifecycleOwner) { state ->
            if (state is ViewState.Error) {
                Snackbar.make(binding.root, state.errorEvent.errorStringRes, Snackbar.LENGTH_LONG).show()
            }
            controller.setData(state)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding.unbind()
        _binding = null
    }
}
