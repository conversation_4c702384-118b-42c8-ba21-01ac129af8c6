package com.stt.android.workout.details.intensity.composables

import android.widget.FrameLayout
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import com.stt.android.core.domain.GraphType
import com.stt.android.infomodel.SummaryGraph
import com.stt.android.workout.details.ZoneAnalysisData
import com.stt.android.workout.details.charts.IntensityZoneLineChart

@Composable
fun ZoneAnalysisChart(
    zoneAnalysisData: ZoneAnalysisData,
    modifier: Modifier = Modifier,
) {
    AndroidView(factory = { context ->
        // Wrapping the Chart with FrameLayout is needed to draw markers on top of the chart
        FrameLayout(context).apply {
            addView(
                IntensityZoneLineChart(context).apply {
                    this.setupChart()
                }
            )
        }
    }, modifier = modifier, update = { relativeLayout ->
        val intensityZoneLineChart = relativeLayout.getChildAt(0) as IntensityZoneLine<PERSON>hart
        val mainGraphHasZoneColouring = zoneAnalysisData.mainGraphZoneLimits != null
        val axisLeftLabelCount =
            getAxisLabelCount(zoneAnalysisData.mainGraphData?.lineChartData?.graphType)
        val axisRightLabelCount =
            getAxisLabelCount(zoneAnalysisData.secondaryGraphData?.lineChartData?.graphType)
        intensityZoneLineChart.setWorkoutLineChartData(
            zoneAnalysisData.mainGraphData?.lineChartData,
            zoneAnalysisData.secondaryGraphData?.lineChartData,
            mainGraphHasZoneColouring,
            axisLeftLabelCount = axisLeftLabelCount,
            axisRightLabelCount = axisRightLabelCount
        )

        // Add zoned colouring for the main graph
        intensityZoneLineChart.resetColouring()
        zoneAnalysisData.mainGraphZoneLimits?.let {
            val mainGraphIsInverted =
                zoneAnalysisData.mainGraphData?.lineChartData?.isInverted == true
            intensityZoneLineChart.setZoneLimits(it.zoneLimits)
            intensityZoneLineChart.setupLefYAxisRenderer(it, mainGraphIsInverted)
        }

        intensityZoneLineChart.invalidate()
    })
}

private fun getAxisLabelCount(graphType: GraphType?): Int {
    return if (graphType == GraphType.Summary(SummaryGraph.AEROBICZONE)) {
        7
    } else {
        5
    }
}
