package com.stt.android.workout.details.multisport

import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.domain.sml.MultisportPartActivity
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject

interface MultisportPartActivityLoader {
    val multisportPartActivityFlow: StateFlow<ViewState<MultisportPartActivity?>>
    fun setMultisportPartActivity(multisportPartActivity: MultisportPartActivity?): StateFlow<ViewState<MultisportPartActivity?>>
}

@ActivityRetainedScoped
class DefaultMultisportPartActivityLoader
@Inject constructor() : MultisportPartActivityLoader {
    override val multisportPartActivityFlow: MutableStateFlow<ViewState<MultisportPartActivity?>> =
        MutableStateFlow(loading())

    override fun setMultisportPartActivity(multisportPartActivity: MultisportPartActivity?): StateFlow<ViewState<MultisportPartActivity?>> {
        return multisportPartActivityFlow.apply {
            value = loaded(multisportPartActivity)
        }
    }
}
