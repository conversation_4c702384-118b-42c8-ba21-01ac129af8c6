package com.stt.android.workout.details.charts

import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.util.AttributeSet
import androidx.annotation.ColorInt
import androidx.core.content.ContextCompat
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.ValueFormatter
import com.github.mikephil.charting.utils.EntryXComparator
import com.github.mikephil.charting.utils.Utils
import com.stt.android.ThemeColors
import com.stt.android.infomodel.SummaryItem
import com.stt.android.intensityzone.IntensityZoneLimits
import com.stt.android.intensityzone.ZoneRange
import com.stt.android.intensityzone.ZoneRangeWithColor
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.components.charts.ZoneLineChartRenderer
import com.stt.android.utils.FontUtils
import com.stt.android.workout.details.R
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import kotlin.math.max
import kotlin.math.min
import com.stt.android.R as BaseR

@AndroidEntryPoint
class IntensityZoneLineChart @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : LineChart(context, attrs, defStyle) {
    @Inject
    lateinit var infoModelFormatter: InfoModelFormatter

    @ColorInt
    private val mainChartColor: Int =
        context.getColor(R.color.zoned_analysis_main_color)

    @ColorInt
    private var secondaryChartColor: Int = context.getColor(R.color.zoned_analysis_comparison_color)

    @ColorInt
    private var labelColor: Int = ThemeColors.primaryTextColor(context)

    @ColorInt
    val chartColorGrey = context.getColor(BaseR.color.medium_grey)

    private val chartTypeface: Typeface = FontUtils.getChartLabelsTypeface(context)

    fun setupChart() {
        renderer = ZoneLineChartRenderer(this, mAnimator, mViewPortHandler)
        rendererLeftYAxis = ZoneAnalysisYAxisRenderer(
            viewPortHandler = mViewPortHandler,
            yAxis = mAxisLeft,
            transformer = mLeftAxisTransformer,
        )
        setXAxisRenderer(ZoneAnalysisXAxisRenderer(
            mViewPortHandler,
            mXAxis,
            mLeftAxisTransformer
        ))

        @ColorInt val gridColor = ThemeColors.resolveColor(context, BaseR.attr.analysisGridColor)

        description.isEnabled = false
        setScaleEnabled(false)
        legend.isEnabled = false

        with(xAxis) {
            position = XAxis.XAxisPosition.BOTTOM
            setDrawGridLines(false)
            setDrawLabels(true)
            setDrawAxisLine(true)
            textColor = chartColorGrey
            typeface = chartTypeface
            textSize = 12f
            axisLineWidth = 1.5f
            axisLineColor = gridColor
        }

        with(axisLeft) {
            setDrawAxisLine(false)
            setDrawZeroLine(false)
            setDrawGridLines(true)
            gridLineWidth = 1f
            setDrawLabels(true)
            setDrawTopYLabelEntry(true)
            setLabelCount(5, true)
            textColor = labelColor
            textSize = 12f
            typeface = chartTypeface
            this.gridColor = gridColor
        }

        with(axisRight) {
            setDrawAxisLine(false)
            setDrawZeroLine(false)
            setDrawLabels(true)
            setDrawTopYLabelEntry(true)
            setLabelCount(5, true)
            textColor = labelColor
            textSize = 12f
            typeface = chartTypeface
            this.gridColor = gridColor
        }
    }

    fun setZoneLimits(zoneRanges: List<ZoneRange>) {
        if (zoneRanges.isEmpty()) return

        val renderer = mRenderer
        if (renderer is ZoneLineChartRenderer) {
            // TODO: Improve this, duplication in order to make sure we matches the limits with the ranges
            val zoneRanges2: List<ZoneRange> = zoneRanges.map { it } + zoneRanges.last()
            val zoneLimits: List<Float> = zoneRanges.map { it.start } + zoneRanges.last().end
            // Drop the zone max limit which is unnecessary to color the zones.
            // Reverse the limits as drawing starts from the top of the chart
            val adjustedZoneRange = zoneRanges2.reversed().drop(1)
            val adjustedZoneLimits = zoneLimits.reversed().drop(1)

            val colors =
                adjustedZoneRange.map { ContextCompat.getColor(context, it.intensityZone.color) }
            val zones = adjustedZoneLimits.mapIndexed { index, _ ->
                val color = if (index <= colors.lastIndex) colors[index] else colors.last()
                val limit = adjustedZoneLimits[index]
                ZoneLineChartRenderer.Zone(limit, color)
            }
            renderer.setZones(zones)
        }
    }

    fun setupLefYAxisRenderer(intensityZoneLimits: IntensityZoneLimits, isInverted: Boolean) {
        val leftRenderer = rendererLeftYAxis
        if (leftRenderer is ZoneAnalysisYAxisRenderer) {
            val zoneRangeWithColors = intensityZoneLimits.zoneLimits.map {
                ZoneRangeWithColor(
                    zoneRange = it,
                    color = it.intensityZone.getColor(context)
                )
            }
            leftRenderer.setZones(isInverted, zoneRangeWithColors)
            axisLeft.xOffset = Utils.convertDpToPixel(4f) // Additional space for vertical zone colors bar
        }
    }

    private fun resetLeftRenderer() {
        val leftRenderer = rendererLeftYAxis
        if (leftRenderer is ZoneAnalysisYAxisRenderer) {
            leftRenderer.reset()
            axisLeft.xOffset = 5f // This is the default value
        }
    }

    private fun resetZoneLimits() {
        val renderer = mRenderer
        if (renderer is ZoneLineChartRenderer) {
            renderer.resetZoneLimits()
        }
    }

    fun resetColouring() {
        resetZoneLimits()
        resetLeftRenderer()
    }

    private fun prepareXAxis(
        main: WorkoutLineChartData?,
        secondary: WorkoutLineChartData?,
    ) {
        val maxEntryCount = maxOf(
            main?.data?.maxOf { it.entries.size } ?: 0,
            secondary?.data?.maxOf { it.entries.size } ?: 0,
        )

        with(xAxis) {
            if (maxEntryCount > 0 && !isScaleXEnabled) {
                setLabelCount(min(maxEntryCount, 5), true)
            }
            this.valueFormatter = xAxisDurationFormatter
        }
    }

    fun setWorkoutLineChartData(
        mainChartData: WorkoutLineChartData?,
        secondaryChartData: WorkoutLineChartData?,
        mainGraphHasZoneColouring: Boolean,
        axisLeftLabelCount: Int = 5,
        axisRightLabelCount: Int = 5
    ) {
        val mainDataSets = mainChartData?.data?.map {
            createLineDataSet(
                it.entries,
                color = it.lineColor ?: mainChartColor,
                label = it.name,
                yAxisDependency = YAxis.AxisDependency.LEFT,
                zoneColouringEnabled = mainGraphHasZoneColouring
            )
        } ?: emptyList()

        val secondaryDataSets = secondaryChartData?.data?.map {
            createLineDataSet(
                it.entries,
                color = secondaryChartColor,
                label = it.name,
                yAxisDependency = YAxis.AxisDependency.RIGHT
            )
        } ?: emptyList()

        prepareXAxis(mainChartData, secondaryChartData)
        prepareYAxis(axisLeft, mainChartData, LineData(mainDataSets), axisLeftLabelCount)
        prepareYAxis(axisRight, secondaryChartData, LineData(secondaryDataSets), axisRightLabelCount)
        data = LineData(secondaryDataSets + mainDataSets)

        notifyDataSetChanged()
        invalidate()
    }

    private fun prepareYAxis(
        axis: YAxis,
        workoutLineChartData: WorkoutLineChartData?,
        lineData: LineData,
        axisLeftLabelCount: Int = 5
    ) {
        with(axis) {
            if (workoutLineChartData != null) {
                setLabelCount(axisLeftLabelCount, true)
                isEnabled = true
                isInverted = workoutLineChartData.isInverted
                this.valueFormatter = object : ValueFormatter() {
                    override fun getFormattedValue(value: Float) =
                        workoutLineChartData.formatter.formatConvertedYValue(value)
                }

                if (workoutLineChartData.minValueStrict != null) {
                    axisMinimum = workoutLineChartData.minValueStrict
                } else {
                    resetAxisMinimum()
                    if (!workoutLineChartData.isInverted || workoutLineChartData.minAllowedValue != null) {
                        calculate(lineData.yMin, lineData.yMax)
                        val computedMinRoundedTo10Below = if (axisMinimum > 0.0) {
                            axisMinimum - (axisMinimum % 10f)
                        } else {
                            axisMinimum - (10.0f + axisMinimum % 10f)
                        }
                        axisMinimum = if (workoutLineChartData.minAllowedValue != null) {
                            max(computedMinRoundedTo10Below, workoutLineChartData.minAllowedValue)
                        } else {
                            computedMinRoundedTo10Below
                        }
                    }
                }
                axisMaximum = if (workoutLineChartData.maxValueStrict != null) {
                    workoutLineChartData.maxValueStrict
                } else if (workoutLineChartData.minRange == null) {
                    if (workoutLineChartData.isInverted) {
                        lineData.yMax
                    } else {
                        (10 * ((lineData.yMax / 10.0f).toInt() + 1)).toFloat()
                    }
                } else {
                    getMaxFromMinRange(
                        if (workoutLineChartData.isInverted) workoutLineChartData.minValueStrict else axisMinimum,
                        lineData,
                        workoutLineChartData.isInverted,
                        workoutLineChartData.maxValueStrict,
                        workoutLineChartData.minRange
                    )
                }
            } else {
                isEnabled = false
            }
        }
    }

    private fun createLineDataSet(
        entries: List<Entry?>,
        color: Int,
        label: String = "",
        yAxisDependency: YAxis.AxisDependency,
        zoneColouringEnabled: Boolean = false
    ): LineDataSet {
        val sortedEntries = entries.sortedWith(EntryXComparator())
        return LineDataSet(sortedEntries, label).apply {
            this.color = color
            setDrawValues(false)
            setDrawCircles(false)
            mode = LineDataSet.Mode.LINEAR
            setDrawHighlightIndicators(false)
            highLightColor = Color.BLACK
            highlightLineWidth = 1.0f
            setDrawHorizontalHighlightIndicator(false)
            axisDependency = yAxisDependency
            lineWidth = if (zoneColouringEnabled) 1.5f else 1.0f
        }
    }

    private val xAxisDurationFormatter = object : ValueFormatter() {
        override fun getFormattedValue(value: Float): String = when {
            value == 0.0f -> "0"
            value.isSafeValue -> infoModelFormatter.formatValue(SummaryItem.DURATION, value).value.orEmpty()
            else -> ""
        }
    }
}
