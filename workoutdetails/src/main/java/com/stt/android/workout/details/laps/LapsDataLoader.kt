package com.stt.android.workout.details.laps

import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.WorkoutData
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.laps.AutomaticLaps
import com.stt.android.laps.ManualLaps
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ski.CompleteSkiRun
import com.stt.android.ski.SlopeSkiCalculator
import com.stt.android.utils.traceSuspend
import com.stt.android.workout.details.LapsData
import com.stt.android.workout.details.advancedlaps.AdvancedLapsDataLoader
import com.stt.android.workout.details.workoutdata.WorkoutDataLoader
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.Dispatchers.Default
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject
import com.stt.android.R as BaseR

interface LapsDataLoader {
    val lapsDataStateFlow: StateFlow<ViewState<LapsData?>>
    suspend fun loadLapsData(workoutHeader: WorkoutHeader): StateFlow<ViewState<LapsData?>>
}

@ActivityRetainedScoped
class DefaultLapsDataLoader
@Inject constructor(
    private val advancedLapsDataLoader: AdvancedLapsDataLoader,
    private val workoutDataLoader: WorkoutDataLoader,
    private val infoModelFormatter: InfoModelFormatter,
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope
) : LapsDataLoader {

    override val lapsDataStateFlow: MutableStateFlow<ViewState<LapsData?>> =
        MutableStateFlow(loading())

    override suspend fun loadLapsData(workoutHeader: WorkoutHeader): StateFlow<ViewState<LapsData?>> {
        val activityType = workoutHeader.activityType
        val isSlopeSki = activityType.isSlopeSki
        val isDiving = activityType.isDiving
        val unit = infoModelFormatter.unit

        if (isDiving) {
            // Laps data not needed for dives
            lapsDataStateFlow.value = loaded()
        } else {
            activityRetainedCoroutineScope.launch {
                advancedLapsDataLoader.lapsDataStateFlow.collect {
                    when (it) {
                        is ViewState.Loading -> {
                        } // do nothing
                        is ViewState.Error,
                        is ViewState.Loaded -> {
                            if (it.data != null) {
                                lapsDataStateFlow.value = loaded()
                            } else {
                                traceSuspend("loadOldLapsData") {
                                    loadOldLapsData(isSlopeSki, unit)
                                }
                            }
                        }
                    }
                }
            }
        }
        return lapsDataStateFlow
    }

    private suspend fun loadOldLapsData(
        isSlopeSki: Boolean,
        unit: MeasurementUnit
    ): Unit = withContext(Default) {
        workoutDataLoader.workoutDataStateFlow.collect { viewState ->
            when (viewState) {
                is ViewState.Error -> lapsDataStateFlow.value = loaded()
                is ViewState.Loading -> lapsDataStateFlow.value = loading()
                is ViewState.Loaded -> {
                    val workoutData = viewState.data

                    if (workoutData != null) {
                        lapsDataStateFlow.value = loaded(
                            if (isSlopeSki) {
                                getSkiLaps(workoutData, unit)
                            } else {
                                getLaps(workoutData, unit)
                            }
                        )
                    } else {
                        lapsDataStateFlow.value = loaded()
                    }
                }
            }
        }
    }

    private fun getLaps(workoutData: WorkoutData, unit: MeasurementUnit): LapsData {
        return LapsData(
            workoutData.manualLaps?.run { ManualLaps(this) },
            // automatic laps calculated by distance and calculated on fly
            AutomaticLaps.createFromStaticWorkoutData(unit, workoutData)
        )
    }

    private fun getSkiLaps(
        workoutData: WorkoutData,
        unit: MeasurementUnit
    ): LapsData {
        val route = workoutData.routePoints
        val slopeSkiCalculator = SlopeSkiCalculator()
        for (geoPoint in route) {
            slopeSkiCalculator.addObservation(
                geoPoint.millisecondsInWorkout,
                geoPoint.totalDistance,
                geoPoint.altitude,
                geoPoint.speedMetersPerSecond.toDouble()
            )
        }
        val completeLaps = slopeSkiCalculator.calculateRuns().runs
            .map {
                CompleteSkiRun.create(it, ALTITUDE_COLOR_RES, unit, workoutData.heartRateEvents)
            }
        return LapsData(ManualLaps(completeLaps), null)
    }

    companion object {
        private val ALTITUDE_COLOR_RES = BaseR.color.graphlib_altitude
    }
}
