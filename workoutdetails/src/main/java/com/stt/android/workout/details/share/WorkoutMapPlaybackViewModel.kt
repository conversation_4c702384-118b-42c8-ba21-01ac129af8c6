package com.stt.android.workout.details.share

import android.content.Context
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.android.gms.maps.model.CameraPosition
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.mapbox.geojson.Point
import com.mapbox.turf.TurfConstants
import com.mapbox.turf.TurfMeasurement
import com.soy.algorithms.camerapath.entities.CameraPath
import com.soy.algorithms.camerapath.entities.LonLatAltTimestamp
import com.stt.android.analytics.AnalyticsPropertyValue.WorkoutPlaybackInitiatedFrom
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.SummaryExtensionDataModel
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.sml.SmlTimedExtensionStreamPoint
import com.stt.android.domain.sml.dataPointsWithoutPauses
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workouts.GetWorkoutByIdUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.camerapath.CreateCameraPathUseCase
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.extensions.loadExtension
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.mapping.getActivitySummaryForActivityId
import com.stt.android.maps.colortrack.ColorTrackDescriptor
import com.stt.android.maps.google.toSuunto
import com.stt.android.maps.mapbox.domain.TerrainExaggerationUseCase
import com.stt.android.utils.STTConstants.WorkoutAnalysis.MIN_PLAYBACK_DISTANCE_METERS
import com.stt.android.workout.details.graphanalysis.playback.PlaybackState
import com.stt.android.workout.details.graphanalysis.playback.PlaybackStateModel
import com.stt.android.workout.details.graphanalysis.playback.Workout3DPlaybackCameraConfig
import com.stt.android.workout.details.graphanalysis.playback.WorkoutPlaybackCameraConfig
import com.stt.android.workout.details.graphanalysis.playback.WorkoutPlaybackGeopointLoader
import com.stt.android.workout.details.graphanalysis.playback.WorkoutPlaybackPauseReason
import com.stt.android.workout.details.multisport.MultisportPartActivityLoader
import com.stt.android.workout.details.share.colortrack.AltitudeColorTrackGenerator
import com.stt.android.workout.details.share.colortrack.HeartRateColorTrackGenerator
import com.stt.android.workout.details.share.colortrack.PaceColorTrackerGenerator
import com.stt.android.workout.details.share.util.VideoFileUtil
import com.stt.android.workout.details.share.video.VideoShareInfoData
import com.stt.android.workout.details.share.video.VideoShareInfoDataLoader
import com.stt.android.workout.details.share.video.VideoShareInfoRowAnimation
import com.stt.android.workout.details.share.video.WorkoutMapPlaybackGraphType
import com.stt.android.workout.details.share.video.WorkoutMapPlaybackMapType
import com.stt.android.workout.details.share.video.WorkoutMapPlaybackOptions
import com.stt.android.workout.details.share.video.workoutMapPlaybackPreferences
import com.stt.android.workout.details.sml.SmlDataLoader
import com.stt.android.workout.details.workoutdata.WorkoutDataLoader
import com.stt.android.workout.details.workoutheader.WorkoutHeaderLoader
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.dropWhile
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import sanitisePaceStreamPoints
import java.io.File
import javax.inject.Inject
import kotlin.math.roundToLong

data class WorkoutData(
    val workoutHeader: WorkoutHeader,
    val geoPoints: List<WorkoutGeoPoint>,
    val sml: Sml?,
    val speedPoints: List<SmlTimedExtensionStreamPoint>,
)

data class ValueDescriptor(
    val value: String,
    val geoPoint: WorkoutGeoPoint,
    val isMaximum: Boolean,
)

enum class MapPlaybackError {
    NOT_SUPPORTED,
    NO_CAMERA_PATH,
    RECORDING_ERROR,
}

@HiltViewModel
class WorkoutMapPlaybackViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    savedStateHandle: SavedStateHandle,
    private val dispatchers: CoroutinesDispatchers,
    private val getWorkoutByIdUseCase: GetWorkoutByIdUseCase,
    private val workoutHeaderLoader: WorkoutHeaderLoader,
    smlDataLoader: SmlDataLoader,
    multisportPartActivityLoader: MultisportPartActivityLoader,
    private val workoutPlaybackGeopointLoader: WorkoutPlaybackGeopointLoader,
    private val terrainExaggerationUseCase: TerrainExaggerationUseCase,
    private val playbackStateModel: PlaybackStateModel,
    private val createCameraPathUseCase: CreateCameraPathUseCase,
    private val workoutDataLoader: WorkoutDataLoader,
    private val videoShareInfoDataLoader: VideoShareInfoDataLoader,
    private val infoModelFormatter: InfoModelFormatter,
    private val summaryExtensionDataModel: SummaryExtensionDataModel,
) : ViewModel() {

    private val workoutId: Int = savedStateHandle[EXTRA_WORKOUT_ID]!!

    @Suppress("NullableBooleanElvis")
    val sharePopup: Boolean = savedStateHandle[EXTRA_SHARE_POPUP] ?: false

    private val preferences = context.workoutMapPlaybackPreferences

    private val graphType = WorkoutMapPlaybackGraphType.from(preferences)

    val mapType = WorkoutMapPlaybackMapType.from(preferences)

    val dataOptions = WorkoutMapPlaybackOptions.from(preferences)

    private val _thirdSummaryItem = MutableStateFlow<SummaryItem>(SummaryItem.AVGPACE)
    val thirdSummaryItem: SummaryItem
        get() = _thirdSummaryItem.value

    private val _cameraBounds = MutableStateFlow<LatLngBounds?>(null)
    val cameraBounds = _cameraBounds.asStateFlow()

    private val _startEndPoints = MutableStateFlow<Pair<WorkoutGeoPoint, WorkoutGeoPoint>?>(null)
    val startEndPoints = _startEndPoints.asStateFlow()

    private val _colorTrack = MutableStateFlow<ColorTrackDescriptor?>(null)
    val colorTrack = _colorTrack.asStateFlow()

    private val _markers = MutableStateFlow<List<ValueDescriptor>>(emptyList())
    val markers = _markers.asStateFlow()

    private val _workoutDataToSummaryExtension =
        MutableStateFlow<Pair<WorkoutData, SummaryExtension?>?>(null)
    val workoutDataToSummaryExtension = _workoutDataToSummaryExtension.asStateFlow()

    private val _cameraConfig = MutableStateFlow<WorkoutPlaybackCameraConfig?>(null)
    val cameraConfig = _cameraConfig.asStateFlow()

    private val _chartProgress = MutableStateFlow<Pair<Float, Boolean>>(0f to true)
    val chartProgress = _chartProgress.asStateFlow()

    val infoData: StateFlow<VideoShareInfoData?>
        get() = videoShareInfoDataLoader.dataFlow

    private val _infoRowAnimation = MutableStateFlow(VideoShareInfoRowAnimation.SHOW)
    val infoRowAnimation = _infoRowAnimation.asStateFlow()

    private val _playbackState = MutableStateFlow<PlaybackState?>(null)
    val playbackState = _playbackState.asStateFlow()

    private val _autoPlayback = MutableStateFlow(false)
    val autoPlayback = _autoPlayback.asStateFlow()

    private val _playbackError = MutableStateFlow<MapPlaybackError?>(null)
    val playbackError = _playbackError.asStateFlow()

    private val _sharingFile = MutableStateFlow<File?>(null)
    val sharingFile = _sharingFile.asStateFlow()

    init {
        viewModelScope.launch {
            val workoutHeader = withContext(dispatchers.io) {
                getWorkoutByIdUseCase(workoutId).header
            }
            val summaryItem = withContext(dispatchers.io) {
                val items = getActivitySummaryForActivityId(workoutHeader.activityType.id).items
                if (items.contains(SummaryItem.AVGPACE)) {
                    SummaryItem.AVGPACE
                } else {
                    SummaryItem.AVGSPEED
                }
            }
            _thirdSummaryItem.tryEmit(summaryItem)
            workoutHeaderLoader.setWorkoutHeader(workoutHeader)
            smlDataLoader.loadSml(workoutHeader)
            workoutDataLoader.loadWorkoutData(workoutHeader)
            multisportPartActivityLoader.setMultisportPartActivity(null)
            videoShareInfoDataLoader.load(workoutHeader)
        }

        viewModelScope.launch {
            playbackStateModel.playbackStateFlow
                .dropWhile { it.resumed == false }
                .collect { state ->
                    _playbackState.tryEmit(state)
                }
        }

        combine(
            workoutHeaderLoader.workoutHeaderFlow.filter { it.isLoaded() },
            smlDataLoader.smlStateFlow.filter { it.isLoaded() },
            workoutPlaybackGeopointLoader.observeGeoPointForPlayback(),
        ) { headerState, smlState, geoPoints ->
            val header = headerState.data!!
            val sml = smlState.data

            val supportsPlayback = !header.activityType.isDiving &&
                geoPoints.size > 1 && geoPoints.last().totalDistance >= MIN_PLAYBACK_DISTANCE_METERS
            if (!supportsPlayback) {
                _playbackError.tryEmit(MapPlaybackError.NOT_SUPPORTED)
                return@combine
            }

            _startEndPoints.tryEmit(geoPoints.first() to geoPoints.last())

            val speedPoints = sml?.streamData?.let { streamData ->
                streamData
                    .dataPointsWithoutPauses(streamData.speed)
                    .sanitisePaceStreamPoints(header.activityType)
            } ?: emptyList()

            val summaryExtension = withContext(dispatchers.io) {
                summaryExtensionDataModel.loadExtension(header)
            }

            _workoutDataToSummaryExtension.tryEmit(
                WorkoutData(
                    header,
                    geoPoints,
                    sml,
                    speedPoints
                ) to summaryExtension
            )

            coroutineScope {
                val cameraBoundsTask = async { calcCameraBounds(geoPoints) }
                val colorTrackTask = async { loadColorTrack(geoPoints, sml) }
                val maxMinValuesTask = async { calcMaxMinValues(geoPoints, sml) }
                val cameraPathTask = async { loadCameraPath(geoPoints) }
                _cameraBounds.tryEmit(cameraBoundsTask.await())
                _colorTrack.tryEmit(colorTrackTask.await())
                val maxMinValues = maxMinValuesTask.await()
                val cameraPath = cameraPathTask.await()
                if (cameraPath != null) {
                    withContext(dispatchers.main) {
                        val playbackDuration = getPlaybackDuration(cameraPath.distance)
                        playbackStateModel.setPlaybackDuration(playbackDuration)
                        playbackStateModel.setTimeInWorkoutAndAnimationInterpolator(
                            MapGraphTimeInWorkoutAndAnimationInterpolator(cameraPath, geoPoints)
                        )
                        _autoPlayback.tryEmit(true)
                        playbackStateModel.playbackProgressFlow.collect { step ->
                            val phase = step.timeInAnimationFraction
                            val cameraConfig = get3DCameraConfig(cameraPath, geoPoints, phase)
                            _cameraConfig.tryEmit(cameraConfig)
                            val totalDistance = geoPoints.last().totalDistance
                            val ratio = when {
                                totalDistance > 0.0 -> cameraConfig.markerDistance / totalDistance
                                else -> 0.0
                            }.toFloat().coerceIn(0f, 1f)
                            val duration = playbackDuration * ratio
                            val durationLeft = playbackDuration * (1 - ratio)
                            _chartProgress.tryEmit(Pair(ratio, durationLeft > 500L))
                            val animation = when {
                                duration <= 1500L -> VideoShareInfoRowAnimation.FADE_OUT
                                durationLeft <= 0L -> VideoShareInfoRowAnimation.SHOW
                                durationLeft <= 1500L -> VideoShareInfoRowAnimation.FADE_IN
                                else -> VideoShareInfoRowAnimation.HIDE
                            }
                            _infoRowAnimation.tryEmit(animation)
                            _markers.tryEmit(maxMinValues.filter { it.geoPoint.totalDistance <= cameraConfig.markerDistance })
                        }
                    }
                } else {
                    _playbackError.tryEmit(MapPlaybackError.NO_CAMERA_PATH)
                }
            }
        }.flowOn(dispatchers.io).launchIn(viewModelScope)
    }

    private fun calcCameraBounds(geoPoints: List<WorkoutGeoPoint>): LatLngBounds? {
        if (geoPoints.isEmpty()) return null
        return LatLngBounds.Builder().apply {
            geoPoints.forEach { include(LatLng(it.latitude, it.longitude)) }
        }.build()
    }

    private suspend fun loadColorTrack(
        geoPoints: List<WorkoutGeoPoint>,
        sml: Sml?,
    ): ColorTrackDescriptor {
        val generator = when (graphType) {
            WorkoutMapPlaybackGraphType.HEART_RATE -> {
                val state = workoutDataLoader.workoutDataStateFlow.first { it.isLoaded() }
                HeartRateColorTrackGenerator(state.data?.heartRateEvents ?: emptyList())
            }

            WorkoutMapPlaybackGraphType.ALTITUDE -> AltitudeColorTrackGenerator()

            WorkoutMapPlaybackGraphType.PACE -> PaceColorTrackerGenerator()
        }
        return generator.generate(geoPoints, sml)
    }

    private suspend fun calcMaxMinValues(
        geoPoints: List<WorkoutGeoPoint>,
        sml: Sml?
    ): List<ValueDescriptor> {
        fun <T> findMaxMinValues(
            list: List<T>,
            value: T.() -> Double,
            isValid: T.() -> Boolean = { true },
        ): Pair<T, T> {
            var minValue = list[0]
            var maxValue = list[0]
            list.forEach {
                val value = it.value()
                when {
                    !it.isValid() -> Unit
                    value < minValue.value() -> minValue = it
                    value > maxValue.value() -> maxValue = it
                }
            }
            return minValue to maxValue
        }

        fun findGeoPoint(timestamp: Long): WorkoutGeoPoint? {
            geoPoints.indices.forEach { index ->
                if (index < geoPoints.size - 1) {
                    val current = geoPoints[index]
                    val next = geoPoints[index + 1]
                    if (current.timestamp <= timestamp && timestamp < next.timestamp) {
                        return current
                    }
                }
            }
            return null
        }

        when (graphType) {
            WorkoutMapPlaybackGraphType.HEART_RATE -> {
                val state = workoutDataLoader.workoutDataStateFlow.first { it.isLoaded() }
                val events = state.data?.heartRateEvents
                if (events.isNullOrEmpty()) return emptyList()

                val (minValue, maxValue) = findMaxMinValues(
                    events,
                    { heartRate.toDouble() },
                    { heartRate > 0 },
                )
                return buildList {
                    findGeoPoint(minValue.timestamp)?.let {
                        add(ValueDescriptor(minValue.heartRate.toString(), it, false))
                    }
                    findGeoPoint(maxValue.timestamp)?.let {
                        add(ValueDescriptor(maxValue.heartRate.toString(), it, true))
                    }
                }
            }

            WorkoutMapPlaybackGraphType.ALTITUDE -> {
                val (minValue, maxValue) = findMaxMinValues(geoPoints, { altitude })
                return buildList {
                    add(ValueDescriptor(minValue.altitude.toInt().toString(), minValue, false))
                    add(ValueDescriptor(maxValue.altitude.toInt().toString(), maxValue, true))
                }
            }

            WorkoutMapPlaybackGraphType.PACE -> {
                val speedPoints = sml?.streamData?.speed
                if (speedPoints.isNullOrEmpty()) return emptyList()

                val (_, rawMaxValue) = findMaxMinValues(speedPoints, { value.toDouble() })
                val maxValue = infoModelFormatter.formatValue(
                    SummaryItem.MAXPACE,
                    rawMaxValue.value,
                ).value
                if (!maxValue.isNullOrBlank()) {
                    val maxPoint = findGeoPoint(rawMaxValue.timestamp)
                    if (maxPoint != null) {
                        return listOf(ValueDescriptor(maxValue, maxPoint, true))
                    }
                }

                return emptyList()
            }
        }
    }

    private suspend fun loadCameraPath(geoPoints: List<WorkoutGeoPoint>): CameraPath? {
        val startPoint = geoPoints.first()
        val stopPoint = geoPoints.last()

        return runSuspendCatching {
            if (!terrainExaggerationUseCase.initialized) {
                terrainExaggerationUseCase.initialize()
            }
            val center = LatLng(
                (stopPoint.latitude + startPoint.latitude) / 2.0,
                (stopPoint.longitude + startPoint.longitude) / 2.0
            )
            val exaggeration = terrainExaggerationUseCase.getExaggerationFactor(
                CameraPosition.fromLatLngZoom(center, 12f).toSuunto()
            )
            createCameraPath(geoPoints, exaggeration)
        }.getOrNull()
    }

    private suspend fun createCameraPath(
        geoPoints: List<WorkoutGeoPoint>,
        exaggeration: Double,
    ): CameraPath? = createCameraPathUseCase(
        CreateCameraPathUseCase.Params(
            geoPoints.map {
                LonLatAltTimestamp(it.longitude, it.latitude, it.altitude, it.timestamp)
            },
            exaggeration
        )
    )

    fun startPlayback(restart: Boolean = false) {
        _autoPlayback.tryEmit(false)
        playbackStateModel.resumePlayback(WorkoutPlaybackInitiatedFrom.UNKNOWN, restart)
    }

    fun pausePlayback() {
        playbackStateModel.pausePlayback(WorkoutPlaybackPauseReason.ScreenExit)
    }

    fun onRecordError() {
        _playbackError.tryEmit(MapPlaybackError.RECORDING_ERROR)
    }

    fun getOutputFile(): File {
        return VideoFileUtil.getVideoOutputFile(context, workoutId, dataOptions, graphType, mapType)
    }

    fun setSharingFile(file: File?) {
        _sharingFile.tryEmit(file)
    }

    override fun onCleared() {
        super.onCleared()
        playbackStateModel.pausePlayback(WorkoutPlaybackPauseReason.ScreenExit)
        playbackStateModel.reset()
        workoutPlaybackGeopointLoader.reset()
    }

    class MapGraphTimeInWorkoutAndAnimationInterpolator(
        private val cameraPath: CameraPath,
        private val geoPoints: List<WorkoutGeoPoint>,
    ) : PlaybackStateModel.TimeInWorkoutAndAnimationInterpolator {
        private var previousTimeInAnimationMillis = 0L

        override fun timeInAnimationToTimeInWorkout(
            timeInAnimationMillis: Long,
            animationDurationMillis: Long,
            workoutDurationMillis: Long
        ): Long {
            val phase = timeInAnimationMillis.toDouble() / animationDurationMillis
            if (phase >= 1.0) {
                return workoutDurationMillis
            }

            // Animation & the camera path expect first geopoint to be at distance 0, add the first
            // geoPoint's distance as an offset to compensate for missing data.
            val cameraConfig3d = get3DCameraConfig(cameraPath, geoPoints, phase)
            val geoPointIndex = getGeoPointIndexByDistance(
                geoPoints,
                geoPoints.first().totalDistance + cameraConfig3d.markerDistance
            )

            previousTimeInAnimationMillis = timeInAnimationMillis
            return geoPoints[geoPointIndex].millisecondsInWorkout.toLong()
        }

        override fun timeInWorkoutToTimeInAnimation(
            timeInWorkoutMillis: Long,
            animationDurationMillis: Long,
            workoutDurationMillis: Long
        ): Long {
            if (((geoPoints.lastOrNull()?.totalDistance) ?: 0.0) == 0.0) {
                return 0L
            }

            if (timeInWorkoutMillis >= workoutDurationMillis) {
                return animationDurationMillis
            }

            val distancePhaseFactor = TRACK_PROGRESS_FACTOR
            val curGeoPointIndex = getGeoPointIndexByWorkoutTime(geoPoints, timeInWorkoutMillis)
            val curGeoPoint = geoPoints[curGeoPointIndex]
            val curDistancePhase =
                (curGeoPoint.totalDistance / geoPoints.last().totalDistance) / distancePhaseFactor

            // curGeoPoint is ahead of the animation if we haven't gone past the last geoPoint,
            // if the data allows try to find the animation phase between the geoPoints
            val animationPhase = if (curGeoPoint.millisecondsInWorkout > timeInWorkoutMillis) {
                val prevGeoPoint = geoPoints[(curGeoPointIndex - 1).coerceAtLeast(0)]
                if (curGeoPoint.millisecondsInWorkout != prevGeoPoint.millisecondsInWorkout) {
                    val prevDistancePhase =
                        (prevGeoPoint.totalDistance / geoPoints.last().totalDistance) / distancePhaseFactor
                    val phaseDiff = curDistancePhase - prevDistancePhase
                    val betweenGeoPointsFraction =
                        (timeInWorkoutMillis - prevGeoPoint.millisecondsInWorkout).toDouble() /
                            (curGeoPoint.millisecondsInWorkout - prevGeoPoint.millisecondsInWorkout).toDouble()

                    prevDistancePhase + (betweenGeoPointsFraction * phaseDiff)
                } else {
                    // we're at the start
                    curDistancePhase
                }
            } else {
                // data missing at the end
                curDistancePhase
            }

            val timeInAnimationMillis = (animationPhase * animationDurationMillis).roundToLong()
            previousTimeInAnimationMillis = timeInAnimationMillis
            return timeInAnimationMillis
        }

        override fun timeInAnimationAfterInterpolatorChange(
            timeInWorkoutMillis: Long,
            timeInAnimationMillis: Long,
            animationDurationMillis: Long,
            workoutDurationMillis: Long
        ): Long {
            // Prevent jumping due to interpolation inaccuracies if animation time hasn't changed
            if (timeInAnimationMillis == previousTimeInAnimationMillis) {
                return timeInAnimationMillis
            }

            val geoPointIdx = getGeoPointIndexByWorkoutTime(geoPoints, timeInWorkoutMillis)
            return if (geoPointIdx == geoPoints.lastIndex) {
                0L
            } else {
                timeInWorkoutToTimeInAnimation(
                    timeInWorkoutMillis,
                    animationDurationMillis,
                    workoutDurationMillis
                )
            }
        }
    }

    companion object {
        const val EXTRA_WORKOUT_ID = "workoutId"
        const val EXTRA_SHARE_POPUP = "sharePopup"

        private const val MIN_3D_PLAYBACK_DURATION = 15_000.0
        private const val MAX_3D_PLAYBACK_DURATION = 45_000.0
        private const val BASE_DURATION = 25_000.0
        private const val BASE_DISTANCE = 6_000.0
        private const val TRACK_PROGRESS_FACTOR = 1.1
        private const val ASPECT_RATIO = 0.5

        private fun getGeoPointIndexByDistance(
            geoPoints: List<WorkoutGeoPoint>,
            markerDistance: Double,
        ) = with(geoPoints.indexOfFirst { it.totalDistance > markerDistance }) {
            if (this == -1) geoPoints.lastIndex else this
        }

        private fun getGeoPointIndexByWorkoutTime(
            geoPoints: List<WorkoutGeoPoint>,
            millisInWorkout: Long,
        ) = with(geoPoints.indexOfFirst { it.millisecondsInWorkout >= millisInWorkout }) {
            if (this == -1) geoPoints.lastIndex else this
        }

        private fun getPlaybackDuration(distance: Double) = if (distance < BASE_DISTANCE) {
            MIN_3D_PLAYBACK_DURATION + (BASE_DURATION - MIN_3D_PLAYBACK_DURATION) * distance / BASE_DISTANCE
        } else {
            BASE_DURATION + (MAX_3D_PLAYBACK_DURATION - BASE_DURATION) * (1 - BASE_DISTANCE / distance)
        }.roundToLong()

        private fun get3DCameraConfig(
            cameraPath: CameraPath,
            geoPoints: List<WorkoutGeoPoint>,
            phase: Double,
        ): Workout3DPlaybackCameraConfig {
            return cameraPath.getCamera(phase.coerceAtMost(1.0), ASPECT_RATIO).run {
                val points = geoPoints.map { Point.fromLngLat(it.longitude, it.latitude) }
                val markerPosition = TurfMeasurement.along(
                    points,
                    markerDistance,
                    TurfConstants.UNIT_METERS
                )
                val userLocation = geoPoints[getGeoPointIndexByDistance(geoPoints, markerDistance)]

                Workout3DPlaybackCameraConfig(
                    cameraPosition = LatLng(cameraPosition.lat, cameraPosition.lon),
                    cameraAltitude = cameraPosition.alt,
                    cameraPitch = pitch,
                    cameraBearing = bearing,
                    markerPosition = LatLng(markerPosition.latitude(), markerPosition.longitude()),
                    markerAltitude = userLocation.altitude,
                    markerDistance = markerDistance
                )
            }
        }
    }
}
