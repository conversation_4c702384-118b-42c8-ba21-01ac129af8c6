package com.stt.android.workout.details.graphanalysis.map

import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.stt.android.domain.sml.TraverseEvent
import com.stt.android.ui.components.RouteWithDashLinePoints

sealed class WorkoutMapRouteData(
    val routeWithDashLinePoints: RouteWithDashLinePoints?,
    val highlightedLapLatLngs: List<LatLng>,
    val nonHighlightedLatLngs: List<List<LatLng>>,
    val bounds: LatLngBounds,
    val disableZoomToBounds: Boolean, // e.g. indoor workout with a manually added location
)

class BasicWorkoutMapRouteData(
    routeWithDashLinePoints: RouteWithDashLinePoints?,
    highlightedLapLatLngs: List<LatLng>,
    nonHighlightedLatLngs: List<List<LatLng>>,
    bounds: LatLngBounds,
    disableZoomToBounds: Boolean,
    val startPoint: LatLng,
    val endPoint: LatLng,
) : WorkoutMapRouteData(
    routeWithDashLinePoints = routeWithDashLinePoints,
    highlightedLapLatLngs = highlightedLapLatLngs,
    nonHighlightedLatLngs = nonHighlightedLatLngs,
    bounds = bounds,
    disableZoomToBounds = disableZoomToBounds
)

class MultisportWorkoutMapRouteData(
    routeWithDashLinePoints: RouteWithDashLinePoints?,
    highlightedLapLatLngs: List<LatLng>,
    nonHighlightedLatLngs: List<List<LatLng>>,
    bounds: LatLngBounds,
    disableZoomToBounds: Boolean,
    val inactiveMultisportPartRoutes: List<List<LatLng>>,
    val activityTypeChangeLocations: List<LatLng>,
    val startPoint: LatLng,
    val endPoint: LatLng
) : WorkoutMapRouteData(
    routeWithDashLinePoints = routeWithDashLinePoints,
    highlightedLapLatLngs = highlightedLapLatLngs,
    nonHighlightedLatLngs = nonHighlightedLatLngs,
    bounds = bounds,
    disableZoomToBounds = disableZoomToBounds
)

/**
 * Doesn't support default [highlightedLapLatLngs] and [nonHighlightedLatLngs],
 * use the separate versions for runs and lifts.
 *
 * @param fullRoute - Full route that hasn't been divided to runs and skis or
 *                    highlighted & non-highlighted parts. Used for dividing
 *                    the route to highlights and non-highlights without the need
 *                    of re-combining from lifts and runs
 */
class SkiWorkoutMapRouteData(
    routeWithDashLinePoints: RouteWithDashLinePoints?,
    val fullRoute: List<LatLng>,
    bounds: LatLngBounds,
    disableZoomToBounds: Boolean,
    val highlightedRuns: List<List<LatLng>>,
    val nonHighlightedRuns: List<List<LatLng>>,
    val highlightedLifts: List<List<LatLng>>,
    val nonHighlightedLifts: List<List<LatLng>>
) : WorkoutMapRouteData(
    routeWithDashLinePoints = routeWithDashLinePoints,
    highlightedLapLatLngs = emptyList(),
    nonHighlightedLatLngs = emptyList(),
    bounds = bounds,
    disableZoomToBounds = disableZoomToBounds
)

class HuntingOrFishingMapRouteData(
    routeWithDashLinePoints: RouteWithDashLinePoints?,
    highlightedLapLatLngs: List<LatLng>,
    nonHighlightedLatLngs: List<List<LatLng>>,
    val traverseEvents: List<TraverseEvent>,
    bounds: LatLngBounds,
    disableZoomToBounds: Boolean,
    val startPoint: LatLng,
    val endPoint: LatLng
) : WorkoutMapRouteData(
    routeWithDashLinePoints = routeWithDashLinePoints,
    highlightedLapLatLngs = highlightedLapLatLngs,
    nonHighlightedLatLngs = nonHighlightedLatLngs,
    bounds = bounds,
    disableZoomToBounds = disableZoomToBounds
)
