package com.stt.android.workout.details

import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import android.view.inputmethod.InputMethodManager
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.commit
import androidx.lifecycle.Lifecycle
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.navigation.NavController
import com.stt.android.common.navigation.findNavController
import com.stt.android.common.ui.observeNotNull
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.intentresolver.IntentKey
import com.stt.android.intentresolver.IntentResolver
import com.stt.android.multimedia.sportie.SportieShareSource
import com.stt.android.utils.STTConstants.ExtraKeys.FROM_NOTIFICATION
import com.stt.android.utils.STTConstants.ExtraKeys.NAVIGATED_FROM_SOURCE
import com.stt.android.workout.details.WorkoutDetailsViewModelNew.Companion.EXTRA_HIDE_BAR_INFO
import com.stt.android.workout.details.WorkoutDetailsViewModelNew.Companion.EXTRA_SHOW_COMMENTS
import com.stt.android.workout.details.WorkoutDetailsViewModelNew.Companion.EXTRA_SHOW_MAP_GRAPH_ANALYSIS
import com.stt.android.workout.details.analytics.WorkoutDetailsAnalytics
import com.stt.android.workout.details.diveprofile.DiveEventsBottomSheetFragment
import com.stt.android.workout.details.graphanalysis.map.WorkoutMapGraphAnalysisViewModel
import com.stt.android.workoutdetail.comments.CommentsDialogFragment
import com.stt.android.workouts.details.values.WorkoutValueDescriptionPopupFragment
import com.stt.android.workouts.sharepreview.customshare.ScreenshotObserver
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareHelper
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareTargetListDialogFragment
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareTargetListDialogFragment.Companion.newInstanceForLinkSharing
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@FlowPreview
@AndroidEntryPoint
class WorkoutDetailsActivityNew : AppCompatActivity(R.layout.workout_details_activity_new),
    ScreenshotObserver.ScreenshotObservable {
    @Inject
    internal lateinit var localBroadcastManager: LocalBroadcastManager

    @Inject
    internal lateinit var workoutShareHelper: WorkoutShareHelper

    @Inject
    internal lateinit var workoutDetailsAnalytics: WorkoutDetailsAnalytics

    @Inject
    @FeatureTogglePreferences
    internal lateinit var featureTogglePreferences: SharedPreferences

    private lateinit var navController: NavController

    private val viewModel: WorkoutDetailsViewModelNew by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        viewModel.loadDataIfNeeded()

        navController = findNavController(R.id.nav_host_fragment_container)
        val graph = navController.navInflater.inflate(R.navigation.workout_details_nav)
        if (intent.getBooleanExtra(EXTRA_SHOW_MAP_GRAPH_ANALYSIS, false)) {
            graph.setStartDestination(R.id.workoutMapGraphAnalysisFragment)
        }
        navController.setGraph(graph, intent.extras)
        setupLiveData()
    }

    private fun setupLiveData() {
        viewModel.navigationEventDispatcher.navigationEvent.observeNotNull(this, ::handleNavEvent)
        viewModel.onShowDiveEvents.observeNotNull(this, ::handleShowDiveEvent)
        viewModel.onShareLink.observeNotNull(this, ::handleOnShareLink)
    }

    private fun handleNavEvent(navEvent: WorkoutDetailsNavEvent) {
        // This is a workaround to back button disappearing from the toolbar when navigating
        // back from a dialog fragment using the nav component. Can be replaced with only the
        // contents of the else branch if an update to nav component fixes the issue or
        // alternatively our broader usage of nav component fixes the issue.
        when (navEvent) {
            is WorkoutDetailsValueDescriptionNavEvent -> {
                trackWorkoutValueDescriptionOpenedEvent(navEvent)
                WorkoutValueDescriptionPopupFragment.newInstance(
                    workoutValue = navEvent.workoutValue,
                    workoutId = navEvent.workoutId,
                    workoutKey = navEvent.workoutKey,
                )
                    .show(supportFragmentManager, null)
            }
            is WorkoutDetailsCommentListDialogNavEvent -> {
                CommentsDialogFragment.newInstance(
                    navEvent.workoutHeader,
                    false
                ).show(supportFragmentManager, null)
            }
            is WorkoutDetailsMapGraphAnalysisNavEvent -> {
                hideKeyboard()
                navEvent.navigate(navController, featureTogglePreferences)
            }
            is WorkoutDetailsSharePhotoNavEvent -> {
                if (workoutShareHelper.showMultipleWorkoutShareWays()) {
                    handleOnShareEvent(
                        navEvent.index,
                        navEvent.workoutHeader,
                        navEvent.workoutExtension,
                        navEvent.byScreenshot
                    )
                } else {
                    navEvent.navigate(navController, featureTogglePreferences)
                }
            }
            else -> {
                navEvent.navigate(navController, featureTogglePreferences)
            }
        }
    }

    private fun hideKeyboard() {
        val imm = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager?
        imm?.hideSoftInputFromWindow(window.currentFocus?.windowToken, 0)
    }

    private fun handleShowDiveEvent(diveProfileData: DiveProfileData?) {
        val currentFragment = supportFragmentManager
            .findFragmentByTag(DiveEventsBottomSheetFragment.TAG) as? DiveEventsBottomSheetFragment
        if (currentFragment != null) {
            currentFragment.setDiveProfileData(diveProfileData)
        } else {
            val newFragment = DiveEventsBottomSheetFragment().apply {
                setDiveProfileData(diveProfileData)
            }
            supportFragmentManager.commit {
                add(newFragment, DiveEventsBottomSheetFragment.TAG)
            }
        }
    }

    private fun trackWorkoutValueDescriptionOpenedEvent(navEvent: WorkoutDetailsValueDescriptionNavEvent) {
        val handler = CoroutineExceptionHandler { _, exception ->
            Timber.w(exception, "Tracking workout value description opened event failed")
        }
        GlobalScope.launch(handler) {
            workoutDetailsAnalytics.trackWorkoutValueDescriptionOpenedEvent(
                navEvent.workoutValue.item?.key ?: "",
                navEvent.analyticsContext
            )
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        if (!navController.navigateUp()) {
            // Parent activity is HomeActivity, but WorkoutDetails can ben opened from multiple
            // places. Better to return where we came from so use back navigation instead of up
            // here.
            super.onBackPressed()
        }
        return true
    }

    private fun handleOnShareLink(workoutHeader: WorkoutHeader) {
        if (workoutShareHelper.hasCustomIntentHandling()) {
            val fragment: DialogFragment = newInstanceForLinkSharing(
                workoutHeader,
                SportieShareSource.WORKOUT_DETAILS
            )
            fragment.show(supportFragmentManager, WorkoutShareTargetListDialogFragment.TAG)
        } else {
            workoutShareHelper.sendImplicitWorkoutLinkShareIntent(
                this,
                workoutHeader,
                SportieShareSource.WORKOUT_DETAILS
            )
        }
    }

    private fun handleOnShareEvent(
        index: Int,
        workoutHeader: WorkoutHeader,
        workoutExtension: WorkoutExtension?,
        byScreenShot: Boolean
    ) {
        var watchName = ""
        workoutExtension?.let { extension ->
            if (extension is SummaryExtension) {
                watchName = extension.displayName ?: ""
            }
        }
        workoutShareHelper.toMultipleWorkoutShareWays(
            this,
            workoutHeader,
            index,
            watchName,
            byScreenShot,
        )
    }

    override fun onDestroy() {
        supportFragmentManager.findFragmentByTag(WorkoutShareTargetListDialogFragment.TAG)?.let {
            (it as? DialogFragment)?.dismiss()
        }
        super.onDestroy()
    }

    override fun onScreenshot(data: String?) {
        Timber.d("screenshot captured: $data")
        if (lifecycle.currentState == Lifecycle.State.RESUMED) {
            viewModel.onScreenshot()
        }
    }

    companion object {
        fun getWorkoutDetailsIntentResolver(): IntentResolver<IntentKey.WorkoutDetails> {
            return object : IntentResolver<IntentKey.WorkoutDetails> {
                override fun getIntent(
                    context: Context,
                    keyT: IntentKey.WorkoutDetails
                ): Intent {
                    return newStartIntent(
                        context,
                        keyT.username,
                        keyT.workoutId,
                        keyT.workoutKey,
                        keyT.analyticsSource,
                        keyT.isFromNotification,
                        keyT.showCommentsDialog,
                        keyT.showMapGraphAnalysis,
                        keyT.autoPlayback,
                        keyT.forceSkiMap,
                        keyT.hideBarInfo,
                    )
                }
            }
        }
        private fun newStartIntent(
            context: Context,
            username: String,
            workoutId: Int?,
            workoutKey: String?,
            analyticsSource: String?,
            isFromNotification: Boolean,
            showComments: Boolean = false,
            showMapGraphAnalysis: Boolean = false,
            autoPlayback: Boolean = false,
            forceSkiMap: Boolean = false,
            hideBarInfo: Boolean = false,
        ): Intent {
            return Intent(context, WorkoutDetailsActivityNew::class.java).apply {
                putExtra("username", username)
                putExtra("workoutId", workoutId)
                putExtra("workoutKey", workoutKey)
                putExtra(NAVIGATED_FROM_SOURCE, analyticsSource)
                putExtra(FROM_NOTIFICATION, isFromNotification)
                putExtra(EXTRA_SHOW_COMMENTS, showComments)
                putExtra(EXTRA_SHOW_MAP_GRAPH_ANALYSIS, showMapGraphAnalysis)
                putExtra(EXTRA_HIDE_BAR_INFO, hideBarInfo)
                putExtra(WorkoutMapGraphAnalysisViewModel.ARG_AUTO_PLAYBACK, autoPlayback)
                putExtra(WorkoutMapGraphAnalysisViewModel.ARG_FORCE_SKI_MAP, forceSkiMap)
            }
        }
    }
}
