package com.stt.android.workout.details.shareactivity

import android.content.res.ColorStateList
import android.graphics.PorterDuff
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.ContextCompat
import androidx.core.widget.ImageViewCompat
import androidx.databinding.BindingAdapter
import com.stt.android.ThemeColors.primaryTextColor
import com.stt.android.ThemeColors.resolveColor
import com.stt.android.domain.workout.SharingOption
import com.stt.android.workout.details.R
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

@BindingAdapter("sharingFlags")
fun bindIconOptionState(
    imageView: ImageView,
    sharingFlags: Int
) {
    val context = imageView.context
    bindOptionState(
        viewId = imageView.id,
        sharingFlags = sharingFlags,
        selected = {
            configureImageView(
                imageView = imageView,
                iconColor = ContextCompat.getColor(context, CR.color.white),
                background = BaseR.drawable.sharing_option_background_selected
            )
        },
        unselected = {
            configureImageView(
                imageView = imageView,
                iconColor = primaryTextColor(context),
                background = BaseR.drawable.sharing_option_background
            )
        }
    )
}

private fun configureImageView(
    imageView: ImageView,
    @ColorInt iconColor: Int,
    @DrawableRes background: Int
) {
    ImageViewCompat.setImageTintMode(imageView, PorterDuff.Mode.SRC_IN)
    ImageViewCompat.setImageTintList(
        imageView,
        ColorStateList.valueOf(iconColor)
    )
    imageView.background = AppCompatResources.getDrawable(imageView.context, background)
}

@BindingAdapter("sharingFlags")
fun bindTextOptionState(
    textView: TextView,
    sharingFlags: Int
) {
    val context = textView.context
    bindOptionState(
        viewId = textView.id,
        sharingFlags = sharingFlags,
        selected = {
            textView.setTextColor(resolveColor(context, BaseR.attr.newAccentColor))
        },
        unselected = {
            textView.setTextColor(primaryTextColor(context))
        }
    )
}

private fun bindOptionState(
    viewId: Int,
    sharingFlags: Int,
    selected: () -> Unit,
    unselected: () -> Unit
) {
    val flag = getSharingFlagByViewId(viewId)
    val sharingNoneView = viewId == R.id.sharingNoneIcon || viewId == R.id.sharingNoneText
    if ((sharingNoneView && !sharingFlags.isShared()) ||
        (!sharingNoneView && isOptionSelected(flag, sharingFlags))
    ) {
        selected()
    } else {
        unselected()
    }
}

private fun isOptionSelected(flag: Int, sharingFlags: Int) =
    flag == SharingOption.NOT_SHARED.backendId || (flag and sharingFlags) != 0

fun getSharingFlagByViewId(viewId: Int): Int = when (viewId) {
    R.id.sharingNoneIcon, R.id.sharingNoneText -> SharingOption.NOT_SHARED.backendId
    R.id.sharingFollowersIcon, R.id.sharingFollowersText -> SharingOption.FOLLOWERS.backendId
    R.id.sharingEveryoneIcon, R.id.sharingEveryoneText -> SharingOption.EVERYONE.backendId
    else -> throw IllegalStateException("View ID not mapped to a sharing flag")
}

fun Int.isShared(): Boolean {
    return (this and SharingOption.FOLLOWERS.backendId) != 0 ||
        (this and SharingOption.EVERYONE.backendId) != 0 ||
        (this and SharingOption.FACEBOOK.backendId) != 0 ||
        (this and SharingOption.TWITTER.backendId) != 0
}
