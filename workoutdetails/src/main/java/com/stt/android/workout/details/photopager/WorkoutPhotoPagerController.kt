package com.stt.android.workout.details.photopager

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleCoroutineScope
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.stt.android.workout.details.CoverImage
import com.stt.android.workout.details.photo
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

class WorkoutPhotoPagerController
@Inject constructor() : ViewStateEpoxyController<List<CoverImage>>() {
    var viewLifecycle: Lifecycle? = null
    var lifecycleScope: LifecycleCoroutineScope? = null

    override fun buildModels(viewState: ViewState<List<CoverImage>?>) {
        viewState.data?.forEach { coverImage ->
            when (coverImage) {
                is CoverImage.PhotoCoverImage -> addPhoto(coverImage)
                is CoverImage.VideoCoverImage -> addVideo(coverImage)
                is CoverImage.DefaultCoverImage -> return@forEach
                is CoverImage.RouteCoverImage -> return@forEach
            }
        }
        super.buildModels(viewState)
    }

    private fun addPhoto(coverImage: CoverImage.PhotoCoverImage) {
        Timber.d("Adding photo model")
        photo {
            id(coverImage.picture.id)
            picture(coverImage.picture)
        }
    }

    private fun addVideo(coverImage: CoverImage.VideoCoverImage) {
        Timber.d("Adding video model")
        epoxyVideoView {
            id(coverImage.video.id)
            video(coverImage)
            viewLifecycle(viewLifecycle ?: return)
            onMuteButtonClicked { model, _, _, position ->
                lifecycleScope?.launch {
                    model.video().onMuteClickHandler.invoke(model.video(), position)
                }
            }
            onVisibilityChanged { _, view, _, percentVisibleWidth, _, _ ->
                if (percentVisibleWidth < 90f) {
                    view.onInvisible()
                } else {
                    view.onVisible()
                }
            }
        }
    }
}
