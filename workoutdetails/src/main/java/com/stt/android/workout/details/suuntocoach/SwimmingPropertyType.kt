package com.stt.android.workout.details.suuntocoach

enum class SwimmingPropertyType(
    val showPriority: Int,
    val maxThreshold: Int,
    val minThreshold: Int
) {
    BREASTSTROKE_AVG_BREATHE_ANGLE(showPriority = 1, maxThreshold = 55, minThreshold = 30),
    FREESTYLE_AVG_BREATHE_ANGLE(showPriority = 2, maxThreshold = 120, minThreshold = 90),
    FREESTYLE_HEAD_ANGLE(showPriority = 3, maxThreshold = 40, minThreshold = 1),
    BREASTSTROKE_HEAD_ANGLE(showPriority = 4, maxThreshold = 40, minThreshold = 1),
    BREASTSTROKE_MAX_BREATHE_ANGLE(showPriority = 5, maxThreshold = 70, minThreshold = 1),
    FREESTYLE_MAX_BREATHE_ANGLE(showPriority = 6, maxThreshold = 140, minThreshold = 1),
    BREASTSTROKE_PERCENT(showPriority = 7, maxThreshold = 80, minThreshold = 50),
    FREESTYLE_PERCENT(showPriority = 8, maxThreshold = 80, minThreshold = 50)
}
