package com.stt.android.workout.details.graphanalysis

import com.stt.android.domain.advancedlaps.LapsTable
import com.stt.android.domain.advancedlaps.LapsTableRow
import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.intensityzone.IntensityZoneLimits
import com.stt.android.workout.details.graphanalysis.typeselection.WorkoutGraphAnalysisInfo

data class GraphAnalysisData(
    val mainGraphData: GraphAnalysisChartData?,
    val comparisonGraphData: GraphAnalysisChartData?,
    val backgroundGraphData: GraphAnalysisChartData?,
    val graphTypeInfo: WorkoutGraphAnalysisInfo,
    val workoutHeader: WorkoutHeader,
    val multisportPartActivity: MultisportPartActivity?,
    val mainGraphZoneLimits: IntensityZoneLimits?,
    val isSwimming: Boolean,
    val usesNauticalUnits: Boolean,
    val durationSeconds: Float,
    val timeInWorkoutToDistance: (Float) -> (Float?),
    val canSelectLap: Boolean,
)

data class AnalysisLapsData(
    val lapsTables: List<LapsTable>,
    val lapsTableType: LapsTableType?,
    val selectedLap: LapsTableRow?,
    val lapStartSecondsInWorkout: Float?,
    val lapEndSecondsInWorkout: Float?,
) {
    /**
     * Null if either lap start or end seconds is null, indicating that the whole workout is selected
     */
    val lapDurationSeconds: Float? = if (lapStartSecondsInWorkout != null && lapEndSecondsInWorkout != null) {
        lapEndSecondsInWorkout - lapStartSecondsInWorkout
    } else {
        null
    }

    val isCustomLapSelected: Boolean
        get() = selectedLap == null && lapStartSecondsInWorkout != null && lapEndSecondsInWorkout != null

    val hasLapsData: Boolean
        get() = lapsTables.any { it.lapsTableRows.isNotEmpty() }
}
