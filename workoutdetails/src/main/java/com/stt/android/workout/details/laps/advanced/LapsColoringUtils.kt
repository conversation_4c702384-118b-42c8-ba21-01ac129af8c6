package com.stt.android.workout.details.laps.advanced

import com.amersports.formatter.Success
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter

/**
 * These functions are used in the Advanced Laps Coloring feature.
 * We aim to use formatted values when calculating percentiles per column.
 * This approach ensures that the UI does not display different colors for the same value due to formatting/rounding differences.
 */

fun getSuuntoPlusRowValueForCalculations(
    infoModelFormatter: InfoModelFormatter,
    rowValue: Number,
    formatStyle: String
): Double? {
    val result = infoModelFormatter.formatValue(
        formatName = formatStyle,
        value = rowValue,
        withStyle = true,
        forceRangeUnitOutput = true
    )
    return if (result is Success) {
        try {
            result.value.toDouble()
        } catch (e: NumberFormatException) {
            null
        }
    } else {
        null
    }
}

fun getSummaryRowValueForCalculations(
    infoModelFormatter: InfoModelFormatter,
    rowValue: Number,
    summaryItem: SummaryItem
): Double? {
    return try {
        val formatted = infoModelFormatter.formatValue(
            summaryItem,
            rowValue,
            forceRangeUnitOutput = true
        )
        formatted.value?.toDouble()
    } catch (e: NumberFormatException) {
        null
    }
}
