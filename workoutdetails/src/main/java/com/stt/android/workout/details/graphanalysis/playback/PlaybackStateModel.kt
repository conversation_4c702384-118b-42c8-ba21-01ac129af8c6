package com.stt.android.workout.details.graphanalysis.playback

import android.animation.TimeAnimator
import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.workout.details.analytics.WorkoutDetailsAnalytics
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import javax.inject.Inject
import kotlin.math.roundToLong

@ActivityRetainedScoped
class PlaybackStateModel @Inject constructor(
    private val workoutDetailsAnalytics: WorkoutDetailsAnalytics,
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope
) {
    private val fullPlaybackStateFlow = MutableStateFlow(EMPTY_STATE)

    val playbackStateFlow: Flow<PlaybackState>
        get() = fullPlaybackStateFlow.map {
            it.toPlaybackState()
        }.distinctUntilChanged()

    val playbackState: PlaybackState
        get() = fullPlaybackStateFlow.value.toPlaybackState()

    val playbackProgressFlow: Flow<PlaybackProgress>
        get() = fullPlaybackStateFlow.map {
            it.toPlaybackProgress()
        }.distinctUntilChanged()

    val playbackProgress: PlaybackProgress
        get() = fullPlaybackStateFlow.value.toPlaybackProgress()

    val playbackTimeWindowFlow: Flow<PlaybackTimeWindow>
        get() = fullPlaybackStateFlow.map {
            it.toPlaybackTimeWindow()
        }.distinctUntilChanged()

    val playbackTimeWindow: PlaybackTimeWindow
        get() = fullPlaybackStateFlow.value.toPlaybackTimeWindow()

    val isRunning: Boolean
        get() = timeAnimator?.isRunning == true

    val isWaitingForReadyState: Boolean
        get() = isRunning && readyForAnimationStates.values.any { !it }

    private var timeAnimator: TimeAnimator? = null
    private var readyForAnimationStates = mutableMapOf<String, Boolean>()
    private var interpolatorChangedSinceLastSeek = false

    private var timeInterpolator: TimeInWorkoutAndAnimationInterpolator =
        LinearTimeInWorkoutAndAnimationInterpolator()

    fun reset() {
        fullPlaybackStateFlow.tryEmit(EMPTY_STATE)
        timeAnimator?.cancel()
        timeAnimator = null
        readyForAnimationStates.clear()
        interpolatorChangedSinceLastSeek = false
        timeInterpolator = LinearTimeInWorkoutAndAnimationInterpolator()
    }

    fun setReadyForAnimation(key: String, isReady: Boolean) {
        readyForAnimationStates[key] = isReady
    }

    fun setTimeInWorkoutAndAnimationInterpolator(interpolator: TimeInWorkoutAndAnimationInterpolator) {
        timeInterpolator = interpolator
        interpolatorChangedSinceLastSeek = true
    }

    fun setWorkoutDuration(workoutDurationMillis: Long) {
        val currentState = fullPlaybackStateFlow.value

        if (workoutDurationMillis == currentState.workoutDurationMillis) {
            return
        }

        val (updatedTimeInAnimationMillis, updatedTimeInWorkoutMillis) = if (currentState.workoutDurationMillis > workoutDurationMillis) {
            0L to 0L
        } else {
            currentState.timeInAnimationMillis to currentState.timeInWorkoutMillis
        }

        interpolatorChangedSinceLastSeek = false
        fullPlaybackStateFlow.tryEmit(
            currentState.copy(
                timeInAnimationMillis = updatedTimeInAnimationMillis,
                timeInWorkoutMillis = updatedTimeInWorkoutMillis,
                workoutDurationMillis = workoutDurationMillis,
            )
        )
    }

    fun setPlaybackDuration(animationDurationMillis: Long) {
        val currentState = fullPlaybackStateFlow.value

        if (animationDurationMillis == currentState.animationDurationMillis) {
            return
        }

        val (updatedTimeInAnimationMillis, updatedTimeInWorkoutMillis) = if (currentState.timeInAnimationMillis > animationDurationMillis) {
            0L to 0L
        } else {
            currentState.timeInAnimationMillis to currentState.timeInWorkoutMillis
        }

        interpolatorChangedSinceLastSeek = false
        fullPlaybackStateFlow.tryEmit(
            currentState.copy(
                timeInAnimationMillis = updatedTimeInAnimationMillis,
                animationDurationMillis = animationDurationMillis,
                timeInWorkoutMillis = updatedTimeInWorkoutMillis,
            )
        )
    }

    fun setLapTimeWindow(
        windowStartTimeInWorkoutMillis: Long,
        windowEndTimeInWorkoutMillis: Long
    ) {
        seekToTimeInWorkout(
            windowStartTimeInWorkoutMillis,
            PlaybackProgressionReason.SELECTED_LAP_CHANGED,
            windowStartTimeInWorkoutMillis,
            windowEndTimeInWorkoutMillis
        )
    }

    fun removeLapTimeWindow() {
        val currentState = fullPlaybackStateFlow.value
        seekToTimeInWorkout(
            currentState.timeInWorkoutMillis,
            PlaybackProgressionReason.SELECTED_LAP_CHANGED,
            null,
            null
        )
    }

    fun seekToTimeInWorkout(
        timeInWorkoutMillis: Long,
        progressionReason: PlaybackProgressionReason
    ) {
        val currentState = fullPlaybackStateFlow.value
        seekToTimeInWorkout(
            timeInWorkoutMillis,
            progressionReason,
            currentState.lapWindowStartMillis,
            currentState.lapWindowEndMillis
        )
    }

    private fun seekToTimeInWorkout(
        timeInWorkoutMillis: Long,
        progressionReason: PlaybackProgressionReason,
        windowStartTimeInWorkoutMillis: Long?,
        windowEndTimeInWorkoutMillis: Long?
    ) {
        if (fullPlaybackStateFlow.value.workoutDurationMillis == DURATION_NOT_SET ||
            fullPlaybackStateFlow.value.animationDurationMillis == DURATION_NOT_SET
        ) {
            return
        }

        if (isRunning) {
            if (progressionReason == PlaybackProgressionReason.USER_SCRUB_TIMELINE) {
                pausePlayback(WorkoutPlaybackPauseReason.MoveTimelinePoint)
            } else if (progressionReason == PlaybackProgressionReason.SELECTED_LAP_CHANGED) {
                pausePlayback(WorkoutPlaybackPauseReason.SelectedLapChanged)
            }
        }

        val currentState = fullPlaybackStateFlow.value

        if (currentState.timeInWorkoutMillis == timeInWorkoutMillis &&
            currentState.lapWindowStartMillis == windowStartTimeInWorkoutMillis &&
            currentState.lapWindowEndMillis == windowEndTimeInWorkoutMillis &&
            progressionReason == currentState.progressionReason
        ) {
            return
        }

        val minTimeInWorkout = windowStartTimeInWorkoutMillis ?: 0L
        val maxTimeInWorkout = windowEndTimeInWorkoutMillis ?: currentState.workoutDurationMillis

        val clampedTimeInWorkout = try {
            timeInWorkoutMillis.coerceIn(minTimeInWorkout, maxTimeInWorkout)
        } catch (_: IllegalArgumentException) {
            return
        }

        if (clampedTimeInWorkout == currentState.timeInWorkoutMillis) {
            // Lap change doesn't affect time in workout, just update the window in the state
            // but don't redo interpolation or reset interpolatorChangedSinceLastSeek
            fullPlaybackStateFlow.tryEmit(
                currentState.copy(
                    lapWindowStartMillis = windowStartTimeInWorkoutMillis,
                    lapWindowEndMillis = windowEndTimeInWorkoutMillis
                )
            )
            return
        }

        val timeInAnimationMillis = timeInterpolator.timeInWorkoutToTimeInAnimation(
            clampedTimeInWorkout,
            currentState.animationDurationMillis,
            currentState.workoutDurationMillis
        )

        interpolatorChangedSinceLastSeek = false
        fullPlaybackStateFlow.tryEmit(
            currentState.copy(
                timeInAnimationMillis = timeInAnimationMillis,
                timeInWorkoutMillis = clampedTimeInWorkout,
                progressionReason = progressionReason,
                lapWindowStartMillis = windowStartTimeInWorkoutMillis,
                lapWindowEndMillis = windowEndTimeInWorkoutMillis
            )
        )
    }

    fun resumePlayback(initiatedFrom: String, restart: Boolean = false) {
        if (isRunning) {
            return
        }

        var playbackState = fullPlaybackStateFlow.value
        val startedFrom = when {
            restart -> 0L

            playbackState.timeInLapWindowFraction < 1.0 -> {
                when {
                    interpolatorChangedSinceLastSeek -> {
                        // timeInAnimationAfterInterpolatorChange has to be called first
                        // because the MapGraphTimeInWorkoutAndAnimationInterpolator remembers latest
                        // return value of timeInWorkoutToTimeInAnimation to prevent jumps
                        val interpolatedWithoutLapStart =
                            timeInterpolator.timeInAnimationAfterInterpolatorChange(
                                playbackState.timeInWorkoutMillis,
                                playbackState.timeInAnimationMillis,
                                playbackState.animationDurationMillis,
                                playbackState.workoutDurationMillis
                            )
                        val lapStartAnimationTime = playbackState.lapWindowStartMillis?.let {
                            timeInterpolator.timeInWorkoutToTimeInAnimation(
                                it,
                                playbackState.animationDurationMillis,
                                playbackState.workoutDurationMillis
                            )
                        } ?: 0L

                        interpolatedWithoutLapStart.coerceAtLeast(lapStartAnimationTime)
                    }

                    playbackState.timeInAnimationFraction < 1.0 -> {
                        playbackState.timeInAnimationMillis
                    }

                    else -> {
                        0L
                    }
                }
            }

            else -> {
                playbackState.lapWindowStartMillis?.let {
                    timeInterpolator.timeInWorkoutToTimeInAnimation(
                        it,
                        playbackState.animationDurationMillis,
                        playbackState.workoutDurationMillis
                    )
                } ?: 0L
            }
        }
        interpolatorChangedSinceLastSeek = false
        val isLapChosen = playbackState.lapWindowStartMillis != null && playbackState.lapWindowEndMillis != null
        val previousPauseReason = playbackState.pauseReason
        activityRetainedCoroutineScope.launch(NonCancellable) {
            if (previousPauseReason != null) {
                workoutDetailsAnalytics.trackWorkoutPlaybackResume(
                    previousPauseReason,
                    isLapChosen,
                    initiatedFrom
                )
            } else {
                workoutDetailsAnalytics.trackWorkoutPlaybackInitialPlay(
                    startedFrom == 0L,
                    isLapChosen,
                    initiatedFrom
                )
            }
        }
        playbackState = playbackState.copy(
            playbackResumed = true,
            pauseReason = null
        )
        fullPlaybackStateFlow.tryEmit(playbackState)

        var waitForReadyOffset = 0L

        timeAnimator?.cancel()
        timeAnimator = TimeAnimator().apply {
            setTimeListener { animator, totalTime, _ ->
                if (playbackState.animationDurationMillis == DURATION_NOT_SET) {
                    val updatedState = fullPlaybackStateFlow.value
                    if (updatedState.animationDurationMillis != DURATION_NOT_SET) {
                        playbackState = updatedState
                    } else {
                        return@setTimeListener
                    }
                }

                if (readyForAnimationStates.isNotEmpty() && readyForAnimationStates.any { !it.value }) {
                    waitForReadyOffset = totalTime
                    return@setTimeListener
                }

                val timeInAnimationMillis = totalTime + startedFrom - waitForReadyOffset
                val timeInWorkoutMillis = timeInterpolator.timeInAnimationToTimeInWorkout(
                    timeInAnimationMillis,
                    playbackState.animationDurationMillis,
                    playbackState.workoutDurationMillis
                )

                val lapWorkoutTimeFraction = timeInWorkoutMillis.toDouble() /
                    (
                        playbackState.lapWindowEndMillis
                            ?: playbackState.workoutDurationMillis
                        ).toDouble()
                val playbackResumed =
                    if (lapWorkoutTimeFraction >= 1.0) {
                        activityRetainedCoroutineScope.launch(NonCancellable) {
                            workoutDetailsAnalytics.trackWorkoutPlaybackEnd(initiatedFrom, isLapChosen)
                        }
                        animator.cancel()
                        false
                    } else {
                        true
                    }

                playbackState = fullPlaybackStateFlow.value.copy(
                    timeInAnimationMillis = timeInAnimationMillis,
                    timeInWorkoutMillis = timeInWorkoutMillis,
                    animationStartedAtMillis = startedFrom,
                    playbackResumed = playbackResumed,
                    progressionReason = PlaybackProgressionReason.AUTOMATIC_PLAYBACK,
                    pauseReason = null,
                )

                fullPlaybackStateFlow.tryEmit(playbackState)
            }
            start()
        }
    }

    fun pausePlayback(pauseReason: WorkoutPlaybackPauseReason) {
        if (!isRunning) {
            return
        }

        timeAnimator?.cancel()
        fullPlaybackStateFlow.tryEmit(
            fullPlaybackStateFlow.value.copy(
                playbackResumed = false,
                pauseReason = pauseReason
            )
        )
    }

    private fun PlaybackStateModelState.toPlaybackState() = PlaybackState(
        playbackResumed,
        pauseReason
    )

    private fun PlaybackStateModelState.toPlaybackProgress() = PlaybackProgress(
        timeInAnimationMillis,
        animationDurationMillis,
        timeInWorkoutMillis,
        workoutDurationMillis,
        progressionReason
    )

    private fun PlaybackStateModelState.toPlaybackTimeWindow(): PlaybackTimeWindow =
        if (lapWindowStartMillis != null && lapWindowEndMillis != null) {
            PlaybackLapWindow(lapWindowStartMillis, lapWindowEndMillis)
        } else {
            PlaybackFullWorkoutWindow
        }

    interface TimeInWorkoutAndAnimationInterpolator {
        fun timeInAnimationToTimeInWorkout(
            timeInAnimationMillis: Long,
            animationDurationMillis: Long,
            workoutDurationMillis: Long
        ): Long

        fun timeInWorkoutToTimeInAnimation(
            timeInWorkoutMillis: Long,
            animationDurationMillis: Long,
            workoutDurationMillis: Long
        ): Long

        /**
         * Can be used to do some changes after interpolator changes, such as wrapping the playback
         * back to start early if time-based playback went past ending point of distance based
         * one's ending point.
         */
        fun timeInAnimationAfterInterpolatorChange(
            timeInWorkoutMillis: Long,
            timeInAnimationMillis: Long,
            animationDurationMillis: Long,
            workoutDurationMillis: Long
        ): Long = timeInWorkoutToTimeInAnimation(
            timeInWorkoutMillis,
            animationDurationMillis,
            workoutDurationMillis
        )
    }

    open class LinearTimeInWorkoutAndAnimationInterpolator :
        TimeInWorkoutAndAnimationInterpolator {
        override fun timeInAnimationToTimeInWorkout(
            timeInAnimationMillis: Long,
            animationDurationMillis: Long,
            workoutDurationMillis: Long
        ): Long =
            ((timeInAnimationMillis.toDouble() / animationDurationMillis) * workoutDurationMillis).roundToLong()

        override fun timeInWorkoutToTimeInAnimation(
            timeInWorkoutMillis: Long,
            animationDurationMillis: Long,
            workoutDurationMillis: Long
        ): Long =
            ((timeInWorkoutMillis.toDouble() / workoutDurationMillis) * animationDurationMillis).roundToLong()
    }

    companion object {
        const val DURATION_NOT_SET = -1L
        private val EMPTY_STATE = PlaybackStateModelState(
            timeInAnimationMillis = 0L,
            animationDurationMillis = DURATION_NOT_SET,
            timeInWorkoutMillis = 0L,
            workoutDurationMillis = DURATION_NOT_SET,
            animationStartedAtMillis = 0L,
            playbackResumed = false,
            progressionReason = PlaybackProgressionReason.STARTING_POINT,
            pauseReason = null,
            lapWindowStartMillis = null,
            lapWindowEndMillis = null
        )
    }
}

private data class PlaybackStateModelState(
    val timeInAnimationMillis: Long,
    val animationDurationMillis: Long,
    val timeInWorkoutMillis: Long,
    val workoutDurationMillis: Long,
    val animationStartedAtMillis: Long,
    val playbackResumed: Boolean,
    val progressionReason: PlaybackProgressionReason,
    val pauseReason: WorkoutPlaybackPauseReason?,
    val lapWindowStartMillis: Long?,
    val lapWindowEndMillis: Long?
) {
    val timeInAnimationFraction: Double
        get() = if (animationDurationMillis > 0) {
            timeInAnimationMillis.toDouble() / animationDurationMillis.toDouble()
        } else {
            0.0
        }

    val timeInWorkoutFraction: Double
        get() =
            if (workoutDurationMillis > 0) {
                timeInWorkoutMillis.toDouble() / workoutDurationMillis.toDouble()
            } else {
                0.0
            }

    val timeInLapWindowFraction: Double
        get() =
            if (lapWindowEndMillis != null && lapWindowEndMillis > 0) {
                timeInWorkoutMillis.toDouble() / lapWindowEndMillis.toDouble()
            } else {
                timeInWorkoutFraction
            }
}

data class PlaybackState(
    val resumed: Boolean,
    val pauseReason: WorkoutPlaybackPauseReason?
)

enum class PlaybackProgressionReason {
    STARTING_POINT,
    AUTOMATIC_PLAYBACK,
    USER_SCRUB_TIMELINE,
    SELECTED_LAP_CHANGED
}

data class PlaybackProgress(
    val timeInAnimationMillis: Long,
    val animationDurationMillis: Long,
    val timeInWorkoutMillis: Long,
    val workoutDurationMillis: Long,
    val progressionReason: PlaybackProgressionReason
) {
    val timeInAnimationFraction: Double
        get() = if (animationDurationMillis > 0) {
            timeInAnimationMillis.toDouble() / animationDurationMillis.toDouble()
        } else {
            0.0
        }

    val timeInWorkoutFraction: Double
        get() = if (workoutDurationMillis > 0) {
            timeInWorkoutMillis.toDouble() / workoutDurationMillis.toDouble()
        } else {
            0.0
        }
}

sealed class PlaybackTimeWindow
object PlaybackFullWorkoutWindow : PlaybackTimeWindow()
data class PlaybackLapWindow(
    val windowStartMillis: Long,
    val windowEndMillis: Long
) : PlaybackTimeWindow()
