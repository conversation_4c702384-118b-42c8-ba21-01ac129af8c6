package com.stt.android.workout.details.share.colortrack

import com.google.android.gms.maps.model.LatLng
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.maps.colortrack.ColorTrackDescriptor
import com.stt.android.maps.colortrack.FractionColor

fun ColorTrackDescriptor.Companion.from(geoPoints: List<WorkoutGeoPoint>): ColorTrackDescriptor {
    return ColorTrackDescriptor(
        points = geoPoints.map { LatLng(it.latitude, it.longitude) },
        colors = listOf(FractionColor(0.0, 255.0, 255.0, 0.0))
    )
}

fun ColorTrackDescriptor.Companion.from(
    geoPoints: List<WorkoutGeoPoint>,
    buckets: List<Bucket>,
): ColorTrackDescriptor {
    if (geoPoints.isEmpty() || buckets.isEmpty()) {
        return ColorTrackDescriptor(
            points = emptyList(),
            colors = emptyList(),
        )
    }

    val points = mutableListOf<LatLng>()
    val colors = mutableListOf<FractionColor>()

    val iterator = buckets.iterator()
    var bucket = iterator.next()
    val totalDistance = geoPoints.last().totalDistance
    geoPoints.windowed(2, partialWindows = true).forEach {
        val start = it[0]
        val end = it.getOrNull(1)

        points.add(LatLng(start.latitude, start.longitude))

        fun addColor() {
            val fraction = start.totalDistance / totalDistance
            val prevColor = colors.lastOrNull()
            if (prevColor != null && prevColor.fraction >= fraction) return

            val color = FractionColor(
                fraction,
                255.0,
                (1.0 - bucket.fraction) * 255.0,
                0.0,
            )
            colors.add(color)
        }

        if (end != null) {
            if (end.timestamp > bucket.timestamp) {
                addColor()

                while (end.timestamp > bucket.timestamp) {
                    if (iterator.hasNext()) {
                        bucket = iterator.next()
                    } else {
                        break
                    }
                }
            }
        } else {
            addColor()
        }
    }

    val firstColor = colors.firstOrNull()
    if (firstColor == null || firstColor.fraction > 0.0) {
        colors.add(0, FractionColor(0.0, 255.0, 255.0, 0.0))
    }

    return ColorTrackDescriptor(points, colors)
}
