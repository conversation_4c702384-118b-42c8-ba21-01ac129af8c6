package com.stt.android.workout.details

import android.content.res.Resources
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.Group
import androidx.core.view.doOnNextLayout
import androidx.lifecycle.LifecycleCoroutineScope
import coil3.load
import coil3.request.allowHardware
import coil3.request.crossfade
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.review.ReviewState
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.user.VideoInformation
import com.stt.android.maps.MapSnapshotSpec
import com.stt.android.maps.MapSnapshotter
import com.stt.android.maps.MapType
import com.stt.android.maps.MapTypeHelper
import com.stt.android.maps.bindMapSnapshot
import com.stt.android.ui.map.MapHelper
import kotlinx.coroutines.Job
import timber.log.Timber
import com.stt.android.R as BaseR

@EpoxyModelClass
abstract class WorkoutCoverImageModel : EpoxyModelWithHolder<CoverImageViewHolder>() {
    private var coverImageJob: Job? = null

    @EpoxyAttribute
    var showPlayButton: Boolean = false

    // when draw image to bitmap, can't allowHardware
    @EpoxyAttribute
    var allowHardware: Boolean = true

    @EpoxyAttribute(EpoxyAttribute.Option.IgnoreRequireHashCode)
    lateinit var coverImage: CoverImage

    @EpoxyAttribute
    lateinit var mapType: MapType

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var scope: LifecycleCoroutineScope? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var mapSnapshotter: MapSnapshotter? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var onClickListener: View.OnClickListener

    override fun getDefaultLayout() = R.layout.model_cover_image

    override fun bind(holder: CoverImageViewHolder) {
        super.bind(holder)
        with(holder) {
            playImageView.visibility =
                if (showPlayButton && videoReviewPassed()) View.VISIBLE else View.GONE
            coverImageView.setOnClickListener(onClickListener)
            setReviewStateViewVisible(reviewingView, contentIllegalViewGroup, coverImageView)
        }
    }

    private fun videoReviewPassed(): Boolean {
        return coverImage is CoverImage.VideoCoverImage && (coverImage as CoverImage.VideoCoverImage).video.reviewState == ReviewState.PASS
    }

    private fun setReviewStateViewVisible(
        reviewingView: TextView,
        contentIllegalViewGroup: View,
        coverImageView: ImageView
    ) {
        val (reviewState, reviewingText) = when (val coverImage = coverImage) {
            is CoverImage.PhotoCoverImage -> {
                coverImage.picture.reviewState to reviewingView.resources.getString(BaseR.string.image_reviewing)
            }

            is CoverImage.VideoCoverImage -> {
                coverImage.video.reviewState to reviewingView.resources.getString(BaseR.string.video_reviewing)
            }

            is CoverImage.DefaultCoverImage -> ReviewState.PASS to ""
            is CoverImage.RouteCoverImage -> ReviewState.PASS to ""
        }
        when (reviewState) {
            ReviewState.REVIEWING -> {
                reviewingView.visibility = View.VISIBLE
                reviewingView.text = reviewingText
                bindCoverImage(
                    coverImageView,
                    coverImage,
                    scope
                )
            }

            ReviewState.FAIL -> {
                contentIllegalViewGroup.visibility = View.VISIBLE
            }

            ReviewState.PASS -> {
                reviewingView.visibility = View.GONE
                contentIllegalViewGroup.visibility = View.GONE
                bindCoverImage(
                    coverImageView,
                    coverImage,
                    scope
                )
            }
        }
    }

    override fun unbind(holder: CoverImageViewHolder) {
        super.unbind(holder)
        coverImageJob?.cancel()
        coverImageJob = null
    }

    private fun bindCoverImage(
        imageView: ImageView,
        coverImage: CoverImage?,
        scope: LifecycleCoroutineScope?
    ) {
        imageView.layoutParams = imageView.layoutParams.apply {
            this.height = getCoverPhotoHeight(imageView.resources, coverImage ?: return)
        }
        coverImageJob = scope?.launchWhenCreated {
            runSuspendCatching {
                val context = imageView.context
                when (coverImage) {
                    is CoverImage.DefaultCoverImage -> {
                        imageView.load(coverImage.activityCoverPicture) {
                            crossfade(false)
                            allowHardware(allowHardware)
                        }
                    }

                    is CoverImage.PhotoCoverImage -> {
                        imageView.load(
                            ImageInformation.fromPicture(coverImage.picture).getHighResUri(context)
                        ) {
                            allowHardware(allowHardware)
                        }
                    }

                    is CoverImage.VideoCoverImage -> {
                        val video = coverImage.video
                        if (video.thumbnailUrl != null) {
                            imageView.load(video.thumbnailUrl) {
                                allowHardware(allowHardware)
                            }
                        } else {
                            val uri =
                                VideoInformation.fromVideo(video).getThumbnailUri(imageView.context)
                            imageView.load(uri) {
                                allowHardware(allowHardware)
                            }
                        }
                    }

                    is CoverImage.RouteCoverImage -> {
                        // if mapType requiresPremium == true, mapSnapshot will fail
                        // so update mapType to DEFAULT_MAP_TYPE
                        val updateMapType = if (mapType.requiresPremium) {
                            MapTypeHelper.DEFAULT_MAP_TYPE
                        } else {
                            mapType
                        }
                        imageView.doOnNextLayout {
                            val spec = MapSnapshotSpec.Workout(
                                workoutId = coverImage.workoutHeaderId,
                                width = imageView.width,
                                height = imageView.height,
                                explicitProviderName = MapHelper.getMapsProviderNameWithoutGoogle(),
                                explicitMapType = updateMapType,
                                explicitRoutePadding = imageView.height / 5,
                            )
                            val newSpec = coverImage.colorfulPolylines?.copy(
                                width = spec.width,
                                height = spec.height,
                                explicitProviderName = MapHelper.getMapsProviderNameWithoutGoogle(),
                                explicitMapType = updateMapType,
                                explicitRoutePadding = imageView.height / 5
                            ) ?: spec
                            imageView.bindMapSnapshot(spec = newSpec)
                        }
                    }

                    else -> {
                        return@launchWhenCreated
                    }
                }
            }.onFailure {
                Timber.w(it, "Binding cover image failed.")
            }
        }
    }

    private fun getCoverPhotoHeight(
        resources: Resources,
        coverImage: CoverImage
    ) =
        resources.getDimensionPixelSize(if (coverImage is CoverImage.DefaultCoverImage) R.dimen.placeholder_cover_image_landscape_height else R.dimen.cover_image_height)
}

class CoverImageViewHolder : KotlinEpoxyHolder() {
    val coverImageView: ImageView by bind(R.id.cover_image_view)
    val playImageView: ImageView by bind(R.id.play)
    val reviewingView: TextView by bind(R.id.content_reviewing)
    val contentIllegalViewGroup: Group by bind(R.id.content_illegal_view_group)
}
