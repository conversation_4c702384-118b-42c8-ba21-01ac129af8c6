package com.stt.android.workout.details.share.video

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Switch
import androidx.compose.material.SwitchDefaults
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.spacing
import com.stt.android.workout.details.R

@Composable
fun WorkoutMapPlaybackOptionsScreen(
    viewModel: WorkoutMapPlaybackOptionsViewModel,
    showDeviceOption: Boolean,
    modifier: Modifier = Modifier,
) {
    val options by viewModel.options.collectAsState()
    Column(
        modifier = modifier
            .verticalScroll(rememberScrollState())
            .fillMaxSize(),
    ) {
        PreferenceItem(
            label = stringResource(R.string.workout_map_playback_option_username),
            checked = options.username,
            onCheckedChange = { viewModel.setUserInformation(it) },
        )
        Divider(color = MaterialTheme.colors.dividerColor)
        PreferenceItem(
            label = stringResource(R.string.workout_map_playback_option_start_time),
            checked = options.startTime,
            onCheckedChange = { viewModel.setExerciseDate(it) },
        )
        Divider(color = MaterialTheme.colors.dividerColor)
        PreferenceItem(
            label = stringResource(R.string.workout_map_playback_option_location),
            checked = options.location,
            onCheckedChange = { viewModel.setLocationInformation(it) },
        )
        Divider(color = MaterialTheme.colors.dividerColor)
        if (showDeviceOption) {
            PreferenceItem(
                label = stringResource(R.string.workout_map_playback_option_device),
                checked = options.device,
                onCheckedChange = { viewModel.setDeviceInformation(it) },
            )
            Divider(color = MaterialTheme.colors.dividerColor)
        }
    }
}

@Composable
fun PreferenceItem(
    label: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .clickable(
                role = Role.Switch,
                onClickLabel = label,
            ) {
                onCheckedChange(!checked)
            }
            .fillMaxWidth()
            .padding(MaterialTheme.spacing.medium),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            modifier = Modifier.weight(1f),
            text = label,
            color = MaterialTheme.colors.onSurface,
            style = MaterialTheme.typography.bodyLarge,
        )
        Switch(
            checked = checked,
            onCheckedChange = null,
            colors = SwitchDefaults.colors(checkedThumbColor = MaterialTheme.colors.primary),
        )
    }
}

@Preview
@Composable
private fun PreferenceItemPreview() {
    AppTheme {
        PreferenceItem(
            modifier = Modifier.background(Color.White),
            label = "Preview Item",
            checked = true,
            onCheckedChange = {},
        )
    }
}
