package com.stt.android.workout.details.graphanalysis

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.core.widget.NestedScrollView
import kotlin.math.absoluteValue

class DisallowScrollingOnHorizontalTouchMovementScrollView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : NestedScrollView(context, attrs) {

    private val xDistanceThreshold: Float
    private var touchDownXPosition: Float = 0f

    init {
        val resources = context.resources
        val smallerScreenDimensionInPixels =
            resources.configuration.smallestScreenWidthDp * resources.displayMetrics.density
        xDistanceThreshold = smallerScreenDimensionInPixels / X_DISTANCE_FACTOR
    }

    override fun onInterceptTouchEvent(event: MotionEvent): Boolean {
        if (event.actionMasked == MotionEvent.ACTION_DOWN) {
            allowScrolling()
            touchDownXPosition = event.rawX
        }

        if (event.actionMasked == MotionEvent.ACTION_MOVE) {
            if ((event.rawX - touchDownXPosition).absoluteValue > xDistanceThreshold) {
                disallowScrolling()
            }
        }
        return super.onInterceptTouchEvent(event)
    }

    private fun disallowScrolling() {
        requestDisallowInterceptTouchEvent(true)
    }

    private fun allowScrolling() {
        requestDisallowInterceptTouchEvent(false)
    }

    companion object {
        // Factor of how large portion of the screen's smaller dimension the touch has to move
        // before scrolling is disabled due to the overall horizontal distance moved. Larger value
        // means less distance needs to be moved.
        private const val X_DISTANCE_FACTOR = 12f
    }
}
