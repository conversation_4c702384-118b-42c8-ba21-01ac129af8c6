package com.stt.android.workout.details.ads.hrbelt

import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.workout.details.HrBeltAdData
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Named

interface HrBeltAdDataLoader {
    val hrBeltAdStateFlow: StateFlow<ViewState<HrBeltAdData?>>
    suspend fun loadHrBeltAdData(workoutHeader: WorkoutHeader): StateFlow<ViewState<HrBeltAdData?>>
}

@ActivityRetainedScoped
class DefaultHrBeltAdDataLoader
@Inject constructor(
    @Named("HideAdFlag") private val hideAdFlag: Boolean,
    @Named("HasBeltPaired") private val hasBeltPaired: Boolean,
    private val currentUserController: CurrentUserController,
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope
) : HrBeltAdDataLoader {

    override val hrBeltAdStateFlow: MutableStateFlow<ViewState<HrBeltAdData?>> =
        MutableStateFlow(loading())

    override suspend fun loadHrBeltAdData(workoutHeader: WorkoutHeader): StateFlow<ViewState<HrBeltAdData?>> {
        activityRetainedCoroutineScope.launch {
            runSuspendCatching {
                getHrBeltAdData(workoutHeader)
            }.onFailure {
                Timber.w(it, "Getting HR belt ad data failed.")
                hrBeltAdStateFlow.value = loaded(HrBeltAdData(isVisible = false))
            }
        }
        return hrBeltAdStateFlow
    }

    private suspend fun getHrBeltAdData(workoutHeader: WorkoutHeader) = withContext(IO) {
        val hasHr = workoutHeader.heartRateAverage != 0.0
        val isCurrentUserWorkout = currentUserController.username == workoutHeader.username
        hrBeltAdStateFlow.value = loaded(
            HrBeltAdData(
                isVisible = !hasHr && !hideAdFlag && isCurrentUserWorkout && !hasBeltPaired
            )
        )
    }
}
