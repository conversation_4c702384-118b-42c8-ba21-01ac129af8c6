package com.stt.android.workout.details.suuntocoach

import androidx.annotation.StringRes
import com.stt.android.R

data class CoachValueItem(
    @StringRes val titleRes: Int,
    val value: Int?,
    val valueType: SwimmingPropertyType,
) {
    /**
     * return: swimming suggestion string resource id
     * if no suggestion, return 0
     */
    val feedbackRes: Int
        @StringRes get() {
            return when (valueType) {
                SwimmingPropertyType.BREASTSTROKE_AVG_BREATHE_ANGLE ->
                    when (value) {
                        in valueType.minThreshold..valueType.maxThreshold -> R.string.suunto_coach_breaststroke_avg_breath_angle_less_than_55
                        in valueType.maxThreshold..Int.MAX_VALUE -> R.string.suunto_coach_breaststroke_avg_breath_angle_more_than_55
                        else -> 0
                    }

                SwimmingPropertyType.FREESTYLE_AVG_BREATHE_ANGLE -> when (value) {
                    in valueType.minThreshold..valueType.maxThreshold -> R.string.suunto_coach_freestyle_avg_breath_angle_less_than_120
                    in valueType.maxThreshold..Int.MAX_VALUE -> R.string.suunto_coach_freestyle_avg_breath_angle_more_than_120
                    else -> 0
                }

                SwimmingPropertyType.FREESTYLE_PERCENT -> when (value) {
                    in valueType.minThreshold..valueType.maxThreshold -> R.string.suunto_coach_freestyle_percent_less_than_80
                    in valueType.maxThreshold..Int.MAX_VALUE -> R.string.suunto_coach_freestyle_percent_more_than_80
                    else -> 0
                }

                SwimmingPropertyType.BREASTSTROKE_PERCENT -> when (value) {
                    in valueType.minThreshold..valueType.maxThreshold -> R.string.suunto_coach_breaststroke_percent_less_than_80
                    in valueType.maxThreshold..Int.MAX_VALUE -> R.string.suunto_coach_breaststroke_percent_more_than_80
                    else -> 0
                }

                SwimmingPropertyType.BREASTSTROKE_MAX_BREATHE_ANGLE -> when (value) {
                    in valueType.minThreshold..valueType.maxThreshold -> R.string.suunto_coach_breaststroke_max_angle_less_than_70
                    in valueType.maxThreshold..Int.MAX_VALUE -> R.string.suunto_coach_breaststroke_max_angle_more_than_70
                    else -> 0
                }

                SwimmingPropertyType.BREASTSTROKE_HEAD_ANGLE -> when (value) {
                    in valueType.minThreshold..valueType.maxThreshold -> R.string.suunto_coach_breaststroke_head_angle_less_than_40
                    in valueType.maxThreshold..Int.MAX_VALUE -> R.string.suunto_coach_breaststroke_head_angle_more_than_40
                    else -> 0
                }

                SwimmingPropertyType.FREESTYLE_HEAD_ANGLE -> when (value) {
                    in valueType.minThreshold..valueType.maxThreshold -> R.string.suunto_coach_freestyle_head_angle_less_than_40
                    in valueType.maxThreshold..Int.MAX_VALUE -> R.string.suunto_coach_freestyle_head_angle_more_than_40
                    else -> 0
                }

                SwimmingPropertyType.FREESTYLE_MAX_BREATHE_ANGLE -> when (value) {
                    in valueType.minThreshold..valueType.maxThreshold -> R.string.suunto_coach_freestyle_max_angle_less_than_140
                    in valueType.maxThreshold..Int.MAX_VALUE -> R.string.suunto_coach_freestyle_max_angle_more_than_140
                    else -> 0
                }
            }
        }
}
