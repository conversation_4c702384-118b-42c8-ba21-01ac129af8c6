package com.stt.android.workout.details.share.colortrack

import com.stt.android.domain.sml.Sml
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.maps.colortrack.ColorTrackDescriptor
import com.stt.android.ui.map.MapHelper

class AltitudeColorTrackGenerator : ColorTrackGenerator {
    override fun generate(geoPoints: List<WorkoutGeoPoint>, sml: Sml?): ColorTrackDescriptor {
        if (geoPoints.isEmpty()) return ColorTrackDescriptor.from(emptyList())

        val buckets = Bucket.createBucketsFromList(
            geoPoints,
            value = { altitude },
            timestamp = { timestamp },
            pauseResumeTimes = with(MapHelper) {
                sml?.streamData?.findPauseResumeTimePairs()
            },
        )
        return ColorTrackDescriptor.from(geoPoints, buckets)
    }
}
