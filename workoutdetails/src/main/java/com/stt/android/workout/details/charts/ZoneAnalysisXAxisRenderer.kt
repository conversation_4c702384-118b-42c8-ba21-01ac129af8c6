package com.stt.android.workout.details.charts

import android.graphics.Canvas
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.renderer.XAxisRenderer
import com.github.mikephil.charting.utils.MPPointF
import com.github.mikephil.charting.utils.Transformer
import com.github.mikephil.charting.utils.Utils
import com.github.mikephil.charting.utils.ViewPortHandler

/**
 * This renderer adds custom tick marks for each label on the X-axis, except for the first and last labels.
 */
class ZoneAnalysisXAxisRenderer(
    viewPortHandler: ViewPortHandler,
    xAxis: XAxis,
    trans: Transformer
) : XAxis<PERSON>enderer(viewPortHandler, xAxis, trans) {

    override fun renderAxisLine(c: Canvas) {
        if (!mXAxis.isEnabled || !mXAxis.isDrawAxisLineEnabled) {
            return
        }

        val tickLength = Utils.convertDpToPixel(4f)
        mAxisLinePaint.color = mXAxis.axisLineColor
        mAxisLinePaint.strokeWidth = mXAxis.axisLineWidth

        // Draw ticks for each label except the first and last
        val labelCount = mXAxis.mEntryCount
        val positions = FloatArray(labelCount * 2)
        for (i in 0 until labelCount) {
            positions[i * 2] = mXAxis.mEntries[i]
        }

        // Transform data values to pixel coordinates
        mTrans.pointValuesToPixel(positions)

        for (i in positions.indices step 2) {
            if (i == 0 || i == positions.size - 2) {
                continue
            }
            val xPos = positions[i]
            val tickStart = mViewPortHandler.contentBottom()
            val tickEnd = tickStart + tickLength
            c.drawLine(xPos, tickStart, xPos, tickEnd, mAxisLinePaint)
        }
    }

    override fun drawLabel(
        c: Canvas,
        formattedLabel: String,
        x: Float,
        y: Float,
        anchor: MPPointF,
        angleDegrees: Float
    ) {
        val textWidth = Utils.calcTextWidth(mAxisLabelPaint, formattedLabel)
        val canvasWidth = c.width
        val offsetX = anchor.x * textWidth
        val translatedX = if (x - offsetX + textWidth > canvasWidth) {
            (canvasWidth - textWidth + offsetX).toFloat()
        } else x
        super.drawLabel(c, formattedLabel, translatedX, y, anchor, angleDegrees)
    }
}
