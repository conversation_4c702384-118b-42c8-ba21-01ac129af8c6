package com.stt.android.workout.details.share.video

import android.content.res.Resources
import androidx.annotation.DrawableRes
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.ExitTransition
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.layout
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import coil3.request.transformations
import coil3.size.Size
import coil3.transform.CircleCropTransformation
import com.stt.android.coil.placeholderWithFallback
import com.stt.android.compose.component.SuuntoActivityIcon
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyBold
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.material3.bodyMegaBold
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.workout.ActivityType
import com.stt.android.infomodel.SummaryItem
import com.stt.android.workout.details.R
import com.stt.android.workouts.details.values.WorkoutValue
import com.suunto.connectivity.suuntoconnectivity.device.ProductType
import kotlinx.coroutines.delay
import kotlin.math.roundToInt
import com.stt.android.R as BR
import com.stt.android.core.R as CR

@Composable
internal fun VideoShareInfoView(
    data: VideoShareInfoData,
    options: WorkoutMapPlaybackOptions,
    modifier: Modifier = Modifier,
    onClick: (() -> Unit)? = null,
    showEditIcon: Boolean = false,
    rowDataAnimation: VideoShareInfoRowAnimation = VideoShareInfoRowAnimation.HIDE,
    onHideEditIcon: () -> Unit = {},
) {
    var rowIndex by remember(rowDataAnimation, data.rowData) { mutableIntStateOf(-1) }

    Column(modifier = modifier) {
        Row(
            modifier = Modifier
                .then(onClick?.let { Modifier.clickable(onClick = it) } ?: Modifier)
                .fillMaxWidth()
                .defaultMinSize(minHeight = 54.dp)
                .padding(MaterialTheme.spacing.medium),
            horizontalArrangement = Arrangement.spacedBy(10.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            AvatarView(imageUrl = data.imageUrl ?: "")
            Box(modifier = Modifier.weight(1f)) {
                Column(
                    modifier = Modifier
                        .align(Alignment.CenterStart)
                        .fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
                ) {
                    if (options.username) {
                        Text(
                            text = data.username,
                            style = MaterialTheme.typography.bodyBold,
                            color = Color.White,
                        )
                    }
                    if (options.startTime || (options.location && data.location != null)) {
                        IconLabelRow(
                            iconRes = data.activityType.iconId,
                            text = buildList {
                                if (options.startTime) {
                                    add(data.datetime)
                                }
                                if (options.location) {
                                    data.location?.let { add(it) }
                                }
                            }.joinToString(" • "),
                            iconSize = 20.dp,
                        )
                    }
                    if (options.device) {
                        val productName = data.productName
                        val productType = data.productType
                        if (!productName.isNullOrBlank() && productType != null) {
                            IconLabelRow(
                                iconRes = productType.deviceIconFromProductType(),
                                text = productName,
                            )
                        }
                    }
                }
                AnimatedEditIcon(
                    modifier = Modifier
                        .padding(end = 20.dp)
                        .align(Alignment.CenterEnd),
                    showEditIcon = showEditIcon,
                )
            }
            Image(
                modifier = Modifier.heightIn(max = 25.5.dp),
                painter = painterResource(BR.drawable.sportie_logo),
                contentDescription = null,
            )
        }
        data.rowData.forEachIndexed { index, value ->
            AnimatedVisibility(
                visible = index <= rowIndex,
                label = "Row $index",
                enter = fadeIn(),
                exit = ExitTransition.None,
            ) {
                VideoShareInfoRow(
                    workoutValue = value,
                    activityType = data.activityType,
                )
            }
        }
    }

    LaunchedEffect(showEditIcon) {
        if (showEditIcon) {
            delay(1000L)
            onHideEditIcon()
        }
    }

    LaunchedEffect(rowDataAnimation, data.rowData) {
        when (rowDataAnimation) {
            VideoShareInfoRowAnimation.FADE_IN -> {
                data.rowData.indices.forEach {
                    rowIndex = it
                    delay(500L)
                }
            }

            VideoShareInfoRowAnimation.FADE_OUT -> {
                data.rowData.indices.reversed().forEach {
                    rowIndex = it
                    delay(500L)
                }
            }

            VideoShareInfoRowAnimation.SHOW -> {
                rowIndex = data.rowData.size - 1
            }

            VideoShareInfoRowAnimation.HIDE -> Unit
        }
    }
}

@Composable
private fun VideoShareInfoRow(
    workoutValue: WorkoutValue,
    activityType: ActivityType,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(MaterialTheme.spacing.medium),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        if (workoutValue.item == SummaryItem.DISTANCE) {
            SuuntoActivityIcon(activityTypeId = activityType.id)
        } else {
            Icon(
                modifier = Modifier.size(MaterialTheme.iconSizes.medium),
                painter = painterResource(workoutValue.item!!.iconRes),
                tint = Color.White,
                contentDescription = null,
            )
        }
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
        ) {
            Text(
                text = workoutValue.formatValue(context.resources),
                style = with(MaterialTheme.typography) {
                    if (workoutValue.item == SummaryItem.DISTANCE) bodyMegaBold else bodyLargeBold
                },
                color = Color.White,
            )
            Text(
                text = workoutValue.label,
                style = MaterialTheme.typography.body,
                color = Color.White,
            )
        }
    }
}

@Composable
private fun AvatarView(imageUrl: String, modifier: Modifier = Modifier) {
    val size = with(LocalDensity.current) { MaterialTheme.iconSizes.large.toPx() }.roundToInt()
    AsyncImage(
        model = ImageRequest.Builder(LocalContext.current)
            .data(imageUrl)
            .crossfade(true)
            .size(Size(size, size))
            .placeholderWithFallback(
                LocalContext.current,
                CR.drawable.ic_default_profile_image_light
            )
            .transformations(CircleCropTransformation())
            .build(),
        contentDescription = null,
        modifier = modifier
            .clip(CircleShape)
            .size(MaterialTheme.iconSizes.large),
    )
}

@Composable
private fun IconLabelRow(
    @DrawableRes iconRes: Int,
    text: String,
    modifier: Modifier = Modifier,
    iconSize: Dp = MaterialTheme.iconSizes.mini,
) {
    val iconSizePx = with(LocalDensity.current) { iconSize.toPx() }.roundToInt()
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        SuuntoActivityIcon(
            modifier = Modifier
                .size(MaterialTheme.iconSizes.mini)
                .layout { measurable, constraints ->
                    val placeable = measurable.measure(
                        constraints.copy(
                            minWidth = iconSizePx,
                            maxWidth = iconSizePx,
                            minHeight = iconSizePx,
                            maxHeight = iconSizePx,
                        )
                    )
                    layout(placeable.width, placeable.height) {
                        placeable.placeRelative(0, 0)
                    }
                },
            iconRes = iconRes,
            tint = Color.White,
            background = Color.Transparent,
            iconSize = MaterialTheme.iconSizes.mini,
        )
        Text(
            modifier = Modifier.weight(1f),
            text = text,
            style = MaterialTheme.typography.labelMedium,
            color = Color.White,
        )
    }
}

@Composable
private fun AnimatedEditIcon(
    showEditIcon: Boolean,
    modifier: Modifier = Modifier,
) {
    AnimatedVisibility(
        modifier = modifier,
        visible = showEditIcon,
        label = "Edit icon",
        enter = fadeIn(),
        exit = fadeOut(),
    ) {
        Image(
            painter = painterResource(BR.drawable.ic_edit_small),
            contentDescription = null,
        )
    }
}

data class VideoShareInfoData(
    val imageUrl: String?,
    val username: String,
    val activityType: ActivityType,
    val datetime: String,
    val location: String?,
    val productName: String?,
    val productType: ProductType?,
    val rowData: List<WorkoutValue>,
)

enum class VideoShareInfoRowAnimation {
    FADE_IN,
    FADE_OUT,
    SHOW,
    HIDE,
}

private fun ProductType.deviceIconFromProductType(): Int = when (this) {
    ProductType.SPORT_WATCH,
    ProductType.DIVE_WATCH -> R.drawable.ic_device_watch_share

    ProductType.DIVE_COMPUTER -> R.drawable.ic_device_dive_computer_share
    ProductType.BIKE_COMPUTER -> R.drawable.ic_device_bike_computer_share
    ProductType.SPORT_EARPHONE -> R.drawable.ic_device_sport_earphone_share
}

private val SummaryItem.iconRes: Int
    get() = when (this) {
        SummaryItem.DURATION -> R.drawable.ic_summary_duration_share
        SummaryItem.AVGPACE,
        SummaryItem.AVGSPEED -> R.drawable.ic_summary_avg_pace_share

        else -> throw IllegalArgumentException("Not supported")
    }

private fun WorkoutValue.formatValue(resources: Resources): String {
    return listOfNotNull(
        value?.takeIf { it.isNotBlank() },
        unitString?.takeIf { it.isNotBlank() } ?: unit?.let { resources.getString(it) },
    ).joinToString(" ")
}

@Preview
@Composable
private fun VideoShareInfoViewPreview() {
    VideoShareInfoView(
        data = VideoShareInfoData(
            imageUrl = "https://example.com/image.jpg",
            username = "John Doe",
            activityType = ActivityType.MULTISPORT,
            datetime = "2021-01-01 12:00",
            location = "New York",
            productName = "Suunto 9",
            productType = null,
            rowData = emptyList(),
        ),
        options = WorkoutMapPlaybackOptions(
            username = true,
            startTime = true,
            location = true,
            device = true,
        ),
        showEditIcon = true,
    )
}
