package com.stt.android.workout.details.graphanalysis.typeselection

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.stt.android.core.domain.GraphType
import com.stt.android.ui.utils.SmartBottomSheetDialogFragment
import com.stt.android.workout.details.databinding.GraphAnalysisTypeSelectionDialogBinding

class GraphTypeSelectionDialogFragment : SmartBottomSheetDialogFragment() {
    private lateinit var binding: GraphAnalysisTypeSelectionDialogBinding

    private lateinit var mainGraphController: GraphTypeSelectionListController
    private lateinit var comparisonGraphController: GraphTypeSelectionListController
    private lateinit var backgroundGraphController: GraphTypeSelectionListController

    private var selectionsChanged: Boolean = false
    private var onSelectionsChangedListener: OnSelectionsChangedListener? = null

    private var availableGraphTypes: List<GraphType> = emptyList()
    private var selectedMainGraph: GraphType = GraphType.NONE
    private var selectedComparisonGraph: GraphType = GraphType.NONE
    private var selectedBackgroundGraph: GraphType = GraphType.NONE

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = GraphAnalysisTypeSelectionDialogBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        (dialog as? BottomSheetDialog)?.behavior?.state = BottomSheetBehavior.STATE_EXPANDED

        mainGraphController = GraphTypeSelectionListController(requireContext())
        comparisonGraphController = GraphTypeSelectionListController(requireContext())
        backgroundGraphController = GraphTypeSelectionListController(requireContext())

        with(binding) {
            fun applyAnimationDurationMultiplier(animator: RecyclerView.ItemAnimator?) {
                animator?.let {
                    it.removeDuration = (it.removeDuration * ANIMATION_DURATION_MULTIPLIER).toLong()
                    it.addDuration = (it.addDuration * ANIMATION_DURATION_MULTIPLIER).toLong()
                }
            }

            mainGraphOptionsList.setController(mainGraphController)
            applyAnimationDurationMultiplier(mainGraphOptionsList.itemAnimator)

            comparisonGraphOptionsList.setController(comparisonGraphController)
            applyAnimationDurationMultiplier(comparisonGraphOptionsList.itemAnimator)

            backgroundGraphOptionsList.setController(backgroundGraphController)
            applyAnimationDurationMultiplier(backgroundGraphOptionsList.itemAnimator)
        }

        updateControllerDataLists()
    }

    override fun onPause() {
        super.onPause()
        informOnSelectionChangedListener()
    }

    override fun onCancel(dialog: DialogInterface) {
        super.onCancel(dialog)
        informOnSelectionChangedListener()
    }

    fun setGraphTypeInfo(graphTypeInfo: WorkoutGraphAnalysisInfo) {
        availableGraphTypes = graphTypeInfo.supportedGraphTypes
        selectedMainGraph = graphTypeInfo.mainGraphType
        selectedComparisonGraph = graphTypeInfo.comparisonGraphType
        selectedBackgroundGraph = graphTypeInfo.backgroundGraphType
        selectionsChanged = false

        // If called after view has been created, update Controllers.
        // Otherwise let view creation handle creating the Controllers with initial data
        if (view != null) {
            updateControllerDataLists()
        }
    }

    fun setOnSelectionsChangedListener(listener: OnSelectionsChangedListener) {
        onSelectionsChangedListener = listener
    }

    private fun updateControllerDataLists(isSelectionChange: Boolean = false) {
        if (isSelectionChange) {
            selectionsChanged = true
        }

        val availableMainGraphTypes = buildList {
            addAll(availableGraphTypes)
            remove(selectedComparisonGraph)
            remove(selectedBackgroundGraph)

            if (selectedMainGraph == GraphType.NONE) {
                add(0, GraphType.NONE)
            }
        }
        mainGraphController.setData(
            availableMainGraphTypes.map { graphType ->
                GraphTypeSelectionData(
                    graphType = graphType,
                    selected = graphType == selectedMainGraph,
                    onClick = { clicked ->
                        if (clicked.graphType != selectedMainGraph) {
                            selectedMainGraph = clicked.graphType
                            updateControllerDataLists(isSelectionChange = true)
                        }
                    }
                )
            }
        )

        val availableComparisonGraphTypes = buildList {
            addAll(availableGraphTypes)
            remove(selectedMainGraph)
            remove(selectedBackgroundGraph)
            add(0, GraphType.NONE)
        }
        comparisonGraphController.setData(
            availableComparisonGraphTypes.map { graphType ->
                GraphTypeSelectionData(
                    graphType = graphType,
                    selected = graphType == selectedComparisonGraph,
                    onClick = { clicked ->
                        if (clicked.graphType != selectedComparisonGraph) {
                            selectedComparisonGraph = clicked.graphType
                            updateControllerDataLists(isSelectionChange = true)
                        }
                    }
                )
            }
        )

        val availableBackgroundGraphTypes = buildList {
            addAll(availableGraphTypes)
            remove(selectedMainGraph)
            remove(selectedComparisonGraph)
            add(0, GraphType.NONE)
        }
        backgroundGraphController.setData(
            availableBackgroundGraphTypes.map { graphType ->
                GraphTypeSelectionData(
                    graphType = graphType,
                    selected = graphType == selectedBackgroundGraph,
                    onClick = { clicked ->
                        if (clicked.graphType != selectedBackgroundGraph) {
                            selectedBackgroundGraph = clicked.graphType
                            updateControllerDataLists(isSelectionChange = true)
                        }
                    }
                )
            }
        )
    }

    private fun informOnSelectionChangedListener() {
        if (selectionsChanged) {
            onSelectionsChangedListener?.onSelectionsChanged(
                selectedMainGraph,
                selectedComparisonGraph,
                selectedBackgroundGraph
            )
        }
        onSelectionsChangedListener = null
    }

    fun interface OnSelectionsChangedListener {
        fun onSelectionsChanged(
            selectedMainGraph: GraphType,
            selectedComparisonGraph: GraphType,
            selectedBackgroundGraph: GraphType
        )
    }

    companion object {
        const val FRAGMENT_TAG =
            "com.stt.android.workout.details.graphanalysis.typeselection.TypeSelectionDialogFragment"

        private const val ANIMATION_DURATION_MULTIPLIER = 1.5
    }
}
