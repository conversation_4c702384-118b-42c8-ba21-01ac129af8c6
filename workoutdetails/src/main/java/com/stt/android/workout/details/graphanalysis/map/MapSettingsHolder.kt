package com.stt.android.workout.details.graphanalysis.map

import com.stt.android.maps.MapType

class MapSettingsHolder(
    _mapType: MapType,
    _map3dEnabled: Boolean
) {
    var mapType = _mapType
        private set
    private var updateMapType: Boolean = true

    var map3dEnabled = _map3dEnabled
    private var updateMap3dEnabled: Boolean = true

    var forceSkiMap = false
        private set

    fun onMapTypeChanged(mapType: MapType) {
        if (updateMapType) {
            this.mapType = mapType
        }
        updateMapType = true
    }

    fun onDeveloperMapTypeChange(forceSkiMap: Boolean = false) {
        if (forceSkiMap) {
            this.forceSkiMap = forceSkiMap
        }
        updateMapType = false
    }

    fun onMap3dEnabledChanged(enabled: Boolean) {
        if (updateMap3dEnabled) {
            this.map3dEnabled = enabled
        }
        updateMap3dEnabled = true
    }

    fun onDeveloperMap3dEnabledChange() {
        updateMap3dEnabled = false
    }
}
