package com.stt.android.workout.details

import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.google.maps.android.PolyUtil
import com.stt.android.cardlist.MapCard
import com.stt.android.cardlist.MultisportMapCard
import com.stt.android.cardlist.TraverseMapCard
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.sml.TraverseEvent
import kotlinx.coroutines.withContext
import javax.inject.Inject

class MapCardCreator @Inject constructor(
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : MapCard.Factory, MultisportMapCard.Factory, TraverseMapCard.Factory {
    override suspend fun create(
        polyline: String,
        activityRoutes: List<List<LatLng>>,
        nonActivityRoutes: List<List<LatLng>>,
    ): MapCard = withContext(coroutinesDispatchers.computation) {
        val decodedPolyline = PolyUtil.decode(polyline)

        object : MapCard {
            override val polylineHashCode: Int
                get() = polyline.hashCode()
            override val bounds: LatLngBounds
                get() {
                    return LatLngBounds.builder().apply {
                        route.forEach { include(it) }
                    }.build()
                }
            override val route: List<LatLng> = decodedPolyline
            override val activityRoutes: List<List<LatLng>> = activityRoutes
            override val nonActivityRoutes: List<List<LatLng>> = nonActivityRoutes

            override fun equals(other: Any?): Boolean {
                if (this === other) return true
                if (javaClass != other?.javaClass) return false

                other as MapCard

                if (route != other.route) return false
                if (activityRoutes != other.activityRoutes) return false
                if (nonActivityRoutes != other.nonActivityRoutes) return false

                return true
            }

            override fun hashCode() = route.hashCode()
        }
    }

    override suspend fun create(
        polyline: String,
        routes: List<List<LatLng>>,
        indexOfHighlightedRoute: Int,
        activityRoutes: List<List<LatLng>>,
        nonActivityRoutes: List<List<LatLng>>,
    ): MapCard = withContext(coroutinesDispatchers.computation) {
        val decodedPolyline = PolyUtil.decode(polyline)

        object : MapCard, MultisportMapCard {
            override val polylineHashCode: Int
                get() = polyline.hashCode()
            override val bounds: LatLngBounds
                get() {
                    return LatLngBounds.builder().apply {
                        route.forEach { include(it) }
                    }.build()
                }
            override val route: List<LatLng> = decodedPolyline
            override val routes: List<List<LatLng>> = routes
            override val indexOfHighlightedRoute = indexOfHighlightedRoute
            override val activityRoutes: List<List<LatLng>> = activityRoutes
            override val nonActivityRoutes: List<List<LatLng>> = nonActivityRoutes

            override fun equals(other: Any?): Boolean {
                if (this === other) return true
                if (javaClass != other?.javaClass) return false

                other as MultisportMapCard

                if (route != other.route) return false
                if (routes != other.routes) return false
                if (indexOfHighlightedRoute != other.indexOfHighlightedRoute) return false
                return true
            }

            override fun hashCode(): Int {
                var result = route.hashCode()
                result = 31 * result + routes.hashCode()
                result = 31 * result + indexOfHighlightedRoute
                return result
            }
        }
    }

    override suspend fun create(
        polyline: String,
        traverseEvents: List<TraverseEvent>,
    ): MapCard = withContext(coroutinesDispatchers.computation) {
        val decodedPolyline = PolyUtil.decode(polyline)

        object : MapCard, TraverseMapCard {
            override val polylineHashCode: Int
                get() = polyline.hashCode()
            override val bounds: LatLngBounds
                get() {
                    return LatLngBounds.builder().apply {
                        route.forEach { include(it) }
                    }.build()
                }
            override val route: List<LatLng> = decodedPolyline
            override val activityRoutes: List<List<LatLng>>? = null
            override val nonActivityRoutes: List<List<LatLng>>? = null
            override val traverseEvents: List<TraverseEvent> = traverseEvents
        }
    }

    override suspend fun create(
        position: LatLng,
    ): MapCard = withContext(coroutinesDispatchers.computation) {
        object : MapCard {
            override val polylineHashCode: Int = position.hashCode()
            override val bounds: LatLngBounds
                get() = LatLngBounds.builder().apply {
                    include(position)
                }.build()

            override val route: List<LatLng> = listOf(position)
            override val activityRoutes: List<List<LatLng>>? = null
            override val nonActivityRoutes: List<List<LatLng>>? = null

            override fun equals(other: Any?): Boolean {
                if (this === other) return true
                if (javaClass != other?.javaClass) return false

                other as MapCard
                return route == other.route
            }

            override fun hashCode(): Int = route.hashCode()
        }
    }
}
