package com.stt.android.workout.details.graphanalysis.playback

import com.google.maps.android.SphericalUtil
import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.viewstate.ViewState
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.utils.sumByFloat
import com.stt.android.workout.details.multisport.MultisportPartActivityLoader
import com.stt.android.workout.details.sml.SmlDataLoader
import com.stt.android.workout.details.workoutdata.WorkoutDataLoader
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import javax.inject.Inject

@ActivityRetainedScoped
class WorkoutPlaybackGeopointLoader @Inject constructor(
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope,
    private val dispatchers: CoroutinesDispatchers,
    private val workoutDataLoader: WorkoutDataLoader,
    private val smlDataLoader: SmlDataLoader,
    private val multisportPartActivityLoader: MultisportPartActivityLoader
) {
    private var loadJob: Job? = null
    private val sharedGeoPointFlow = MutableSharedFlow<List<WorkoutGeoPoint>>(
        replay = 1,
        extraBufferCapacity = 1,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )

    fun observeGeoPointForPlayback(): Flow<List<WorkoutGeoPoint>> {
        loadGeoPoints()
        return sharedGeoPointFlow.asSharedFlow()
    }

    @Synchronized
    private fun loadGeoPoints() {
        if (loadJob == null || loadJob?.isActive == false) {
            loadJob = activityRetainedCoroutineScope.launch(dispatchers.computation) {
                val multisportStateWithSmlStateFlow =
                    multisportPartActivityLoader.multisportPartActivityFlow
                        .filter { it.isLoaded() }
                        .flatMapLatest { multisportState ->
                            // SML is needed only for multisport part windows, skip loading it
                            // for whole workouts
                            if (multisportState.data != null) {
                                smlDataLoader.smlStateFlow
                                    .filter { it.isLoaded() }
                                    .map {
                                        multisportState to it
                                    }
                            } else {
                                flowOf(multisportState to ViewState.Loaded(null))
                            }
                        }

                combine(
                    workoutDataLoader.workoutDataStateFlow.filter { it.isLoaded() },
                    multisportStateWithSmlStateFlow
                ) { dataState, multisportAndSmlStates ->
                    Triple(dataState, multisportAndSmlStates.first, multisportAndSmlStates.second)
                }.distinctUntilChanged { old, new ->
                    // WorkoutData and SML should always be same cached instances,
                    // just check reference equality
                    old.first.data?.routePoints === new.first.data?.routePoints &&
                        old.second.data?.elapsed == new.second.data?.elapsed &&
                        old.third.data === new.third.data
                }.map { (dataState, multisportPartState, smlState) ->
                    val data = dataState.data
                    val multisportPart = multisportPartState.data
                    val sml = smlState.data

                    if (data != null) {
                        processRoutePoints(data.routePoints, sml, multisportPart)
                    } else {
                        emptyList()
                    }
                }.collect {
                    sharedGeoPointFlow.emit(it)
                }
            }
        }
    }

    private fun processRoutePoints(
        routePoints: List<WorkoutGeoPoint>,
        sml: Sml?,
        multisportPartActivity: MultisportPartActivity?,
    ): List<WorkoutGeoPoint> {
        val (partRoutePoints, elapsedAtStartOfPart) = if (multisportPartActivity != null) {
            val partRoutePoints = routePoints.filter {
                it.timestamp in multisportPartActivity.startTime..multisportPartActivity.stopTime
            }

            val durationOfPreviousParts =
                sml?.getActivityWindow(multisportPartActivity)?.let { partWindow ->
                    val previousWindows = sml.summary.activityWindows.takeWhile { it != partWindow }
                    previousWindows.sumByFloat { it.duration ?: 0f }.times(1000).toLong()
                } ?: multisportPartActivity.elapsed ?: 0L

            partRoutePoints to durationOfPreviousParts
        } else {
            routePoints to 0L
        }

        // With some workouts the data includes negative or otherwise inaccurate distances that
        // can break the playback, recalculate distances and total distances to make sure the
        // data is what playback expects
        val distanceRecalculatedRoutePoints = mutableListOf<WorkoutGeoPoint>()

        partRoutePoints.forEach { routePoint ->
            val previous = distanceRecalculatedRoutePoints.lastOrNull()
            val (actualDistance, actualTotalDistance) = if (previous != null) {
                val distance =
                    SphericalUtil.computeDistanceBetween(previous.latLng, routePoint.latLng)
                distance to previous.totalDistance + distance
            } else {
                0.0 to 0.0
            }

            distanceRecalculatedRoutePoints.add(
                WorkoutGeoPoint(
                    routePoint.latitudeE6,
                    routePoint.longitudeE6,
                    routePoint.altitude,
                    routePoint.hasAltitude(),
                    routePoint.speedMetersPerSecond,
                    actualDistance,
                    routePoint.millisecondsInWorkout.toDouble() - elapsedAtStartOfPart,
                    actualTotalDistance,
                    routePoint.course,
                    routePoint.timestamp
                )
            )
        }

        return distanceRecalculatedRoutePoints.toList()
    }

    fun resetUpstreamCaches(workoutHeader: WorkoutHeader) {
        workoutDataLoader.resetCache()
        workoutDataLoader.loadWorkoutData(workoutHeader)

        smlDataLoader.resetCache()
        smlDataLoader.loadSml(workoutHeader)
    }

    fun reset() {
        sharedGeoPointFlow.resetReplayCache()
        loadJob?.cancel()
        loadJob = null
    }
}
