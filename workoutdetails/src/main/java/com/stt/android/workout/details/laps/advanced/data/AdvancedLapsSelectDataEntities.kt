package com.stt.android.workout.details.laps.advanced.data

import com.stt.android.core.domain.AdvancedLapsSelectDataCategoryItem
import com.stt.android.core.domain.LapsTableDataType
import com.stt.android.domain.advancedlaps.LapsTableType

typealias OnClicked = (columnIndex: Int, dataType: LapsTableDataType) -> Unit

data class AdvancedLapsSelectDataType constructor(
    val dataType: LapsTableDataType,
    val isSelected: Boolean,
    val columnIndex: Int,
    val lapsTableType: LapsTableType,
    val onClicked: OnClicked
)

data class AdvancedLapsSelectDataContainer(
    val map: Map<AdvancedLapsSelectDataCategoryItem, List<AdvancedLapsSelectDataType>>
)

data class SelectedSummaryItemData constructor(
    val columnIndex: Int,
    val dataType: LapsTableDataType,
    val previousDataType: LapsTableDataType
)
