package com.stt.android.workout.details.graphanalysis

import android.os.Parcelable
import com.stt.android.domain.advancedlaps.LapsTableRow
import com.stt.android.domain.advancedlaps.LapsTableType
import kotlinx.parcelize.Parcelize

@Parcelize
class GraphAnalysisSelections(
    val workoutTimeInMillis: Long,
    val lapSelection: LapSelection?
) : Parcelable {
    sealed class LapSelection : Parcelable

    @Parcelize
    class LapsTableRowSelection(
        val lapsTableType: LapsTableType,
        val lapsTableRow: LapsTableRow
    ) : LapSelection()

    @Parcelize
    class TimeWindowLapSelection(
        val startSeconds: Float,
        val endSeconds: Float
    ) : LapSelection()
}
