package com.stt.android.workout.details.charts

import androidx.tracing.Trace
import com.github.mikephil.charting.data.Entry
import timber.log.Timber
import kotlin.math.pow

// Based on DouglasPeckerPointReducer.kt from android-wear-app
// https://bitbucket.org/suunto/android-wear-app/src/develop/historyrequest/src/main/java/com/soy/android/historyrequest/entities/DouglasPeckerPointReducer.kt
class DouglasPeuckerPointReducer {

    fun reduce(initialList: List<Entry>, maxPoints: Int): List<Entry> {
        Trace.beginSection("DouglasPeckerReducer_${initialList.size}")
        var toleranceSquared = 0.01
        var resultList = initialList

        while (resultList.size > maxPoints) {
            toleranceSquared *= 2
            resultList = simplifyDouglasPeucker(resultList, toleranceSquared)
        }
        Trace.endSection()
        Timber.d("Reduced from ${initialList.size} to ${resultList.size} entries, tolerance=$toleranceSquared")
        return resultList
    }

    /**
     * Reduces amount of points, see https://en.wikipedia.org/wiki/Ramer%E2%80%93Douglas%E2%80%93Peucker_algorithm
     */
    private fun simplifyDouglasPeucker(points: List<Entry>, sqTolerance: Double): List<Entry> {
        var first = 0
        var last = points.size - 1
        val stackList: MutableList<Int> = mutableListOf()
        val newPoints: MutableList<Entry> = mutableListOf()
        val markers = MutableList(points.size) { 0 }
        markers[0] = 1
        markers[points.size - 1] = 1

        while (last > 0) {
            var maxSqDist = 0.0
            var index = 0

            for (i in first + 1 until last) {
                val sqDist = getSqSegDist(points[i], points[first], points[last])
                if (sqDist > maxSqDist) {
                    index = i
                    maxSqDist = sqDist
                }
            }

            if (maxSqDist > sqTolerance) {
                markers[index] = 1
                stackList.addAll(listOf(first, index, index, last))
            }

            if (stackList.size < 2) {
                break
            }

            last = stackList.removeAt(stackList.lastIndex)
            first = stackList.removeAt(stackList.lastIndex)
        }

        for (i in points.indices) {
            if (markers[i] == 1) {
                newPoints.add(points[i])
            }
        }

        return newPoints
    }

    /**
     * Square distance from a point to a segment, disregarding x component since the units are not comparable
     */
    private fun getSqSegDist(p: Entry, p1: Entry, p2: Entry): Double {
        val x = p1.x
        val y = p1.y
        val dx = p2.x - x
        val dy = p2.y - y
        val yCrossDelta = p2.y - dy / dx * (p2.x - p.x) - p.y
        return yCrossDelta.toDouble().pow(2.0)
    }
}
