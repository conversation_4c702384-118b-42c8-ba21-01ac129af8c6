package com.stt.android.workout.details.graphanalysis.laps

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import com.airbnb.epoxy.LifecycleAwareEpoxyViewBinder
import com.airbnb.epoxy.ModelCollector
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.stt.android.common.ui.observeK
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loading
import com.stt.android.coroutines.launchOnLifecycle
import com.stt.android.domain.advancedlaps.LapsTableRow
import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.workout.details.AdvancedLapsData
import com.stt.android.workout.details.R
import com.stt.android.workout.details.advancedlaps.advancedLapMarkers
import com.stt.android.workout.details.databinding.AnalysisLapMarkerSelectionDialogFragmentBinding
import com.stt.android.workout.details.laps.advanced.AdvancedLapsViewModel
import com.stt.android.workout.details.workoutDetailsItemLoading
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import javax.inject.Inject

@AndroidEntryPoint
class AnalysisLapMarkerSelectionDialogFragment : BottomSheetDialogFragment() {

    @Inject
    internal lateinit var infoModelFormatter: InfoModelFormatter

    // The Activity scoped ViewModel isn't used on purpose to have our separate
    // state for opened intervals and such
    private val advancedLapsViewModel: AdvancedLapsViewModel by viewModels()

    private var _binding: AnalysisLapMarkerSelectionDialogFragmentBinding? = null
    private val binding: AnalysisLapMarkerSelectionDialogFragmentBinding
        get() = _binding!!
    private var epoxyViewBinder: LifecycleAwareEpoxyViewBinder? = null

    private var listener: OnAnalysisLapsTableTypeSelectedListener? = null
    private var selectLapsTableType: LapsTableType? = null
    private var selectLapsTableRow: LapsTableRow? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding =
            AnalysisLapMarkerSelectionDialogFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        (dialog as BottomSheetDialog).apply {
            behavior.skipCollapsed = true
            behavior.state = BottomSheetBehavior.STATE_EXPANDED
        }
        advancedLapsViewModel.setSelectedLap(selectLapsTableType, selectLapsTableRow)
        epoxyViewBinder = LifecycleAwareEpoxyViewBinder(
            lifecycleOwner = this,
            rootView = { binding.root },
            viewId = R.id.laps_fragment_container,
            useVisibilityTracking = false,
            fallbackToNameLookup = false,
            modelProvider = {
                addLaps(advancedLapsViewModel.viewState.value ?: loading(null))
            }
        )

        advancedLapsViewModel.viewState.observeK(viewLifecycleOwner) {
            epoxyViewBinder?.invalidate()
        }

        advancedLapsViewModel.loadData(useAnalysisLaps = true)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        epoxyViewBinder = null
        _binding = null
    }

    fun setOnAnalysisLapsTableTypeSelectedListener(listener: OnAnalysisLapsTableTypeSelectedListener) {
        this.listener = listener
    }

    fun setSelectedLap(lapsTableType: LapsTableType?, lapsTableRow: LapsTableRow?) {
        this.selectLapsTableType = lapsTableType
        this.selectLapsTableRow = lapsTableRow
    }

    private fun onLapSelected(lapsTableType: LapsTableType) {
        listener?.onLapsTableTypeSelected(lapsTableType)

        // Delayed dismiss to give time for ripple effect for the selected item and map to update
        launchOnLifecycle {
            delay(
                requireContext().resources.getInteger(android.R.integer.config_shortAnimTime)
                    .toLong()
            )
            dismiss()
        }
    }

    private fun ModelCollector.addLaps(
        advancedLapsDataViewState: ViewState<AdvancedLapsData?>
    ) {
        if (advancedLapsDataViewState.isLoading()) {
            addLapsLoadingModel()
        } else {
            addAdvancedLaps(advancedLapsDataViewState)
        }
    }

    private fun ModelCollector.addAdvancedLaps(advancedLapsDataViewState: ViewState<AdvancedLapsData?>) {
        when (advancedLapsDataViewState) {
            is ViewState.Error -> {
            } // do nothing
            is ViewState.Loading -> addLapsLoadingModel()
            is ViewState.Loaded -> {
                val data = advancedLapsDataViewState.data ?: return
                if (data.container.tables.isNotEmpty()) {
                    advancedLapMarkers {
                        id("laps_fragment_container")
                        stId(data.stId)
                        lapTables(data.container.tables)
                        infoModelFormatter(infoModelFormatter)
                        selectLapsTableType(selectLapsTableType)
                        onLapsTableTypeSelected { lapsTableType ->
                            onLapSelected(lapsTableType)
                        }
                        lapMarkerEnabled(advancedLapsViewModel.isLapMarkerEnabled)
                        onLapMarkerSwitchChanged { checked ->
                            advancedLapsViewModel.isLapMarkerEnabled = checked
                        }
                    }
                }
            }
        }
    }

    private fun ModelCollector.addLapsLoadingModel() {
        workoutDetailsItemLoading {
            id("lap_selection_epoxy_view_stub")
        }
    }

    fun interface OnAnalysisLapsTableTypeSelectedListener {
        fun onLapsTableTypeSelected(lapsTableType: LapsTableType)
    }

    companion object {
        const val FRAGMENT_TAG =
            "com.stt.android.workout.details.graphanalysis.laps.AnalysisLapMarkerSelectionDialogFragment"
    }
}
