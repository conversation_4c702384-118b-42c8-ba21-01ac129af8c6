package com.stt.android.workout.details.achievements

import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.SessionController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.achievements.GetAchievementUseCase
import com.stt.android.domain.ranking.GetRankingsByWorkoutKeyUseCase
import com.stt.android.domain.ranking.Ranking
import com.stt.android.domain.user.workout.SimilarWorkoutSummary
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.tasks.SimilarWorkoutLoader
import com.stt.android.ui.extensions.hasRoute
import com.stt.android.utils.traceSuspend
import com.stt.android.workout.details.AchievementsData
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

interface AchievementsDataLoader {
    val achievementsDataStateFlow: StateFlow<ViewState<AchievementsData?>>
    suspend fun loadAchievements(workoutHeader: WorkoutHeader): StateFlow<ViewState<AchievementsData?>>
}

@ActivityRetainedScoped
class DefaultAchievementsDataLoader @Inject constructor(
    private val currentUserController: CurrentUserController,
    private val sessionController: SessionController,
    private val getRankingsByWorkoutKeyUseCase: GetRankingsByWorkoutKeyUseCase,
    private val getAchievementUseCase: GetAchievementUseCase,
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope
) : AchievementsDataLoader {
    override val achievementsDataStateFlow: MutableStateFlow<ViewState<AchievementsData?>> =
        MutableStateFlow(loading())

    override suspend fun loadAchievements(workoutHeader: WorkoutHeader): StateFlow<ViewState<AchievementsData?>> {
        activityRetainedCoroutineScope.launch {
            runSuspendCatching {
                traceSuspend("loadAchievements") {
                    getAchievements(workoutHeader)
                }
            }.onFailure {
                Timber.w(it, "Loading achievements in workout details failed.")
                achievementsDataStateFlow.value = loaded()
            }
        }
        return achievementsDataStateFlow
    }

    private suspend fun getAchievements(workoutHeader: WorkoutHeader) = withContext(IO) {
        val workoutKey = workoutHeader.key ?: return@withContext
        if (workoutKey.isBlank()) return@withContext

        val isCurrentUserWorkout = currentUserController.username == workoutHeader.username
        val hasRoute: Boolean = workoutHeader.hasRoute

        val achievementsData = if (isCurrentUserWorkout) {
            getAchievementsForCurrentUser(workoutKey, hasRoute, workoutHeader)
        } else {
            getAchievementsForOtherUser(workoutKey, hasRoute)
        }
        achievementsDataStateFlow.value = loaded(achievementsData)
    }

    private suspend fun getAchievementsForOtherUser(
        workoutKey: String,
        hasRoute: Boolean
    ): AchievementsData {
        // Load old "Fastest on this route" item for followers
        val followerRankings = loadFollowerRankings(workoutKey, hasRoute)
        return AchievementsData(
            isCurrentUserWorkout = false,
            oldFollowerRankings = followerRankings
        )
    }

    private suspend fun getAchievementsForCurrentUser(
        workoutKey: String,
        hasRoute: Boolean,
        workoutHeader: WorkoutHeader
    ): AchievementsData {
        // New achievements for current user
        val achievement = getAchievementUseCase(workoutKey)

        // Old "Fastest on this route" item for current user
        val summary: SimilarWorkoutSummary? = if (hasRoute) {
            getSimilarWorkoutSummary(workoutHeader)
        } else {
            null
        }

        return AchievementsData(
            isCurrentUserWorkout = true,
            achievement = achievement,
            similarWorkoutSummary = summary,
        )
    }

    private fun getSimilarWorkoutSummary(workoutHeader: WorkoutHeader): SimilarWorkoutSummary {
        val rankedWorkoutHeaders = sessionController.getWorkoutsWithSimilarRoute(workoutHeader)
        return SimilarWorkoutSummary(
            SimilarWorkoutLoader.buildRank(
                workoutHeader,
                rankedWorkoutHeaders
            ),
            null,
        )
    }

    private suspend fun loadFollowerRankings(workoutKey: String, hasRoute: Boolean): List<Ranking> =
        withContext(IO) {
            if (!hasRoute) {
                emptyList()
            } else {
                runSuspendCatching {
                    getRankingsByWorkoutKeyUseCase(workoutKey).first()
                }.getOrElse { e ->
                    Timber.w(e, "Fetching old rankings failed")
                    emptyList()
                }
            }
        }
}
