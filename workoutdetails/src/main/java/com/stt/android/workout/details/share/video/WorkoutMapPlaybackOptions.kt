package com.stt.android.workout.details.share.video

import android.content.Context
import android.content.SharedPreferences
import com.stt.android.maps.MAP_TYPE_SKI
import com.stt.android.maps.MAP_TYPE_TERRAIN
import com.stt.android.maps.MapType
import com.stt.android.maps.MapTypeHelper
import com.stt.android.utils.STTConstants.WorkoutMapPlaybackPreferences
import com.stt.android.workout.details.R
import com.stt.android.R as BR

data class WorkoutMapPlaybackOptions(
    val username: <PERSON><PERSON><PERSON>,
    val startTime: Boolean,
    val location: Boolean,
    val device: Boolean,
) {
    companion object {
        fun from(preferences: SharedPreferences) = with(preferences) {
            WorkoutMapPlaybackOptions(
                getBoolean(WorkoutMapPlaybackPreferences.USERNAME, true),
                getBoolean(WorkoutMapPlaybackPreferences.START_TIME, true),
                getBoolean(WorkoutMapPlaybackPreferences.LOCATION, true),
                getBoolean(WorkoutMapPlaybackPreferences.DEVICE, true),
            )
        }

        fun from(context: Context) = from(context.workoutMapPlaybackPreferences)
    }
}

val Context.workoutMapPlaybackPreferences: SharedPreferences
    get() = getSharedPreferences(
        WorkoutMapPlaybackPreferences.PREFS_NAME,
        Context.MODE_PRIVATE,
    )

enum class WorkoutMapPlaybackGraphType {
    PACE,
    ALTITUDE,
    HEART_RATE,
    ;

    companion object {
        fun from(index: Int) = WorkoutMapPlaybackGraphType.entries.firstOrNull {
            it.ordinal == index
        } ?: PACE

        fun from(preferences: SharedPreferences) = from(
            preferences.getInt(WorkoutMapPlaybackPreferences.GRAPH_TYPE, -1)
        )
    }
}

enum class WorkoutMapPlaybackMapType {
    TERRAIN,
    SKI_MAP,
    ;

    companion object {
        fun from(index: Int) = WorkoutMapPlaybackMapType.entries.firstOrNull {
            it.ordinal == index
        } ?: TERRAIN

        fun from(preferences: SharedPreferences) = from(
            preferences.getInt(WorkoutMapPlaybackPreferences.MAP_TYPE, -1)
        )
    }
}

val WorkoutMapPlaybackGraphType.iconRes: Int
    get() = when (this) {
        WorkoutMapPlaybackGraphType.ALTITUDE -> R.drawable.ic_altitude_video_share
        WorkoutMapPlaybackGraphType.HEART_RATE -> R.drawable.ic_heart_rate_video_share
        WorkoutMapPlaybackGraphType.PACE -> R.drawable.ic_pace_video_share
    }

val WorkoutMapPlaybackMapType.iconRes: Int
    get() = when (this) {
        WorkoutMapPlaybackMapType.SKI_MAP -> BR.drawable.map_type_mapbox_ski
        WorkoutMapPlaybackMapType.TERRAIN -> BR.drawable.map_type_mapbox_terrain
    }

val WorkoutMapPlaybackMapType.toMapType: MapType
    get() = when (this) {
        WorkoutMapPlaybackMapType.SKI_MAP -> MapTypeHelper.getOrDefault(MAP_TYPE_SKI)
        WorkoutMapPlaybackMapType.TERRAIN -> MapTypeHelper.getOrDefault(MAP_TYPE_TERRAIN)
    }
