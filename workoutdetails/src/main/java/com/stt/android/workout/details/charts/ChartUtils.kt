package com.stt.android.workout.details.charts

import com.github.mikephil.charting.data.LineData
import kotlin.math.ceil
import kotlin.math.floor

fun getMaxFromMinRange(
    minValue: Float?,
    lineData: LineData,
    inverted: Boolean,
    maxValue: Float?,
    minRange: Float
): Float {
    val min = minValue ?: lineData.yMin
    val max = if (inverted) {
        maxValue ?: lineData.yMax
    } else {
        (10 * (((maxValue ?: lineData.yMax) / 10.0f).toInt() + 1)).toFloat()
    }
    return if (max - min < minRange) min + minRange else max
}

fun Float.ceil() = ceil(this.toDouble()).toFloat()

fun Float.floor() = floor(this.toDouble()).toFloat()

fun Float.roundUpToEven() = (ceil(this.toDouble() / 2) * 2).toFloat()

fun LineData.isMinMaxTheSame(): Boolean = this.yMin == this.yMax

fun Float.lowerByATenth() = this - (this / 10).floor()

fun Float.increaseByATenth() = this + (this / 10).ceil()

val Float.isSafeValue: Boolean
    get() = isFinite() && this != Float.MAX_VALUE && this != -Float.MAX_VALUE
