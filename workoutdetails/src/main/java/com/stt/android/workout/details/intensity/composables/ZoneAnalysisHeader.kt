package com.stt.android.workout.details.intensity.composables

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.OutlinedButton
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import com.stt.android.core.domain.GraphType
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.infomodel.SummaryGraph
import com.stt.android.workout.details.AnalysisDiveGasData
import com.stt.android.workout.details.R
import com.stt.android.workout.details.ZoneAnalysisGraphType
import com.stt.android.workout.details.ZonedAnalysisYAxisType
import java.util.Locale

@Composable
fun ZoneAnalysisHeader(
    availableGraphTypes: Set<ZoneAnalysisGraphType>,
    mainGraphType: ZoneAnalysisGraphType,
    secondaryGraphType: ZoneAnalysisGraphType,
    isZoneColorsEnabled: Boolean,
    diveGasesList: List<AnalysisDiveGasData>?,
    multisportPart: MultisportPartActivity?,
    showFullscreenButton: Boolean,
    onGraphTypeSelected: (item: GraphType, yAxis: ZonedAnalysisYAxisType) -> Unit,
    onSwitchGraphsClicked: () -> Unit,
    onFullScreenAnalysisClicked: (graphType: GraphType, multisportPart: MultisportPartActivity?) -> Unit,
    modifier: Modifier = Modifier,
) {
    val isSwitchGraphsEnabled = secondaryGraphType != ZoneAnalysisGraphType.EMPTY
    val showSingleGraphHeader =
        secondaryGraphType == ZoneAnalysisGraphType.EMPTY && availableGraphTypes.isEmpty()
    Column(
        modifier = modifier
            .fillMaxWidth()
    ) {
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    start = MaterialTheme.spacing.small,
                    top = if (showFullscreenButton) MaterialTheme.spacing.xxsmall else MaterialTheme.spacing.medium
                )
        ) {
            Text(
                text = stringResource(id = R.string.zone_analysis_title)
                    .uppercase(Locale.getDefault()),
                style = MaterialTheme.typography.bodyBold
            )
            if (showFullscreenButton) {
                IconButton(
                    onClick = {
                        onFullScreenAnalysisClicked(
                            mainGraphType.graphType,
                            multisportPart
                        )
                    }
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.fullscreen_icon),
                        contentDescription = null,
                        tint = Color.Unspecified
                    )
                }
            }
        }
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = MaterialTheme.spacing.small,
                    vertical = MaterialTheme.spacing.small
                ),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            if (!showSingleGraphHeader) {
                ZoneAnalysisDropdownMenu(
                    availableGraphTypes = availableGraphTypes - secondaryGraphType,
                    graphType = mainGraphType,
                    onGraphTypeSelected = onGraphTypeSelected,
                    yAxisType = ZonedAnalysisYAxisType.MAIN,
                    isZoneColorsEnabled = isZoneColorsEnabled,
                    diveGasesList = diveGasesList,
                    modifier = Modifier.weight(1f)
                )
                Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))
                OutlinedButton(
                    onClick = {
                        if (isSwitchGraphsEnabled) {
                            onSwitchGraphsClicked.invoke()
                        }
                    },
                    modifier = Modifier.size(40.dp),
                    enabled = isSwitchGraphsEnabled,
                    shape = CircleShape,
                    contentPadding = PaddingValues(0.dp),
                ) {
                    Icon(
                        painterResource(id = R.drawable.resource_switch),
                        contentDescription = null,
                        tint = Color.Unspecified,
                        modifier = Modifier.size(MaterialTheme.iconSizes.small)
                    )
                }
                Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))

                val secondaryGraphTypeList =
                    remember(secondaryGraphType, availableGraphTypes, mainGraphType) {
                        when {
                            mainGraphType.graphType == GraphType.Summary(SummaryGraph.RECOVERYHRINTHREEMINS) -> emptySet()
                            // do special graphs selection logic for swimming workout from headphone
                            mainGraphType.graphType == GraphType.Summary(SummaryGraph.BREASTSTROKEGLIDETIME) ||
                                mainGraphType.graphType == GraphType.Summary(SummaryGraph.BREASTSTROKEHEADANGLE) ||
                                mainGraphType.graphType == GraphType.Summary(SummaryGraph.FREESTYLEPITCHANGLE) ||
                                mainGraphType.graphType == GraphType.Summary(SummaryGraph.AVGFREESTYLEBREATHANGLE) ||
                                mainGraphType.graphType == GraphType.Summary(SummaryGraph.AVGBREASTSTROKEBREATHANGLE) -> {
                                val typeList = availableGraphTypes.filter {
                                    it.graphType == GraphType.Summary(SummaryGraph.PACE) || it.graphType == GraphType.Summary(
                                        SummaryGraph.BREATHINGRATE
                                    )
                                }
                                if (secondaryGraphType == ZoneAnalysisGraphType.EMPTY) {
                                    typeList
                                } else {
                                    typeList + ZoneAnalysisGraphType.EMPTY
                                }
                            }

                            secondaryGraphType == ZoneAnalysisGraphType.EMPTY -> availableGraphTypes - mainGraphType
                            else -> availableGraphTypes - mainGraphType + ZoneAnalysisGraphType.EMPTY
                        }
                    }

                ZoneAnalysisDropdownMenu(
                    availableGraphTypes = secondaryGraphTypeList.filterNot {
                        it.graphType == GraphType.Summary(
                            SummaryGraph.RECOVERYHRINTHREEMINS
                        )
                    }.toSet(),
                    graphType = if (mainGraphType.graphType == GraphType.Summary(SummaryGraph.RECOVERYHRINTHREEMINS)) ZoneAnalysisGraphType.EMPTY else secondaryGraphType,
                    onGraphTypeSelected = onGraphTypeSelected,
                    yAxisType = ZonedAnalysisYAxisType.SECONDARY,
                    isZoneColorsEnabled = false,
                    diveGasesList = null,
                    modifier = Modifier.weight(1f)
                )
            } else {
                // We have only one graph type to show
                ZoneAnalysisDropdownMenu(
                    availableGraphTypes = availableGraphTypes - secondaryGraphType,
                    graphType = mainGraphType,
                    onGraphTypeSelected = onGraphTypeSelected,
                    yAxisType = ZonedAnalysisYAxisType.MAIN,
                    isZoneColorsEnabled = isZoneColorsEnabled,
                    diveGasesList = diveGasesList,
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun ZoneAnalysisHeaderPreview() {
    AppTheme {
        ZoneAnalysisHeader(
            availableGraphTypes = setOf(ZoneAnalysisGraphType(GraphType.NONE, "Heart rate", "bpm")),
            mainGraphType = ZoneAnalysisGraphType(GraphType.NONE, "Heart rate", "bpm"),
            secondaryGraphType = ZoneAnalysisGraphType(GraphType.NONE, "Ascend", "m"),
            isZoneColorsEnabled = true,
            diveGasesList = null,
            multisportPart = null,
            showFullscreenButton = true,
            onGraphTypeSelected = { _, _ -> },
            onSwitchGraphsClicked = {},
            onFullScreenAnalysisClicked = { _, _ -> },
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun ZoneAnalysisHeaderSingleGraphPreview() {
    AppTheme {
        ZoneAnalysisHeader(
            availableGraphTypes = emptySet(),
            mainGraphType = ZoneAnalysisGraphType(GraphType.NONE, "Heart rate", "bpm"),
            secondaryGraphType = ZoneAnalysisGraphType.EMPTY,
            isZoneColorsEnabled = true,
            diveGasesList = null,
            multisportPart = null,
            showFullscreenButton = true,
            onGraphTypeSelected = { _, _ -> },
            onSwitchGraphsClicked = {},
            onFullScreenAnalysisClicked = { _, _ -> },
        )
    }
}
