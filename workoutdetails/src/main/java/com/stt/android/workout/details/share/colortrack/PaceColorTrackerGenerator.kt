package com.stt.android.workout.details.share.colortrack

import com.stt.android.domain.sml.Sml
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.maps.colortrack.ColorTrackDescriptor
import com.stt.android.ui.map.MapHelper

class PaceColorTrackerGenerator : ColorTrackGenerator {
    override fun generate(geoPoints: List<WorkoutGeoPoint>, sml: Sml?): ColorTrackDescriptor {
        val speedList = sml?.streamData?.speed
        if (speedList.isNullOrEmpty()) return ColorTrackDescriptor.from(geoPoints)

        val buckets = Bucket.createBucketsFromList(
            speedList,
            value = { value.toDouble() },
            timestamp = { timestamp },
            pauseResumeTimes = with(MapHelper) {
                sml.streamData.findPauseResumeTimePairs()
            },
        )
        return ColorTrackDescriptor.from(geoPoints, buckets)
    }
}
