package com.stt.android.workout.details.divetrack

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.unit.dp
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.divetrack.DiveTrackSettings
import com.stt.android.divetrack.ui.DiveTrackView
import com.stt.android.workout.details.DiveTrackData
import com.stt.android.workout.details.R

@EpoxyModelClass
abstract class DiveTrackModel : EpoxyModelWithHolder<DiveTrackViewHolder>() {

    @EpoxyAttribute
    lateinit var diveTrackData: DiveTrackData

    override fun getDefaultLayout() = R.layout.model_workout_dive_track

    override fun bind(holder: DiveTrackViewHolder) {
        holder.diveTrackView.setContent {
            Surface(
                modifier = Modifier.fillMaxSize(),
                color = MaterialTheme.colors.surface,
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth(),
                ) {
                    DiveTrackHeader(
                        onShowFullscreenClicked = diveTrackData.onShowFullscreenClicked,
                    )

                    DiveTrackView(
                        diveTrack = diveTrackData.diveTrack,
                        settings = DiveTrackSettings(
                            supportGestures = false,
                            // If true, the rendering couldn't be resumed after scrolling the view out.
                            shouldDestroyWhenViewDetached = false,
                            cameraSetting = DiveTrackSettings.CameraSetting.NEAR,
                            showBottomPlane = false,
                        ),
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(240.dp)
                            .clickable(
                                onClick = diveTrackData.onShowFullscreenClicked,
                            ),
                    )
                }
            }
        }
    }
}

class DiveTrackViewHolder : KotlinEpoxyHolder() {
    val diveTrackView by bind<ComposeView>(R.id.dive_track_view)
}
