package com.stt.android.workout.details.share.video

import android.os.Build
import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.TimeUtils.epochToLocalZonedDateTime
import com.stt.android.domain.mapbox.FetchLocationNameUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.GetExtensionsUseCase
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.mapping.getActivitySummaryForActivityId
import com.stt.android.utils.firstOfType
import com.suunto.connectivity.suuntoconnectivity.device.ProductType
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.selects.select
import java.text.SimpleDateFormat
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle
import java.util.Date
import javax.inject.Inject

interface VideoShareInfoDataLoader {
    val dataFlow: StateFlow<VideoShareInfoData?>
    fun load(workoutHeader: WorkoutHeader)
}

@ActivityRetainedScoped
class DefaultVideoShareInfoDataLoader @Inject constructor(
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope,
    private val dispatchers: CoroutinesDispatchers,
    private val currentUserController: CurrentUserController,
    private val fetchLocationNameUseCase: FetchLocationNameUseCase,
    private val getExtensionsUseCase: GetExtensionsUseCase,
    private val infoModelFormatter: InfoModelFormatter
) : VideoShareInfoDataLoader {
    override val dataFlow: MutableStateFlow<VideoShareInfoData?> = MutableStateFlow(null)

    override fun load(workoutHeader: WorkoutHeader) {
        activityRetainedCoroutineScope.launch(dispatchers.io) {
            runSuspendCatching {
                loadWorkoutInfoData(workoutHeader)
            }
        }
    }

    private suspend fun loadWorkoutInfoData(workoutHeader: WorkoutHeader) = coroutineScope {
        val locationTask = async {
            runSuspendCatching {
                workoutHeader.startPosition?.let {
                    val params = FetchLocationNameUseCase.Params(
                        it.latitude,
                        it.longitude,
                        useCoarseAccuracy = true
                    )
                    fetchLocationNameUseCase(params)?.city
                }
            }
        }
        val extensionTask = async {
            runSuspendCatching<SummaryExtension?> {
                getExtensionsUseCase.getExtensions(workoutHeader).firstOfType()
            }
        }
        emitData(workoutHeader, null, null)
        val (location, extension) = select {
            locationTask.onAwait { Pair(it.getOrNull(), null) }
            extensionTask.onAwait { Pair(null, it.getOrNull()) }
        }
        emitData(workoutHeader, location, extension)
        emitData(workoutHeader, locationTask.await().getOrNull(), extensionTask.await().getOrNull())
    }

    private fun emitData(
        workoutHeader: WorkoutHeader,
        location: String?,
        extension: SummaryExtension?,
    ) {
        val datetime = if (Build.VERSION.SDK_INT == Build.VERSION_CODES.S) {
            val dateTimeFormatter = SimpleDateFormat.getDateTimeInstance(
                SimpleDateFormat.SHORT,
                SimpleDateFormat.SHORT
            )
            dateTimeFormatter.format(Date(workoutHeader.startTime))
        } else {
            val dateTimeFormatter = DateTimeFormatter.ofLocalizedDateTime(
                FormatStyle.SHORT,
                FormatStyle.SHORT
            )
            epochToLocalZonedDateTime(workoutHeader.startTime).format(dateTimeFormatter)
        }
        val user = currentUserController.currentUser
        val activityType = workoutHeader.activityType
        val data = VideoShareInfoData(
            imageUrl = user.profileImageUrl,
            username = user.realName?.takeIf { it.isNotBlank() } ?: workoutHeader.username,
            location = location,
            activityType = activityType,
            datetime = datetime,
            productName = extension?.displayName,
            productType = extension?.productType?.let { runCatching { ProductType.valueOf(it) }.getOrNull() },
            rowData = buildList {
                add(
                    infoModelFormatter.formatValue(
                        SummaryItem.DISTANCE,
                        workoutHeader.totalDistance,
                    )
                )
                add(
                    infoModelFormatter.formatValue(
                        SummaryItem.DURATION,
                        workoutHeader.totalTime,
                    )
                )
                val items = getActivitySummaryForActivityId(activityType.id).items
                val summaryItem = if (items.contains(SummaryItem.AVGPACE)) {
                    SummaryItem.AVGPACE
                } else {
                    SummaryItem.AVGSPEED
                }
                add(
                    infoModelFormatter.formatValue(
                        summaryItem,
                        workoutHeader.avgSpeed,
                    )
                )
            },
        )
        dataFlow.tryEmit(data)
    }
}
