package com.stt.android.workout.details.intensity.composables

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.OutlinedButton
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.darkestGrey
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.workout.details.AnalysisDiveGasData
import com.stt.android.workout.details.R

@Composable
fun ZoneAnalysisButton(
    graphTitle: String,
    graphUnitTitle: String,
    isZoneColorsEnabled: Boolean,
    buttonEnabled: Boolean,
    diveGasesList: List<AnalysisDiveGasData>?,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    OutlinedButton(
        onClick = onClick,
        enabled = buttonEnabled,
        modifier = modifier.height(MaterialTheme.spacing.xxxlarge),
        shape = RoundedCornerShape(MaterialTheme.spacing.small)
    ) {
        Row {
            ZoneAnalysisColorBar(
                isZoneColorsEnabled = isZoneColorsEnabled,
                diveGasesList = diveGasesList,
                modifier = Modifier
                    .align(Alignment.CenterVertically)
                    .height(MaterialTheme.spacing.large)
                    .width(MaterialTheme.spacing.xsmall)
            )
            Spacer(modifier = Modifier.width(MaterialTheme.spacing.xsmaller))
            Column(
                horizontalAlignment = Alignment.Start,
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = graphTitle,
                    style = MaterialTheme.typography.bodyBold,
                    color = MaterialTheme.colors.nearBlack,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                Text(
                    text = graphUnitTitle,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colors.nearBlack
                )
            }
            Spacer(modifier = Modifier.width(MaterialTheme.spacing.xsmall))
            if (buttonEnabled) {
                Icon(
                    modifier = Modifier
                        .size(MaterialTheme.iconSizes.mini)
                        .align(Alignment.CenterVertically),
                    painter = painterResource(id = R.drawable.dropdown_arrow),
                    tint = MaterialTheme.colors.nearBlack,
                    contentDescription = null
                )
            }
        }
    }
}

@Composable
fun ZoneAnalysisButtonSecondary(
    graphTitle: String,
    graphUnitTitle: String,
    showColorBar: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    OutlinedButton(
        onClick = onClick,
        modifier = modifier.height(MaterialTheme.spacing.xxxlarge),
        shape = RoundedCornerShape(MaterialTheme.spacing.small)
    ) {
        Row {
            Icon(
                modifier = Modifier
                    .size(MaterialTheme.iconSizes.mini)
                    .align(Alignment.CenterVertically),
                painter = painterResource(id = R.drawable.dropdown_arrow),
                tint = MaterialTheme.colors.nearBlack,
                contentDescription = null
            )
            Spacer(modifier = Modifier.width(MaterialTheme.spacing.xsmall))
            Column(
                horizontalAlignment = Alignment.End,
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = graphTitle,
                    style = MaterialTheme.typography.bodyBold,
                    color = MaterialTheme.colors.nearBlack,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
                Text(
                    text = graphUnitTitle,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colors.nearBlack,
                )
            }
            Spacer(modifier = Modifier.width(MaterialTheme.spacing.xsmaller))
            if (showColorBar) {
                Box(
                    modifier = Modifier
                        .align(Alignment.CenterVertically)
                        .height(MaterialTheme.spacing.large)
                        .width(MaterialTheme.spacing.xsmall)
                        .background(color = MaterialTheme.colors.darkestGrey)
                )
            }
        }
    }
}

@Composable
fun ZoneAnalysisColorBar(
    isZoneColorsEnabled: Boolean,
    diveGasesList: List<AnalysisDiveGasData>?,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        if (isZoneColorsEnabled) {
            val brush = remember {
                Brush.verticalGradient(
                    listOf(
                        Color(0xFFEF5349),
                        Color(0xFFFFAA00),
                        Color(0xFFFFDD33),
                        Color(0xFF11DD55),
                        Color(0xFF16B4EA)
                    )
                )
            }
            Box(
                modifier = Modifier
                    .background(brush = brush)
                    .fillMaxSize()
            )
        } else if (diveGasesList?.isNotEmpty() == true) {
            DiveGasesColorBar(
                gases = diveGasesList,
                modifier = Modifier.fillMaxSize()
            )
        } else {
            Box(
                modifier = Modifier
                    .background(color = colorResource(id = R.color.zoned_analysis_main_color))
                    .fillMaxSize()
            )
        }
    }
}

@Composable
fun DiveGasesColorBar(
    gases: List<AnalysisDiveGasData>,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
    ) {
        Column {
            for (gas in gases) {
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth()
                        .background(color = Color(gas.color))
                )
            }
        }
    }
}

@Preview(name = "Left", showBackground = true, widthDp = 145)
@Composable
private fun ZoneAnalysisButtonPreview() {
    AppTheme {
        ZoneAnalysisButton(
            graphTitle = "Heart rate",
            graphUnitTitle = "bpm",
            isZoneColorsEnabled = true,
            buttonEnabled = true,
            onClick = {},
            diveGasesList = emptyList()
        )
    }
}

@Preview(name = "Right", showBackground = true, widthDp = 145)
@Composable
private fun ZoneAnalysisButtonSecondaryPreview() {
    AppTheme {
        ZoneAnalysisButtonSecondary(
            graphTitle = "Altitude",
            graphUnitTitle = "m",
            showColorBar = true,
            onClick = {}
        )
    }
}

@Preview(name = "DiveTankPressures", showBackground = true, widthDp = 145)
@Composable
private fun ZoneAnalysisButtonDiveTankPressuresPreview() {
    AppTheme {
        ZoneAnalysisButton(
            graphTitle = "Tank Pressures",
            graphUnitTitle = "bar",
            isZoneColorsEnabled = false,
            buttonEnabled = true,
            diveGasesList = previewGasData(),
            onClick = {}
        )
    }
}

@Composable
private fun previewGasData() = listOf(
    AnalysisDiveGasData("Air", colorResource(id = com.stt.android.R.color.chart_color_1).toArgb()),
    AnalysisDiveGasData("NX50", colorResource(id = com.stt.android.R.color.chart_color_2).toArgb()),
    AnalysisDiveGasData("TX 21/33", colorResource(id = com.stt.android.R.color.chart_color_3).toArgb()),
    AnalysisDiveGasData("TX 12/65", colorResource(id = com.stt.android.R.color.chart_color_4).toArgb())
)
