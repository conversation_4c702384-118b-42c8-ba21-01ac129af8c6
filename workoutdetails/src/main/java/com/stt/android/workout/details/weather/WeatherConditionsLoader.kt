package com.stt.android.workout.details.weather

import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.controllers.WeatherExtensionDataModel
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.weather.WeatherConditions
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.extensions.loadExtension
import com.stt.android.utils.traceSuspend
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

interface WeatherConditionsLoader {
    val weatherConditionsStateFlow: StateFlow<ViewState<WeatherConditions?>>
    suspend fun loadWeatherConditions(workoutHeader: WorkoutHeader): StateFlow<ViewState<WeatherConditions?>>
}

@ActivityRetainedScoped
class DefaultWeatherConditionsLoader
@Inject constructor(
    private val weatherExtensionDataModel: WeatherExtensionDataModel,
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope
) : WeatherConditionsLoader {
    override val weatherConditionsStateFlow: MutableStateFlow<ViewState<WeatherConditions?>> =
        MutableStateFlow(loading())

    override suspend fun loadWeatherConditions(workoutHeader: WorkoutHeader): StateFlow<ViewState<WeatherConditions?>> {
        activityRetainedCoroutineScope.launch(IO) {
            traceSuspend("loadWeatherConditions") {
                runSuspendCatching {
                    val conditions = weatherExtensionDataModel.loadExtension(workoutHeader)
                        ?.toDomainEntity()
                    weatherConditionsStateFlow.value = loaded(conditions)
                }
            }
        }

        return weatherConditionsStateFlow
    }
}
