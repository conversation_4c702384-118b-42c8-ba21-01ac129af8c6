package com.stt.android.workout.details.workoutvalues

import com.stt.android.domain.workout.ActivityType
import com.stt.android.infomodel.SummaryItem
import com.suunto.connectivity.suuntoconnectivity.device.ProductType

private val needFilterSummaryItemsActivities = listOf(
    ActivityType.SWIMMING.id, // POOL_SWIMMING
    ActivityType.OPENWATER_SWIMMING.id, // OPEN_WATER_SWIMMING
    ActivityType.JUMP_ROPE.id, // JUMP_ROPE
    ActivityType.RUNNING.id, // RUNNING
)

private val needRemovedSummaryItems = listOf(
    SummaryItem.TRAININGSTRESSSCORE,
    SummaryItem.RECOVERYTIME,
    SummaryItem.MOVINGTIME,
    SummaryItem.RESTTIME,
)

fun filterSummaryItems(
    items: List<SummaryItem>,
    activityTypeId: Int,
    productType: String?
): List<SummaryItem> {
    return if (
        ProductType.SPORT_EARPHONE.name == productType &&
        needFilterSummaryItemsActivities.contains(activityTypeId)
    ) {
        items.filterNot { item ->
            needRemovedSummaryItems.contains(item)
        }
    } else items
}
