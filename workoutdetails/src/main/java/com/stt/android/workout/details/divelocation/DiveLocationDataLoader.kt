package com.stt.android.workout.details.divelocation

import com.google.android.gms.maps.model.LatLng
import com.stt.android.analytics.AnalyticsPropertyValue.SourceProperty
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.attributes.FetchUnconfirmedWorkoutAttributesUpdateUseCase
import com.stt.android.domain.workouts.attributes.workoutLocation
import com.stt.android.extensions.startOrStopPositionIgnoreNullIsland
import com.stt.android.workout.details.DiveLocationData
import com.stt.android.workout.details.NavigationEventDispatcher
import com.stt.android.workout.details.WorkoutDetailsEditNavEvent
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import timber.log.Timber
import javax.inject.Inject

interface DiveLocationDataLoader {
    val diveLocationStateFlow: StateFlow<ViewState<DiveLocationData?>>
    suspend fun loadDiveLocationData(workoutHeader: WorkoutHeader): StateFlow<ViewState<DiveLocationData?>>
    suspend fun update(workoutHeader: WorkoutHeader)
}

@ActivityRetainedScoped
class DefaultDiveLocationDataLoader
@Inject constructor(
    private val navigationEventDispatcher: NavigationEventDispatcher,
    private val currentUserController: CurrentUserController,
    private val fetchUnconfirmedUseCase: FetchUnconfirmedWorkoutAttributesUpdateUseCase
) : DiveLocationDataLoader {
    lateinit var workoutHeader: WorkoutHeader
    override val diveLocationStateFlow: MutableStateFlow<ViewState<DiveLocationData?>> =
        MutableStateFlow(loading())

    override suspend fun loadDiveLocationData(workoutHeader: WorkoutHeader): StateFlow<ViewState<DiveLocationData?>> {
        runSuspendCatching {
            update(workoutHeader)
        }.onFailure {
            Timber.w(it, "Getting dive location data failed.")
            diveLocationStateFlow.value = loaded()
        }
        return diveLocationStateFlow
    }

    override suspend fun update(workoutHeader: WorkoutHeader) {
        this.workoutHeader = workoutHeader
        runSuspendCatching {
            val workoutId = workoutHeader.id
            val isDiving = workoutHeader.activityType.isDiving
            val isCurrentUserWorkout = currentUserController.username == workoutHeader.username
            if (isDiving && isCurrentUserWorkout) {
                val unconfirmedUpdate = fetchUnconfirmedUseCase(
                    fetchUnconfirmedUseCase.getParams(
                        workoutId,
                        workoutHeader.username
                    )
                )
                val unconfirmedLocation = unconfirmedUpdate?.workoutLocation

                val latLng = if (unconfirmedLocation != null) {
                    LatLng(
                        unconfirmedLocation.latitude,
                        unconfirmedLocation.longitude
                    )
                } else {
                    workoutHeader.startOrStopPositionIgnoreNullIsland
                }

                if (latLng == null || unconfirmedLocation != null) {
                    DiveLocationData(
                        isUnconfirmed = unconfirmedLocation != null,
                        latLng = latLng,
                        onClick = ::navigateToEditWorkoutDetails
                    )
                } else {
                    null
                }
            } else {
                null
            }
        }.onFailure {
            Timber.w(it, "Updating dive location data failed.")
            diveLocationStateFlow.value = loaded()
        }.onSuccess {
            diveLocationStateFlow.value = loaded(it)
        }
    }

    private fun navigateToEditWorkoutDetails() {
        navigationEventDispatcher.dispatchEvent(
            WorkoutDetailsEditNavEvent(
                workoutHeader.id,
                SourceProperty.ADD_LOCATION_BUTTON,
                true
            )
        )
    }
}
