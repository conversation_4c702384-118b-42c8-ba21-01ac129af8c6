package com.stt.android.workout.details.graphanalysis.charts

import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.highlight.Highlight

class GraphAnalysisChartHighlight(
    x: Float,
    y: Float,
    xPx: Float,
    yPx: Float,
    dataSetIndex: Int,
    stackIndex: Int,
    val graphAnalysisAxis: GraphAnalysisYAxisDependency,
    val entry: Entry
) : Highlight(
    x,
    y,
    xPx,
    yPx,
    dataSetIndex,
    stackIndex,
    if (graphAnalysisAxis == GraphAnalysisYAxisDependency.LEFT) YAxis.AxisDependency.LEFT else YAxis.AxisDependency.RIGHT
)
