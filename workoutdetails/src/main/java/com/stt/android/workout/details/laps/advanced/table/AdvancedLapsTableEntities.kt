package com.stt.android.workout.details.laps.advanced.table

import android.view.View
import com.stt.android.core.domain.LapsTableDataType
import com.stt.android.domain.advancedlaps.LapsTable
import com.stt.android.domain.advancedlaps.LapsTableRow
import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.ui.utils.LiveEvent
import com.stt.android.workout.details.laps.advanced.AdvancedLapsRowType

typealias OnExpandToggled = (advancedLapsTableRowItem: AdvancedLapsTableRowItem) -> Unit
typealias OnLapSelected = (lapsTableType: LapsTableType, lapsTableRow: LapsTableRow) -> Unit
typealias OnSelectColumnRequested = (request: LiveEvent<AdvancedLapsSelectColumnRequest>) -> Unit
typealias OnHighlightVariancesToggled = (checked: Boolean) -> Unit

data class AdvancedLapsTableRowItem constructor(
    val id: String,
    val type: AdvancedLapsRowType,
    val selectedColumns: List<LapsColumnData>,
    val row: LapsTableRow,
    val isExpanded: Boolean,
    val subRows: List<AdvancedLapsTableRowItem>,
    val isRecoveryInterval: Boolean = false,
    val onExpandToggled: OnExpandToggled,
    val onLapSelected: OnLapSelected,
    var isSelected: Boolean = false,
) {
    fun getLapNumber() = if (type == AdvancedLapsRowType.SubRow) "" else row.lapNumber.toString()

    fun getLapNumberVisibility() =
        if (isSelected) View.INVISIBLE else if (type == AdvancedLapsRowType.SubRow) View.INVISIBLE else View.VISIBLE
}

data class AdvancedLapsTableHeaderItem constructor(
    val id: String,
    val stId: Int,
    val type: AdvancedLapsRowType,
    val selectedColumns: List<LapsTableDataType>
)

data class AdvancedLapsTable(
    val availableColumns: List<LapsTableDataType>,
    val lapTable: LapsTable
)

data class AdvancedLapsTableItems(
    val lapsTable: LapsTable,
    val availableColumns: List<LapsTableDataType>,
    val headerItem: AdvancedLapsTableHeaderItem,
    val items: List<AdvancedLapsTableRowItem>,
    val autoLapLength: Float? = 0f
) {
    val lapsTableType
        get() = lapsTable.lapsType
}

data class AdvancedLapsTableContainer constructor(
    val stId: Int,
    val tables: List<AdvancedLapsTableItems>,
    var currentLapTable: Int,
    val isLapsTableColouringEnabled: Boolean,
    val showLapCellColorInfoUi: Boolean,
    val onSelectColumnRequested: OnSelectColumnRequested,
    val onHighLightVariancesToggled: OnHighlightVariancesToggled
)

data class AdvancedLapsSelectColumnRequest constructor(
    val stId: Int,
    val lapsTableType: LapsTableType,
    val columnIndex: Int,
    val column: LapsTableDataType
)

data class LapsColumnData(
    val column: LapsTableDataType,
    val percentiles: LapColumnPercentiles?
)

data class LapColumnPercentiles(
    val percentile10: Double,
    val percentile45: Double,
    val percentile55: Double,
    val percentile90: Double,
    val areFormattedValuesUsedForCalculation: Boolean
)

data class LapCellData(
    val text: String,
    val backgroundColor: Int,
    val textColor: Int,
    val bold: Boolean,
)
