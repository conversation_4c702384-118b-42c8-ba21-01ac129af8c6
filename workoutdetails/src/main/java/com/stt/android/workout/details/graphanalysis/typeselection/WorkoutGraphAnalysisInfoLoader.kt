package com.stt.android.workout.details.graphanalysis.typeselection

import com.stt.android.core.domain.GraphType
import com.stt.android.domain.graphanalysis.ActivityTypeGraphAnalysisInfo
import com.stt.android.domain.graphanalysis.ActivityTypeGraphAnalysisSelectionsDataSource
import com.stt.android.domain.graphanalysis.ObserveActivityTypeGraphAnalysisInfoUseCase
import com.stt.android.infomodel.SummaryGraph
import com.stt.android.workout.details.WorkoutAnalysisData
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class WorkoutGraphAnalysisInfoLoader @Inject constructor(
    private val observeActivityTypeInfoUseCase: ObserveActivityTypeGraphAnalysisInfoUseCase,
    private val dataSource: ActivityTypeGraphAnalysisSelectionsDataSource
) {
    private val forcedMainGraphTypeFlow = MutableStateFlow<GraphType?>(null)

    fun observeGraphAnalysisInfoForWorkoutAnalysisData(
        workoutAnalysisData: WorkoutAnalysisData,
        hasHrGraphData: Boolean,
        hasAerobicIqGraphData: Boolean,
        hasHrInThreeMinsGraphData: Boolean
    ): Flow<WorkoutGraphAnalysisInfo> {
        val availableGraphTypesForWorkout: Set<GraphType> = buildSet {
            workoutAnalysisData.pagerData.graphData
                .filter { it.data.isNotEmpty() }
                .mapTo(this) { it.graphType }

            if (hasHrGraphData) {
                add(GraphType.Summary(SummaryGraph.HEARTRATE))
            }

            if (hasAerobicIqGraphData) {
                add(GraphType.Summary(SummaryGraph.AEROBICZONE))
            }
            if (hasHrInThreeMinsGraphData) {
                add(GraphType.Summary(SummaryGraph.RECOVERYHRINTHREEMINS))
            }
        }

        val activityTypeId = workoutAnalysisData.pagerData.multisportPartActivity?.activityType
            ?: workoutAnalysisData.pagerData.workoutHeader.activityTypeId

        var isInitial = true

        return observeActivityTypeInfoUseCase
            .observeGraphAnalysisInfoForActivityType(activityTypeId)
            .combine(forcedMainGraphTypeFlow, ::Pair)
            .debounce {
                if (isInitial) {
                    isInitial = false
                    0L
                } else {
                    // Debounce slightly as otherwise we'd often run the map below twice
                    // when forced main graph is cleared and selections change at the same time
                    3L
                }
            }
            .map { (activityTypeInfo, forcedMainGraphType) ->
                val nonUsedAvailableGraphTypes = availableGraphTypesForWorkout.toMutableSet()

                val mainGraphType = if (
                    forcedMainGraphType != null &&
                    nonUsedAvailableGraphTypes.contains(forcedMainGraphType)
                ) {
                    forcedMainGraphType
                } else {
                    if (nonUsedAvailableGraphTypes.contains(activityTypeInfo.mainGraphType)) {
                        activityTypeInfo.mainGraphType
                    } else {
                        activityTypeInfo.supportedGraphTypes.firstOrNull {
                            nonUsedAvailableGraphTypes.contains(it)
                        }
                    } ?: GraphType.NONE
                }
                nonUsedAvailableGraphTypes.remove(mainGraphType)

                val comparisonGraphType =
                    if (activityTypeInfo.comparisonGraphType == GraphType.NONE ||
                        nonUsedAvailableGraphTypes.contains(activityTypeInfo.comparisonGraphType)
                    ) {
                        activityTypeInfo.comparisonGraphType
                    } else {
                        activityTypeInfo.supportedGraphTypes.firstOrNull {
                            nonUsedAvailableGraphTypes.contains(it)
                        }
                    } ?: GraphType.NONE
                nonUsedAvailableGraphTypes.remove(comparisonGraphType)

                val backgroundGraphType =
                    if (activityTypeInfo.backgroundGraphType == GraphType.NONE ||
                        nonUsedAvailableGraphTypes.contains(activityTypeInfo.backgroundGraphType)
                    ) {
                        activityTypeInfo.backgroundGraphType
                    } else {
                        activityTypeInfo.supportedGraphTypes.firstOrNull {
                            nonUsedAvailableGraphTypes.contains(it)
                        }
                    } ?: GraphType.NONE

                WorkoutGraphAnalysisInfo(
                    workoutAnalysisData = workoutAnalysisData,
                    supportedGraphTypes = availableGraphTypesForWorkout.sortedBy {
                        activityTypeInfo.supportedGraphTypes.indexOf(it)
                            .takeIf { index -> index >= 0 } ?: Int.MAX_VALUE
                    },
                    mainGraphType = mainGraphType,
                    comparisonGraphType = comparisonGraphType,
                    backgroundGraphType = backgroundGraphType,
                    activityTypeInfo = activityTypeInfo
                )
            }
    }

    fun saveGraphTypesForActivityType(
        activityTypeId: Int,
        mainGraphType: GraphType,
        comparisonGraphType: GraphType,
        backgroundGraphType: GraphType
    ) = dataSource.saveSelectedGraphTypesForActivityType(
        activityTypeId,
        mainGraphType,
        comparisonGraphType,
        backgroundGraphType
    )

    fun setForcedMainGraphType(graphType: GraphType?) {
        forcedMainGraphTypeFlow.value = graphType
    }
}

/**
 * Information on which graphs are supported and should be shown for a particular workout or its part.
 * Can differ from the [ActivityTypeGraphAnalysisInfo] for the activity type if the data for some
 * graphs isn't available on this particular workout.
 */
data class WorkoutGraphAnalysisInfo(
    val workoutAnalysisData: WorkoutAnalysisData,
    val supportedGraphTypes: List<GraphType>, // In order to be shown on UI, not including NONE
    val mainGraphType: GraphType,
    val comparisonGraphType: GraphType,
    val backgroundGraphType: GraphType,
    val activityTypeInfo: ActivityTypeGraphAnalysisInfo
)
