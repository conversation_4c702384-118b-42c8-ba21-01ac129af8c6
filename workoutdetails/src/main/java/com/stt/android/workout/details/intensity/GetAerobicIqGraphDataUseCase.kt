package com.stt.android.workout.details.intensity

import com.github.mikephil.charting.data.Entry
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.workouts.WorkoutHeaderDataSource
import com.stt.android.domain.workouts.isMultisport
import com.stt.android.infomodel.SummaryItem
import com.stt.android.intensityzone.IntensityZoneLimits
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.usecases.CalculateDFAUseCase
import com.stt.android.utils.ZoneDurationData
import com.stt.android.utils.ZoneDurationUtils
import com.stt.android.workout.details.AerobicIqGraphData
import com.suunto.algorithms.ddfa.DynamicDFAUtils
import kotlinx.coroutines.withContext
import javax.inject.Inject
import kotlin.time.Duration

class GetAerobicIqGraphDataUseCase @Inject constructor(
    private val infoModelFormatter: InfoModelFormatter,
    private val workoutHeaderDataSource: WorkoutHeaderDataSource,
    private val calculateDFAUseCase: CalculateDFAUseCase,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) {
    suspend operator fun invoke(
        workoutId: Int,
        sml: Sml?,
        multisportPartActivity: MultisportPartActivity?,
    ): AerobicIqGraphData? = withContext(coroutinesDispatchers.io) {
        val isMultisport = workoutHeaderDataSource.findById(workoutId)
            ?.isMultisport
            ?: return@withContext null
        if (isMultisport && multisportPartActivity == null) {
            return@withContext null
        }

        val indexOfMultisportPart = multisportPartActivity?.let {
            sml?.streamData?.multisportPartActivities?.indexOf(it)
        } ?: 0
        val ddfaResult = calculateDFAUseCase(
            workoutId = workoutId,
            sml = sml,
        ).getOrNull(indexOfMultisportPart) ?: return@withContext null

        val adjustedZoneDurationPercentages = ZoneDurationUtils.adjustPercentages(
            durations = listOf(
                ddfaResult.zones.timeInVo2MaxZone,
                ddfaResult.zones.timeInAnaerobicZone,
                ddfaResult.zones.timeInAerobicZone,
            ),
        )

        createAerobicIqGraphData(
            infoModelFormatter = infoModelFormatter,
            ddfaResult = ddfaResult,
            ddfaZoneDurationPercentages = adjustedZoneDurationPercentages,
        )
    }

    private companion object {
        fun createAerobicIqGraphData(
            infoModelFormatter: InfoModelFormatter,
            ddfaResult: DynamicDFAUtils.Result,
            ddfaZoneDurationPercentages: List<Int>,
        ): AerobicIqGraphData = AerobicIqGraphData(
            entries = ddfaResult.alphaWithBaselineOffset.map {
                Entry(it.timeInMillis / 1000f, it.alpha.toFloat())
            },
            zoneLimits = IntensityZoneLimits.createAerobicZoneIntensityZoneLimits(),
            vo2Max = createZoneDurationData(
                infoModelFormatter = infoModelFormatter,
                summaryItem = SummaryItem.VO2MAXDURATION,
                duration = ddfaResult.zones.timeInVo2MaxZone,
                percentage = ddfaZoneDurationPercentages[0],
            ),
            anaerobic = createZoneDurationData(
                infoModelFormatter = infoModelFormatter,
                summaryItem = SummaryItem.ANAEROBICDURATION,
                duration = ddfaResult.zones.timeInAnaerobicZone,
                percentage = ddfaZoneDurationPercentages[1],
            ),
            aerobic = createZoneDurationData(
                infoModelFormatter = infoModelFormatter,
                summaryItem = SummaryItem.AEROBICDURATION,
                duration = ddfaResult.zones.timeInAerobicZone,
                percentage = ddfaZoneDurationPercentages[2],
            ),
            anaerobicHrThreshold = ddfaResult.zones.anaerobicThreshold?.inBpm,
            aerobicHrThreshold = ddfaResult.zones.aerobicThreshold?.inBpm,
            baseline = ddfaResult.baseline,
            cumulativeBaseline = ddfaResult.cumulativeBaseline,
        )

        private fun createZoneDurationData(
            infoModelFormatter: InfoModelFormatter,
            summaryItem: SummaryItem,
            duration: Duration,
            percentage: Int,
        ): ZoneDurationData = ZoneDurationData(
            duration = duration,
            formattedDuration = infoModelFormatter.formatDuration(summaryItem, duration),
            percentage = percentage,
        )

        private fun InfoModelFormatter.formatDuration(summaryItem: SummaryItem, duration: Duration): String =
            formatValue(summaryItem, duration.inWholeSeconds)
                .value
                ?.takeUnless { it.isBlank() }
                ?: "-"
    }
}
