package com.stt.android.workout.details.mml

import com.google.android.gms.maps.model.LatLng
import com.soy.algorithms.camerapath.MmlBounds
import com.stt.android.maps.mapbox.domain.DemSourceUseCase
import javax.inject.Inject

class MmlUtils @Inject constructor(
    private val demSourceUseCase: DemSourceUseCase
) : MmlBounds {

    override fun withinMmlBounds(latitude: Double, longitude: Double): Boolean {
        return demSourceUseCase.isInsideMmlBounds(LatLng(latitude, longitude))
    }
}
