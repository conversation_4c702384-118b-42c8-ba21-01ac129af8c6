package com.stt.android.workout.details.share.video

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.addCallback
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import com.stt.android.compose.util.setContentWithTheme
import com.stt.android.workout.details.databinding.ActivityWorkoutMapPlaybackOptionsBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class WorkoutMapPlaybackOptionsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityWorkoutMapPlaybackOptionsBinding

    private val viewModel: WorkoutMapPlaybackOptionsViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityWorkoutMapPlaybackOptionsBinding.inflate(layoutInflater)
        val showDeviceOption = intent.getBooleanExtra(EXTRA_SHOW_DEVICE_OPTION, true)
        with(binding) {
            setContentView(root)
            setSupportActionBar(toolbar)
            supportActionBar?.setDisplayShowHomeEnabled(false)
            supportActionBar?.setDisplayHomeAsUpEnabled(true)
            toolbar.setNavigationOnClickListener { onBackPressedDispatcher.onBackPressed() }

            composeView.setContentWithTheme {
                WorkoutMapPlaybackOptionsScreen(
                    viewModel = viewModel,
                    showDeviceOption = showDeviceOption,
                )
            }
        }

        onBackPressedDispatcher.addCallback {
            if (viewModel.hasChanged) {
                setResult(RESULT_OK)
            }
            finish()
        }
    }

    companion object {
        private const val EXTRA_SHOW_DEVICE_OPTION = "show_device_option"

        fun newStartIntent(context: Context, showDeviceOption: Boolean): Intent {
            return Intent(context, WorkoutMapPlaybackOptionsActivity::class.java)
                .putExtra(EXTRA_SHOW_DEVICE_OPTION, showDeviceOption)
        }
    }
}
