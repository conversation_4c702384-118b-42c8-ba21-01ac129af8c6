package com.stt.android.workout.details.graphanalysis.charts

import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.DataSet.Rounding
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.highlight.ChartHighlighter
import com.github.mikephil.charting.highlight.Highlight
import com.github.mikephil.charting.interfaces.datasets.IDataSet
import com.github.mikephil.charting.utils.MPPointD
import java.util.ArrayList
import kotlin.math.abs

class GraphAnalysisChartHighlighter(chart: GraphAnalysisLineChart) :
    ChartHighlighter<GraphAnalysisLineChart>(chart) {

    /**
     * Modified from original to ignore y value when determining closest highlight
     */
    override fun getHighlightForX(
        xVal: Float,
        x: Float,
        y: Float
    ): Highlight? = getHighlightsAtXValue(xVal, x, y).minByOrNull { abs(it.xPx - x) }

    /**
     * Modified from original to also include the secondYAxisData
     *
     * DO NOT START USING THE X AND Y PARAMETERS FOR ANYTHING WITHOUT CHECKING THE USAGE OF
     * THE VERSION THAT DOESN'T TAKE THEM DEFINED BELOW
     */
    override fun getHighlightsAtXValue(xVal: Float, x: Float, y: Float): List<Highlight> {
        mHighlightBuffer.clear()

        fun getHighlightsForLineData(data: LineData?) {
            data?.dataSets?.forEachIndexed { i, dataSet ->
                if (dataSet.isHighlightEnabled) {
                    mHighlightBuffer.addAll(buildHighlights(dataSet, i, xVal, Rounding.CLOSEST))
                }
            }
        }

        getHighlightsForLineData(mChart.data)
        getHighlightsForLineData(mChart.secondYAxisData)

        return mHighlightBuffer
    }

    /**
     * Custom STT method for getting highlights for x value without any screen position params
     */
    fun getHighlightsAtXValue(xVal: Float): List<Highlight> =
        getHighlightsAtXValue(xVal, Float.NaN, Float.NaN)

    /**
     * Modified from original so that it creates special GraphAnalysisChartHighlights,
     * which contain the same more detailed Y-Axis dependency info as GraphAnalysisLineDataSets
     * and a reference to the Entry its highlighting so we don't need to do expensive lookup
     * for it afterwards
     */
    override fun buildHighlights(
        set: IDataSet<*>,
        dataSetIndex: Int,
        xVal: Float,
        rounding: Rounding?
    ): List<Highlight> {
        val highlights = ArrayList<Highlight>()
        var entries = set.getEntriesForXValue(xVal)
        if (entries.size == 0) {
            // Try to find closest x-value and take all entries for that x-value
            val closest = set.getEntryForXValue(xVal, Float.NaN, rounding)
            if (closest != null) {
                entries = set.getEntriesForXValue(closest.x)
            }
        }
        if (entries.size == 0) return highlights
        for (e in entries) {
            val pixels = mChart.getTransformer(
                set.axisDependency
            ).getPixelForValues(e.x, e.y)

            val graphAnalysisAxis = when {
                set is GraphAnalysisLineDataSet -> {
                    set.graphAnalysisYAxisDependency
                }
                set.axisDependency == YAxis.AxisDependency.LEFT -> {
                    GraphAnalysisYAxisDependency.LEFT
                }
                else -> {
                    GraphAnalysisYAxisDependency.RIGHT_FIRST
                }
            }

            highlights.add(
                GraphAnalysisChartHighlight(
                    e.x,
                    e.y,
                    pixels.x.toFloat(),
                    pixels.y.toFloat(),
                    dataSetIndex,
                    -1,
                    graphAnalysisAxis,
                    e
                )
            )
        }
        return highlights
    }

    /**
     * If the chart doesn't have data on the default axis, the super method can't handle it properly
     * so we in that case do the same lookup on the second right axis transformer
     */
    override fun getValsForTouch(x: Float, y: Float): MPPointD {
        return if (mChart.data.dataSetCount == 0 && mChart.secondYAxisData.dataSetCount > 0) {
            mChart.secondRightAxisTransformer.getValuesByTouchPoint(x, y)
        } else {
            super.getValsForTouch(x, y)
        }
    }

    /**
     * Expose the buffer so that the Chart doesn't need to do the lookup for Entries at the X position
     * again
     */
    fun getLatestHighlightBuffer(): List<GraphAnalysisChartHighlight> =
        mHighlightBuffer.filterIsInstance(GraphAnalysisChartHighlight::class.java)
}
