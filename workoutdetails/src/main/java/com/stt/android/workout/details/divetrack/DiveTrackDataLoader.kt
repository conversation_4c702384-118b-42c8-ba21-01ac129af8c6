package com.stt.android.workout.details.divetrack

import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.workout.details.DiveTrackData
import com.stt.android.workout.details.DiveTrackFullscreenNavEvent
import com.stt.android.workout.details.NavigationEventDispatcher
import com.stt.android.workout.details.sml.SmlDataLoader
import com.stt.android.workoutdetail.divetrack.DiveTrackUtil
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

interface DiveTrackDataLoader {
    val diveTrackStateFlow: StateFlow<ViewState<DiveTrackData?>>
    fun loadDiveTrackData(header: WorkoutHeader): Flow<ViewState<DiveTrackData?>>
}

@ActivityRetainedScoped
class DefaultDiveTrackDataLoader
@Inject constructor(
    private val smlDataLoader: SmlDataLoader,
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope,
    private val navigationEventDispatcher: NavigationEventDispatcher,
) : DiveTrackDataLoader {

    private lateinit var workoutHeader: WorkoutHeader

    override val diveTrackStateFlow: MutableStateFlow<ViewState<DiveTrackData?>> =
        MutableStateFlow(loading())

    override fun loadDiveTrackData(header: WorkoutHeader): Flow<ViewState<DiveTrackData?>> {
        workoutHeader = header
        activityRetainedCoroutineScope.launch(IO) {
            smlDataLoader.smlStateFlow.collect { smlDataState ->
                diveTrackStateFlow.value = if (smlDataState.isLoading()) {
                    loading(null)
                } else {
                    loaded(
                        DiveTrackUtil.diveTrackFromSml(smlDataState.data)
                            ?.run { DiveTrackData(this, ::navigateToFullScreen) }
                    )
                }
            }
        }
        return diveTrackStateFlow
    }

    private fun navigateToFullScreen() {
        if (::workoutHeader.isInitialized) {
            navigationEventDispatcher.dispatchEvent(DiveTrackFullscreenNavEvent(workoutHeader))
        }
    }
}
