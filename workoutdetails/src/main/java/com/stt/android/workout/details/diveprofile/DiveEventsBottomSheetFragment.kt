package com.stt.android.workout.details.diveprofile

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import com.github.mikephil.charting.utils.Utils
import com.stt.android.common.ui.observeK
import com.stt.android.common.ui.observeNotNull
import com.stt.android.domain.sml.Sml
import com.stt.android.ui.utils.SmartBottomSheetDialogFragment
import com.stt.android.workout.details.DiveProfileData
import com.stt.android.workout.details.R
import com.stt.android.workout.details.databinding.DiveProfileShowEventsPopupFragmentBinding
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject
import androidx.core.view.isEmpty

@AndroidEntryPoint
class DiveEventsBottomSheetFragment : SmartBottomSheetDialogFragment() {
    @Inject
    internal lateinit var controller: DiveEventsController

    private val viewModel: DiveEventsViewModel by viewModels()

    private var _binding: DiveProfileShowEventsPopupFragmentBinding? = null
    private val binding get() = _binding!!

    private val diveProfileData = MutableLiveData<DiveProfileData?>()

    private val scrollListener = object : RecyclerView.OnScrollListener() {

        override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
            super.onScrollStateChanged(recyclerView, newState)
            viewModel.setDividerVisibility(!isRecyclerViewAtTop())
        }
    }

    fun setDiveProfileData(diveProfileData: DiveProfileData?) {
        this.diveProfileData.postValue(diveProfileData)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        Utils.init(requireContext())
        _binding = DataBindingUtil.inflate(
            inflater,
            R.layout.dive_profile_show_events_popup_fragment,
            container,
            false
        )
        binding.lifecycleOwner = this
        binding.list.adapter = controller.adapter
        setupLiveData()
        diveProfileData.value?.let { viewModel.loadData(it) }
        return binding.root
    }

    override fun onResume() {
        super.onResume()
        binding.list.addOnScrollListener(scrollListener)
        binding.showDivider = viewModel.showDivider
    }

    override fun onPause() {
        binding.list.removeOnScrollListener(scrollListener)
        super.onPause()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding.unbind()
        _binding = null
    }

    private fun setupLiveData() {
        viewModel.viewState.observeNotNull(viewLifecycleOwner) {
            val data =
                if (it.isLoaded()) it.data ?: return@observeNotNull else return@observeNotNull
            drawGraph(data)
        }
        viewModel.diveEvents.observeK(viewLifecycleOwner) { state ->
            controller.setData(state)
        }
        diveProfileData.observeK(viewLifecycleOwner) { viewModel.loadData(it) }
    }

    private fun drawGraph(data: DiveProfileData) {
        val sml = data.sml ?: return
        val graphData = data.graphData ?: return
        // Setting extra offsets instead of defining margins in the layout gives the chart room to
        // draw icons fully at start and end points
        binding.workoutLineChart.setExtraOffsets(
            Utils.convertPixelsToDp(resources.getDimension(com.stt.android.R.dimen.size_spacing_medium)),
            Utils.convertPixelsToDp(resources.getDimension(com.stt.android.R.dimen.size_spacing_medium)),
            Utils.convertPixelsToDp(resources.getDimension(com.stt.android.R.dimen.size_spacing_medium)),
            Utils.convertPixelsToDp(resources.getDimension(com.stt.android.R.dimen.size_spacing_medium))
        )
        viewLifecycleOwner.lifecycleScope.launch {
            binding.workoutLineChart.setDiveChartData(
                workoutLineChartData = graphData,
                showEvents = true
            )
            createDiveEvents(sml)
        }
    }

    private suspend fun createDiveEvents(sml: Sml) {
        val lineData = binding.workoutLineChart.data
        val dataSet = lineData?.dataSets?.firstOrNull()
        if (dataSet == null || dataSet.entryCount == 0) return

        viewModel.createEvents(sml, dataSet)
    }

    private fun isRecyclerViewAtTop(): Boolean =
        if (binding.list.isEmpty()) {
            true
        } else {
            binding.list.getChildAt(0).top == 0
        }

    companion object {
        const val TAG = "com.stt.android.workout.details.diveprofile.DiveEventsBottomSheetFragment"
    }
}
