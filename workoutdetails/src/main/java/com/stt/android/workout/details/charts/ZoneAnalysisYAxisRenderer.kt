package com.stt.android.workout.details.charts

import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.renderer.YAxisRenderer
import com.github.mikephil.charting.utils.Transformer
import com.github.mikephil.charting.utils.Utils
import com.github.mikephil.charting.utils.ViewPortHandler
import com.stt.android.intensityzone.ZoneRangeWithColor

/**
 * This class extends YAxisRenderer and enhances its functionality to customize the rendering
 * of Y-axis labels and a vertical color bar adjacent to the left Y-axis, based on intensity zones.
 * These intensity zones are defined by a list of ZoneRange objects, with each zone associated
 * with a specific color.
 */
class ZoneAnalysisYAxisRenderer(
    viewPortHandler: ViewPortHandler,
    yAxis: YAxis,
    transformer: Transformer?,
) : YAxisRenderer(viewPortHandler, yAxis, transformer) {

    private var isInverted = false
    private lateinit var zoneRangesAndColors: List<ZoneRangeWithColor>

    override fun drawYLabels(
        c: Canvas,
        fixedPosition: Float,
        positions: FloatArray,
        offset: Float
    ) {
        val from = if (mYAxis.isDrawBottomYLabelEntryEnabled) 0 else 1
        val to =
            if (mYAxis.isDrawTopYLabelEntryEnabled) mYAxis.mEntryCount else mYAxis.mEntryCount - 1

        for (i in from..<to) {
            val text = mYAxis.getFormattedLabel(i)
            c.drawText(text, fixedPosition, positions[i * 2 + 1] + offset, mAxisLabelPaint)
        }
    }

    fun reset() {
        zoneRangesAndColors = emptyList()
    }

    override fun renderAxisLine(c: Canvas) {
        if (!mYAxis.isEnabled || zoneRangesAndColors.isEmpty()) {
            return
        }

        mAxisLinePaint.style = Paint.Style.FILL
        val verticalZoneColorBarWidth = Utils.convertDpToPixel(4f)
        val contentTop = mViewPortHandler.contentTop()
        val contentBottom = mViewPortHandler.contentBottom()
        val left = mViewPortHandler.contentLeft() - verticalZoneColorBarWidth
        val right = mViewPortHandler.contentLeft()

        val lastIndex = zoneRangesAndColors.size - 1
        for ((index, zoneRangeWithColor) in zoneRangesAndColors.withIndex()) {
            val startValue = zoneRangeWithColor.zoneRange.end
            val endValue = zoneRangeWithColor.zoneRange.start
            val color = zoneRangeWithColor.color

            var startPosY = transformer.getPixelForValues(0f, startValue).y.toFloat()
                .coerceIn(contentTop, contentBottom)
            var endPosY = transformer.getPixelForValues(0f, endValue).y.toFloat()
                .coerceIn(contentTop, contentBottom)

            // For the last item, extend the start position to the top of the content area
            if (index == lastIndex) {
                startPosY = contentTop
            }

            // For the first item, extend the end position to the bottom of the content area
            if (index == 0) {
                endPosY = contentBottom
            }

            mAxisLinePaint.color = color
            if (startPosY < endPosY) {
                val rect = RectF(left, startPosY, right, endPosY)
                c.drawRect(rect, mAxisLinePaint)
            }
        }
    }

    fun setZones(isInverted: Boolean, zoneRangesAndColors: List<ZoneRangeWithColor>) {
        this.isInverted = isInverted
        this.zoneRangesAndColors = zoneRangesAndColors
    }
}
