package com.stt.android.workout.details.divetrack

import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowCompat
import androidx.navigation.navArgs
import com.stt.android.compose.util.setContentWithTheme
import com.stt.android.multimedia.sportie.SportieShareSource
import com.stt.android.workouts.sharepreview.WorkoutSharePreviewActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class FullscreenDiveTrackActivity : AppCompatActivity() {
    private val viewModel: FullscreenDiveTrackViewModel by viewModels()
    private val args: FullscreenDiveTrackActivityArgs by navArgs()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // Allow content to go behind status and gesture navigation bars
        WindowCompat.setDecorFitsSystemWindows(window, false)

        setContentWithTheme {
            DiveTrackScreen(::shareSportie, viewModel)
        }

        viewModel.loadDiveTrack()
    }

    private fun shareSportie() {
        val (intent, options) = WorkoutSharePreviewActivity.newStartIntent(
            args.workoutHeader,
            this,
            if (args.workoutHeader.isPolylineEmpty) 0 else 1,
            SportieShareSource.WORKOUT_DETAILS,
        )
        startActivity(intent, options.toBundle())
    }
}
