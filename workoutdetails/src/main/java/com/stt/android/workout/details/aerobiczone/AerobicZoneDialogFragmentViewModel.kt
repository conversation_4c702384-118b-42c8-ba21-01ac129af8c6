package com.stt.android.workout.details.aerobiczone

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.amersports.formatter.Unit
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.soy.algorithms.intensity.IntensityZones
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.core.R
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.sml.FetchSmlUseCase
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.user.UserSettings
import com.stt.android.domain.workouts.extensions.intensity.GetIntensityExtensionUseCase
import com.stt.android.hr.HeartRateZone
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.usecases.CalculateDFAUseCase
import com.stt.android.workout.details.aerobiczone.AerobicZoneDialogFragmentCreatorImpl.Companion.AEROBIC_ZONE_INFO_SHEET_WORKOUT_ID
import com.stt.android.workout.details.aerobiczone.AerobicZoneDialogFragmentCreatorImpl.Companion.AEROBIC_ZONE_INFO_SHEET_WORKOUT_KEY
import com.suunto.algorithms.ddfa.DynamicDFAUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.roundToInt

@HiltViewModel
class AerobicZoneDialogFragmentViewModel @Inject constructor(
    private val intensityExtensionUseCase: GetIntensityExtensionUseCase,
    private val userSettingsController: UserSettingsController,
    private val fetchSmlUseCase: FetchSmlUseCase,
    private val calculateDfaUseCase: CalculateDFAUseCase,
    private val infoModelFormatter: InfoModelFormatter,
    private val workoutHeaderController: WorkoutHeaderController,
    private val currentUserController: CurrentUserController,
    dispatchers: CoroutinesDispatchers,
    savedStateHandle: SavedStateHandle,
) : ViewModel() {
    private val workoutId = savedStateHandle.get<Int>(AEROBIC_ZONE_INFO_SHEET_WORKOUT_ID)
        ?: throw IllegalStateException("Workout id is not provided")
    private val workoutKey = savedStateHandle.get<String>(AEROBIC_ZONE_INFO_SHEET_WORKOUT_KEY)

    private val _uiState =
        MutableStateFlow<AerobicZoneDialogFragmentUiState>(AerobicZoneDialogFragmentUiState.Loading)
    val uiState = _uiState.asStateFlow()

    private val unitConverter = JScienceUnitConverter()

    init {
        viewModelScope.launch(dispatchers.io) {
            val extension = runSuspendCatching {
                intensityExtensionUseCase.loadLocalExtension(workoutId)
            }.getOrElse { e ->
                Timber.w(e, "intensity extension loading failed for workoutId:$workoutId")
                null
            } ?: run {
                _uiState.value = AerobicZoneDialogFragmentUiState.NoData
                return@launch
            }

            val sml = runSuspendCatching {
                fetchSmlUseCase.fetchSml(workoutId, workoutKey)
            }.getOrElse { e ->
                Timber.w(e, "Sml loading failed for workoutId:$workoutId")
                null
            }

            val ddfaResults = calculateDfaUseCase(
                workoutId = workoutId,
                sml = sml,
            )

            _uiState.value = AerobicZoneDialogFragmentUiState.Data(
                heartRateZoneInfoUiState = createHeartRateZoneInfo(
                    ddfaResult = ddfaResults.firstOrNull(), // TODO
                    intensityZones = extension.intensityZones,
                    sml = sml,
                ),
                paceZoneInfoUiState = createPaceZoneInfo(
                    intensityZones = extension.intensityZones,
                ),
                powerZoneInfoUiState = createPowerZoneInfo(
                    intensityZones = extension.intensityZones,
                ),
            )
        }
    }

    private fun createHeartRateZoneInfo(
        ddfaResult: DynamicDFAUtils.Result?,
        intensityZones: IntensityZones,
        sml: Sml?,
    ): AerobicZoneInfoUiState {
        val maxHr = getMaxHr(sml)
        val hrZonesInBpm = intensityZones.hr?.hzToBpm()
        val zone5LowerLimit = hrZonesInBpm?.zone5LowerLimit?.toInt()
            ?: HeartRateZone.ANAEROBIC.getHighBpm(maxHr)
        val zone4LowerLimit = hrZonesInBpm?.zone4LowerLimit?.toInt()
            ?: HeartRateZone.AEROBIC.getHighBpm(maxHr)
        val zone3LowerLimit = hrZonesInBpm?.zone3LowerLimit?.toInt()
            ?: HeartRateZone.ENDURANCE.getHighBpm(maxHr)
        val zone2LowerLimit = hrZonesInBpm?.zone2LowerLimit?.toInt()
            ?: HeartRateZone.WARMUP.getHighBpm(maxHr)
        return AerobicZoneInfoUiState(
            maxHr = ZoneData(
                value = maxHr.toString(),
                percentage = 100, // Max hr is always 100%
            ),
            anaerobicThreshold = ZoneData(
                value = zone5LowerLimit.toString(),
                percentage = (zone5LowerLimit * 100) / maxHr,
            ),
            measuredAnaerobicThreshold = ddfaResult?.zones
                ?.anaerobicThreshold
                ?.let { threshold -> calculateThreshold(SummaryItem.HRANAEROBICTHRESHOLD, threshold.inBpm) },
            zone4Minimum = ZoneData(
                value = zone4LowerLimit.toString(),
                percentage = (zone4LowerLimit * 100) / maxHr,
            ),
            aerobicThreshold = ZoneData(
                value = zone3LowerLimit.toString(),
                percentage = (zone3LowerLimit * 100) / maxHr,
            ),
            measuredAerobicThreshold = ddfaResult?.zones
                ?.aerobicThreshold
                ?.let { threshold -> calculateThreshold(SummaryItem.HRAEROBICTHRESHOLD, threshold.inBpm) },
            zone2Minimum = ZoneData(
                value = zone2LowerLimit.toString(),
                percentage = (zone2LowerLimit * 100) / maxHr,
            ),
            unit = R.string.TXT_BPM,
        )
    }

    private fun getMaxHr(sml: Sml?): Int {
        // 1) Try to read from SML.
        sml?.summary?.header?.personal?.personalMaxHrInBpm?.let { return it }

        // 2) Fallback to read from workout header.
        val workoutHeader = workoutHeaderController.find(listOf(workoutId))
            .firstOrNull()
            ?: return UserSettings.DEFAULT_MAX_HR

        workoutHeader.heartRateUserSetMax
            .roundToInt()
            .takeIf { it > 0 }
            ?.let { return it }

        // 3) Finally, read from user settings for current user, or default max HR for other's workout.
        return if (workoutHeader.username == currentUserController.username) {
            userSettingsController.settings.hrMaximum
        } else {
            UserSettings.DEFAULT_MAX_HR
        }
    }

    // Decided to use the same logic as we format values in the summary grid to make sure we don't get different values
    private fun calculateThreshold(summaryItem: SummaryItem, value: Double?): ZoneData? {
        if (value == null) return null

        val workoutValue = infoModelFormatter.formatValue(
            summaryItem,
            unitConverter.convert(value, Unit.RPM, Unit.HZ)
        )
        return workoutValue.value?.let {
            if (it.isNotBlank()) {
                ZoneData(value = it)
            } else {
                ZoneData(value = value.toInt().toString())
            }
        }
    }

    private fun createPaceZoneInfo(
        intensityZones: IntensityZones,
    ): AerobicZoneInfoUiState {
        val speedZone = intensityZones.speed
        val zone5LowerLimit = speedZone?.zone5LowerLimit ?: 0.0F
        val zone4LowerLimit = speedZone?.zone4LowerLimit ?: 0.0F
        val zone3LowerLimit = speedZone?.zone3LowerLimit ?: 0.0F
        val zone2LowerLimit = speedZone?.zone2LowerLimit ?: 0.0F
        return AerobicZoneInfoUiState(
            maxHr = null,
            anaerobicThreshold = ZoneData(
                value = infoModelFormatter.formatValue(SummaryItem.AVGPACE, zone5LowerLimit).value.orEmpty(),
            ),
            zone4Minimum = ZoneData(
                value = infoModelFormatter.formatValue(SummaryItem.AVGPACE, zone4LowerLimit).value.orEmpty(),
            ),
            aerobicThreshold = ZoneData(
                value = infoModelFormatter.formatValue(SummaryItem.AVGPACE, zone3LowerLimit).value.orEmpty(),
            ),
            zone2Minimum = ZoneData(
                value = infoModelFormatter.formatValue(SummaryItem.AVGPACE, zone2LowerLimit).value.orEmpty(),
            ),
            unit = userSettingsController.settings.measurementUnit.paceUnit,
        )
    }

    private fun createPowerZoneInfo(
        intensityZones: IntensityZones,
    ): AerobicZoneInfoUiState {
        val powerZone = intensityZones.power
        val zone5LowerLimit = powerZone?.zone5LowerLimit ?: 0.0F
        val zone4LowerLimit = powerZone?.zone4LowerLimit ?: 0.0F
        val zone3LowerLimit = powerZone?.zone3LowerLimit ?: 0.0F
        val zone2LowerLimit = powerZone?.zone2LowerLimit ?: 0.0F
        return AerobicZoneInfoUiState(
            maxHr = null,
            anaerobicThreshold = ZoneData(
                value = infoModelFormatter.formatValue(SummaryItem.AVGPOWER, zone5LowerLimit).value.orEmpty(),
            ),
            zone4Minimum = ZoneData(
                value = infoModelFormatter.formatValue(SummaryItem.AVGPOWER, zone4LowerLimit).value.orEmpty(),
            ),
            aerobicThreshold = ZoneData(
                value = infoModelFormatter.formatValue(SummaryItem.AVGPOWER, zone3LowerLimit).value.orEmpty(),
            ),
            zone2Minimum = ZoneData(
                value = infoModelFormatter.formatValue(SummaryItem.AVGPOWER, zone2LowerLimit).value.orEmpty(),
            ),
            unit = R.string.TXT_W,
        )
    }
}
