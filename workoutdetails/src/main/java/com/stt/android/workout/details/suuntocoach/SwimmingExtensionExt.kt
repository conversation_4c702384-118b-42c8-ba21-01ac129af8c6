package com.stt.android.workout.details.suuntocoach

import com.stt.android.core.R
import com.stt.android.domain.workouts.extensions.SwimmingExtension

fun SwimmingExtension.getCoachValues(): List<CoachValueItem> {
    return listOf(
        CoachValueItem(
            R.string.workout_values_headline_breaststroke_avg_breath_angle,
            breaststrokeAvgBreathAngle,
            SwimmingPropertyType.BREASTSTROKE_AVG_BREATHE_ANGLE
        ),
        CoachValueItem(
            R.string.workout_values_headline_freestyle_avg_breath_angle,
            freestyleAvgBreathAngle,
            SwimmingPropertyType.FREESTYLE_AVG_BREATHE_ANGLE
        ),
        CoachValueItem(
            R.string.workout_values_headline_breaststroke_head_angel,
            breaststrokeHeadAngle,
            SwimmingPropertyType.BREASTSTROKE_HEAD_ANGLE
        ),
        CoachValueItem(
            R.string.workout_values_headline_freestyle_head_angel,
            freestylePitchAngle,
            SwimmingPropertyType.FREESTYLE_HEAD_ANGLE
        ),
        CoachValueItem(
            R.string.workout_values_headline_breaststroke_percent,
            breaststrokePercentage,
            SwimmingPropertyType.BREASTSTROKE_PERCENT
        ),
        CoachValueItem(
            R.string.workout_values_headline_freestyle_percent,
            freestylePercentage,
            SwimmingPropertyType.FREESTYLE_PERCENT
        ),
        CoachValueItem(
            R.string.workout_values_headline_freestyle_max_breath_angle,
            freestyleMaxBreathAngle,
            SwimmingPropertyType.FREESTYLE_MAX_BREATHE_ANGLE
        ),
        CoachValueItem(
            R.string.workout_values_headline_breaststroke_max_breath_angle,
            breaststrokeMaxBreathAngle,
            SwimmingPropertyType.BREASTSTROKE_MAX_BREATHE_ANGLE
        ),
    ).asSequence().filter {
        val value = it.value
        value != null && value > 0
    }.sortedBy { it.valueType.showPriority }
        //  only need the first 3 items and feedbackRes have to be correct res id
        .filter { it.feedbackRes > 0 }.take(3)
        .toList()
}
