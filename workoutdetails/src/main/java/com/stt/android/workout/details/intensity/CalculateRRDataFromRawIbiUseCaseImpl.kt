package com.stt.android.workout.details.intensity

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.logbook.SuuntoLogbookSample
import com.stt.android.usecases.CalculateRRDataFromRawIbiUseCase
import com.stt.android.workout.details.charts.isSafeValue
import kotlinx.coroutines.withContext
import javax.inject.Inject
import kotlin.math.roundToInt

class CalculateRRDataFromRawIbiUseCaseImpl @Inject constructor(
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : CalculateRRDataFromRawIbiUseCase {
    override suspend fun invoke(
        workoutResumePauseTimePairs: List<Pair<Long, Long>>,
        rawIbis: List<SuuntoLogbookSample.HR.RawIbi>,
    ): List<Int> = withContext(coroutinesDispatchers.computation) {
        if (rawIbis.size == 1) {
            // There is a case where there is only one R-R item that contains all the ibis
            // So the rawIbis contains one item only
            rawIbis[0].values
        } else if (rawIbis.size >= 2) {
            val iBIExtensionBuilder = IBIExtensionBuilder()
            rawIbis.forEach { rawIbi ->
                iBIExtensionBuilder.addIBI(timestamp = rawIbi.timestamp, ibi = rawIbi.values)
            }
            buildList {
                workoutResumePauseTimePairs.forEach { (resumeTimestamp, pauseTimestamp) ->
                    iBIExtensionBuilder.setTimePeriod(
                        TimePeriod(
                            start = resumeTimestamp,
                            end = pauseTimestamp,
                        )
                    )
                    iBIExtensionBuilder
                        .build()
                        .forEach { add(it.ibi) }
                }
            }
        } else {
            emptyList()
        }.filter { it.isSafeValue } // This will filter out NaN and non finite values
            .map { (it * 1000f).roundToInt() }
    }
}
