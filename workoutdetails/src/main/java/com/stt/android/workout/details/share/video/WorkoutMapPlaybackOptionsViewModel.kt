package com.stt.android.workout.details.share.video

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import androidx.lifecycle.ViewModel
import com.stt.android.utils.STTConstants.WorkoutMapPlaybackPreferences
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

@HiltViewModel
class WorkoutMapPlaybackOptionsViewModel @Inject constructor(
    @ApplicationContext context: Context,
) : ViewModel(), SharedPreferences.OnSharedPreferenceChangeListener {

    private val preferences = context.workoutMapPlaybackPreferences

    private val _options = MutableStateFlow(WorkoutMapPlaybackOptions.from(preferences))
    val options = _options.asStateFlow()

    var hasChanged = false
        private set

    init {
        preferences.registerOnSharedPreferenceChangeListener(this)
    }

    override fun onCleared() {
        preferences.unregisterOnSharedPreferenceChangeListener(this)
        super.onCleared()
    }

    override fun onSharedPreferenceChanged(preferences: SharedPreferences, key: String?) {
        _options.tryEmit(WorkoutMapPlaybackOptions.from(preferences))
    }

    fun setUserInformation(enabled: Boolean) {
        hasChanged = true
        preferences.edit { putBoolean(WorkoutMapPlaybackPreferences.USERNAME, enabled) }
    }

    fun setExerciseDate(enabled: Boolean) {
        hasChanged = true
        preferences.edit { putBoolean(WorkoutMapPlaybackPreferences.START_TIME, enabled) }
    }

    fun setLocationInformation(enabled: Boolean) {
        hasChanged = true
        preferences.edit { putBoolean(WorkoutMapPlaybackPreferences.LOCATION, enabled) }
    }

    fun setDeviceInformation(enabled: Boolean) {
        hasChanged = true
        preferences.edit { putBoolean(WorkoutMapPlaybackPreferences.DEVICE, enabled) }
    }
}
