package com.stt.android.workout.details.laps.advanced

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.children
import androidx.viewpager.widget.PagerAdapter
import com.stt.android.R
import com.stt.android.ThemeColors
import com.stt.android.databinding.AdvancedLapsLapTableBinding
import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.extensions.stringRes
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.utils.EpoxyConditionalDividerItemDecoration
import com.stt.android.ui.utils.SmarterViewPager
import com.stt.android.ui.utils.WideScreenPaddingDecoration
import com.stt.android.workout.details.AdvancedLapsTableRowBindingModel_
import com.stt.android.workout.details.laps.advanced.table.AdvancedLapsTableController
import com.stt.android.workout.details.laps.advanced.table.AdvancedLapsTableItems
import com.stt.android.workout.details.laps.advanced.table.OnSelectColumnRequested

class AdvancedLapsViewPagerAdapter(
    private val context: Context,
    private val infoModelFormatter: InfoModelFormatter,
    val stId: Int,
    var tableData: List<AdvancedLapsTableItems>,
    private val onSelectColumnRequested: OnSelectColumnRequested?,
    private val lapSelectionEnabled: Boolean,
    var isLapsTableColouringEnabled: Boolean
) : PagerAdapter() {

    private var currentPosition = -1

    override fun getCount(): Int = tableData.count()

    override fun isViewFromObject(view: View, item: Any): Boolean {
        return view == item
    }

    override fun destroyItem(container: ViewGroup, position: Int, item: Any) {
        container.removeView(item as View)
    }

    // Makes notifyDataSetChanged rebuild everything
    override fun getItemPosition(item: Any) = POSITION_NONE

    override fun instantiateItem(container: ViewGroup, position: Int): Any {
        val table = tableData[position]
        val binding = AdvancedLapsLapTableBinding.inflate(
            LayoutInflater.from(context),
            container,
            false
        )

        val controller = AdvancedLapsTableController(
            table.lapsTableType,
            lapSelectionEnabled,
            isLapsTableColouringEnabled,
            onSelectColumnRequested,
            infoModelFormatter,
            context
        )
        controller.setData(table)
        binding.list.setHasFixedSize(false)
        binding.list.addItemDecoration(WideScreenPaddingDecoration(context.resources, context.theme))
        val dividerColor = ThemeColors.resolveColor(context, R.attr.suuntoDividerColor)
        val dividerSize = context.resources.getDimensionPixelSize(R.dimen.size_divider)
        val dividerItemDecoration = EpoxyConditionalDividerItemDecoration(
            dividerColor,
            false,
        ) { prev, next ->
            if (prev is AdvancedLapsTableRowBindingModel_ || next is AdvancedLapsTableRowBindingModel_) {
                dividerSize
            } else null
        }
        binding.list.addItemDecoration(dividerItemDecoration)
        binding.list.setController(controller)
        binding.list.isNestedScrollingEnabled = false // See setPrimaryItem where this is re-enabled for current page

        binding.root.tag = table.lapsTableType
        container.addView(binding.root)
        return binding.root
    }

    override fun getPageTitle(position: Int): CharSequence = getLapsTableTitle(position, context)

    private fun getLapsTableTitle(position: Int, context: Context): CharSequence {
        val advancedLapsTableItems = tableData[position]
        val autoLapLength = advancedLapsTableItems.autoLapLength

        return when (val type = advancedLapsTableItems.lapsTableType) {
            LapsTableType.ONE_KM_AUTO_LAP,
            LapsTableType.FIVE_KM_AUTO_LAP,
            LapsTableType.TEN_KM_AUTO_LAP,
            LapsTableType.ONE_MILE_AUTO_LAP,
            LapsTableType.FIVE_MILE_AUTO_LAP,
            LapsTableType.TEN_MILE_AUTO_LAP,
            LapsTableType.DISTANCE_AUTO_LAP ->
                infoModelFormatter.formatDistanceAutoLapTitle(autoLapLength?.toDouble(), type)
            LapsTableType.DURATION_AUTO_LAP ->
                infoModelFormatter.formatDurationAutoLapTitle(autoLapLength?.toDouble(), type)
            LapsTableType.MANUAL,
            LapsTableType.INTERVAL,
            LapsTableType.DOWNHILL,
            LapsTableType.DIVE -> context.getString(type.stringRes())
        }
    }

    fun getLapsTableType(position: Int): LapsTableType {
        return tableData[position].lapsTableType
    }

    override fun setPrimaryItem(container: ViewGroup, position: Int, item: Any) {
        super.setPrimaryItem(container, position, item)

        if (container is SmarterViewPager && item is View) {
            // Nested scrolling is broken when an old ViewPager is a child of a View with
            // BottomSheetBehavior, and for this ViewPager that happens in the graph analysis'
            // lap selector dialog. The Behavior cares only about its first nested scrolling
            // child (or grandchild), so the scrolling works only for the first page of the ViewPager.
            // Keep only active page's nested scrolling enabled as a workaround.
            container.children.forEach {
                it.isNestedScrollingEnabled = false
            }
            item.isNestedScrollingEnabled = true

            currentPosition = position
            container.measureCurrentView(item)
        }
    }
}
