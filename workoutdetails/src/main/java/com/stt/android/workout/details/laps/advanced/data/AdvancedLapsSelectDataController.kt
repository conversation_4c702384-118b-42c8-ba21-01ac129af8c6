package com.stt.android.workout.details.laps.advanced.data

import android.content.Context
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.stt.android.core.domain.AdvancedLapsSelectDataCategoryItem
import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.extensions.localize
import com.stt.android.workout.details.advancedLapsSelectDataCategoryItem
import com.stt.android.workout.details.advancedLapsSelectDataSummaryItem
import javax.inject.Inject

class AdvancedLapsSelectDataController
@Inject constructor(
    private val context: Context
) : ViewStateEpoxyController<AdvancedLapsSelectDataContainer?>() {
    override fun buildModels(viewState: ViewState<AdvancedLapsSelectDataContainer?>) {
        viewState.data?.let { data ->
            data.map.keys
                // Categories should be sorted alphabetically
                .sortedBy { it.localize(context) }
                .forEach { categoryItem: AdvancedLapsSelectDataCategoryItem ->
                    advancedLapsSelectDataCategoryItem {
                        id(categoryItem.key)
                        item(categoryItem)
                    }

                    data.map[categoryItem]?.let { summaryItems ->
                        summaryItems
                            // Summary items should be sorted alphabetically
                            .sortedBy {
                                it.dataType.localize(
                                    context = context,
                                    isDownhill = it.lapsTableType == LapsTableType.DOWNHILL
                                )
                            }
                            .forEach { summaryItem ->

                                advancedLapsSelectDataSummaryItem {
                                    id(summaryItem.dataType.key)
                                    item(summaryItem)
                                    onClickListener { model, _, _, _ ->
                                        val modelItem = model.item()
                                        modelItem.onClicked(modelItem.columnIndex, modelItem.dataType)
                                    }
                                }
                            }
                    }
                }
        }
        super.buildModels(viewState)
    }
}
