<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.DETECT_SCREEN_CAPTURE" />

    <application
        android:supportsRtl="false"
        tools:replace="android:supportsRtl">
        <activity
            android:name=".WorkoutDetailsActivityNew"
            android:launchMode="singleTop"
            android:parentActivityName="com.stt.android.home.HomeActivity"
            android:theme="@style/WhiteTheme"
            android:windowSoftInputMode="stateUnchanged">
            <!--
                Due to a bug in Android Gradle plugin 3.6, we have to comment out nav-graph
                The issue is fixed in AGP 4.0 so when we that reaches stable we can uncomment it
            -->
            <!--            <nav-graph android:value="@navigation/workout_details_nav" />-->
        </activity>

        <activity
            android:name=".graphanalysis.fullscreen.FullscreenGraphAnalysisActivity"
            android:launchMode="singleTop"
            android:parentActivityName=".WorkoutDetailsActivityNew"
            android:theme="@style/WhiteTheme"
            android:windowSoftInputMode="stateUnchanged" />

        <activity android:name=".divetrack.FullscreenDiveTrackActivity"
            android:parentActivityName=".WorkoutDetailsActivityNew"
            android:theme="@style/WhiteTheme" />

        <activity
            android:name=".share.WorkoutMapPlaybackActivity"
            android:screenOrientation="portrait"
            android:theme="@style/WhiteTheme"
            tools:ignore="DiscouragedApi" />

        <activity
            android:name=".share.video.WorkoutMapPlaybackOptionsActivity"
            android:label="@string/workout_map_playback_options_title"
            android:theme="@style/WhiteTheme" />
    </application>
</manifest>
