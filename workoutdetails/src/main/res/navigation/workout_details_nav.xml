<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/workout_details_nav"
    app:startDestination="@id/workoutDetailsFragmentNew">

    <fragment
        android:id="@+id/workoutDetailsFragmentNew"
        android:name="com.stt.android.workout.details.WorkoutDetailsFragmentNew"
        tools:layout="@layout/workout_details_fragment_new">
        <action
            android:id="@+id/action_workoutDetailsFragmentNew_to_workoutPhotoPagerFragment"
            app:destination="@id/workoutPhotoPagerFragment" />
        <action
            android:id="@+id/action_workoutDetailsFragmentNew_to_workoutMapGraphAnalysisFragment"
            app:destination="@id/workoutMapGraphAnalysisFragment">
        </action>
        <action
            android:id="@+id/action_workoutDetailsFragmentNew_to_reactionUsersList"
            app:destination="@id/reactionUsersListFragment">
            <argument
                android:name="workoutKey"
                app:argType="string" />
        </action>
        <action
            android:id="@+id/action_workoutDetailsFragmentNew_to_workoutDetailsFragmentNew"
            app:destination="@id/workoutDetailsFragmentNew">
            <argument
                android:name="workoutId"
                app:argType="integer" />
            <argument
                android:name="multisportPartActivity"
                app:argType="com.stt.android.domain.sml.MultisportPartActivity" />
            <argument
                android:name="autoPlayback"
                app:argType="boolean"
                android:defaultValue="false" />
        </action>
        <action
            android:id="@+id/action_workoutDetailsFragmentNew_to_commentsDialogFragment"
            app:destination="@id/commentsDialogFragment">
            <argument
                android:name="workoutHeader"
                app:argType="com.stt.android.domain.workouts.WorkoutHeader" />
        </action>
        <action
            android:id="@+id/action_workoutDetailsFragmentNew_to_landscapeAnalysisGraphActivity"
            app:destination="@id/landscapeAnalysisGraphActivity" >
            <argument
                android:name="graphType"
                app:argType="com.stt.android.core.domain.GraphType" />
            <argument
                android:name="workoutHeader"
                app:argType="com.stt.android.domain.workouts.WorkoutHeader" />
        </action>
        <action
            android:id="@+id/action_workoutDetailsFragmentNew_to_landscapeMultisportAnalysisGraphActivity"
            app:destination="@id/landscapeAnalysisGraphActivity" >
            <argument
                android:name="graphType"
                app:argType="com.stt.android.core.domain.GraphType" />
            <argument
                android:name="workoutHeader"
                app:argType="com.stt.android.domain.workouts.WorkoutHeader" />
            <argument
                android:name="multisportPartActivity"
                app:argType="com.stt.android.domain.sml.MultisportPartActivity" />
        </action>
        <action
            android:id="@+id/action_workoutDetailsFragmentNew_to_similarWorkoutsActivity"
            app:destination="@id/similarWorkoutsActivity" />

        <action
            android:id="@+id/action_workoutDetailsFragmentNew_to_workoutCompetitionActivity"
            app:destination="@id/workoutCompetitionActivity" />

        <action
            android:id="@+id/action_workoutDetailsFragmentNew_to_userProfileActivity"
            app:destination="@id/userProfileActivity" />
        <action
            android:id="@+id/action_workoutDetailsFragmentNew_to_editDetailsActivity"
            app:destination="@id/editDetailsActivity" />
        <action
            android:id="@+id/action_workoutDetailsFragmentNew_to_followRouteActivity"
            app:destination="@id/workoutSettingsActivity">
            <argument
                android:name="com.stt.android.FOLLOW_WORKOUT_HEADER"
                app:argType="com.stt.android.domain.workouts.WorkoutHeader" />
        </action>
        <action
            android:id="@+id/action_workoutDetailsFragmentNew_to_followRoute"
            app:destination="@id/workoutSettingsActivity">
            <argument
                android:name="com.stt.android.FOLLOW_WORKOUT_HEADER"
                app:argType="com.stt.android.domain.workouts.WorkoutHeader" />
        </action>
        <action
            android:id="@+id/action_workoutDetailsFragmentNew_to_ghostTarget"
            app:destination="@id/workoutSettingsActivity">
            <argument
                android:name="com.stt.android.GHOST_TARGET_WORKOUT_HEADER"
                app:argType="com.stt.android.domain.workouts.WorkoutHeader" />
        </action>
        <action
            android:id="@+id/action_workoutDetailsFragmentNew_to_workoutSharePreviewActivity"
            app:destination="@id/workoutSharePreviewActivity">
            <argument
                android:name="workoutHeader"
                app:argType="com.stt.android.domain.workouts.WorkoutHeader" />
            <argument
                android:name="com.stt.android.CURRENT_ITEM_INDEX"
                app:argType="integer" />
            <argument
                android:name="EXTRA_SHARE_SOURCE"
                app:argType="com.stt.android.multimedia.sportie.SportieShareSource" />
        </action>
        <action
            android:id="@+id/action_workoutDetailsFragmentNew_to_recentWorkoutTrendActivity"
            app:destination="@id/recentWorkoutTrendActivity">
            <argument
                android:name="workoutHeader"
                app:argType="com.stt.android.domain.workouts.WorkoutHeader" />
        </action>
        <action
            android:id="@+id/action_workoutDetailsFragmentNew_to_workoutComparisonActivity"
            app:destination="@id/workoutComparisonActivity">
            <argument
                android:name="CURRENT_WORKOUT_ARG"
                app:argType="com.stt.android.domain.workouts.WorkoutHeader" />
            <argument
                android:name="CURRENT_RANK_ARG"
                app:argType="integer" />
            <argument
                android:name="TARGET_WORKOUT_ARG"
                app:nullable="true"
                app:argType="com.stt.android.domain.workouts.WorkoutHeader" />
            <argument
                android:name="TARGET_RANK_ARG"
                app:argType="integer" />
            <argument
                android:name="ANALYTICS_SOURCE"
                app:argType="string" />
        </action>
        <action
            android:id="@+id/action_workoutDetailsFragmentNew_to_recentWorkoutSummaryActivity"
            app:destination="@id/recentWorkoutSummaryActivity">
            <argument
                android:name="workoutHeader"
                app:argType="com.stt.android.domain.workouts.WorkoutHeader" />
        </action>
        <action
            android:id="@+id/action_workoutDetailsFragmentNew_to_fullscreenDiveTrackActivity"
            app:destination="@id/fullscreenDiveTrackActivity" />
        <!-- In a bright future we can add deeplinks here -->
    </fragment>
    <fragment
        android:id="@+id/workoutMapGraphAnalysisFragment"
        android:name="com.stt.android.workout.details.graphanalysis.map.WorkoutMapGraphAnalysisFragment"
        android:label="WorkoutMapGraphAnalysisFragment">
        <argument
            android:name="workoutId"
            app:argType="integer" />
        <argument
            android:name="multisportPartActivity"
            app:argType="com.stt.android.domain.sml.MultisportPartActivity"
            app:nullable="true" />
        <argument
            android:name="autoPlayback"
            app:argType="boolean" />
        <argument
            android:name="com.stt.android.NAVIGATED_FROM_SOURCE"
            app:argType="string" />
        <argument
            android:name="initialMainGraphType"
            app:argType="com.stt.android.core.domain.GraphType"
            app:nullable="true"
            android:defaultValue="@null" />
    </fragment>
    <fragment
        android:id="@+id/workoutPhotoPagerFragment"
        android:name="com.stt.android.workout.details.photopager.WorkoutPhotoPagerFragment"
        android:label="WorkoutPhotoPagerFragment" />
    <fragment
        android:id="@+id/reactionUsersListFragment"
        android:name="com.stt.android.workout.details.reactions.ReactionUserListFragment"
        android:label="@string/title_likes">
        <argument
            android:name="workoutKey"
            app:argType="string" />
    </fragment>
    <dialog
        android:id="@+id/commentsDialogFragment"
        android:name="com.stt.android.workoutdetail.comments.CommentsDialogFragment"
        tools:layout="@layout/dialog_workout_comments">
        <argument
            android:name="workoutHeader"
            app:argType="com.stt.android.domain.workouts.WorkoutHeader" />
    </dialog>
    <activity
        android:id="@+id/landscapeAnalysisGraphActivity"
        android:name="com.stt.android.workouts.details.analysis.LandscapeAnalysisGraphActivity"
        android:label="LandscapeAnalysisGraphActivity" />
    <activity
        android:id="@+id/similarWorkoutsActivity"
        android:name="com.stt.android.ui.activities.SimilarWorkoutsActivity"
        android:label="SimilarWorkoutsActivity">
        <argument
            android:name="similarTag"
            app:argType="string" />
        <argument
            android:name="referenceWorkout"
            app:argType="com.stt.android.domain.workouts.WorkoutHeader" />
    </activity>

    <activity
        android:id="@+id/workoutCompetitionActivity"
        android:name="com.stt.android.ui.activities.competition.WorkoutCompetitionActivity"
        android:label="WorkoutCompetitionActivity">
        <argument
            android:name="competitionWorkoutResult"
            app:argType="com.stt.android.domain.workouts.competition.CompetitionWorkoutSummary" />
        <argument
            android:name="referenceWorkout"
            app:argType="com.stt.android.domain.workouts.WorkoutHeader" />
    </activity>
    <activity
        android:id="@+id/userProfileActivity"
        android:name="com.stt.android.social.userprofileV2.UserProfileActivity"
        android:label="UserProfileActivity">
        <argument
            android:name="com.stt.android.KEY_USER_NAME"
            app:argType="string" />
    </activity>
    <activity
        android:id="@+id/editDetailsActivity"
        android:name="com.stt.android.ui.activities.WorkoutEditDetailsActivity"
        android:label="EditDetailsActivity">
        <argument
            android:name="com.stt.android.WORKOUT_ID"
            app:argType="integer" />
        <argument
            android:name="com.stt.android.ui.activities.WorkoutEditDetailsActivity.NAVIGATION_SOURCE"
            app:argType="string" />
        <argument
            android:name="showEditLocation"
            app:argType="boolean" />
    </activity>
    <activity
        android:id="@+id/workoutSettingsActivity"
        android:name="com.stt.android.workoutsettings.WorkoutSettingsActivity"
        android:label="WorkoutSettingsActivity" />
    <activity
        android:id="@+id/workoutSharePreviewActivity"
        android:name="com.stt.android.workouts.sharepreview.WorkoutSharePreviewActivity"
        android:label="WorkoutSharePreviewActivity" />
    <activity
        android:id="@+id/recentWorkoutTrendActivity"
        android:name="com.stt.android.workoutdetail.trend.RecentWorkoutTrendActivity"
        android:label="RecentWorkoutTrendActivity">
        <argument
            android:name="workoutHeader"
            app:argType="com.stt.android.domain.workouts.WorkoutHeader" />
    </activity>
    <activity
        android:id="@+id/workoutComparisonActivity"
        android:name="com.stt.android.workoutcomparison.WorkoutComparisonActivity"
        android:label="WorkoutComparisonActivity">
        <argument
            android:name="CURRENT_WORKOUT_ARG"
            app:argType="com.stt.android.domain.workouts.WorkoutHeader" />
        <argument
            android:name="CURRENT_RANK_ARG"
            app:argType="integer" />
        <argument
            android:name="TARGET_WORKOUT_ARG"
            app:nullable="true"
            app:argType="com.stt.android.domain.workouts.WorkoutHeader" />
        <argument
            android:name="TARGET_RANK_ARG"
            app:argType="integer" />
        <argument
            android:name="ANALYTICS_SOURCE"
            app:argType="string" />
    </activity>

    <activity
        android:id="@+id/recentWorkoutSummaryActivity"
        android:name="com.stt.android.ui.activities.RecentWorkoutSummaryActivity"
        android:label="RecentWorkoutSummaryActivity">
        <argument
            android:name="workoutHeader"
            app:argType="com.stt.android.domain.workouts.WorkoutHeader" />
    </activity>

    <action
        android:id="@+id/action_global_to_fullscreenGraphAnalysisActivity"
        app:destination="@id/graph_analysis_nav" />
    <include app:graph="@navigation/graph_analysis_nav" />

    <activity
        android:id="@+id/fullscreenDiveTrackActivity"
        android:name="com.stt.android.workout.details.divetrack.FullscreenDiveTrackActivity"
        android:label="FullscreenDiveTrackActivity">
        <argument
            android:name="workoutHeader"
            app:argType="com.stt.android.domain.workouts.WorkoutHeader" />
    </activity>
</navigation>
