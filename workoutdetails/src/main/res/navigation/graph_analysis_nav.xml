<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/graph_analysis_nav"
    app:startDestination="@id/fullscreenGraphAnalysisActivity">
    <activity
        android:id="@+id/fullscreenGraphAnalysisActivity"
        android:name="com.stt.android.workout.details.graphanalysis.fullscreen.FullscreenGraphAnalysisActivity"
        android:label="FullscreenGraphAnalysisActivity">
        <argument
            android:name="lockLandscape"
            app:argType="boolean" />
        <argument
            android:name="initialSelections"
            app:argType="com.stt.android.workout.details.graphanalysis.GraphAnalysisSelections"
            app:nullable="false" />
        <argument
            android:name="autoPlayback"
            app:argType="boolean"
            android:defaultValue="false" />
        <argument
            android:name="trackWorkoutAnalysisScreenEvent"
            app:argType="boolean"
            android:defaultValue="false" />
        <argument
            android:name="analyticsSource"
            app:argType="string" />
        <argument
            android:name="workoutHeader"
            app:argType="com.stt.android.domain.workouts.WorkoutHeader" />
        <argument
            android:name="multisportPartActivity"
            app:argType="com.stt.android.domain.sml.MultisportPartActivity"
            app:nullable="true" />
        <argument
            android:name="initialMainGraphType"
            app:argType="com.stt.android.core.domain.GraphType"
            app:nullable="true"
            android:defaultValue="@null" />
    </activity>
</navigation>
