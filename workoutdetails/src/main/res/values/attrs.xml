<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="GraphAnalysisChartAttrs">
        <attr name="showAxisLabels" format="boolean" />
        <attr name="showDurationInHighlight" format="boolean" />
        <attr name="showGrid" format="boolean" />
        <attr name="xAxisMinorLineCount" format="integer" />
        <attr name="yAxisMinorLineCount" format="integer" />
        <attr name="xAxisShowOnlyTickMarks" format="boolean" />
        <attr name="yAxisShowOnlyTickMarks" format="boolean" />
    </declare-styleable>

    <declare-styleable name="GraphAnalysisHighlightInfoViewAttrs">
        <attr name="graphColor" format="color" />
    </declare-styleable>

    <declare-styleable name="GraphAnalysisSelectedLapView">
        <attr name="gaslPaddingHorizontalExtra" format="dimension"/>
    </declare-styleable>
</resources>
