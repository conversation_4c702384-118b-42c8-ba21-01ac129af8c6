<?xml version="1.0" encoding="utf-8"?>
<resources>
    <dimen name="placeholder_cover_image_landscape_height">226dp</dimen>
    <dimen name="cover_image_height">376dp</dimen>
    <dimen name="pager_indicator_height">48dp</dimen>
    <dimen name="pager_indicator_dot_size">4dp</dimen>
    <dimen name="pager_indicator_item_min_width">16dp</dimen>
    <dimen name="pager_indicator_item_max_width">16dp</dimen>
    <dimen name="pager_indicator_width">80dp</dimen>

    <dimen name="playback_progress_seeker_thumb_radius">4dp</dimen>
    <dimen name="playback_progress_seeker_track_height">2dp</dimen>
    <dimen name="graph_analysis_landscape_highlight_info_width">100dp</dimen>
    <dimen name="graph_analysis_landscape_highlight_info_max_height">90dp</dimen>
    <dimen name="graph_analysis_image_button_size">48dp</dimen>
    <dimen name="graph_analysis_bottom_sheet_half_expanded_min_height">278dp</dimen>
    <dimen name="graph_analysis_bottom_sheet_half_expanded_extra">24dp</dimen>
    <dimen name="graph_analysis_margin_from_bottom_sheet_top">30dp</dimen>
    <dimen name="graph_analysis_bottom_sheet_peek_height">150dp</dimen>
    <dimen name="graph_analysis_chart_min_height">215dp</dimen>

    <dimen name="workout_details_image_pager_margin_top">0dp</dimen>

    <!-- Used to align items with the chart itself instead of the Y-axis labels. Not precise as the
         chart's edges move with the changing label sizes, but this is an ok compromise -->
    <dimen name="graph_analysis_full_align_with_chart_inset">20dp</dimen>

    <dimen name="map_playback_color_track_line_width">2dp</dimen>
</resources>
