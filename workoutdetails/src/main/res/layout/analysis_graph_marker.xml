<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/avgLabelLeft"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    tools:ignore="UseCompoundDrawables"
    tools:layout_gravity="start">

    <TextView
        android:id="@+id/avgLabelLeftText"
        style="@style/Body.Small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/axis_avg_label_left_start"
        android:paddingStart="@dimen/size_spacing_xsmall"
        android:paddingEnd="1dp"
        android:textColor="@color/white"
        tools:text="173.75" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginStart="-1dp"
        android:contentDescription="@null"
        android:src="@drawable/axis_avg_label_left_end" />
</LinearLayout>
