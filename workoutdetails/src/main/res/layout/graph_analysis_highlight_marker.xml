<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <TextView
        android:id="@+id/graph_analysis_highlight_marker_value_text"
        style="@style/Body.Small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@id/graph_analysis_highlight_marker_pointer"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="0:00" />

    <ImageView
        android:id="@+id/graph_analysis_highlight_marker_pointer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/graph_analysis_highlight_marker_pointer"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:ignore="ContentDescription" />
</androidx.constraintlayout.widget.ConstraintLayout>
