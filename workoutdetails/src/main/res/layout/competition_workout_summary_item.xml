<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/workoutCompetitionContainer"
    android:layout_width="0dp"
    android:layout_height="wrap_content"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintTop_toBottomOf="@id/workoutCompetitionLabel"
    android:paddingTop="@dimen/size_spacing_medium"
    android:paddingBottom="@dimen/size_spacing_medium"
    tools:showIn="@layout/model_competition_workout_summary">

    <ImageView
        android:id="@+id/icon_trophy"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:contentDescription="@null"
        android:src="@drawable/trophy_outline"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/workoutCompetitionTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        style="@style/Body.Larger.Bold"
        android:layout_marginStart="@dimen/size_spacing_medium"
        tools:text="-00:01:32"
        app:layout_constraintStart_toEndOf="@id/icon_trophy"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/workoutCompetitionSubtitle" />

    <TextView
        android:id="@+id/workoutCompetitionSubtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        style="@style/Body.Larger"
        app:layout_constraintStart_toStartOf="@id/workoutCompetitionTitle"
        app:layout_constraintStart_toEndOf="@id/icon_trophy"
        app:layout_constraintTop_toBottomOf="@id/workoutCompetitionTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:maxLines="2"
        android:ellipsize="end"
        tools:text="You have not completed the competition" />

</androidx.constraintlayout.widget.ConstraintLayout>
