<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="onClick"
            type="View.OnClickListener" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/hr_graph_ad"
            android:orientation="vertical">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:paddingTop="8dp"
                app:srcCompat="@drawable/hr_graph_ad_belt"
                tools:ignore="ContentDescription" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingStart="16dp"
                android:paddingTop="8dp"
                android:paddingEnd="16dp"
                android:text="@string/hr_graph_ad_body"
                android:textAppearance="@style/Body.Medium" />

            <Button
                style="@style/Body.Medium.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@null"
                android:onClick="@{null}"
                android:text="@string/hr_graph_ad_action"
                android:textColor="?colorAccent" />
        </LinearLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="?selectableItemBackground"
            android:onClick="@{onClick}" />
    </FrameLayout>
</layout>
