<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="sharingFlags"
            type="int" />

        <variable
            name="clickListener"
            type="android.view.View.OnClickListener" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="vertical"
        style="@style/WorkoutDetailCard.TopMargin">

        <TextView
            android:id="@+id/sharingOptionsTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:text="@string/sharing_options"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/WorkoutDetailCardTitle" />

        <ImageView
            android:id="@+id/sharingNoneIcon"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:src="@drawable/ic_sharing_private_fill"
            android:foreground="?attr/selectableItemBackgroundBorderless"
            android:background="@drawable/sharing_option_background_selected"
            android:scaleType="center"
            android:onClick="@{clickListener}"
            android:clickable="true"
            android:focusable="true"
            app:sharingFlags="@{sharingFlags}"
            app:layout_constraintTop_toBottomOf="@id/sharingOptionsTitle"
            app:layout_constraintStart_toStartOf="@id/sharingNoneText"
            app:layout_constraintEnd_toEndOf="@id/sharingNoneText"
            style="@style/Icon.Large"
            android:contentDescription="@string/sharing_private"
            tools:ignore="UnusedAttribute" />

        <ImageView
            android:id="@+id/sharingFollowersIcon"
            android:src="@drawable/ic_sharing_social_fill"
            android:foreground="?attr/selectableItemBackgroundBorderless"
            android:background="@drawable/sharing_option_background"
            android:scaleType="center"
            android:onClick="@{clickListener}"
            android:clickable="true"
            android:focusable="true"
            app:sharingFlags="@{sharingFlags}"
            app:layout_constraintTop_toTopOf="@id/sharingNoneIcon"
            app:layout_constraintStart_toStartOf="@id/sharingFollowersText"
            app:layout_constraintEnd_toEndOf="@id/sharingFollowersText"
            style="@style/Icon.Large"
            android:contentDescription="@string/followers"
            tools:ignore="UnusedAttribute" />

        <ImageView
            android:id="@+id/sharingEveryoneIcon"
            android:src="@drawable/ic_explore_outline"
            android:foreground="?attr/selectableItemBackgroundBorderless"
            android:background="@drawable/sharing_option_background"
            android:scaleType="center"
            android:onClick="@{clickListener}"
            android:clickable="true"
            android:focusable="true"
            app:sharingFlags="@{sharingFlags}"
            app:layout_constraintTop_toTopOf="@id/sharingNoneIcon"
            app:layout_constraintStart_toStartOf="@id/sharingEveryoneText"
            app:layout_constraintEnd_toEndOf="@id/sharingEveryoneText"
            style="@style/Icon.Large"
            android:contentDescription="@string/sharing_public"
            tools:ignore="UnusedAttribute" />

        <TextView
            android:id="@+id/sharingNoneText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/size_spacing_medium"
            android:layout_marginTop="@dimen/size_spacing_small"
            android:gravity="center_horizontal"
            android:text="@string/sharing_private"
            android:textAppearance="@style/Body.Small"
            app:sharingFlags="@{sharingFlags}"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/sharingFollowersText"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/sharingNoneIcon"
            app:layout_constraintVertical_bias="0.0"
            style="@style/Body.Small" />

        <TextView
            android:id="@+id/sharingFollowersText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="?android:attr/selectableItemBackground"
            android:gravity="center_horizontal"
            android:text="@string/followers"
            android:textAppearance="@style/Body.Small"
            app:sharingFlags="@{sharingFlags}"
            app:layout_constraintEnd_toStartOf="@+id/sharingEveryoneText"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/sharingNoneText"
            app:layout_constraintTop_toTopOf="@+id/sharingNoneText"
            style="@style/Body.Small" />

        <TextView
            android:id="@+id/sharingEveryoneText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="?android:attr/selectableItemBackground"
            android:gravity="center_horizontal"
            android:text="@string/sharing_public"
            android:textAppearance="@style/Body.Small"
            app:sharingFlags="@{sharingFlags}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/sharingFollowersText"
            app:layout_constraintTop_toTopOf="@+id/sharingNoneText"
            style="@style/Body.Small" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
