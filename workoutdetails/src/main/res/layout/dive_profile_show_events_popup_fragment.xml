<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="showDivider"
            type="androidx.lifecycle.LiveData&lt;Boolean&gt;" />
    </data>

    <LinearLayout
        style="@style/RoundedCornerBottomSheetStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/icHandle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/size_spacing_smaller"
            android:src="@drawable/ic_handle" />

        <TextView
            android:id="@+id/textTitle"
            style="@style/HeaderLabel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:text="@string/dive_profile_show_events_title"
            tools:text="Events"
            tools:textColor="#303030" />

        <com.stt.android.workout.details.charts.DiveProfileLineChart
            android:id="@+id/workoutLineChart"
            android:layout_width="match_parent"
            android:layout_height="@dimen/size_chart_height"
            tools:background="@color/accent" />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <View
                android:id="@+id/topDivider"
                android:layout_width="match_parent"
                android:layout_height="@dimen/size_divider"
                android:background="?suuntoDividerColor"
                android:visibility="gone"
                app:fadeVisible="@{showDivider}" />

            <com.stt.android.utils.EpoxyNonSharingRecyclerView
                android:id="@+id/list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/size_spacing_medium"
                android:paddingBottom="@dimen/size_spacing_xxxxlarge"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
        </FrameLayout>
    </LinearLayout>
</layout>
