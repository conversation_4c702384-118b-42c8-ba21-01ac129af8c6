<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false">

    <!--  Play button has negative padding that moves it outside of its parent,
          clipChildren=false is needed to show ripple properly -->
    <com.google.android.flexbox.FlexboxLayout
        android:id="@+id/analysis_controls_and_values"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:layout_marginEnd="@dimen/size_spacing_small"
        android:clipChildren="false"
        app:flexDirection="row_reverse"
        app:flexWrap="wrap"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageButton
                android:id="@+id/select_lap_button"
                android:layout_width="@dimen/graph_analysis_image_button_size"
                android:layout_height="@dimen/graph_analysis_image_button_size"
                android:background="?selectableItemBackground"
                android:contentDescription="@string/select_lap"
                android:src="@drawable/lap_outline"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/open_full_mode_button"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvSelectLapLabel"
                style="@style/Text.Value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="1dp"
                android:layout_marginEnd="-4dp"
                android:background="@drawable/lap_text_bg_indicator"
                android:ellipsize="end"
                android:gravity="center"
                android:maxWidth="80dp"
                android:maxLines="1"
                android:minWidth="23dp"
                android:minHeight="20dp"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:textAlignment="center"
                android:textSize="@dimen/text_size_small"
                app:layout_constraintEnd_toEndOf="@id/select_lap_button"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="1km" />

            <ImageButton
                android:id="@+id/open_full_mode_button"
                android:layout_width="@dimen/graph_analysis_image_button_size"
                android:layout_height="@dimen/graph_analysis_image_button_size"
                android:layout_marginStart="@dimen/size_spacing_medium"
                android:background="?selectableItemBackground"
                android:contentDescription="@string/exo_controls_fullscreen_enter_description"
                android:padding="@dimen/size_spacing_smaller"
                android:scaleType="fitCenter"
                android:src="@drawable/enlarge_fill"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/select_lap_button"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_flexGrow="1">

            <ImageButton
                android:id="@+id/play_button"
                android:layout_width="@dimen/graph_analysis_image_button_size"
                android:layout_height="@dimen/graph_analysis_image_button_size"
                android:background="?selectableItemBackground"
                android:contentDescription="@string/play_button"
                android:src="@drawable/ic_play_white_triangle"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/highlighted_duration_label"
                style="@style/Body.Large.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/size_spacing_smaller"
                app:layout_constraintBottom_toBottomOf="@id/play_button"
                app:layout_constraintStart_toEndOf="@id/play_button"
                app:layout_constraintTop_toTopOf="@id/play_button"
                tools:text="00:00'00" />

            <TextView
                android:id="@+id/highlighted_distance_label"
                style="@style/Body.Large.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/size_spacing_smaller"
                app:layout_constraintBottom_toBottomOf="@id/highlighted_duration_label"
                app:layout_constraintStart_toEndOf="@id/highlighted_duration_label"
                app:layout_constraintTop_toTopOf="@id/highlighted_duration_label"
                tools:text="123.45" />

            <TextView
                android:id="@+id/highlighted_distance_unit"
                style="@style/Body.Small.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/size_spacing_xxsmall"
                app:layout_constraintBaseline_toBaselineOf="@id/highlighted_distance_label"
                app:layout_constraintStart_toEndOf="@id/highlighted_distance_label"
                tools:text="km" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.flexbox.FlexboxLayout>

    <View
        android:id="@+id/graph_highlight_info_divider_top"
        android:layout_width="match_parent"
        android:layout_height="@dimen/size_divider"
        android:layout_marginTop="@dimen/size_spacing_small"
        android:background="?suuntoDividerColor"
        app:layout_constraintTop_toBottomOf="@id/analysis_controls_and_values" />

    <LinearLayout
        android:id="@+id/graph_highlight_info_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:divider="@drawable/graph_highlight_info_horizontal_divider"
        android:orientation="horizontal"
        android:showDividers="middle"
        app:layout_constraintTop_toBottomOf="@id/graph_highlight_info_divider_top">

        <com.stt.android.workout.details.graphanalysis.highlight.GraphAnalysisHighlightInfoView
            android:id="@+id/main_graph_highlight_info"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            app:graphColor="@color/graph_analysis_main" />

        <com.stt.android.workout.details.graphanalysis.highlight.GraphAnalysisHighlightInfoView
            android:id="@+id/comparison_graph_highlight_info"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            app:graphColor="@color/graph_analysis_comparison" />

        <com.stt.android.workout.details.graphanalysis.highlight.GraphAnalysisHighlightInfoView
            android:id="@+id/background_graph_highlight_info"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            app:graphColor="@color/graph_analysis_background" />

    </LinearLayout>

    <View
        android:id="@+id/graph_highlight_info_divider_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/size_divider"
        android:background="?suuntoDividerColor"
        app:layout_constraintTop_toBottomOf="@id/graph_highlight_info_container" />

    <com.stt.android.workout.details.graphanalysis.GraphAnalysisChart
        android:id="@+id/graph_analysis_chart"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:layout_marginTop="@dimen/size_spacing_medium"
        android:layout_marginEnd="@dimen/size_spacing_medium"
        android:layout_marginBottom="@dimen/size_spacing_medium"
        android:background="@color/white"
        app:layout_constraintBottom_toTopOf="@id/analysis_selected_lap"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/graph_highlight_info_divider_bottom"
        app:showAxisLabels="false" />

    <View
        android:id="@+id/graph_analysis_bottom_content_background"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/graph_analysis_bottom_content_background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/analysis_selected_lap" />

    <com.stt.android.workout.details.graphanalysis.laps.GraphAnalysisSelectedLapView
        android:id="@+id/analysis_selected_lap"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/graph_analysis_image_button_size"
        app:gaslPaddingHorizontalExtra="0dp"
        app:layout_constraintBottom_toTopOf="@id/initially_visible_content_bottom_guide"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/initially_visible_content_bottom_guide"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_end="0dp" />

    <com.airbnb.epoxy.EpoxyViewStub
        android:id="@+id/workout_values_root"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/initially_visible_content_bottom_guide" />

    <ProgressBar
        android:id="@+id/loading_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/initially_visible_content_bottom_guide"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/invisible_while_loading_group"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:constraint_referenced_ids="graph_highlight_info_container,analysis_selected_lap,analysis_controls_and_values" />

</androidx.constraintlayout.widget.ConstraintLayout>
