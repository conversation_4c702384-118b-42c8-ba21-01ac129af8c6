<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/WorkoutDetailCard.TopMargin"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/workoutCompetitionLabel"
        android:layout_width="0dp"
        android:layout_height="40dp"
        style="@style/Body.Medium.Bold"
        android:paddingStart="@dimen/size_spacing_medium"
        android:text="@string/comparisons"
        android:textAllCaps="true"
        android:gravity="center_vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include layout="@layout/competition_workout_summary_item" />

</androidx.constraintlayout.widget.ConstraintLayout>
