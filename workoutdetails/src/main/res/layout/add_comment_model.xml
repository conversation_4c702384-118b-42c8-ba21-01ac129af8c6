<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        style="@style/WorkoutDetailCard">

        <TextView
            android:id="@+id/add_comment_label"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/size_spacing_smaller"
            android:paddingBottom="@dimen/size_spacing_medium"
            android:paddingStart="@dimen/size_spacing_medium"
            android:paddingEnd="@dimen/size_spacing_medium"
            android:text="@string/add_comment"
            android:textAllCaps="true"
            android:textColor="?newAccentColor"
            style="@style/Body.Medium.Black" />

        <com.stt.android.ui.components.InlineTextForm
            android:id="@+id/comment_form"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/size_spacing_smaller"
            android:paddingEnd="@dimen/size_spacing_smaller"
            android:layout_marginBottom="@dimen/size_spacing_small"
            android:gravity="center_vertical"
            app:hint="@string/add_comment"/>

    </FrameLayout>
</layout>
