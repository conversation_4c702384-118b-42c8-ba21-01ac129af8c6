<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/hrGraphContainer"
    style="@style/WorkoutDetailCard.TopMargin"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/selectable_card_background"
    android:clickable="true"
    android:focusable="true"
    android:orientation="vertical"
    android:paddingTop="@dimen/size_spacing_medium"
    android:paddingBottom="@dimen/size_spacing_medium"
    android:visibility="gone"
    tools:visibility="visible">

    <TextView
        android:id="@+id/hrZoneTitle"
        style="@style/WorkoutDetailCardTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:text="@string/heart_rate_zones"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/avgHeartRate"
        style="@style/Body.Medium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_spacing_medium"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/hrZoneTitle"
        tools:text="Average 153 bpm" />

    <TextView
        android:id="@+id/graphButton"
        style="@style/Body.Medium.Black"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/size_spacing_medium"
        android:paddingEnd="@dimen/size_spacing_xsmall"
        android:text="@string/workout_analysis_view_on_map"
        android:textColor="?newAccentColor"
        app:layout_constraintBaseline_toBaselineOf="@id/hrZoneTitle"
        app:layout_constraintEnd_toEndOf="parent"
        tools:ignore="RtlSymmetry"
        tools:text="VIEW ON MAP" />

    <LinearLayout
        android:id="@+id/hrZonedChartLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/size_spacing_small"
        android:background="@drawable/selectable_card_background"
        android:orientation="vertical"
        android:paddingStart="@dimen/size_spacing_medium"
        android:paddingEnd="@dimen/size_spacing_medium"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/avgHeartRate">

        <com.stt.android.ui.components.charts.ZonedChartView
            android:id="@+id/hrZonedChart"
            android:layout_width="match_parent"
            android:layout_height="200dp" />

        <com.stt.android.ui.components.ZoneDurationsView
            android:id="@+id/hrZoneDurations"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:divider="@drawable/zone_durations_divider"
            android:showDividers="middle" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
