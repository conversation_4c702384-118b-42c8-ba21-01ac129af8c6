<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/coordinator_parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent" >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <FrameLayout
                android:id="@+id/graphAnalysisWorkoutMapViewContainer"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <FrameLayout
                android:id="@+id/hideMapLoadingView"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/white" />

            <com.google.android.material.progressindicator.CircularProgressIndicator
                android:id="@+id/loadingPlaybackDataSpinner"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:indeterminate="true"
                app:indicatorColor="?colorAccent"
                app:layout_constraintBottom_toBottomOf="@id/graphAnalysisWorkoutMapViewContainer"
                app:layout_constraintEnd_toEndOf="@id/graphAnalysisWorkoutMapViewContainer"
                app:layout_constraintStart_toStartOf="@id/graphAnalysisWorkoutMapViewContainer"
                app:layout_constraintTop_toTopOf="@id/graphAnalysisWorkoutMapViewContainer" />

            <androidx.compose.ui.platform.ComposeView
                android:id="@+id/mapFloatingActionButtonLayout"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <androidx.compose.ui.platform.ComposeView
                android:id="@+id/avalanche_info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/size_spacing_smaller"
                android:layout_marginEnd="@dimen/map_scale_bar_margin_left"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/mapFloatingActionButtonLayout"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.core.widget.NestedScrollView
            android:id="@+id/bottom_sheet_graph_analysis"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:overScrollMode="never"
            app:layout_behavior="com.stt.android.ui.utils.DisallowDraggingOnHorizontalTouchMovementBottomSheetBehavior"
            app:behavior_peekHeight="@dimen/graph_analysis_bottom_sheet_peek_height">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/bottom_sheet_graph_analysis_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bottom_sheet_rounded_top_corners_background"
                android:clickable="true"
                tools:ignore="KeyboardInaccessibleWidget">

                <ImageView
                    android:id="@+id/graph_analysis_drag_handle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/size_spacing_smaller"
                    android:contentDescription="@null"
                    android:elevation="@dimen/elevation_navbar"
                    android:src="@drawable/ic_handle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.fragment.app.FragmentContainerView
                    android:id="@+id/graph_analysis_fragment_container"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/graph_analysis_margin_from_bottom_sheet_top"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>
