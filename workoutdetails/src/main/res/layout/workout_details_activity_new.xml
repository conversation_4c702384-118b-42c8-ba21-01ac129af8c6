<?xml version="1.0" encoding="utf-8"?>
<androidx.fragment.app.FragmentContainerView
    android:id="@+id/nav_host_fragment_container"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:name="androidx.navigation.fragment.NavHostFragment"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:defaultNavHost="true"
    tools:context=".WorkoutDetailsActivityNew"/>
<!-- Nav graph is set programmatically since the start destination requires arguments -->
