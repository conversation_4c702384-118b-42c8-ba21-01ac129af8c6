<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="com.stt.android.extensions.SummaryCategoryExtensionsKt"/>

        <variable
            name="item"
            type="com.stt.android.core.domain.AdvancedLapsSelectDataCategoryItem"/>
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/list_item_single_line_height"
        android:background="?suuntoBackground">

        <TextView
            android:id="@+id/laps_category"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/padding"
            android:text="@{SummaryCategoryExtensionsKt.localize(item, context)}"
            android:textAllCaps="true"
            tools:text="@string/settings_general"
            style="@style/Body.Small.Bold"/>

    </LinearLayout>
</layout>
