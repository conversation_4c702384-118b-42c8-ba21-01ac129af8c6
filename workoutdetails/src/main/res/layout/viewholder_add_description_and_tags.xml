<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <variable
            name="clickListener"
            type="android.view.View.OnClickListener" />
    </data>

    <FrameLayout
        style="@style/WorkoutDetailCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/add_description_and_tags_label"
            style="@style/Body.Medium.Black"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:onClick="@{clickListener}"
            android:paddingStart="@dimen/size_spacing_medium"
            android:paddingTop="@dimen/size_spacing_smaller"
            android:paddingEnd="@dimen/size_spacing_medium"
            android:paddingBottom="@dimen/size_spacing_medium"
            android:text="@string/add_description_and_tags"
            android:textAllCaps="true"
            android:textColor="?newAccentColor" />

    </FrameLayout>
</layout>
