<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/lap_tab_selector"
    android:minWidth="56dp"
    android:gravity="center"
    android:paddingStart="10dp"
    android:paddingEnd="10dp"
    android:minHeight="32dp">

    <ImageView
        android:id="@+id/tabSelectIcon"
        android:visibility="gone"
        android:layout_width="@dimen/advanced_laps_number_width"
        android:layout_marginEnd="10dp"
        android:layout_height="wrap_content"
        android:contentDescription="@null"
        android:src="@drawable/lap_tab_icon_selector"
        tools:visibility="visible" />
    <TextView
        android:textColor="@color/laps_tab_title_color"
        android:id="@+id/tabName"
        style="@style/Body.Medium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="Manual"
        tools:visibility="visible" />



</LinearLayout>
