<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <ImageButton
        android:id="@+id/previous_lap"
        android:layout_width="@dimen/graph_analysis_image_button_size"
        android:layout_height="@dimen/graph_analysis_image_button_size"
        android:background="?actionBarItemBackground"
        android:padding="@dimen/size_spacing_smaller"
        android:scaleType="center"
        android:src="@drawable/icon_arrow_left_black"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/selected_lap_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <LinearLayout
        android:id="@+id/selected_lap_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/next_lap"
        app:layout_constraintStart_toEndOf="@id/previous_lap"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            style="@style/Body.Medium.Gray"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/lap_range"
            android:textColor="#303030" />

        <TextView
            android:id="@+id/selected_lap_description"
            style="@style/Body.Larger.Bold"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxLines="2"
            android:textColor="@color/laps_cell_text_selected"
            tools:text="Man. lap 1 00:44'49 - 00:46'26" />

    </LinearLayout>

    <ImageButton
        android:id="@+id/next_lap"
        android:layout_width="@dimen/graph_analysis_image_button_size"
        android:layout_height="@dimen/graph_analysis_image_button_size"
        android:background="?actionBarItemBackground"
        android:padding="@dimen/size_spacing_smaller"
        android:scaleType="center"
        android:src="@drawable/icon_arrow_right_black"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/clear_lap_selection_button"
        app:layout_constraintStart_toEndOf="@id/selected_lap_container"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

    <ImageButton
        android:id="@+id/clear_lap_selection_button"
        android:layout_width="@dimen/graph_analysis_image_button_size"
        android:layout_height="@dimen/graph_analysis_image_button_size"
        android:background="?actionBarItemBackground"
        android:padding="@dimen/size_spacing_smaller"
        android:scaleType="fitCenter"
        android:src="@drawable/ic_close"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/next_lap"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="ContentDescription" />

</merge>
