<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/cover_image_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:contentDescription="@string/cover_image"
        android:foreground="?attr/selectableItemBackground"
        android:scaleType="centerCrop"
        tools:ignore="UnusedAttribute"
        tools:srcCompat="@drawable/activity_placeholder_running" />

    <ImageView
        android:id="@+id/play"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:contentDescription="@string/play_button"
        android:src="@drawable/ic_play"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/content_reviewing"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="@dimen/text_size_medium"
        android:text="@string/image_reviewing"
        android:layout_gravity="center"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

    <View
        android:id="@+id/background"
        android:layout_width="@dimen/cover_image_height"
        android:layout_height="@dimen/cover_image_height"
        android:background="@color/light_grey"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

    <ImageView
        android:id="@+id/icon_warning"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@null"
        android:src="@drawable/icon_waring"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/text_warning"
        />

    <TextView
        android:id="@+id/text_warning"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/size_spacing_medium"
        android:paddingStart="@dimen/size_spacing_medium"
        android:paddingEnd="@dimen/size_spacing_medium"
        android:text="@string/content_illegal_retry"
        android:textColor="@color/suunto_dark_gray"
        android:textSize="@dimen/text_size_medium"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/icon_warning"
        app:layout_constraintBottom_toBottomOf="parent"
        />
    <androidx.constraintlayout.widget.Group
        android:id="@+id/content_illegal_view_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="icon_warning,text_warning,background"
        android:visibility="gone"
        />
</androidx.constraintlayout.widget.ConstraintLayout>
