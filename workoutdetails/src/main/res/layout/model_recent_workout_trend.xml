<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/WorkoutDetailCard.TopMargin"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/comparisons"
        style="@style/WorkoutDetailCardTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/size_spacing_medium"
        android:text="@string/comparisons"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/comparisons_divider"
        android:background="?suuntoDividerColor"
        android:layout_width="0dp"
        android:layout_height="@dimen/size_divider"
        android:layout_marginTop="@dimen/size_spacing_medium"
        app:layout_constraintTop_toBottomOf="@+id/comparisons"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <include
        android:id="@+id/workoutCompetitionContainer"
        layout="@layout/competition_workout_summary_item"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/comparisons_divider"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <View
        android:id="@+id/competition_divider"
        android:background="?suuntoDividerColor"
        android:layout_width="0dp"
        android:layout_height="@dimen/size_divider"
        app:layout_constraintTop_toBottomOf="@+id/workoutCompetitionContainer"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />


    <View
        android:id="@+id/ranking_touch_area"
        android:background="?selectableItemBackground"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/competition_divider"
        app:layout_constraintBottom_toTopOf="@id/ranking_divider" />

    <ImageView
        android:id="@+id/ranking_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:src="@drawable/star_outline"
        android:contentDescription="@null"
        app:layout_constraintTop_toTopOf="@id/ranking"
        app:layout_constraintBottom_toBottomOf="@id/ranking_description"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/ranking"
        style="@style/Body.Larger.Bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/size_spacing_medium"
        android:textColor="@color/accent"
        app:layout_constraintStart_toEndOf="@id/ranking_icon"
        app:layout_constraintTop_toBottomOf="@id/competition_divider"
        tools:text="1st" />

    <TextView
        android:id="@+id/ranking_description"
        style="@style/Body.Larger"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/rank_on_this_route"
        app:layout_constraintStart_toStartOf="@id/ranking"
        app:layout_constraintEnd_toEndOf="@id/ranking_on_similar_distance_description"
        app:layout_constraintTop_toBottomOf="@id/ranking" />

    <View
        android:id="@+id/ranking_divider"
        android:background="?suuntoDividerColor"
        android:layout_width="0dp"
        android:layout_height="@dimen/size_divider"
        android:layout_marginTop="@dimen/size_spacing_medium"
        app:layout_constraintTop_toBottomOf="@+id/ranking_description"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <View
        android:id="@+id/ranking_on_similar_distance_touch_area"
        android:background="?selectableItemBackground"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/ranking_divider"
        app:layout_constraintBottom_toTopOf="@id/ranking_on_similar_distance_divider" />

    <ImageView
        android:id="@+id/ranking_on_similar_distance_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:src="@drawable/star_outline"
        android:contentDescription="@null"
        app:layout_constraintTop_toTopOf="@id/ranking_on_similar_distance"
        app:layout_constraintBottom_toBottomOf="@id/ranking_on_similar_distance_description"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/ranking_on_similar_distance"
        style="@style/Body.Larger.Bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/size_spacing_medium"
        android:textColor="@color/accent"
        app:layout_constraintStart_toEndOf="@id/ranking_on_similar_distance_icon"
        app:layout_constraintTop_toBottomOf="@id/ranking_divider"
        tools:text="2nd" />

    <TextView
        android:id="@+id/ranking_on_similar_distance_description"
        style="@style/Body.Larger"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/size_spacing_medium"
        android:text="@string/rank_of_similar_distance"
        app:layout_constraintStart_toStartOf="@id/ranking_on_similar_distance"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ranking_on_similar_distance" />

    <View
        android:id="@+id/ranking_on_similar_distance_divider"
        android:background="?suuntoDividerColor"
        android:layout_width="0dp"
        android:layout_height="@dimen/size_divider"
        android:layout_marginTop="@dimen/size_spacing_medium"
        app:layout_constraintTop_toBottomOf="@+id/ranking_on_similar_distance_description"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <View
        android:id="@+id/compared_to_best_touch_area"
        android:background="?selectableItemBackground"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/ranking_on_similar_distance_divider"
        app:layout_constraintBottom_toTopOf="@id/compared_to_best_divider" />

    <ImageView
        android:id="@+id/compared_to_best_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:src="@drawable/trophy_outline"
        android:contentDescription="@null"
        app:layout_constraintTop_toTopOf="@id/compared_to_best"
        app:layout_constraintBottom_toBottomOf="@id/compared_to_best_description"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/compared_to_best"
        style="@style/Body.Larger.Bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/size_spacing_medium"
        app:layout_constraintStart_toEndOf="@id/compared_to_best_icon"
        app:layout_constraintTop_toBottomOf="@id/ranking_on_similar_distance_divider"
        tools:text="-00:01:32" />

    <TextView
        android:id="@+id/compared_to_best_description"
        style="@style/Body.Larger"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/compare_to_best"
        app:layout_constraintStart_toStartOf="@id/compared_to_best"
        app:layout_constraintEnd_toEndOf="@id/ranking_on_similar_distance_description"
        app:layout_constraintTop_toBottomOf="@id/compared_to_best" />

    <View
        android:id="@+id/compared_to_best_divider"
        android:background="?suuntoDividerColor"
        android:layout_width="0dp"
        android:layout_height="@dimen/size_divider"
        android:layout_marginTop="@dimen/size_spacing_medium"
        app:layout_constraintTop_toBottomOf="@+id/compared_to_best_description"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <View
        android:id="@+id/compared_to_previous_touch_area"
        android:background="?selectableItemBackground"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/compared_to_best_divider"
        app:layout_constraintBottom_toTopOf="@id/compared_to_previous_divider" />

    <ImageView
        android:id="@+id/compared_to_previous_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:src="@drawable/calendar_outline"
        android:contentDescription="@null"
        app:layout_constraintTop_toTopOf="@id/compared_to_previous"
        app:layout_constraintBottom_toBottomOf="@id/compared_to_previous_description"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/compared_to_previous"
        style="@style/Body.Larger.Bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/size_spacing_medium"
        app:layout_constraintStart_toEndOf="@id/compared_to_previous_icon"
        app:layout_constraintTop_toBottomOf="@id/compared_to_best_divider"
        tools:text="-00:02:15" />

    <TextView
        android:id="@+id/compared_to_previous_description"
        style="@style/Body.Larger"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/compare_to_previous"
        app:layout_constraintStart_toStartOf="@id/compared_to_previous"
        app:layout_constraintEnd_toEndOf="@id/compared_to_best_description"
        app:layout_constraintTop_toBottomOf="@id/compared_to_previous" />

    <View
        android:id="@+id/compared_to_previous_divider"
        android:background="?suuntoDividerColor"
        android:layout_width="0dp"
        android:layout_height="@dimen/size_divider"
        android:layout_marginTop="@dimen/size_spacing_medium"
        app:layout_constraintTop_toBottomOf="@+id/compared_to_previous_description"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <Spinner
        android:id="@+id/routeSelection"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="?selectableItemBackground"
        android:spinnerMode="dropdown"
        app:layout_constrainedWidth="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/compareButton"
        app:layout_constraintTop_toBottomOf="@id/compared_to_previous_divider"
        app:layout_constraintHorizontal_bias="0.0"
        tools:entries="@array/goal_period_list" />

    <Button
        android:id="@+id/compareButton"
        style="@style/ButtonFlat"
        android:layout_marginEnd="@dimen/size_spacing_small"
        android:minWidth="@dimen/size_spacing_large"
        android:text="@string/compare"
        android:textAllCaps="true"
        android:textColor="?newAccentColor"
        app:layout_constraintBottom_toBottomOf="@id/routeSelection"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/routeSelection" />

    <TextView
        android:id="@+id/dataType"
        style="@style/Body.Small.Bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:layout_marginTop="@dimen/size_spacing_xsmall"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/routeSelection"
        tools:text="Duration" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/trendViewPager"
        android:layout_width="0dp"
        android:layout_height="130dp"
        android:layout_marginBottom="@dimen/size_spacing_xlarge"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/dataType" />

    <me.relex.circleindicator.CircleIndicator
        android:id="@+id/pagerIndicator"
        android:layout_width="match_parent"
        android:layout_height="@dimen/pager_indicator_height"
        app:ci_drawable="@drawable/black_dot"
        app:ci_drawable_unselected="@drawable/grey_dot"
        app:ci_height="@dimen/pager_indicator_dot_size"
        app:ci_width="@dimen/pager_indicator_dot_size"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
