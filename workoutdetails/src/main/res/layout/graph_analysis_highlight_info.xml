<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:layout_height="wrap_content"
    tools:layout_width="match_parent"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <TextView
        android:id="@+id/graph_name"
        style="@style/Body.Medium"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:maxLines="1"
        app:autoSizeMaxTextSize="@dimen/text_size_medium"
        app:autoSizeMinTextSize="@dimen/text_size_small"
        app:autoSizeStepGranularity="@dimen/text_size_granularity"
        app:autoSizeTextType="uniform"
        app:layout_constraintBottom_toTopOf="@id/current_value"
        app:layout_constraintEnd_toStartOf="@id/ivArrow"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Heart rate" />

    <TextView
        android:id="@+id/current_value"
        style="@style/Body.Larger.Bold"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/size_spacing_xxsmall"
        android:includeFontPadding="false"
        android:maxLines="2"
        app:layout_constraintBottom_toTopOf="@id/graph_unit"
        app:layout_constraintEnd_toEndOf="@id/graph_name"
        app:layout_constraintStart_toStartOf="@id/graph_name"
        app:layout_constraintTop_toBottomOf="@id/graph_name"
        tools:text="127" />

    <TextView
        android:id="@+id/graph_unit"
        style="@style/Body.Small"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/size_spacing_xxsmall"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:maxLines="1"
        android:textColor="@color/suunto_dark_gray"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/graph_name"
        app:layout_constraintStart_toStartOf="@id/graph_name"
        app:layout_constraintTop_toBottomOf="@id/current_value"
        tools:text="bpm" />

    <ImageView
        android:id="@+id/ivArrow"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:contentDescription="@null"
        android:src="@drawable/dropdown_arrow"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/graph_name"
        app:layout_constraintTop_toTopOf="parent" />

</merge>
