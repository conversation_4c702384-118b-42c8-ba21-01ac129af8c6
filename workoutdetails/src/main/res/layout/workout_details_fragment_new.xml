<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context="com.stt.android.workout.details.WorkoutDetailsFragmentNew">

    <data>

        <import type="com.stt.android.workout.details.WorkoutDetailsViewState" />

        <import type="com.stt.android.common.viewstate.ViewState" />

        <import type="android.view.View" />

        <variable
            name="state"
            type="ViewState&lt;WorkoutDetailsViewState&gt;" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:id="@+id/coordinatorLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/appBar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?suuntoBackground"
                app:layout_insetEdge="top">

                <com.google.android.material.appbar.CollapsingToolbarLayout
                    android:id="@+id/collapsing_toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layout_scrollFlags="scroll|exitUntilCollapsed|snap">

                    <!-- When adding Views to be shown over ViewPager ensure that they have elevation
                     higher than what is defined in WorkoutDetailCard in order to show them on top of it -->
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/toolbar_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:clipToPadding="false"
                        app:layout_collapseMode="parallax">

                        <com.stt.android.workout.details.WorkoutDetailToolbar
                            android:id="@+id/toolbar"
                            style="@style/Widget.AppCompat.Toolbar"
                            android:theme="@style/Toolbar.Native"
                            android:layout_width="match_parent"
                            android:layout_height="?attr/actionBarSize"
                            android:background="@color/white"
                            android:elevation="@dimen/elevation_toolbar"
                            app:hideBarInfo="@{state.data.hideBarInfo}"
                            app:hideMenuActions="@{state.data.multisportPartActivity != null &amp;&amp; state.data.multisportPartActivity.data != null}"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:userInfo="@{state.data.toolbar}" />

                        <androidx.viewpager2.widget.ViewPager2
                            android:id="@+id/cover_image_pager"
                            style="@style/WorkoutDetailCard"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/workout_details_image_pager_margin_top"
                            android:background="@color/photo_view_background"
                            android:orientation="horizontal"
                            android:outlineProvider="bounds"
                            app:enableUserInput="@{state.data.showPagerIndicator}"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/toolbar"
                            app:layout_constraintWidth_max="@dimen/content_max_width"
                            tools:background="@null"
                            tools:layout_height="367dp" />

                        <com.google.android.material.tabs.TabLayout
                            android:id="@+id/pager_indicator"
                            android:layout_width="@dimen/pager_indicator_width"
                            android:layout_height="@dimen/pager_indicator_height"
                            android:elevation="@dimen/feed_card_elevation"
                            app:layout_constraintBottom_toBottomOf="@id/cover_image_pager"
                            app:layout_constraintEnd_toEndOf="@id/cover_image_pager"
                            app:layout_constraintStart_toStartOf="@id/cover_image_pager"
                            app:tabBackground="@drawable/tab_selector"
                            app:tabGravity="center"
                            app:tabIndicatorHeight="0dp"
                            app:tabMaxWidth="@dimen/pager_indicator_item_max_width"
                            app:tabMinWidth="@dimen/pager_indicator_item_min_width"
                            app:tabMode="scrollable"
                            app:tabRippleColor="@null"
                            app:visible="@{state.data.showPagerIndicator}" />

                        <com.stt.android.home.explore.routes.RouteView
                            android:id="@+id/routeView"
                            style="@style/Fab.Small"
                            android:layout_width="@dimen/route_view_fab_size"
                            android:layout_height="@dimen/route_view_fab_size"
                            android:layout_marginTop="16dp"
                            android:layout_marginEnd="@dimen/padding"
                            android:visibility="gone"
                            app:layout_constraintEnd_toEndOf="@id/cover_image_pager"
                            app:layout_constraintTop_toTopOf="@id/cover_image_pager"
                            tools:background="@color/blue"
                            tools:layout_height="30dp"
                            tools:layout_width="30dp"
                            tools:visibility="visible" />

                        <com.google.android.material.floatingactionbutton.FloatingActionButton
                            android:id="@+id/shareImageButton"
                            style="@style/Fab.Small"
                            android:layout_marginEnd="@dimen/size_spacing_medium"
                            android:layout_marginBottom="@dimen/size_spacing_medium"
                            android:contentDescription="@string/share"
                            android:visibility="gone"
                            app:layout_constraintBottom_toBottomOf="@id/cover_image_pager"
                            app:layout_constraintEnd_toEndOf="@id/cover_image_pager"
                            app:srcCompat="@drawable/share_outline"
                            tools:background="@color/green"
                            tools:layout_height="30dp"
                            tools:layout_width="30dp"
                            tools:src="@drawable/share_outline"
                            tools:visibility="visible" />

                        <com.google.android.material.floatingactionbutton.FloatingActionButton
                            android:id="@+id/addPhotoButton"
                            style="@style/Fab.Small"
                            android:layout_marginEnd="@dimen/size_spacing_medium"
                            android:layout_marginBottom="@dimen/size_spacing_medium"
                            android:contentDescription="@string/add_photos_and_videos"
                            android:visibility="gone"
                            app:layout_constraintBottom_toBottomOf="@id/cover_image_pager"
                            app:layout_constraintEnd_toStartOf="@id/shareImageButton"
                            app:srcCompat="@drawable/ic_add_photo_outline"
                            tools:background="@color/yellow"
                            tools:layout_height="30dp"
                            tools:layout_width="30dp"
                            tools:src="@drawable/ic_add_photo_outline"
                            tools:visibility="visible" />

                        <com.google.android.material.floatingactionbutton.FloatingActionButton
                            android:id="@+id/openGraphAnalysisButton"
                            style="@style/Fab.Small"
                            android:layout_marginEnd="@dimen/size_spacing_medium"
                            android:layout_marginBottom="@dimen/size_spacing_medium"
                            android:contentDescription="@string/workout_playback_route_on_map"
                            android:visibility="gone"
                            app:layout_constraintBottom_toBottomOf="@id/cover_image_pager"
                            app:layout_constraintEnd_toStartOf="@id/addPhotoButton"
                            app:srcCompat="@drawable/ic_play_outline"
                            tools:background="@color/bright_cyan"
                            tools:layout_height="30dp"
                            tools:layout_width="30dp"
                            tools:src="@drawable/ic_play_outline"
                            tools:visibility="visible" />

                        <com.stt.android.ui.components.WeatherConditionsView
                            android:id="@+id/weatherConditionsView"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/size_spacing_medium"
                            android:layout_marginTop="16dp"
                            app:layout_constraintStart_toStartOf="@id/cover_image_pager"
                            app:layout_constraintTop_toTopOf="@id/cover_image_pager"
                            tools:background="@color/red"
                            tools:layout_height="20dp"
                            tools:layout_width="200dp" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </com.google.android.material.appbar.CollapsingToolbarLayout>
            </com.google.android.material.appbar.AppBarLayout>

            <com.stt.android.utils.EpoxyNonSharingRecyclerView
                android:id="@+id/list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="?suuntoBackground"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_behavior="@string/appbar_scrolling_view_behavior"
                app:layout_dodgeInsetEdges="top"
                tools:listitem="@layout/model_heart_rate_zones" />

        </androidx.coordinatorlayout.widget.CoordinatorLayout>

        <Button
            android:id="@+id/selectBt"
            style="@style/AccentButton"
            android:background="@drawable/bg_rounded_accent_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/smaller_padding"
            android:layout_marginEnd="@dimen/smaller_padding"
            android:layout_marginBottom="@dimen/padding"
            android:minHeight="@dimen/height_elevated_button"
            android:text="@string/dialog_title_select"
            android:visibility="@{state.data.hideBarInfo ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
