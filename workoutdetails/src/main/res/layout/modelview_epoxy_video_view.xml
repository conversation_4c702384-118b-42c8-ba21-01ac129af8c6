<?xml version="1.0" encoding="utf-8"?>
<com.stt.android.workout.details.photopager.EpoxyVideoView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <com.google.android.exoplayer2.ui.StyledPlayerView
        android:id="@+id/video_player"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:surface_type="texture_view"
        app:use_controller="false" />

    <ImageButton
        android:id="@+id/mute_video"
        style="@style/IconButton.Small.Padding"
        android:layout_marginStart="@dimen/size_spacing_small"
        android:layout_marginBottom="@dimen/size_icon_xlarge"
        android:background="?android:attr/selectableItemBackground"
        android:contentDescription="@string/mute_video"
        app:tint="?colorPrimary"
        app:layout_constraintBottom_toBottomOf="@id/video_player"
        app:layout_constraintStart_toStartOf="@id/video_player"
        tools:src="@drawable/ic_speaker_off"
        tools:visibility="visible" />
</com.stt.android.workout.details.photopager.EpoxyVideoView>
