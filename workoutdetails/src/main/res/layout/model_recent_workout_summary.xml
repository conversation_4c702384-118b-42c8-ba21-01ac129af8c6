<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    style="@style/WorkoutDetailCard.TopMargin"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/activityIcon"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:layout_marginTop="@dimen/size_spacing_smaller"
        android:contentDescription="@null"
        android:tint="?android:textColorPrimary"
        android:tintMode="src_in"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/ic_activity_badminton" />

    <TextView
        android:id="@+id/title"
        style="@style/WorkoutDetailCardTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:drawablePadding="8dp"
        android:paddingStart="@dimen/size_spacing_smaller"
        android:paddingEnd="@dimen/size_spacing_medium"
        app:layout_constraintBottom_toBottomOf="@id/activityIcon"
        app:layout_constraintStart_toEndOf="@id/activityIcon"
        app:layout_constraintTop_toTopOf="@id/activityIcon"
        tools:text="30-Day Summary" />

    <Button
        android:id="@+id/viewMoreButton"
        style="@style/ButtonFlat"
        android:layout_marginEnd="@dimen/size_spacing_small"
        android:minWidth="@dimen/size_spacing_large"
        android:text="@string/view_more_short"
        android:textAllCaps="true"
        android:textColor="?newAccentColor"
        app:layout_constraintBottom_toBottomOf="@id/activityIcon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/activityIcon" />

    <com.stt.android.ui.components.RecentWorkoutSummaryView
        android:id="@+id/recentWorkoutSummaryView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/size_spacing_small"
        app:layout_constraintTop_toBottomOf="@id/title"
        tools:background="@color/light_gray"
        tools:maxHeight="120dp" />

    <TextView
        android:id="@+id/dataType"
        style="@style/Body.Small.Bold"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/size_spacing_large"
        android:layout_marginStart="@dimen/size_spacing_medium"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/recentWorkoutSummaryView"
        tools:text="Duration" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/summaryViewPager"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        app:layout_constraintTop_toBottomOf="@id/dataType"
        tools:background="@color/light_gray" />

    <TextView
        android:id="@+id/startDate"
        style="@style/Body.Small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_spacing_medium"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/summaryViewPager"
        tools:text="60 days ago" />

    <TextView
        android:id="@+id/endDate"
        style="@style/Body.Small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/size_spacing_medium"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/startDate"
        tools:text="Now" />

    <me.relex.circleindicator.CircleIndicator
        android:id="@+id/pagerIndicator"
        android:layout_width="match_parent"
        android:layout_height="@dimen/pager_indicator_height"
        app:ci_drawable="@drawable/black_dot"
        app:ci_drawable_unselected="@drawable/grey_dot"
        app:ci_height="@dimen/pager_indicator_dot_size"
        app:ci_width="@dimen/pager_indicator_dot_size"
        app:layout_constraintTop_toBottomOf="@id/summaryViewPager" />

</androidx.constraintlayout.widget.ConstraintLayout>
