<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="com.stt.android.extensions.SummaryItemExtensionsKt"/>

        <import type="com.stt.android.domain.advancedlaps.LapsTableType"/>

        <import type="android.view.View"/>

        <import type="androidx.appcompat.content.res.AppCompatResources"/>

        <variable
            name="item"
            type="com.stt.android.workout.details.laps.advanced.data.AdvancedLapsSelectDataType"/>

        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener"/>
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/list_item_single_line_height"
        android:onClick="@{onClickListener}"
        style="@style/FeedCard.ripple.noelevation">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/laps_summary_item_icon"
            android:layout_width="@dimen/size_icon_medium"
            android:layout_height="@dimen/size_icon_medium"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:scaleType="centerInside"
            android:src="@{AppCompatResources.getDrawable(context, SummaryItemExtensionsKt.icon(item.dataType, item.lapsTableType == LapsTableType.DOWNHILL))}"
            android:tint="?android:textColorPrimary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@tools:sample/avatars"/>

        <TextView
            android:id="@+id/laps_summary_item_title"
            android:layout_width="@dimen/size_spacing_zero"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:text="@{SummaryItemExtensionsKt.localize(item.dataType, context, item.lapsTableType == LapsTableType.DOWNHILL)}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/laps_summary_item_icon"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@tools:sample/full_names"
            style="@style/Body.Larger"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/laps_summary_item_selected_icon"
            android:layout_width="@dimen/size_icon_medium"
            android:layout_height="@dimen/size_icon_medium"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:scaleType="centerInside"
            android:tint="?android:textColorPrimary"
            android:visibility="@{item.isSelected ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_checkmark_fill"
            tools:src="@tools:sample/avatars"
            tools:visibility="visible"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
