<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/workout_values_root"
    style="@style/WorkoutDetailCard.TopMargin"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.compose.ui.platform.ComposeView
        android:id="@+id/workout_values_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
