<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="title"
            type="String" />

        <variable
            name="description"
            type="String" />

        <variable
            name="onClicked"
            type="android.view.View.OnClickListener" />
    </data>

    <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        style="@style/WorkoutDetailCard.TopMargin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:onClick="@{onClicked}"
            android:background="?android:attr/selectableItemBackground">

            <TextView
                android:id="@+id/buy_premium_placeholder_title"
                style="@style/WorkoutDetailCardTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/size_spacing_medium"
                android:layout_marginTop="@dimen/size_spacing_medium"
                android:paddingBottom="@dimen/size_spacing_small"
                android:text="@{title}"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="@string/on_this_route_capital" />

            <TextView
                android:id="@+id/buy_premium_placeholder_premium_note"
                style="@style/Body.Medium.Black"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/size_spacing_medium"
                android:minWidth="@dimen/size_spacing_large"
                android:text="@string/premium"
                android:textAllCaps="true"
                android:textColor="@color/secondary_accent"
                app:layout_constraintBaseline_toBaselineOf="@id/buy_premium_placeholder_title"
                app:layout_constraintEnd_toEndOf="parent" />

            <TextView
                android:id="@+id/buy_premium_placeholder_description"
                style="@style/Body.Larger.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingStart="@dimen/size_spacing_large"
                android:paddingTop="@dimen/size_spacing_medium"
                android:paddingEnd="@dimen/size_spacing_large"
                android:paddingBottom="@dimen/size_spacing_large"
                android:text="@{description}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/buy_premium_placeholder_title"
                tools:text="@string/buy_premium_workout_comparisons_description" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>
</layout>
