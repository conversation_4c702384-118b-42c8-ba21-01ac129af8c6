<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/workout_values_root"
    style="@style/WorkoutDetailCard"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/selectable_card_background"
    android:clickable="true"
    android:focusable="true">

    <ImageView
        android:id="@+id/activity_icon"
        android:layout_width="@dimen/size_icon_medium"
        android:layout_height="@dimen/size_icon_medium"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:layout_marginTop="@dimen/size_spacing_smaller"
        android:contentDescription="@string/activity_icon"
        app:tint="?android:textColorPrimary"
        android:tintMode="src_in"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/ic_activity_running" />

    <TextView
        android:id="@+id/activity_name"
        style="@style/WorkoutDetailCardTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:drawablePadding="8dp"
        android:paddingStart="@dimen/size_spacing_smaller"
        android:paddingEnd="@dimen/size_spacing_medium"
        app:layout_constraintBottom_toBottomOf="@id/activity_icon"
        app:layout_constraintStart_toEndOf="@id/activity_icon"
        app:layout_constraintTop_toTopOf="@id/activity_icon"
        tools:text="Running" />

    <TextView
        android:id="@+id/details_button"
        style="@style/Body.Medium.Black"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/size_spacing_medium"
        android:text="@string/multisport_details"
        android:textColor="?newAccentColor"
        android:visibility="gone"
        app:layout_constraintBaseline_toBaselineOf="@id/activity_name"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="DETAILS" />

    <com.stt.android.ui.utils.SmartViewPager
        android:id="@+id/values_pager"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/size_spacing_small"
        app:layout_constraintBottom_toTopOf="@+id/pager_indicator"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/activity_icon"
        app:layout_goneMarginBottom="@dimen/size_spacing_medium"
        app:layout_goneMarginTop="0dp"
        tools:layout_height="200dp" />

    <me.relex.circleindicator.CircleIndicator
        android:id="@+id/pager_indicator"
        android:layout_width="match_parent"
        android:layout_height="@dimen/pager_indicator_height"
        app:ci_drawable="@drawable/black_dot"
        app:ci_drawable_unselected="@drawable/grey_dot"
        app:ci_height="@dimen/pager_indicator_dot_size"
        app:ci_width="@dimen/pager_indicator_dot_size"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/values_pager" />

</androidx.constraintlayout.widget.ConstraintLayout>
