<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="selected"
            type="boolean" />

        <variable
            name="iconDrawable"
            type="android.graphics.drawable.Drawable" />

        <variable
            name="localizedName"
            type="java.lang.String" />

        <variable
            name="onClick"
            type="android.view.View.OnClickListener" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="80dp"
        android:layout_height="match_parent">

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/option_button"
            style="@style/Fab.Small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription="@{localizedName}"
            android:onClick="@{onClick}"
            android:src="@{iconDrawable}"
            android:tint="@{selected ? @color/newAccent : @color/near_black}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/graph_type_title"
            style="@style/Body.Small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_xsmall"
            android:layout_marginTop="@dimen/size_spacing_small"
            android:layout_marginEnd="@dimen/size_spacing_xsmall"
            android:gravity="center"
            android:text="@{localizedName}"
            android:textColor="@{selected ? @color/newAccent : @color/near_black}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/option_button"
            tools:text="Heartrate" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
