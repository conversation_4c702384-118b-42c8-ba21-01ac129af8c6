<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="androidx.core.content.ContextCompat" />

        <import type="com.stt.android.workout.details.laps.advanced.AdvancedLapsRowType" />

        <import type="android.view.View" />

        <variable
            name="item"
            type="com.stt.android.workout.details.laps.advanced.table.AdvancedLapsTableRowItem" />

        <variable
            name="lapCell1"
            type="com.stt.android.workout.details.laps.advanced.table.LapCellData" />

        <variable
            name="lapCell2"
            type="com.stt.android.workout.details.laps.advanced.table.LapCellData" />

        <variable
            name="lapCell3"
            type="com.stt.android.workout.details.laps.advanced.table.LapCellData" />

        <variable
            name="lapCell4"
            type="com.stt.android.workout.details.laps.advanced.table.LapCellData" />

        <variable
            name="onRowClicked"
            type="android.view.View.OnClickListener" />

        <variable
            name="onExpandClicked"
            type="android.view.View.OnClickListener" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lap_row_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/size_spacing_xxlarge"
        android:onClick="@{onRowClicked}">

        <View
            android:id="@+id/expand_row_background"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:onClick="@{onExpandClicked}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="@id/laps_row_number" />

        <ImageView
            android:id="@+id/lap_row_expandable_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:contentDescription="@null"
            android:rotation="270"
            android:visibility="@{item.type == AdvancedLapsRowType.Expandable ? View.VISIBLE : View.INVISIBLE}"
            app:icon="@{@drawable/row_collapse_animated}"
            app:iconToggled="@{@drawable/row_expand_animated}"
            app:isToggled="@{item.isExpanded}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="?android:textColorPrimary" />

        <TextView
            android:id="@+id/laps_row_number"
            style="@style/Body.Medium"
            android:layout_width="@dimen/advanced_laps_number_width"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:enabled="@{!item.isRecoveryInterval}"
            android:maxLines="1"
            android:text="@{item.lapNumber}"
            android:textColor="@color/advanced_laps_tab_row_color"
            android:textSize="@dimen/text_size_medium_dp"
            android:visibility="@{item.getLapNumberVisibility()}"
            app:autoSizeMaxTextSize="@dimen/text_size_medium_dp"
            app:autoSizeMinTextSize="@dimen/text_size_small_dp"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/lap_row_expandable_icon"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="SpUsage"
            tools:visibility="gone"
            tools:text="999" />

        <ImageView
            tools:visibility="visible"
            android:visibility="@{item.isSelected? View.VISIBLE : View.GONE}"
            android:contentDescription="@null"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/lap_row_expandable_icon"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="@dimen/advanced_laps_number_width"
            android:layout_marginStart="@dimen/negative_size_spacing_small"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_check_circle" />

        <TextView
            android:id="@+id/laps_header_1"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:enabled="@{!item.isRecoveryInterval}"
            android:gravity="center"
            android:maxLines="1"
            android:text="@{lapCell1.text}"
            android:background="@{lapCell1.backgroundColor}"
            android:textColor="@{lapCell1.textColor}"
            app:autoSizeMaxTextSize="@dimen/text_size_medium_dp"
            app:autoSizeMinTextSize="@dimen/text_size_small_dp"
            app:autoSizeTextType="uniform"
            app:isBold="@{lapCell1.bold}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/laps_header_2"
            app:layout_constraintStart_toEndOf="@id/laps_row_number"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="1'02"
            style="@style/LapTable.Values" />

        <TextView
            android:id="@+id/laps_header_2"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:enabled="@{!item.isRecoveryInterval}"
            android:gravity="center"
            android:maxLines="1"
            android:text="@{lapCell2.text}"
            android:background="@{lapCell2.backgroundColor}"
            android:textColor="@{lapCell2.textColor}"
            app:autoSizeMaxTextSize="@dimen/text_size_medium_dp"
            app:autoSizeMinTextSize="@dimen/text_size_small_dp"
            app:autoSizeTextType="uniform"
            app:isBold="@{lapCell2.bold}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/laps_header_3"
            app:layout_constraintStart_toEndOf="@id/laps_header_1"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="1'02"
            style="@style/LapTable.Values" />

        <TextView
            android:id="@+id/laps_header_3"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:enabled="@{!item.isRecoveryInterval}"
            android:gravity="center"
            android:maxLines="1"
            android:text="@{lapCell3.text}"
            android:background="@{lapCell3.backgroundColor}"
            android:textColor="@{lapCell3.textColor}"
            app:autoSizeMaxTextSize="@dimen/text_size_medium_dp"
            app:autoSizeMinTextSize="@dimen/text_size_small_dp"
            app:autoSizeTextType="uniform"
            app:isBold="@{lapCell3.bold}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/laps_header_4"
            app:layout_constraintStart_toEndOf="@id/laps_header_2"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="1'02"
            style="@style/LapTable.Values" />

        <TextView
            android:id="@+id/laps_header_4"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:enabled="@{!item.isRecoveryInterval}"
            android:gravity="center"
            android:maxLines="1"
            android:text="@{lapCell4.text}"
            android:background="@{lapCell4.backgroundColor}"
            android:textColor="@{lapCell4.textColor}"
            app:autoSizeMaxTextSize="@dimen/text_size_medium_dp"
            app:autoSizeMinTextSize="@dimen/text_size_small_dp"
            app:autoSizeTextType="uniform"
            app:isBold="@{lapCell4.bold}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/laps_header_3"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="1'02"
            style="@style/LapTable.Values" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
