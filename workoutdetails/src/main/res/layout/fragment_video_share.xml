<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/previewContainer"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/shareVideoBtn"
        app:layout_constraintDimensionRatio="1:1.32"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0"
        app:layout_constraintVertical_chainStyle="packed">

        <FrameLayout
            android:id="@+id/mapContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <androidx.compose.ui.platform.ComposeView
            android:id="@+id/composeView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
            android:id="@+id/playFab"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:backgroundTint="@color/white"
            app:icon="@drawable/ic_play_fill"
            app:iconTint="@color/near_black"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
            android:id="@+id/graphTypeFabSub1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/size_spacing_medium"
            app:backgroundTint="@color/white"
            app:collapsedSize="40dp"
            app:iconTint="@color/near_black"
            app:layout_constraintBottom_toTopOf="@id/graphTypeFabSub2"
            app:layout_constraintEnd_toEndOf="@id/graphTypeFabMain"
            app:layout_constraintStart_toStartOf="@id/graphTypeFabMain"
            tools:icon="@drawable/ic_altitude_video_share"
            tools:ignore="ContentDescription" />

        <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
            android:id="@+id/graphTypeFabSub2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/size_spacing_medium"
            app:backgroundTint="@color/white"
            app:collapsedSize="40dp"
            app:iconTint="@color/near_black"
            app:layout_constraintBottom_toTopOf="@id/graphTypeFabMain"
            app:layout_constraintEnd_toEndOf="@id/graphTypeFabMain"
            app:layout_constraintStart_toStartOf="@id/graphTypeFabMain"
            tools:icon="@drawable/ic_heart_rate_video_share"
            tools:ignore="ContentDescription" />

        <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
            android:id="@+id/graphTypeFabMain"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginBottom="@dimen/size_spacing_xlarge"
            app:backgroundTint="@color/white"
            app:collapsedSize="56dp"
            app:iconTint="@color/near_black"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:icon="@drawable/ic_pace_video_share"
            tools:ignore="ContentDescription" />

        <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
            android:id="@+id/mapStyleFabSub1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/size_spacing_medium"
            app:backgroundTint="@color/white"
            app:collapsedSize="40dp"
            app:iconSize="40dp"
            app:iconTint="@null"
            app:layout_constraintBottom_toTopOf="@id/mapStyleFabMain"
            app:layout_constraintEnd_toEndOf="@id/mapStyleFabMain"
            app:layout_constraintStart_toStartOf="@id/mapStyleFabMain"
            tools:icon="@drawable/map_type_mapbox_ski"
            tools:ignore="ContentDescription" />

        <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
            android:id="@+id/mapStyleFabMain"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:layout_marginBottom="@dimen/size_spacing_xlarge"
            app:backgroundTint="@color/white"
            app:collapsedSize="56dp"
            app:iconSize="56dp"
            app:iconTint="@null"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:icon="@drawable/map_type_mapbox_terrain"
            tools:ignore="ContentDescription" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <Button
        android:id="@+id/shareVideoBtn"
        style="@style/Button.Wide"
        android:layout_width="0dp"
        android:layout_marginStart="@dimen/size_spacing_large"
        android:layout_marginTop="@dimen/size_spacing_xlarge"
        android:layout_marginEnd="@dimen/size_spacing_large"
        android:text="@string/video_share_share_video"
        android:textColor="@color/color_text_share_primary_button"
        app:layout_constraintBottom_toTopOf="@id/shareSummaryLinkBtn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/previewContainer" />

    <TextView
        android:id="@+id/shareSummaryLinkBtn"
        style="@style/HeaderLabel.Medium"
        android:layout_width="0dp"
        android:layout_height="@dimen/height_button"
        android:layout_marginStart="@dimen/size_spacing_large"
        android:layout_marginTop="@dimen/size_spacing_medium"
        android:layout_marginEnd="@dimen/size_spacing_large"
        android:layout_marginBottom="@dimen/size_spacing_medium"
        android:gravity="center"
        android:text="@string/share_activity"
        android:textColor="?colorAccent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/shareVideoBtn" />

</androidx.constraintlayout.widget.ConstraintLayout>
