<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/laps_fragment_container"
    style="@style/WorkoutDetailCard.TopMargin"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        style="@style/HeaderLabel.Medium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/size_spacing_medium"
        android:text="@string/select_lap"
        android:textAllCaps="false" />

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/laps_tab_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/size_spacing_small"
        app:tabIndicator="@null"
        app:tabMode="scrollable"
        app:tabPaddingEnd="@dimen/size_spacing_small"
        app:tabPaddingStart="@dimen/size_spacing_small"
        app:tabTextAppearance="@style/AdvancedLapsTabTextAppearance" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/size_divider"
        android:background="?suuntoDividerColor" />

    <androidx.compose.ui.platform.ComposeView
        android:id="@+id/lap_cell_color_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <com.stt.android.ui.utils.SmarterViewPager
        android:id="@+id/laps_view_pager"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</LinearLayout>
