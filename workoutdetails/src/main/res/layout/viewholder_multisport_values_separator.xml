<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <variable
            name="formattedTransitionTime"
            type="String" />
    </data>

    <TextView
        android:id="@+id/transition_text"
        style="@style/Body.Medium"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/size_spacing_smaller"
        android:layout_marginBottom="@dimen/size_spacing_smaller"
        android:gravity="center"
        android:text="@{formattedTransitionTime != null ? @string/multisport_transition_time(formattedTransitionTime) : @string/multisport_transition}" />
</layout>
