<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <merge>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/toolbar_title_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:orientation="vertical">

            <TextView
                android:id="@+id/title"
                style="@style/Body.Medium.Username"
                android:layout_width="wrap_content"
                android:ellipsize="end"
                android:freezesText="true"
                android:maxLines="1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="<PERSON>" />

            <TextView
                android:id="@+id/subtitle"
                style="@style/ActionBarSubtitle"
                android:drawablePadding="@dimen/item_spacing"
                android:ellipsize="end"
                android:freezesText="true"
                android:maxLines="2"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title"
                tools:text="3 months ago ・ The Royal Town of Sutton · " />

            <TextView
                android:id="@+id/barTitle"
                style="@style/Body.Medium.Username"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/actionbar_icon_height"
                android:ellipsize="end"
                android:freezesText="true"
                android:maxLines="1"
                android:gravity="center"
                android:textColor="@color/white"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:visibility="gone"
                android:textAllCaps="true"
                android:text="@string/competition_target_select_title" />

            <ImageView
                android:id="@+id/shareIcon"
                style="@style/Icon.Tiny"
                android:layout_marginStart="@dimen/size_spacing_xxsmall"
                android:contentDescription="@null"
                app:layout_constraintBottom_toBottomOf="@id/subtitle"
                app:layout_constraintStart_toEndOf="@id/subtitle"
                app:layout_constraintTop_toTopOf="@id/subtitle"
                tools:src="@drawable/ic_privacy_12" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </merge>
</layout>
