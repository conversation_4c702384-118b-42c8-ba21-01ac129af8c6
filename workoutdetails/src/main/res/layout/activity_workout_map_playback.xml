<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/mapContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dataContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.compose.ui.platform.ComposeView
            android:id="@+id/composeView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent" />

        <com.stt.android.ui.fragments.workout.WorkoutChartShareWithCustomAxisView
            android:id="@+id/chartContainer"
            android:layout_width="match_parent"
            android:layout_height="128dp"
            android:layout_gravity="bottom"
            android:layout_marginHorizontal="@dimen/size_spacing_medium"
            app:layout_constraintBottom_toTopOf="@id/bottomBarrier" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/bottomBarrier"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:barrierDirection="top"
            app:constraint_referenced_ids="firstData,secondData,thirdData" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/firstData"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start|bottom"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:includeFontPadding="false"
            android:maxLines="3"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/secondData"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="14.7 km\nDistance" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/secondData"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal|bottom"
            android:includeFontPadding="false"
            android:maxLines="3"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/thirdData"
            app:layout_constraintStart_toEndOf="@id/firstData"
            tools:text="00:43:28\nDuration" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/thirdData"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|bottom"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:includeFontPadding="false"
            android:maxLines="3"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/secondData"
            tools:text="5:43 /km\nAvg pace" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/bottomGroup"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:constraint_referenced_ids="chartContainer,firstData,secondData,thirdData" />

        <Button
            android:id="@+id/shareVideoBtn"
            style="@style/Button.Wide"
            android:layout_width="0dp"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:layout_marginBottom="@dimen/size_spacing_xlarge"
            android:maxWidth="@dimen/content_max_width"
            android:text="@string/video_share_share_video"
            android:textColor="@color/color_text_share_primary_button"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/credit"
        style="@style/Label15normal"
        android:layout_gravity="bottom|start"
        android:layout_marginStart="@dimen/leftMargin"
        android:visibility="invisible"
        tools:text="Open Street map®"
        tools:visibility="visible" />

    <FrameLayout
        android:id="@+id/loading"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_gravity="center"
        android:background="@drawable/bg_loading_map_playback">

        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:indeterminate="true"
            app:indicatorColor="@android:color/white"
            app:indicatorSize="24dp"
            app:trackThickness="3dp" />

    </FrameLayout>

    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/playFab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone"
        app:backgroundTint="@color/white"
        app:icon="@drawable/ic_play_fill"
        app:iconTint="@color/near_black"
        tools:ignore="ContentDescription"
        tools:visibility="visible" />

</FrameLayout>
