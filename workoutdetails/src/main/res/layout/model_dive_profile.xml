<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/container"
        style="@style/WorkoutDetailCard.TopMargin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/selectable_card_background"
        android:clickable="true"
        android:focusable="true">

        <TextView
            android:id="@+id/diveProfileTitle"
            style="@style/WorkoutDetailCardTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:text="@string/dive_profile"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/diveProfileInfo"
            style="@style/Body.Small"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/size_spacing_xxsmall"
            app:layout_constraintLeft_toLeftOf="@+id/diveProfileTitle"
            app:layout_constraintTop_toBottomOf="@+id/diveProfileTitle"
            tools:text="Max depth 48m" />

        <TextView
            android:id="@+id/diveProfileViewEvents"
            style="@style/Body.Medium.Black"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:text="@string/dive_profile_show_events"
            android:textColor="?newAccentColor"
            android:visibility="gone"
            app:layout_constraintBaseline_toBaselineOf="@id/diveProfileTitle"
            app:layout_constraintRight_toRightOf="parent"
            tools:visibility="visible" />

        <com.stt.android.workout.details.charts.DiveProfileLineChart
            android:id="@+id/workoutLineChart"
            android:layout_width="0dp"
            android:layout_height="@dimen/size_chart_height"
            android:layout_marginBottom="@dimen/size_spacing_medium"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/diveProfileInfo"
            tools:background="@color/accent"
            tools:layout_constraintLeft_creator="1"
            tools:layout_constraintRight_creator="1"
            tools:layout_constraintTop_creator="1" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
