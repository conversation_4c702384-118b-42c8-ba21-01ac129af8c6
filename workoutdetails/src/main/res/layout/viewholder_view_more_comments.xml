<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="commentsCount"
            type="int" />

        <variable
            name="clickListener"
            type="android.view.View.OnClickListener" />

    </data>

    <TextView
        style="@style/WorkoutDetailCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:foreground="?attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:onClick="@{clickListener}"
        android:paddingStart="@dimen/size_spacing_medium"
        android:paddingEnd="@dimen/size_spacing_medium"
        android:paddingTop="@dimen/size_spacing_xsmall"
        android:paddingBottom="@dimen/size_spacing_xsmall"
        android:textAppearance="@style/Body.Medium"
        android:textColor="?newAccentColor"
        android:text="@{@plurals/view_all_x_comments(commentsCount, commentsCount)}"
        tools:text="View all 13 comments"
        tools:ignore="UnusedAttribute" />
</layout>
