<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <import type="com.stt.android.workout.details.WorkoutDetailsViewState"/>
        <import type="com.stt.android.common.viewstate.ViewState"/>
        <import type="android.view.View"/>

        <variable
            name="state"
            type="ViewState&lt;WorkoutDetailsViewState&gt;" />

    </data>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/pager"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:enableUserInput="@{state.data.showPagerIndicator}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/pager_indicator"
            android:layout_width="@dimen/pager_indicator_width"
            android:layout_height="@dimen/pager_indicator_height"
            app:tabBackground="@drawable/tab_selector"
            app:tabGravity="center"
            app:tabIndicatorHeight="0dp"
            app:tabMaxWidth="@dimen/pager_indicator_item_max_width"
            app:tabMinWidth="@dimen/pager_indicator_item_min_width"
            app:tabMode="scrollable"
            app:tabRippleColor="@null"
            app:visible="@{state.data.showPagerIndicator}"
            app:layout_constraintBottom_toBottomOf="@id/pager"
            app:layout_constraintStart_toStartOf="@id/pager"
            app:layout_constraintEnd_toEndOf="@id/pager"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>

