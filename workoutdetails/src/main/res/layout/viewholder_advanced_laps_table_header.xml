<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="com.stt.android.workout.details.laps.advanced.AdvancedLapsRowType"/>

        <import type="android.view.View"/>

        <import type="androidx.appcompat.content.res.AppCompatResources"/>

        <variable
            name="item"
            type="com.stt.android.workout.details.laps.advanced.table.AdvancedLapsTableHeaderItem"/>

        <variable
            name="column1DrawableResId"
            type="Integer"/>

        <variable
            name="column2DrawableResId"
            type="Integer"/>

        <variable
            name="column3DrawableResId"
            type="Integer"/>

        <variable
            name="column4DrawableResId"
            type="Integer"/>

        <variable
            name="onColumn1HeaderClicked"
            type="android.view.View.OnClickListener"/>

        <variable
            name="onColumn2HeaderClicked"
            type="android.view.View.OnClickListener"/>

        <variable
            name="onColumn3HeaderClicked"
            type="android.view.View.OnClickListener"/>

        <variable
            name="onColumn4HeaderClicked"
            type="android.view.View.OnClickListener"/>
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lap_row_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/advanced_laps_header_row_height"
        android:paddingTop="@dimen/size_spacing_small">

        <TextView
            android:id="@+id/laps_row_number_column_label"
            android:layout_width="@dimen/advanced_laps_number_width"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/size_spacing_large"
            android:maxLines="1"
            android:text="@string/lap_number_label"
            android:textSize="@dimen/text_size_medium_dp"
            app:autoSizeMaxTextSize="@dimen/text_size_medium_dp"
            app:autoSizeMinTextSize="@dimen/text_size_small_dp"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="SpUsage"
            tools:text="#"
            style="@style/Body.Medium"/>

        <FrameLayout
            android:id="@+id/laps_header_1_layout"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/laps_header_2_layout"
            app:layout_constraintStart_toEndOf="@id/laps_row_number_column_label"
            app:layout_constraintTop_toTopOf="parent">
            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/laps_header_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:onClick="@{onColumn1HeaderClicked}"
                android:enabled="@{onColumn1HeaderClicked != null}"
                android:src="@{AppCompatResources.getDrawable(context, column1DrawableResId)}"
                android:contentDescription="@null"
                app:tint="?android:textColorPrimary"
                style="@style/Fab.Small"/>
        </FrameLayout>

        <FrameLayout
            android:id="@+id/laps_header_2_layout"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/laps_header_3_layout"
            app:layout_constraintStart_toEndOf="@id/laps_header_1_layout"
            app:layout_constraintTop_toTopOf="parent">
            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/laps_header_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:onClick="@{onColumn2HeaderClicked}"
                android:enabled="@{onColumn2HeaderClicked != null}"
                android:src="@{AppCompatResources.getDrawable(context, column2DrawableResId)}"
                android:contentDescription="@null"
                app:tint="?android:textColorPrimary"
                style="@style/Fab.Small"/>
        </FrameLayout>

        <FrameLayout
            android:id="@+id/laps_header_3_layout"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/laps_header_4_layout"
            app:layout_constraintStart_toEndOf="@id/laps_header_2_layout"
            app:layout_constraintTop_toTopOf="parent">
            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/laps_header_3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:onClick="@{onColumn3HeaderClicked}"
                android:enabled="@{onColumn3HeaderClicked != null}"
                android:src="@{AppCompatResources.getDrawable(context, column3DrawableResId)}"
                android:contentDescription="@null"
                app:tint="?android:textColorPrimary"
                style="@style/Fab.Small"/>
        </FrameLayout>

        <FrameLayout
            android:id="@+id/laps_header_4_layout"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/laps_header_3_layout"
            app:layout_constraintTop_toTopOf="parent">
            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/laps_header_4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:onClick="@{onColumn4HeaderClicked}"
                android:enabled="@{onColumn4HeaderClicked != null}"
                android:src="@{AppCompatResources.getDrawable(context, column4DrawableResId)}"
                android:contentDescription="@null"
                app:tint="?android:textColorPrimary"
                style="@style/Fab.Small"/>
        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
