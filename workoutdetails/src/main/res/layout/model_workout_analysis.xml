<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    style="@style/WorkoutDetailCard.TopMargin"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/selectable_card_background"
    android:clickable="true"
    android:focusable="true">

    <TextView
        android:id="@+id/graphTitle"
        style="@style/WorkoutDetailCardTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:layout_marginTop="@dimen/size_spacing_medium"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Speed" />

    <TextView
        android:id="@+id/graphSubtitle"
        style="@style/Body.Small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/size_spacing_xxsmall"
        app:layout_constraintLeft_toLeftOf="@id/graphTitle"
        app:layout_constraintTop_toBottomOf="@id/graphTitle"
        tools:text="Average 8.25 km/h" />

    <TextView
        android:id="@+id/graphButton"
        style="@style/Body.Medium.Black"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/size_spacing_medium"
        android:paddingEnd="@dimen/size_spacing_xsmall"
        android:textColor="?newAccentColor"
        app:layout_constraintBaseline_toBaselineOf="@id/graphTitle"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="VIEW ON MAP" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/graphPager"
        android:layout_width="0dp"
        android:layout_height="@dimen/size_chart_height"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:layout_marginTop="@dimen/size_spacing_medium"
        android:layout_marginEnd="@dimen/size_spacing_medium"
        app:layout_constraintBottom_toTopOf="@+id/graphPagerIndicator"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/graphSubtitle"
        app:layout_goneMarginBottom="@dimen/size_spacing_medium"
        tools:layout_constraintLeft_creator="1"
        tools:layout_constraintRight_creator="1"
        tools:layout_constraintTop_creator="1" />

    <me.relex.circleindicator.CircleIndicator3
        android:id="@+id/graphPagerIndicator"
        android:layout_width="match_parent"
        android:layout_height="@dimen/pager_indicator_height"
        app:ci_drawable="@drawable/black_dot"
        app:ci_drawable_unselected="@drawable/grey_dot"
        app:ci_height="@dimen/pager_indicator_dot_size"
        app:ci_width="@dimen/pager_indicator_dot_size"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/graphPager" />
</androidx.constraintlayout.widget.ConstraintLayout>
