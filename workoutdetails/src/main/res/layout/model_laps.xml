<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        style="@style/WorkoutDetailCard.TopMargin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingBottom="@dimen/padding"
        android:theme="@style/WhiteTheme"
        tools:visibility="visible">

        <TextView
            android:id="@+id/title"
            style="@style/WorkoutDetailCardTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/padding"
            android:paddingTop="@dimen/padding"
            android:text="@string/laps"
            tools:ignore="RtlSymmetry" />

        <include
            android:id="@+id/lapsTypeSelector"
            layout="@layout/laps_type_selector_widget" />

        <include
            android:id="@+id/lapTableWidget"
            layout="@layout/lap_table_widget" />
    </LinearLayout>
</layout>
