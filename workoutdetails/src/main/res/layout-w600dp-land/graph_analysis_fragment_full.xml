<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/analysis_controls_and_values"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_spacing_large"
        app:layout_constraintBottom_toTopOf="@id/top_contents_bottom_barrier"
        app:layout_constraintEnd_toStartOf="@id/parent_horizontal_center_guide"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/play_button"
            android:layout_width="@dimen/graph_analysis_image_button_size"
            android:layout_height="@dimen/graph_analysis_image_button_size"
            android:background="?selectableItemBackground"
            android:contentDescription="@string/play_button"
            android:src="@drawable/ic_play_white_triangle"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/highlighted_duration_label"
            style="@style/Body.Large.Bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_xsmall"
            app:layout_constraintBottom_toBottomOf="@id/play_button"
            app:layout_constraintStart_toEndOf="@id/play_button"
            app:layout_constraintTop_toTopOf="@id/play_button"
            tools:text="00:00'00" />

        <TextView
            android:id="@+id/highlighted_distance_label"
            style="@style/Body.Large.Bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_medium"
            app:layout_constraintBottom_toBottomOf="@id/highlighted_duration_label"
            app:layout_constraintStart_toEndOf="@id/highlighted_duration_label"
            app:layout_constraintTop_toTopOf="@id/highlighted_duration_label"
            tools:text="123.45" />

        <TextView
            android:id="@+id/highlighted_distance_unit"
            style="@style/Body.Small.Bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_spacing_xxsmall"
            app:layout_constraintBaseline_toBaselineOf="@id/highlighted_distance_label"
            app:layout_constraintStart_toEndOf="@id/highlighted_distance_label"
            tools:text="km" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/parent_horizontal_center_guide"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.4" />

    <LinearLayout
        android:id="@+id/graph_highlight_info_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_spacing_small"
        android:orientation="horizontal"
        app:layout_constraintBottom_toTopOf="@id/top_contents_bottom_barrier"
        app:layout_constraintEnd_toStartOf="@id/close_fullscreen_button"
        app:layout_constraintStart_toEndOf="@id/parent_horizontal_center_guide"
        app:layout_constraintTop_toTopOf="parent">

        <com.stt.android.workout.details.graphanalysis.highlight.GraphAnalysisHighlightInfoView
            android:id="@+id/main_graph_highlight_info"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            app:graphColor="@color/graph_analysis_main" />

        <com.stt.android.workout.details.graphanalysis.highlight.GraphAnalysisHighlightInfoView
            android:id="@+id/comparison_graph_highlight_info"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            app:graphColor="@color/graph_analysis_comparison" />

        <com.stt.android.workout.details.graphanalysis.highlight.GraphAnalysisHighlightInfoView
            android:id="@+id/background_graph_highlight_info"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            app:graphColor="@color/graph_analysis_background" />

    </LinearLayout>

    <ImageButton
        android:id="@+id/close_fullscreen_button"
        android:layout_width="@dimen/graph_analysis_image_button_size"
        android:layout_height="@dimen/graph_analysis_image_button_size"
        android:layout_marginStart="@dimen/size_spacing_small"
        android:layout_marginEnd="@dimen/size_spacing_large"
        android:background="?actionBarItemBackground"
        android:contentDescription="@string/exo_controls_fullscreen_enter_description"
        android:padding="@dimen/size_spacing_smaller"
        android:scaleType="fitCenter"
        android:src="@drawable/minimize_fill"
        app:layout_constraintBottom_toTopOf="@id/top_contents_bottom_barrier"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/graph_highlight_info_container"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/top_contents_bottom_barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="graph_highlight_info_container,analysis_controls_and_values" />

    <com.stt.android.workout.details.graphanalysis.GraphAnalysisChart
        android:id="@+id/graph_analysis_chart"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/size_spacing_large"
        android:layout_marginTop="@dimen/size_spacing_medium"
        android:layout_marginEnd="@dimen/size_spacing_large"
        android:layout_marginBottom="@dimen/size_spacing_xsmall"
        android:background="@color/white"
        app:layout_constraintBottom_toTopOf="@id/analysis_selected_lap"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/top_contents_bottom_barrier"
        app:showAxisLabels="true"
        app:xAxisMinorLineCount="4"
        app:xAxisShowOnlyTickMarks="false"
        app:yAxisMinorLineCount="4"
        app:yAxisShowOnlyTickMarks="false" />

    <com.stt.android.workout.details.graphanalysis.laps.GraphAnalysisSelectedLapView
        android:id="@+id/analysis_selected_lap"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/graph_analysis_image_button_size"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <ProgressBar
        android:id="@+id/loading_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/invisible_while_loading_group"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:constraint_referenced_ids="analysis_controls_and_values,analysis_selected_lap" />
</androidx.constraintlayout.widget.ConstraintLayout>
