<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:orientation="vertical"
    android:layout_height="match_parent">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nested_scrollview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/layout_share">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:clipToPadding="false">

            <ImageView
                android:id="@+id/user_profile_iv"
                android:layout_width="32dp"
                android:layout_height="32dp"
                app:layout_constraintStart_toStartOf="@+id/cover_image_pager"
                android:layout_marginStart="@dimen/size_spacing_medium"
                app:layout_constraintTop_toTopOf="@+id/user_name"
                tools:background="@drawable/activity_type_icon_background_oval"
                android:src="@drawable/ic_default_profile_image_light"
                android:layout_marginTop="@dimen/size_spacing_xxsmall" />

            <TextView
                android:id="@+id/user_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:textColor="@color/near_black"
                android:textStyle="bold"
                android:layout_marginStart="@dimen/size_spacing_smaller"
                tools:text="Antonio Ruiz"
                android:textSize="@dimen/text_size_medium"
                android:paddingEnd="@dimen/size_spacing_small"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toEndOf="@+id/user_profile_iv"
                app:layout_constraintEnd_toStartOf="@+id/guideline"
                android:layout_marginTop="@dimen/size_spacing_medium" />

            <ImageView
                android:id="@+id/activity_type_image"
                android:layout_width="32dp"
                android:layout_height="32dp"
                app:layout_constraintTop_toTopOf="@id/user_profile_iv"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginEnd="@dimen/size_spacing_smaller"
                tools:background="@drawable/activity_type_icon_background_oval"
                tools:src="@drawable/ic_activity_running"
                android:layout_marginTop="@dimen/size_spacing_xxsmall" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.5" />

            <TextView
                android:id="@+id/activity_date"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:textSize="@dimen/text_size_small"
                app:layout_constraintTop_toBottomOf="@+id/user_name"
                app:layout_constraintStart_toEndOf="@+id/user_profile_iv"
                android:layout_marginStart="@dimen/size_spacing_smaller"
                android:textColor="@color/near_black"
                app:layout_constraintEnd_toStartOf="@+id/guideline"
                android:paddingEnd="@dimen/size_spacing_small"
                tools:text="2022/01/03 10:20" />

            <LinearLayout
                android:id="@+id/activity_type_and_watch_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toStartOf="@+id/activity_type_image"
                app:layout_constraintStart_toEndOf="@+id/guideline"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="@id/activity_date"
                android:layout_marginTop="@dimen/size_spacing_medium"
                android:layout_marginEnd="@dimen/size_spacing_smaller"
                android:gravity="end|center_vertical"
                android:orientation="vertical">
                <TextView
                    android:id="@+id/activity_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/near_black"
                    android:textStyle="bold"
                    tools:text="跑步"
                    android:textSize="@dimen/text_size_medium" />

                <TextView
                    android:id="@+id/watch_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/near_black"
                    android:textSize="@dimen/text_size_small"
                    tools:text="Suunto Spartan" />
            </LinearLayout>

            <androidx.constraintlayout.widget.Barrier
                android:id="@+id/barrier"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="activity_date,activity_type_and_watch_name"
                app:barrierDirection="bottom" />

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/cover_image_pager"
                style="@style/WorkoutDetailCard"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/size_spacing_medium"
                android:background="@color/photo_view_background"
                android:orientation="horizontal"
                android:outlineProvider="bounds"
                app:layout_constraintTop_toBottomOf="@+id/barrier"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintWidth_max="@dimen/content_max_width"
                tools:background="@null"
                tools:layout_height="367dp" />

            <androidx.fragment.app.FragmentContainerView
                android:id="@+id/detail_map_snapshotter"
                android:name="com.stt.android.maps.MapSnapshotterFragment"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/cover_image_pager"
                app:layout_constraintEnd_toEndOf="@id/cover_image_pager"
                app:layout_constraintStart_toStartOf="@id/cover_image_pager"
                app:layout_constraintTop_toTopOf="@id/cover_image_pager" />

            <com.stt.android.home.explore.routes.RouteView
                android:id="@+id/routeView"
                style="@style/Fab.Small"
                android:layout_width="@dimen/route_view_fab_size"
                android:layout_height="@dimen/route_view_fab_size"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="@dimen/padding"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/cover_image_pager"
                app:layout_constraintTop_toTopOf="@id/cover_image_pager"
                tools:background="@color/blue"
                tools:layout_height="30dp"
                tools:layout_width="30dp"
                tools:visibility="visible" />

            <com.stt.android.ui.components.WeatherConditionsView
                android:id="@+id/weatherConditionsView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/size_spacing_medium"
                android:layout_marginTop="16dp"
                app:layout_constraintStart_toStartOf="@id/cover_image_pager"
                app:layout_constraintTop_toTopOf="@id/cover_image_pager"
                tools:background="@color/red"
                tools:layout_height="20dp"
                tools:layout_width="200dp" />

            <com.stt.android.utils.EpoxyNonSharingRecyclerView
                android:id="@+id/list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?suuntoBackground"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintTop_toBottomOf="@id/cover_image_pager"
                tools:listitem="@layout/model_heart_rate_zones" />

            <ImageView
                android:id="@+id/iv_logo"
                android:layout_width="60dp"
                android:layout_height="22dp"
                android:src="@drawable/suunto_logo"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/list"
                android:layout_marginTop="@dimen/size_spacing_xlarge" />
            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/size_spacing_large"
                app:layout_constraintTop_toBottomOf="@+id/iv_logo" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>


   <LinearLayout
       android:id="@+id/layout_share"
       android:layout_width="match_parent"
       android:layout_height="wrap_content"
       android:orientation="vertical"
       android:background="@color/white"
       android:layout_alignParentBottom="true">
       <Button
           android:id="@+id/shareImageBtn"
           style="@style/Button.Wide"
           android:layout_width="match_parent"
           android:layout_marginStart="@dimen/size_spacing_large"
           android:layout_marginTop="@dimen/size_spacing_large"
           android:layout_marginEnd="@dimen/size_spacing_large"
           android:text="@string/share_photo"
           android:enabled="false"
           android:textColor="@color/color_text_share_primary_button" />

       <TextView
           android:id="@+id/shareSummaryLink"
           style="@style/HeaderLabel.Medium"
           android:layout_width="match_parent"
           android:layout_height="@dimen/height_button"
           android:layout_marginStart="@dimen/size_spacing_large"
           android:layout_marginTop="@dimen/size_spacing_medium"
           android:layout_marginEnd="@dimen/size_spacing_large"
           android:gravity="center"
           android:text="@string/share_activity"
           android:textColor="@color/color_text_disabled" />

       <TextView
           android:id="@+id/share3DLink"
           style="@style/HeaderLabel.Medium"
           android:layout_width="match_parent"
           android:layout_height="@dimen/height_button"
           android:layout_marginStart="@dimen/size_spacing_large"
           android:layout_marginTop="@dimen/size_spacing_small"
           android:layout_marginEnd="@dimen/size_spacing_large"
           android:gravity="center"
           android:text="@string/share_3d_video_activity"
           android:textColor="@color/color_text_disabled" />

       <Space
           android:layout_width="wrap_content"
           android:layout_height="@dimen/size_spacing_large" />
   </LinearLayout>

</RelativeLayout>
