<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white"
    >

    <TextView
        android:id="@+id/dataType"
        style="@style/Body.Medium.Bold"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/size_spacing_large"
        android:layout_marginStart="@dimen/size_spacing_medium"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Duration" />

    <com.stt.android.ui.components.charts.RecentWorkoutSummaryChart
        android:id="@+id/recentWorkoutSummaryChart"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        app:layout_constraintTop_toBottomOf="@+id/dataType"
        />

    <TextView
        android:id="@+id/startDate"
        style="@style/Body.Small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_spacing_medium"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/recentWorkoutSummaryChart"
        android:paddingBottom="@dimen/size_spacing_small"
        tools:text="60 days ago" />

    <TextView
        android:id="@+id/endDate"
        style="@style/Body.Small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/size_spacing_medium"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/startDate"
        android:paddingBottom="@dimen/size_spacing_small"
        tools:text="Now" />

</androidx.constraintlayout.widget.ConstraintLayout>
