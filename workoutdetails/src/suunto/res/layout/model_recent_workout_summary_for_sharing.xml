<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    style="@style/WorkoutDetailCard.TopMargin"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/activityIcon"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:layout_marginTop="@dimen/size_spacing_smaller"
        android:contentDescription="@null"
        android:tint="?android:textColorPrimary"
        android:tintMode="src_in"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/ic_activity_badminton" />

    <TextView
        android:id="@+id/title"
        style="@style/WorkoutDetailCardTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:drawablePadding="8dp"
        android:paddingStart="@dimen/size_spacing_smaller"
        android:paddingEnd="@dimen/size_spacing_medium"
        app:layout_constraintBottom_toBottomOf="@id/activityIcon"
        app:layout_constraintStart_toEndOf="@id/activityIcon"
        app:layout_constraintTop_toTopOf="@id/activityIcon"
        tools:text="30-Day Summary" />


    <com.stt.android.ui.components.RecentWorkoutSummaryView
        android:id="@+id/recentWorkoutSummaryView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/size_spacing_small"
        app:layout_constraintTop_toBottomOf="@id/title"
        tools:background="@color/light_gray"
        tools:maxHeight="120dp" />

    <com.stt.android.utils.EpoxyNonSharingRecyclerView
        android:id="@+id/list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/recentWorkoutSummaryView"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        android:background="?suuntoBackground"
        />

</androidx.constraintlayout.widget.ConstraintLayout>
