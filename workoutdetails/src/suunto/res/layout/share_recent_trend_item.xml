<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:paddingTop="@dimen/smaller_padding"
    android:paddingBottom="@dimen/smaller_padding">

    <TextView
        android:id="@+id/dataType"
        style="@style/Body.Medium.Bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:layout_marginTop="@dimen/size_spacing_xsmall"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Duration" />


    <com.stt.android.ui.components.charts.RecentWorkoutTrendChart
        android:id="@+id/recentWorkoutTrendChart"
        android:layout_width="0dp"
        android:layout_height="130dp"
        app:layout_constraintTop_toBottomOf="@+id/dataType"
        android:layout_marginTop="@dimen/size_spacing_smaller"
        app:layout_constraintEnd_toStartOf="@+id/comparisonContainer"
        app:layout_constraintStart_toStartOf="parent" />

    <LinearLayout
        android:id="@+id/comparisonContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="?android:attr/selectableItemBackground"
        android:orientation="vertical"
        android:padding="@dimen/padding"
        app:layout_constraintBottom_toBottomOf="@id/recentWorkoutTrendChart"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/recentWorkoutTrendChart">

        <TextView
            android:id="@+id/comparisonValue"
            tools:text="00:21 /km"
            tools:textColor="@color/green"
            style="@style/Body.Large.Bold"/>

        <TextView
            android:id="@+id/comparisonTitle"
            tools:text="@string/faster_than_previous"
            style="@style/Body.Medium"/>

        <TextView
            android:id="@+id/compareText"
            style="@style/Body.Medium.Black"
            android:textAllCaps="true"
            android:textColor="?newAccentColor"
            android:textSize="@dimen/text_size_medium"
            android:paddingTop="8dp"
            android:visibility="gone"
            android:text="@string/compare"
            tools:text="@string/compare" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
