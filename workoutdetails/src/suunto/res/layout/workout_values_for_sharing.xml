<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/workout_values_root"
    style="@style/WorkoutDetailCard"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/selectable_card_background">

    <ImageView
        android:id="@+id/activity_icon"
        android:layout_width="@dimen/size_icon_medium"
        android:layout_height="@dimen/size_icon_medium"
        android:layout_marginStart="@dimen/size_spacing_medium"
        android:layout_marginTop="@dimen/size_spacing_smaller"
        android:contentDescription="@string/activity_icon"
        app:tint="?android:textColorPrimary"
        android:tintMode="src_in"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/ic_activity_running" />

    <TextView
        android:id="@+id/activity_name"
        style="@style/WorkoutDetailCardTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:drawablePadding="8dp"
        android:paddingStart="@dimen/size_spacing_smaller"
        android:paddingEnd="@dimen/size_spacing_medium"
        app:layout_constraintBottom_toBottomOf="@id/activity_icon"
        app:layout_constraintStart_toEndOf="@id/activity_icon"
        app:layout_constraintTop_toTopOf="@id/activity_icon"
        tools:text="Running" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/activity_values_rv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/activity_icon"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        />

</androidx.constraintlayout.widget.ConstraintLayout>
