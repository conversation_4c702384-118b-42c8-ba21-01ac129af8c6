package com.stt.android.workout.details.share

import android.content.Context
import android.content.SharedPreferences
import androidx.fragment.app.FragmentManager
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.stt.android.common.viewstate.ViewState
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.di.ActivityFragmentManager
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.competition.CompetitionWorkoutSummaryData
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.utils.CalendarProvider
import com.stt.android.workout.details.BaseWorkoutDetailsController
import com.stt.android.workout.details.OnPageSelected
import com.stt.android.workout.details.RecentTrendData
import com.stt.android.workout.details.WorkoutDetailsViewState
import com.stt.android.workout.details.share.recentsummary.LongScreenshotRecentWorkoutSummaryController
import com.stt.android.workout.details.share.recentsummary.longScreenshotRecentWorkoutSummary
import com.stt.android.workout.details.share.recenttrend.LongScreenshotRecentTrendController
import com.stt.android.workout.details.share.recenttrend.longScreenshotRecentTrendData
import dagger.hilt.android.qualifiers.ActivityContext
import javax.inject.Inject

class LongScreenshotShareWorkoutDetailsController @Inject constructor(
    @ActivityContext context: Context,
    @ActivityFragmentManager fragmentManager: FragmentManager,
    infoModelFormatter: InfoModelFormatter,
    private val calendarProvider: CalendarProvider,
    private val longScreenshotRecentWorkoutSummaryController: LongScreenshotRecentWorkoutSummaryController,
    private val longScreenshotRecentTrendController: LongScreenshotRecentTrendController,
    private val workoutHeaderController: WorkoutHeaderController,
    unitConverter: JScienceUnitConverter,
    @FeatureTogglePreferences featureTogglePreferences: SharedPreferences,
) : BaseWorkoutDetailsController(
    context,
    fragmentManager,
    infoModelFormatter,
    unitConverter,
    featureTogglePreferences
) {
    override fun addTopModel(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        // do nothing
    }

    override fun addDiveLocation(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        // do nothing
    }

    override fun addShareActivity(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        // do nothing
    }

    override fun addRecentTrendData(
        competitionWorkoutSummaryData: CompetitionWorkoutSummaryData?,
        workoutHeader: WorkoutHeader,
        recentTrendData: RecentTrendData,
        onPageSelected: OnPageSelected?
    ) {
        longScreenshotRecentTrendData {
            id("recentTrendData")
            recentTrendData(recentTrendData)
            longScreenshotRecentTrendController(longScreenshotRecentTrendController)
        }
    }

    override fun addRecentWorkoutSummary(workoutDetailsViewState: ViewState<WorkoutDetailsViewState?>) {
        val data = workoutDetailsViewState.data?.recentWorkoutSummaryData?.data ?: return
        if (data.summary.workouts > 0) {
            longScreenshotRecentWorkoutSummary {
                id("recentWorkoutSummary")
                recentWorkoutSummary(data.summary)
                infoModelFormatter(infoModelFormatter)
                workoutHeaderController(workoutHeaderController)
                calendarProvider(calendarProvider)
                lifecycleScope(lifecycleScope)
                longScreenshotRecentWorkoutSummaryController(
                    longScreenshotRecentWorkoutSummaryController
                )
            }
        }
    }

    override fun longScreenshotLayout(): Boolean = true
}
