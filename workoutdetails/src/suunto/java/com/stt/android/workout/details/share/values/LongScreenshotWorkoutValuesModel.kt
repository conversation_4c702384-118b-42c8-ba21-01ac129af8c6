package com.stt.android.workout.details.share.values

import android.content.res.Resources
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.ColorRes
import androidx.annotation.DimenRes
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.domain.workout.ActivityType
import com.stt.android.workout.details.R
import com.stt.android.workout.details.WorkoutValuesContainer
import com.stt.android.workouts.details.values.WorkoutValueAdapter
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

@EpoxyModelClass
abstract class LongScreenshotWorkoutValuesModel : EpoxyModelWithHolder<Holder>() {
    @EpoxyAttribute
    lateinit var workoutValuesContainer: WorkoutValuesContainer

    @EpoxyAttribute
    var addTopMargin: Boolean = false

    @EpoxyAttribute
    var showActivityType: Boolean = true

    @EpoxyAttribute
    @DimenRes
    var elevationDimenRes = BaseR.dimen.feed_card_elevation

    @EpoxyAttribute
    @ColorRes
    var backgroundColorRes = CR.color.white

    @EpoxyAttribute
    @ColorRes
    var valueSeparatorColorRes = CR.color.very_light_gray

    @EpoxyAttribute
    @ColorRes
    var textColorRes = CR.color.near_black

    private var itemsPerRow = 3

    override fun getDefaultLayout() = R.layout.workout_values_for_sharing

    override fun bind(holder: Holder) {
        super.bind(holder)
        with(holder) {
            val resources = activityIcon.resources
            setTopMargin(resources)
            setElevation(resources)
            setActivityTypeVisibility(resources)
            setBackgroundColor()
            itemsPerRow =
                holder.valuesRV.resources.getInteger(R.integer.workout_value_items_per_row)
            holder.valuesRV.layoutManager = GridLayoutManager(holder.valuesRV.context, itemsPerRow)
        }
        bindRV(holder)
    }

    private fun Holder.setElevation(resources: Resources) {
        root.elevation = if (elevationDimenRes != 0) {
            resources.getDimension(elevationDimenRes)
        } else {
            0f
        }
    }

    private fun Holder.setActivityTypeVisibility(resources: Resources) {
        if (showActivityType) {
            val activityType = ActivityType.valueOf(workoutValuesContainer.activityType)
            activityIcon.setImageDrawable(activityType.getIcon(activityIcon.context))
            activityName.text = activityType.getLocalizedName(resources)
            activityIcon.visibility = View.VISIBLE
            activityName.visibility = View.VISIBLE
        } else {
            activityIcon.visibility = View.GONE
            activityName.visibility = View.GONE
        }
    }

    private fun Holder.setBackgroundColor() {
        if (backgroundColorRes != 0) {
            root.setBackgroundColor(ContextCompat.getColor(root.context, backgroundColorRes))
        } else {
            root.background = null
        }
    }

    private fun bindRV(holder: Holder) {
        val valueRV = holder.valuesRV
        val adapter = WorkoutValueAdapter(
            initialEmptyAmount = 0,
            separatorColor = ContextCompat.getColor(holder.root.context, valueSeparatorColorRes),
            textColor = ContextCompat.getColor(holder.root.context, textColorRes),
            itemsPerRow = itemsPerRow,
            itemViewClickable = false,
            initialValues = workoutValuesContainer.workoutValues
        )
        valueRV.adapter = adapter
    }

    private fun Holder.setTopMargin(resources: Resources) {
        root.layoutParams = (root.layoutParams as ViewGroup.MarginLayoutParams).apply {
            topMargin = if (addTopMargin) {
                resources.getDimensionPixelSize(BaseR.dimen.size_spacing_medium)
            } else {
                0
            }
        }
    }

    override fun shouldSaveViewState(): Boolean = true
}

class Holder : KotlinEpoxyHolder() {
    val root: View by bind(R.id.workout_values_root)
    val activityIcon: ImageView by bind(R.id.activity_icon)
    val activityName: TextView by bind(R.id.activity_name)
    val valuesRV: RecyclerView by bind(R.id.activity_values_rv)
}
