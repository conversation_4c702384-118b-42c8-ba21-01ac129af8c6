package com.stt.android.workout.details.share

import androidx.annotation.IntDef
import androidx.annotation.StringRes
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.stt.android.multimedia.sportie.SportieShareSource
import com.stt.android.workout.details.R
import com.stt.android.workout.details.share.brief.BriefShareFragment
import com.stt.android.workout.details.share.video.VideoShareFragment
import com.stt.android.workouts.sharepreview.WorkoutSharePreviewFragment

class FragmentShareAdapter(
    fa: FragmentActivity,
    private val workoutHeaderId: Int,
    private val currentIndex: Int,
    private val workoutDetails: SportieShareSource?,
    private val defaultSummaryItems: ArrayList<String> = arrayListOf(),
    chinaFlavor: <PERSON>olean,
    enable3DVideoSharing: Boolean,
) : FragmentStateAdapter(fa) {

    private val pageArray = when {
        chinaFlavor && !enable3DVideoSharing -> CHINA_OLD
        chinaFlavor && enable3DVideoSharing -> CHINA_NEW
        enable3DVideoSharing -> GLOBAL_NEW
        else -> GLOBAL_OLD
    }

    private val enable3DVideoLinkSharing = if (chinaFlavor) !enable3DVideoSharing else true

    override fun getItemCount(): Int = pageArray.size

    override fun createFragment(position: Int): Fragment {
        return when (getPage(position)) {
            PAGE_VIDEO -> VideoShareFragment.newInstance(workoutHeaderId)

            PAGE_SUMMARY -> WorkoutSharePreviewFragment.newInstance(
                workoutHeaderId,
                currentIndex,
                workoutDetails,
                defaultSummaryItems,
                false,
                enable3DVideoLinkSharing,
            )

            PAGE_BRIEF -> BriefShareFragment.newInstance(
                workoutHeaderId,
                workoutDetails,
                currentIndex,
                enable3DVideoLinkSharing,
                pageArray.contains(PAGE_DETAIL),
            )

            PAGE_DETAIL -> LongScreenshotShareFragment.newInstance(
                workoutHeaderId,
                workoutDetails,
                currentIndex,
                enable3DVideoLinkSharing,
            )

            else -> throw IllegalArgumentException()
        }
    }

    @StringRes
    fun getItemTitle(position: Int): Int {
        return when (getPage(position)) {
            PAGE_VIDEO -> R.string.share_video
            PAGE_SUMMARY -> R.string.share_summary
            PAGE_BRIEF -> R.string.share_brief
            PAGE_DETAIL -> R.string.share_detail
            else -> throw IllegalArgumentException()
        }
    }

    @Page
    private fun getPage(position: Int) = pageArray[position]

    companion object {
        private const val PAGE_VIDEO = 1
        private const val PAGE_SUMMARY = 2
        private const val PAGE_BRIEF = 3
        private const val PAGE_DETAIL = 4

        @IntDef(value = [PAGE_VIDEO, PAGE_SUMMARY, PAGE_BRIEF, PAGE_DETAIL])
        annotation class Page

        private val GLOBAL_OLD = intArrayOf(PAGE_SUMMARY, PAGE_BRIEF)
        private val GLOBAL_NEW = intArrayOf(PAGE_VIDEO, PAGE_SUMMARY, PAGE_BRIEF)
        private val CHINA_OLD = intArrayOf(PAGE_SUMMARY, PAGE_BRIEF, PAGE_DETAIL)
        private val CHINA_NEW = intArrayOf(PAGE_VIDEO, PAGE_SUMMARY, PAGE_BRIEF)
    }
}
