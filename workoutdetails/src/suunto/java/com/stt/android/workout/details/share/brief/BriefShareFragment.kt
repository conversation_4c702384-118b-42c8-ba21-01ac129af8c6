package com.stt.android.workout.details.share.brief

import android.os.Bundle
import com.stt.android.multimedia.sportie.SportieShareSource
import com.stt.android.multimedia.sportie.SportieShareType
import com.stt.android.utils.STTConstants
import com.stt.android.workout.details.BaseWorkoutDetailsController
import com.stt.android.workout.details.share.LongScreenshotShareFragment
import com.stt.android.workout.details.share.MultipleWaysWorkoutShareActivity
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class BriefShareFragment : LongScreenshotShareFragment() {
    @Inject
    lateinit var briefWorkoutDetailController: BriefShareWorkoutDetailsController

    override val workoutDetailsController: BaseWorkoutDetailsController
        get() = briefWorkoutDetailController

    override fun loadData() {
        if (arguments?.getBoolean(EXTRA_DETAIL_PAGE_AVAILABLE) != true) {
            super.loadData()
        } else {
            // only load in LongScreenshotShareFragment
        }
    }

    override fun getScreenshotShareType(): SportieShareType = SportieShareType.BRIEF

    companion object {
        const val TAG = "FragmentLongScreenshotShare"

        private const val EXTRA_DETAIL_PAGE_AVAILABLE = "EXTRA_DETAIL_PAGE_AVAILABLE"

        fun newInstance(
            workoutHeaderId: Int,
            sportieShareSource: SportieShareSource?,
            currentIndex: Int,
            enable3dVideoLinkSharing: Boolean,
            detailPageAvailable: Boolean,
        ): BriefShareFragment {
            val briefShareFragment = BriefShareFragment()
            val args = Bundle()
            args.putInt(STTConstants.ExtraKeys.WORKOUT_ID, workoutHeaderId)
            args.putParcelable(
                MultipleWaysWorkoutShareActivity.EXTRA_SHARE_SOURCE,
                sportieShareSource
            )
            args.putInt(CURRENT_INDEX, currentIndex)
            args.putBoolean(EXTRA_3D_VIDEO_LINK_SHARING, enable3dVideoLinkSharing)
            args.putBoolean(EXTRA_DETAIL_PAGE_AVAILABLE, detailPageAvailable)
            briefShareFragment.arguments = args
            return briefShareFragment
        }
    }
}
