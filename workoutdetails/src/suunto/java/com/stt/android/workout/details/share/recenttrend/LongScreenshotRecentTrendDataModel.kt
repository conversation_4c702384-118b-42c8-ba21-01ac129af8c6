package com.stt.android.workout.details.share.recenttrend

import android.content.Context
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyItemSpacingDecorator
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.common.viewstate.ViewState
import com.stt.android.workout.details.R
import com.stt.android.workout.details.RecentTrendData
import com.stt.android.workoutdetail.trend.RouteSelection
import java.lang.ref.WeakReference
import com.stt.android.R as BaseR

@EpoxyModelClass
abstract class LongScreenshotRecentTrendDataModel :
    EpoxyModelWithHolder<RecentWorkoutTrendViewHolder>() {
    private lateinit var viewHolder: WeakReference<RecentWorkoutTrendViewHolder>

    @EpoxyAttribute
    lateinit var recentTrendData: RecentTrendData

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var longScreenshotRecentTrendController: LongScreenshotRecentTrendController

    override fun getDefaultLayout() = R.layout.model_share_recent_workout_trend

    override fun bind(holder: RecentWorkoutTrendViewHolder) {
        if (::viewHolder.isInitialized) viewHolder.clear()
        viewHolder = WeakReference(holder)
        val context = holder.trendRV.context
        setupRV(context, holder)
        holder.selectionText.text = context.resources.getString(
            BaseR.string.previous_on_all_route_capital
        )
    }

    override fun unbind(holder: RecentWorkoutTrendViewHolder) {
        viewHolder.clear()
    }

    private fun setupRV(
        context: Context,
        holder: RecentWorkoutTrendViewHolder
    ) {
        val isSimilar = recentTrendData.routeSelection == RouteSelection.ON_THIS_ROUTE &&
            recentTrendData.recentWorkoutTrend.previousWorkout != null
        longScreenshotRecentTrendController.initData(
            context,
            recentTrendData.recentWorkoutTrend.currentWorkout,
            isSimilar
        )
        holder.trendRV.adapter = longScreenshotRecentTrendController.adapter
        holder.trendRV.addItemDecoration(
            EpoxyItemSpacingDecorator(
                context.resources.getDimension(BaseR.dimen.size_spacing_medium).toInt()
            )
        )
        longScreenshotRecentTrendController.setData(
            ViewState.Loaded(
                recentTrendData.recentWorkoutTrend.toOldModel(
                    context
                )
            )
        )
    }
}

class RecentWorkoutTrendViewHolder : KotlinEpoxyHolder() {
    val selectionText by bind<TextView>(R.id.routeSelection)
    val trendRV by bind<RecyclerView>(R.id.trend_recyclerview)
}
