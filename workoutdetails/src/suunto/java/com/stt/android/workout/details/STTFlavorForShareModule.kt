package com.stt.android.workout.details

import com.stt.android.workouts.sharepreview.customshare.WorkoutShareHelper
import com.stt.android.workouts.details.MultipleWorkoutShareWaysHelperImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class STTFlavorForShareModule {

    @Binds
    abstract fun bindMultipleWorkoutShareWaysHelper(multipleWorkoutShareWaysHelper: MultipleWorkoutShareWaysHelperImpl): WorkoutShareHelper
}
