package com.stt.android.workout.details.share.recenttrend

import android.content.Context
import android.widget.TextView
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.domain.user.workout.RecentWorkoutTrend
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.components.charts.RecentWorkoutMarkerView
import com.stt.android.ui.components.charts.RecentWorkoutTrendChart
import com.stt.android.workout.details.R
import com.stt.android.workoutdetail.recentsummary.RecentWorkoutItemHelper.positionToPage
import com.stt.android.workoutdetail.trend.RecentTrendItemHelper

@EpoxyModelClass
abstract class LongScreenshotRecentTrendItemDataModel :
    EpoxyModelWithHolder<RecentWorkoutTrendItemViewHolder>() {

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var recentWorkoutTrend: RecentWorkoutTrend

    @EpoxyAttribute
    lateinit var context: Context

    @EpoxyAttribute
    var similarWorkout: Boolean = false

    @EpoxyAttribute
    var position: Int = 0

    @EpoxyAttribute
    lateinit var dataType: String

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var infoModelFormatter: InfoModelFormatter

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var unitConverter: JScienceUnitConverter

    override fun bind(holder: RecentWorkoutTrendItemViewHolder) {
        super.bind(holder)
        holder.dataType.text = dataType
        val page = positionToPage(recentWorkoutTrend.currentWorkout.activityType, position)
        holder.chart.xAxis.labelCount = 4
        holder.chart.marker = RecentWorkoutMarkerView(
            context,
            page,
            recentWorkoutTrend.currentWorkout.activityType,
            infoModelFormatter,
            unitConverter,
        )
        if (recentWorkoutTrend.previousWorkout != null) {
            RecentTrendItemHelper.setPreviousData(
                position,
                recentWorkoutTrend,
                context.resources,
                similarWorkout,
                holder.chart,
                holder.comparisonValue,
                holder.comparisonTitle,
                infoModelFormatter,
                unitConverter,
            )
        } else {
            RecentTrendItemHelper.setCurrentData(
                position,
                recentWorkoutTrend,
                holder.chart,
                context.resources
            )
        }
    }

    override fun getDefaultLayout(): Int = R.layout.share_recent_trend_item
}

class RecentWorkoutTrendItemViewHolder : KotlinEpoxyHolder() {
    val chart by bind<RecentWorkoutTrendChart>(R.id.recentWorkoutTrendChart)
    val comparisonValue by bind<TextView>(R.id.comparisonValue)
    val comparisonTitle by bind<TextView>(R.id.comparisonTitle)
    val dataType by bind<TextView>(R.id.dataType)
}
