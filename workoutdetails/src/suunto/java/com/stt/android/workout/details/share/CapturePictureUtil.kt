package com.stt.android.workout.details.share

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import androidx.core.graphics.createBitmap
import androidx.core.widget.NestedScrollView
import timber.log.Timber

object CapturePictureUtil {

    fun screenshotByNestedScrollView(
        nestedScrollView: NestedScrollView,
    ): Bitmap? {
        var bitmap: Bitmap? = null
        try {
            val view = nestedScrollView.getChildAt(0)
            bitmap = createBitmap(view.width, view.height)
            val canvas = Canvas(bitmap)
            canvas.drawColor(Color.TRANSPARENT)
            nestedScrollView.layout(
                0,
                0,
                nestedScrollView.measuredWidth,
                nestedScrollView.measuredHeight
            )
            nestedScrollView.draw(canvas)
        } catch (e: Exception) {
            Timber.w(e, "screenshot error")
        }
        return bitmap
    }
}
