package com.stt.android.workout.details.share.analysis

import android.content.Context
import android.view.View
import android.widget.TextView
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.core.domain.GraphType
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.domain.workouts.toDomainWindow
import com.stt.android.ui.fragments.workout.analysis.WorkoutAnalysisHelper
import com.stt.android.workout.details.OnWorkoutAnalysisGraphTapped
import com.stt.android.workout.details.R
import com.stt.android.workout.details.WorkoutAnalysisPagerData
import com.stt.android.workout.details.charts.AnalysisWorkoutLineChart

@EpoxyModelClass
abstract class LongScreenshotWorkoutAnalysisGraphModel :
    EpoxyModelWithHolder<NewWorkoutAnalysisGraphViewHolder>() {

    @EpoxyAttribute
    lateinit var graphType: GraphType

    @EpoxyAttribute
    var chartDataPosition: Int = 0

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var pagerData: WorkoutAnalysisPagerData

    @EpoxyAttribute
    var multisportPartActivity: MultisportPartActivity? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var onClick: OnWorkoutAnalysisGraphTapped

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var workoutAnalysisHelper: WorkoutAnalysisHelper

    override fun getDefaultLayout() = R.layout.page_workout_analysis_graph_for_sharing

    override fun bind(holder: NewWorkoutAnalysisGraphViewHolder) {
        drawGraph(holder)
        initTitle(holder)
        holder.workoutLineChart.setOnClickListener {
            onClick(graphType, multisportPartActivity)
        }
    }

    private fun initTitle(holder: NewWorkoutAnalysisGraphViewHolder) {
        val graphType = pagerData.graphData[chartDataPosition].graphType
        val context = holder.title.context ?: return
        holder.title.text = getTitle(context, graphType)
        holder.subTitle.text = getSubtitle(
            context,
            pagerData.workoutHeader,
            graphType,
            pagerData.diveExtension,
            pagerData.sml
        )
    }

    private fun drawGraph(holder: NewWorkoutAnalysisGraphViewHolder) {
        holder.workoutLineChart.setWorkoutLineChartData(pagerData.graphData[chartDataPosition])
    }

    private fun getTitle(context: Context, graphType: GraphType?) =
        graphType?.run { WorkoutAnalysisHelper.getGraphNameTitle(context, this) } ?: ""

    private fun getSubtitle(
        context: Context,
        workoutHeader: WorkoutHeader,
        graphType: GraphType,
        diveExtension: DiveExtension?,
        sml: Sml?
    ): String = workoutAnalysisHelper.getGraphNameSubtitle(
        context,
        graphType,
        workoutHeader,
        diveExtension,
        sml,
        sml?.getActivityWindow(multisportPartActivity)?.toDomainWindow()
    ) ?: ""
}

class NewWorkoutAnalysisGraphViewHolder : KotlinEpoxyHolder() {
    val root by bind<View>(R.id.root)
    val workoutLineChart by bind<AnalysisWorkoutLineChart>(R.id.workoutLineChart)
    val title by bind<TextView>(R.id.graphTitle)
    val subTitle by bind<TextView>(R.id.graphSubtitle)
}
