package com.stt.android.workout.details.share.analysis

import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.stt.android.ui.fragments.workout.analysis.WorkoutAnalysisHelper
import com.stt.android.workout.details.WorkoutAnalysisPagerData
import kotlinx.coroutines.CoroutineScope
import javax.inject.Inject

class LongScreenshotWorkoutAnalysisGraphController
@Inject constructor() : ViewStateEpoxyController<WorkoutAnalysisPagerData>() {
    var lifecycleScope: CoroutineScope? = null
    var workoutAnalysisHelper: WorkoutAnalysisHelper? = null
    override fun buildModels(viewState: ViewState<WorkoutAnalysisPagerData?>) {
        viewState.data?.let { data ->
            for ((index, graph) in data.graphData.withIndex()) {
                longScreenshotWorkoutAnalysisGraph {
                    id("${graph.graphType.key}_${data.multisportPartActivity?.toString() ?: "analysis"}")
                    multisportPartActivity(data.multisportPartActivity)
                    graphType(graph.graphType)
                    chartDataPosition(index)
                    workoutAnalysisHelper(workoutAnalysisHelper)
                    pagerData(data)
                    onClick(data.onGraphTapped)
                }
            }
        }
    }
}
