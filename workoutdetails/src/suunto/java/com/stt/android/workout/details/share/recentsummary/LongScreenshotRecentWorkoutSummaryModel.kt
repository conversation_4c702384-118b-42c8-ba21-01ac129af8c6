package com.stt.android.workout.details.share.recentsummary

import android.content.res.Resources
import android.text.format.DateUtils
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyItemSpacingDecorator
import com.airbnb.epoxy.EpoxyModelClass
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.common.viewstate.ViewState
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.domain.workout.ActivityType
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.components.RecentWorkoutSummaryView
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.utils.CalendarProvider
import com.stt.android.utils.DateUtils.DAY_IN_MILLIS
import com.stt.android.utils.DateUtils.isSameDay
import com.stt.android.utils.EpoxyNonSharingRecyclerView
import com.stt.android.workout.details.R
import com.stt.android.workout.details.RecentWorkoutSummary
import com.stt.android.workout.details.summary.BaseRecentWorkoutSummaryModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import com.stt.android.R as BR

@EpoxyModelClass
abstract class LongScreenshotRecentWorkoutSummaryModel :
    BaseRecentWorkoutSummaryModel<LongScreenshotRecentWorkoutSummaryViewHolder>() {

    @EpoxyAttribute
    lateinit var recentWorkoutSummary: RecentWorkoutSummary

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var infoModelFormatter: InfoModelFormatter

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var workoutHeaderController: WorkoutHeaderController

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var calendarProvider: CalendarProvider

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    var lifecycleScope: CoroutineScope? = null

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var longScreenshotRecentWorkoutSummaryController: LongScreenshotRecentWorkoutSummaryController

    override fun getDefaultLayout() = R.layout.model_recent_workout_summary_for_sharing

    override fun bind(holder: LongScreenshotRecentWorkoutSummaryViewHolder) {
        initColor(holder.root.context)
        initViewHolder(holder)
        val activityType: ActivityType = recentWorkoutSummary.referenceWorkout.activityType
        holder.activityIcon.setImageResource(activityType.iconId)
        showSummary(holder)
    }

    override fun unbind(holder: LongScreenshotRecentWorkoutSummaryViewHolder) {
        viewHolder.clear()
    }

    private fun showSummary(
        holder: LongScreenshotRecentWorkoutSummaryViewHolder
    ) {
        lifecycleScope?.launch {
            createSummary(recentWorkoutSummary, workoutHeaderController, infoModelFormatter)?.run {
                val summary = this.toOldModel()
                val startTime: Long = recentWorkoutSummary.startTime
                val endTime: Long = recentWorkoutSummary.endTime
                holder.summaryView.setRecentWorkoutSummary(summary, infoModelFormatter.unit)
                val resources: Resources = holder.root.context.resources
                val now = System.currentTimeMillis()
                val startDateText = if (isSameDay(endTime, now, calendarProvider)) {
                    val dayGap = ((now - startTime) / DateUtils.DAY_IN_MILLIS).toInt()
                    resources.getQuantityString(
                        BR.plurals.days_ago,
                        dayGap,
                        dayGap
                    )
                } else {
                    TextFormatter.formatRelativeDateSpan(
                        resources,
                        startTime
                    )
                }
                with(holder.rv) {
                    longScreenshotRecentWorkoutSummaryController.initData(
                        context,
                        recentWorkoutSummary.referenceWorkout,
                        startDateText.toString(),
                        TextFormatter.formatRelativeDateSpan(resources, endTime).toString()
                    )
                    // Avoid adding more than once itemDecoration
                    if (itemDecorationCount == 0) {
                        addItemDecoration(
                            EpoxyItemSpacingDecorator(
                                context.resources.getDimension(BR.dimen.size_spacing_medium).toInt()
                            )
                        )
                    }
                    adapter = longScreenshotRecentWorkoutSummaryController.adapter
                    longScreenshotRecentWorkoutSummaryController.setData(ViewState.Loaded(summary))
                }
                holder.title.text = resources.getString(
                    BR.string.days_summary,
                    (endTime - startTime) / DAY_IN_MILLIS
                )
            }
        }
    }
}

class LongScreenshotRecentWorkoutSummaryViewHolder : KotlinEpoxyHolder() {
    val root by bind<View>(R.id.root)
    val activityIcon by bind<ImageView>(R.id.activityIcon)
    val summaryView by bind<RecentWorkoutSummaryView>(R.id.recentWorkoutSummaryView)
    val rv by bind<EpoxyNonSharingRecyclerView>(R.id.list)
    val title by bind<TextView>(R.id.title)
}
