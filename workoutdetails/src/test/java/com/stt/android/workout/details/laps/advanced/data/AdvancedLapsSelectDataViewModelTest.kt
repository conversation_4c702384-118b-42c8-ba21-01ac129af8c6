package com.stt.android.workout.details.laps.advanced.data

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.lifecycle.SavedStateHandle
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.core.domain.AdvancedLapsSelectDataCategoryItem
import com.stt.android.core.domain.LapsTableDataType
import com.stt.android.core.domain.SuuntoPlusChannel
import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.domain.workout.ActivityType
import com.stt.android.infomodel.SummaryCategory
import com.stt.android.infomodel.SummaryItem
import io.reactivex.schedulers.Schedulers
import kotlinx.coroutines.Dispatchers
import org.assertj.core.api.Assertions.assertThat
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.junit.MockitoJUnitRunner

@RunWith(MockitoJUnitRunner::class)
class AdvancedLapsSelectDataViewModelTest {
    @JvmField
    @Rule
    val archTaskExecutorRule = InstantTaskExecutorRule()

    private val testDispatchers = object : CoroutinesDispatchers {
        override val main = Dispatchers.Unconfined
        override val computation = Dispatchers.Unconfined
        override val io = Dispatchers.Unconfined
    }

    private lateinit var viewModel: AdvancedLapsSelectDataViewModel

    private lateinit var expected: Map<AdvancedLapsSelectDataCategoryItem, List<AdvancedLapsSelectDataType>>

    private val stId = ActivityType.RUNNING.id

    // Simulates available summary items i.e. a subset of allItemsForLapType for which data exists. Other items
    // from allItemsForLapType should be filtered out.
    private val mockAvailableItems: List<LapsTableDataType> =
        listOf(SummaryItem.DURATION, SummaryItem.CUMULATEDDISTANCE)
            .map { LapsTableDataType.Summary(it) } + LapsTableDataType.SuuntoPlus(
            SuuntoPlusChannel(
                zappId = "zappId",
                channelId = 2,
                format = "format",
                inverted = true,
                name = "name",
                variableId = "variableId"
            )
        )

    @Before
    fun setup() {
        viewModel = AdvancedLapsSelectDataViewModel(
            SavedStateHandle(
                mutableMapOf(
                    AdvancedLapsSelectDataViewModel.KEY_ST_ID to stId,
                    AdvancedLapsSelectDataViewModel.KEY_DATA_TYPE to LapsTableDataType.Summary(SummaryItem.DISTANCE),
                    AdvancedLapsSelectDataViewModel.KEY_AVAILABLE_DATA_TYPES to mockAvailableItems,
                    AdvancedLapsSelectDataViewModel.KEY_COLUMN_INDEX to 0,
                    AdvancedLapsSelectDataViewModel.KEY_TABLE_TYPE to LapsTableType.DISTANCE_AUTO_LAP,
                )
            ),
            testDispatchers,
            Schedulers.trampoline(),
            Schedulers.trampoline()
        )
        expected = expectedResult()
    }

    @Test
    fun `loadData calls notifyDataLoaded with available items`() {
        viewModel.loadData()
        assertThat(viewModel.viewState.value?.data).isEqualTo(AdvancedLapsSelectDataContainer(expected))
    }

    private fun expectedResult(): Map<AdvancedLapsSelectDataCategoryItem, List<AdvancedLapsSelectDataType>> {
        return mapOf(
            AdvancedLapsSelectDataCategoryItem.Summary(
                SummaryCategory.DISTANCE
            ) to listOf(
                AdvancedLapsSelectDataType(
                    LapsTableDataType.Summary(SummaryItem.CUMULATEDDISTANCE),
                    false,
                    0,
                    LapsTableType.DISTANCE_AUTO_LAP,
                    viewModel.onSelectSummaryItem
                )
            ),
            AdvancedLapsSelectDataCategoryItem.Summary(
                SummaryCategory.DURATION
            ) to listOf(
                AdvancedLapsSelectDataType(
                    LapsTableDataType.Summary(SummaryItem.DURATION),
                    false,
                    0,
                    LapsTableType.DISTANCE_AUTO_LAP,
                    viewModel.onSelectSummaryItem
                )
            ),
            AdvancedLapsSelectDataCategoryItem.SuuntoPlus to listOf(
                AdvancedLapsSelectDataType(
                    LapsTableDataType.SuuntoPlus(
                        SuuntoPlusChannel(
                            zappId = "zappId",
                            channelId = 2,
                            format = "format",
                            inverted = true,
                            name = "name",
                            variableId = "variableId"
                        )
                    ),
                    false,
                    0,
                    LapsTableType.DISTANCE_AUTO_LAP,
                    viewModel.onSelectSummaryItem
                )
            )
        )
    }
}
