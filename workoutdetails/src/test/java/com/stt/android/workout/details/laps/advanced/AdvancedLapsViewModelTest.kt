package com.stt.android.workout.details.laps.advanced

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.lifecycle.SavedStateHandle
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.coroutines.CoroutinesDispatcherProvider
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loaded
import com.stt.android.core.domain.LapsTableDataType
import com.stt.android.core.domain.SuuntoPlusChannel
import com.stt.android.domain.advancedlaps.AdvancedLapsUseCase
import com.stt.android.domain.advancedlaps.FetchLapsTableColumnStatesUseCase
import com.stt.android.domain.advancedlaps.LapsTableColouringEnabledUseCase
import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.domain.advancedlaps.SaveLapsTableColumnsStatesUseCase
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.sml.reader.SmlFactory
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.infomodel.SummaryItem
import com.stt.android.logbook.SmlFromJson
import com.stt.android.logbook.SmlParser
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.workout.details.advancedlaps.AdvancedLapsDataLoader
import com.stt.android.workout.details.advancedlaps.DefaultAdvancedLapsDataLoader
import com.stt.android.workout.details.analytics.WorkoutDetailsAnalytics
import com.stt.android.workout.details.graphanalysis.laps.AnalysisLapsLoader
import com.stt.android.workout.details.graphanalysis.laps.LapMarkerModel
import com.stt.android.workout.details.multisport.DefaultMultisportPartActivityLoader
import com.stt.android.workout.details.sml.SmlDataLoader
import com.stt.android.workout.details.workoutheader.DefaultWorkoutHeaderLoader
import io.reactivex.schedulers.Schedulers
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestCoroutineScheduler
import kotlinx.coroutines.test.runCurrent
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.assertj.core.api.Assertions.assertThat
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.anyInt
import org.mockito.ArgumentMatchers.anyList
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.Mockito.doReturn
import org.mockito.Mockito.verify
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.doNothing
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.whenever
import java.util.zip.GZIPInputStream

@RunWith(MockitoJUnitRunner::class)
class AdvancedLapsViewModelTest {
    @JvmField
    @Rule
    val archTaskExecutorRule = InstantTaskExecutorRule()

    private val scheduler = TestCoroutineScheduler()
    private val testDispatcher = StandardTestDispatcher(scheduler)
    private val testDispatcherProvider = CoroutinesDispatcherProvider(
        main = testDispatcher,
        computation = testDispatcher,
        io = testDispatcher
    )

    @Mock
    private lateinit var workoutHeader: WorkoutHeader

    private val workoutHeaderLoader = DefaultWorkoutHeaderLoader()

    private val multisportPartActivityLoader = DefaultMultisportPartActivityLoader()

    private lateinit var advancedLapsDataLoader: AdvancedLapsDataLoader

    @Mock
    private lateinit var analysisLapsLoader: AnalysisLapsLoader

    @Mock
    private lateinit var fetchLapsTableColumnStatesUseCase: FetchLapsTableColumnStatesUseCase

    @Mock
    private lateinit var saveLapsTableColumnsStatesUseCase: SaveLapsTableColumnsStatesUseCase

    @Mock
    private lateinit var infoModelFormatter: InfoModelFormatter

    @Mock
    private lateinit var lapMarkerModel: LapMarkerModel

    @Mock
    private lateinit var workoutDetailsAnalytics: WorkoutDetailsAnalytics

    @Mock
    private lateinit var amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker

    @Mock
    private lateinit var lapsTableColouringEnabledUseCase: LapsTableColouringEnabledUseCase

    private lateinit var runningAutoLapsSml: Sml

    private lateinit var viewModel: AdvancedLapsViewModel

    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)

        whenever(fetchLapsTableColumnStatesUseCase.invoke(anyInt(), any()))
            .thenReturn(
                listOf(
                    LapsTableDataType.Summary(SummaryItem.DISTANCE),
                    LapsTableDataType.SuuntoPlus(
                        SuuntoPlusChannel(
                            zappId = "zappId",
                            channelId = 1,
                            format = "format",
                            inverted = true,
                            name = "name",
                            variableId = "variableId"
                        )
                    ),
                ),
                listOf(
                    LapsTableDataType.Summary(SummaryItem.AVGCADENCE),
                    LapsTableDataType.SuuntoPlus(
                        SuuntoPlusChannel(
                            zappId = "zappId",
                            channelId = 2,
                            format = "format",
                            inverted = true,
                            name = "name",
                            variableId = "variableId"
                        )
                    ),
                ),
            )

        doNothing().whenever(saveLapsTableColumnsStatesUseCase).invoke(anyInt(), any(), anyList())

        whenever(infoModelFormatter.unit).thenReturn(MeasurementUnit.METRIC)

        whenever(workoutHeader.activityTypeId).thenReturn(ActivityType.RUNNING.id)
        whenever(workoutHeader.activityType).thenReturn(ActivityType.RUNNING)

        workoutHeaderLoader.setWorkoutHeader(workoutHeader)
        multisportPartActivityLoader.setMultisportPartActivity(null)

        runningAutoLapsSml = getSml()
        val smlDataLoader = StaticSmlDataLoader(runningAutoLapsSml)

        advancedLapsDataLoader = DefaultAdvancedLapsDataLoader(
            AdvancedLapsUseCase(),
            infoModelFormatter,
            smlDataLoader,
            multisportPartActivityLoader,
            ActivityRetainedCoroutineScope(testDispatcherProvider, mock())
        )

        viewModel = AdvancedLapsViewModel(
            SavedStateHandle(),
            advancedLapsDataLoader,
            analysisLapsLoader,
            workoutHeaderLoader,
            multisportPartActivityLoader,
            fetchLapsTableColumnStatesUseCase,
            saveLapsTableColumnsStatesUseCase,
            lapsTableColouringEnabledUseCase,
            workoutDetailsAnalytics,
            amplitudeAnalyticsTracker,
            infoModelFormatter,
            lapMarkerModel,
            testDispatcherProvider,
            Schedulers.trampoline(),
            Schedulers.trampoline()
        )
    }

    @Test
    fun `test that loadData updates view state`() = runTest {
        val spy: AdvancedLapsViewModel = Mockito.spy(viewModel)
        doReturn(false).`when`(spy).isCellColouringEnabled(any())
        spy.loadData()
        runCurrent()
        val data = spy.viewState.value?.data
        assertThat(data).isNotNull
        data?.let {
            assertThat(data.container.tables.size).isEqualTo(4)
        }
    }

    @Test
    fun `test that onSelectSummaryItem updates view state`() = runTest {
        val spy: AdvancedLapsViewModel = Mockito.spy(viewModel)
        doReturn(false).`when`(spy).isCellColouringEnabled(any())
        spy.loadData()

        runCurrent()

        spy.onSelectDataType(
            ActivityType.RUNNING.id,
            LapsTableType.DISTANCE_AUTO_LAP,
            0,
            LapsTableDataType.Summary(SummaryItem.DISTANCE),
            LapsTableDataType.Summary(SummaryItem.NONE)
        )

        runCurrent()

        verify(spy, times(2)).updateData(any(), any())
    }

    private fun getSml(): Sml {
        val smlFromJson: SmlFromJson =
            GZIPInputStream(
                javaClass.classLoader?.getResourceAsStream("smlzip/running_autolaps.gzipped")
            ).use {
                SmlParser().parseSml(it)
            }

        return SmlFactory.create(smlFromJson.summaryContent, smlFromJson.data.samples, smlFromJson.data.statistics)
    }
}

private class StaticSmlDataLoader(sml: Sml) : SmlDataLoader {
    override val smlStateFlow = MutableStateFlow<ViewState<Sml?>>(loaded(sml))

    override fun loadSml(workoutHeader: WorkoutHeader): StateFlow<ViewState<Sml?>> {
        return smlStateFlow
    }

    override fun resetCache() {
    }

    override suspend fun getSml(workoutHeader: WorkoutHeader): Sml? {
        return null
    }
}
