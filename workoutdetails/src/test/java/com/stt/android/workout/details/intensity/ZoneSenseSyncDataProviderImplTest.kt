package com.stt.android.workout.details.intensity

import com.google.common.truth.Truth
import com.stt.android.controllers.CurrentUserController
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutDataSource
import com.stt.android.domain.workouts.ZoneSenseSyncWorkoutHeader
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.mockito.Mockito.`when`
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import kotlin.test.BeforeTest

class ZoneSenseSyncDataProviderImplTest {
    private val currentUserController: CurrentUserController = mock()
    private val workoutDataSource: WorkoutDataSource = mock()

    private lateinit var zoneSenseSyncDataProviderImpl: ZoneSenseSyncDataProviderImpl

    @BeforeTest
    fun setup() {
        whenever(currentUserController.username).thenReturn("username")
        zoneSenseSyncDataProviderImpl = ZoneSenseSyncDataProviderImpl(currentUserController, workoutDataSource)
    }

    @Test
    fun `when no workout then invalid baseline should be returned`() =
        runTest {
            `when`(workoutDataSource.getAllWorkoutsForZoneSenseSync("username")).thenReturn(emptyList())

            val data = zoneSenseSyncDataProviderImpl.getZoneSenseSyncData()

            Truth.assertThat(data.baselineValues.running).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.cycling).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.swimming).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.skiing).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.rowing).isEqualTo(-100f)
        }

    @Test
    fun `when there is only one workout, the baseline of that workout should be used 100 percent`() =
        runTest {
            `when`(workoutDataSource.getAllWorkoutsForZoneSenseSync("username")).thenReturn(
                listOf(
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.RUNNING.id,
                        zoneSenseAerobicBaseline = 0.45
                    )
                )
            )

            val data = zoneSenseSyncDataProviderImpl.getZoneSenseSyncData()

            Truth.assertThat(data.baselineValues.running).isEqualTo(0.45f)
            Truth.assertThat(data.baselineValues.cycling).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.swimming).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.skiing).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.rowing).isEqualTo(-100f)
        }

    @Test
    fun `when there is more than one workout, previous baseline 0 dot 25 and current one by zero dot 75`() =
        runTest {
            `when`(workoutDataSource.getAllWorkoutsForZoneSenseSync("username")).thenReturn(
                listOf(
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.RUNNING.id,
                        zoneSenseAerobicBaseline = 0.12
                    ),
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.TRAIL_RUNNING.id,
                        zoneSenseAerobicBaseline = 0.19
                    )
                )
            )

            val data = zoneSenseSyncDataProviderImpl.getZoneSenseSyncData()

            Truth.assertThat(data.baselineValues.running).isEqualTo(0.1375f)
            Truth.assertThat(data.baselineValues.cycling).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.swimming).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.skiing).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.rowing).isEqualTo(-100f)
        }

    @Test
    fun `null baseline in workouts should be skipped`() =
        runTest {
            `when`(workoutDataSource.getAllWorkoutsForZoneSenseSync("username")).thenReturn(
                listOf(
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.RUNNING.id,
                        zoneSenseAerobicBaseline = 0.12
                    ),
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.TRAIL_RUNNING.id,
                        zoneSenseAerobicBaseline = 0.19
                    ),
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.RUNNING.id,
                        zoneSenseAerobicBaseline = null
                    )
                )
            )

            val data = zoneSenseSyncDataProviderImpl.getZoneSenseSyncData()

            Truth.assertThat(data.baselineValues.running).isEqualTo(0.1375f)
            Truth.assertThat(data.baselineValues.cycling).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.swimming).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.skiing).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.rowing).isEqualTo(-100f)
        }

    @Test
    fun `invalid baseline in workouts should be ignored`() =
        runTest {
            `when`(workoutDataSource.getAllWorkoutsForZoneSenseSync("username")).thenReturn(
                listOf(
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.RUNNING.id,
                        zoneSenseAerobicBaseline = 0.12
                    ),
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.TRAIL_RUNNING.id,
                        zoneSenseAerobicBaseline = 0.19
                    ),
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.RUNNING.id,
                        zoneSenseAerobicBaseline = -50.0
                    ),
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.RUNNING.id,
                        zoneSenseAerobicBaseline = 50.0
                    )
                )
            )

            val data = zoneSenseSyncDataProviderImpl.getZoneSenseSyncData()

            Truth.assertThat(data.baselineValues.running).isEqualTo(0.1375f)
            Truth.assertThat(data.baselineValues.cycling).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.swimming).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.skiing).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.rowing).isEqualTo(-100f)
        }

    @Test
    fun `sports must be separated by group when calculating`() =
        runTest {
            `when`(workoutDataSource.getAllWorkoutsForZoneSenseSync("username")).thenReturn(
                listOf(
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.RUNNING.id,
                        zoneSenseAerobicBaseline = 0.10
                    ),
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.CYCLING.id,
                        zoneSenseAerobicBaseline = 0.20
                    ),
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.SWIMMING.id,
                        zoneSenseAerobicBaseline = 0.30
                    ),
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.CROSS_COUNTRY_SKIING.id,
                        zoneSenseAerobicBaseline = 0.40
                    ),
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.ROWING.id,
                        zoneSenseAerobicBaseline = 0.50
                    )
                )
            )

            val data = zoneSenseSyncDataProviderImpl.getZoneSenseSyncData()

            Truth.assertThat(data.baselineValues.running).isWithin(0.01f).of(0.10f)
            Truth.assertThat(data.baselineValues.cycling).isWithin(0.01f).of(0.20f)
            Truth.assertThat(data.baselineValues.swimming).isWithin(0.01f).of(0.30f)
            Truth.assertThat(data.baselineValues.skiing).isWithin(0.01f).of(0.40f)
            Truth.assertThat(data.baselineValues.rowing).isWithin(0.01f).of(0.50f)
        }

    @Test
    fun `all running group`() =
        runTest {
            `when`(workoutDataSource.getAllWorkoutsForZoneSenseSync("username")).thenReturn(
                listOf(
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.RUNNING.id,
                        zoneSenseAerobicBaseline = 0.10
                    ),
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.TRAIL_RUNNING.id,
                        zoneSenseAerobicBaseline = 0.20
                    ),
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.TREADMILL.id,
                        zoneSenseAerobicBaseline = 0.30
                    ),
                )
            )

            val data = zoneSenseSyncDataProviderImpl.getZoneSenseSyncData()

            Truth.assertThat(data.baselineValues.running).isEqualTo(0.16875f)
            Truth.assertThat(data.baselineValues.cycling).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.swimming).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.skiing).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.rowing).isEqualTo(-100f)
        }

    @Test
    fun `all cycling group`() =
        runTest {
            `when`(workoutDataSource.getAllWorkoutsForZoneSenseSync("username")).thenReturn(
                listOf(
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.CYCLING.id,
                        zoneSenseAerobicBaseline = 0.10
                    ),
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.INDOOR_CYCLING.id,
                        zoneSenseAerobicBaseline = 0.20
                    ),
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.GRAVEL_CYCLING.id,
                        zoneSenseAerobicBaseline = 0.30
                    ),
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.MOUNTAIN_BIKING.id,
                        zoneSenseAerobicBaseline = 0.40
                    ),
                )
            )

            val data = zoneSenseSyncDataProviderImpl.getZoneSenseSyncData()

            Truth.assertThat(data.baselineValues.running).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.cycling).isEqualTo(0.2265625f)
            Truth.assertThat(data.baselineValues.swimming).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.skiing).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.rowing).isEqualTo(-100f)
        }

    @Test
    fun `all swimming group`() =
        runTest {
            `when`(workoutDataSource.getAllWorkoutsForZoneSenseSync("username")).thenReturn(
                listOf(
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.SWIMMING.id,
                        zoneSenseAerobicBaseline = 0.10
                    ),
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.OPENWATER_SWIMMING.id,
                        zoneSenseAerobicBaseline = 0.20
                    ),
                )
            )

            val data = zoneSenseSyncDataProviderImpl.getZoneSenseSyncData()

            Truth.assertThat(data.baselineValues.running).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.cycling).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.swimming).isEqualTo(0.125f)
            Truth.assertThat(data.baselineValues.skiing).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.rowing).isEqualTo(-100f)
        }

    @Test
    fun `all skiing group`() =
        runTest {
            `when`(workoutDataSource.getAllWorkoutsForZoneSenseSync("username")).thenReturn(
                listOf(
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.CROSS_COUNTRY_SKIING.id,
                        zoneSenseAerobicBaseline = 0.10
                    ),
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.SKI_TOURING.id,
                        zoneSenseAerobicBaseline = 0.20
                    ),
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.ROLLER_SKIING.id,
                        zoneSenseAerobicBaseline = 0.30
                    ),
                )
            )

            val data = zoneSenseSyncDataProviderImpl.getZoneSenseSyncData()

            Truth.assertThat(data.baselineValues.running).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.cycling).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.swimming).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.skiing).isEqualTo(0.16875f)
            Truth.assertThat(data.baselineValues.rowing).isEqualTo(-100f)
        }

    @Test
    fun `all rowing group`() =
        runTest {
            `when`(workoutDataSource.getAllWorkoutsForZoneSenseSync("username")).thenReturn(
                listOf(
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.ROWING.id,
                        zoneSenseAerobicBaseline = 0.10
                    ),
                    ZoneSenseSyncWorkoutHeader(
                        activityTypeId = ActivityType.INDOOR_ROWING.id,
                        zoneSenseAerobicBaseline = 0.20
                    ),
                )
            )

            val data = zoneSenseSyncDataProviderImpl.getZoneSenseSyncData()

            Truth.assertThat(data.baselineValues.running).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.cycling).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.swimming).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.skiing).isEqualTo(-100f)
            Truth.assertThat(data.baselineValues.rowing).isEqualTo(0.125f)
        }
}
