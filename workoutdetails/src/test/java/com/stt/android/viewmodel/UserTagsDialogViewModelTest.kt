package com.stt.android.viewmodel

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import app.cash.turbine.test
import com.google.common.truth.Truth.assertThat
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.tags.UserTag
import com.stt.android.domain.tags.UserTagsRepository
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.tags.CheckAndGetSuuntoTagFromCustomTagNameUseCase
import com.stt.android.testutils.NewCoroutinesTestRule
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.whenever

@RunWith(MockitoJUnitRunner::class)
class UserTagsDialogViewModelTest {

    @Mock
    private lateinit var userTagsRepository: UserTagsRepository
    private lateinit var userTagsDialogViewModel: UserTagsDialogViewModel

    @get:Rule
    val coroutineRule = NewCoroutinesTestRule()

    private val coroutinesDispatchers = object : CoroutinesDispatchers {
        override val main = coroutineRule.testDispatcher
        override val io = coroutineRule.testDispatcher
        override val computation = coroutineRule.testDispatcher
    }

    @get:Rule
    var instantExecutorRule = InstantTaskExecutorRule()

    @Mock
    private lateinit var checkAndGetSuuntoTagFromCustomTagNameUseCase: CheckAndGetSuuntoTagFromCustomTagNameUseCase

    @Mock
    lateinit var isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase

    @Before
    fun before() = runTest {
        whenever(userTagsRepository.getAllUserTags())
            .thenReturn(
                listOf(
                    UserTag(id = 1, key = "key1", name = "name1"),
                    UserTag(id = 2, key = "key2", name = "name2"),
                    UserTag(id = 3, key = "key3", name = "thirdName"),
                    UserTag(id = 4, key = "key4", name = "fourthName"),
                    UserTag(id = 5, key = "key5", name = "Name3"),
                )
            )
        whenever(isSubscribedToPremiumUseCase.invoke())
            .thenReturn(flowOf(true))
        userTagsDialogViewModel = UserTagsDialogViewModel(
            userTagsRepository,
            checkAndGetSuuntoTagFromCustomTagNameUseCase,
            isSubscribedToPremiumUseCase,
            coroutinesDispatchers,
        )
        userTagsDialogViewModel.init(fakeWorkoutHeader)
    }

    @Test
    fun `init should setup viewed correctly`() = runBlocking {
        userTagsDialogViewModel.viewState.test {
            val item = awaitItem()
            assertThat(item.isLoading).isFalse()
            assertThat(item.selectedUserTags).containsExactlyElementsIn(fakeWorkoutHeader.userTags)
        }
    }

    @Test
    fun `given empty or blank user tag name, should not be accepted`() = runTest {
        userTagsDialogViewModel.insertNewUserTag("")
        userTagsDialogViewModel.insertNewUserTag("  ")

        userTagsDialogViewModel.viewState.test {
            // the workout has only one user tag by default
            assertThat(awaitItem().selectedUserTags.size).isEqualTo(1)
        }
    }

    @Test
    fun `given user tag name with starting spaces, starting spaces should be trimmed`() = runTest {
        userTagsDialogViewModel.onFilterChanged("    a")

        userTagsDialogViewModel.viewState.test {
            assertThat(awaitItem().filterValue).isEqualTo("a")
        }
    }

    @Test
    fun `given user tag name with ending spaces, ending spaces should be trimmed`() = runTest {
        userTagsDialogViewModel.insertNewUserTag("a     ")

        userTagsDialogViewModel.viewState.test {
            assertThat(awaitItem().selectedUserTags).containsExactlyElementsIn(
                fakeWorkoutHeader.userTags.plus(
                    UserTag.empty("a")
                )
            )
        }
    }

    @Test
    fun `given user tag with same name as a suunto tag, then the suunto tag should be selected`() = runTest {
        whenever(checkAndGetSuuntoTagFromCustomTagNameUseCase("COMMute")).thenReturn(SuuntoTag.COMMUTE)
        userTagsDialogViewModel.insertNewUserTag("COMMute")

        userTagsDialogViewModel.viewState.test {
            val item = awaitItem()
            assertThat(item.selectedSuuntoTags()).containsExactlyElementsIn(
                fakeWorkoutHeader.suuntoTags.plus(SuuntoTag.COMMUTE)
            )
            assertThat(item.selectedUserTags).doesNotContain("COMMute")
        }
    }

    @Test
    fun `given a random text different than Suunto's tags, then it should be added as user tag`() = runTest {
        userTagsDialogViewModel.insertNewUserTag("NotACommute")

        userTagsDialogViewModel.viewState.test {
            val item = awaitItem()
            assertThat(item.selectedSuuntoTags()).containsExactlyElementsIn(fakeWorkoutHeader.suuntoTags)
            assertThat(item.selectedUserTags).containsExactlyElementsIn(
                fakeWorkoutHeader.userTags.plus(UserTag.empty("NotACommute"))
            )
        }
    }

    @Test
    fun `suggested user tags should be sorted by name`() = runTest {
        userTagsDialogViewModel.viewState.test {
            assertThat(awaitItem().suggestedUserTags)
                .containsExactly(
                    UserTag(id = 4, key = "key4", name = "fourthName"),
                    UserTag(id = 2, key = "key2", name = "name2"),
                    UserTag(id = 5, key = "key5", name = "Name3"),
                    UserTag(id = 3, key = "key3", name = "thirdName")
                ).inOrder()
        }
    }

    companion object {
        val fakeWorkoutHeader = WorkoutHeader(
            id = 0,
            key = "test1",
            totalDistance = 900.0,
            maxSpeed = 20.0,
            activityTypeId = 2,
            avgSpeed = 0.0,
            description = "",
            startPosition = null,
            stopPosition = null,
            centerPosition = null,
            startTime = 1575206596000, // 1.12.2019 13:23:16
            stopTime = 110,
            totalTime = 3600.0,
            energyConsumption = 50.0,
            username = "test",
            heartRateAverage = 0.0,
            heartRateAvgPercentage = 0.0,
            heartRateMax = 0.0,
            heartRateMaxPercentage = 0.0,
            heartRateUserSetMax = 0.0,
            averageCadence = 0,
            maxCadence = 0,
            pictureCount = 0,
            viewCount = 0,
            commentCount = 0,
            sharingFlags = 0,
            stepCount = 100,
            polyline = null,
            manuallyAdded = false,
            reactionCount = 0,
            totalAscent = 50.5,
            totalDescent = 10.2,
            recoveryTime = 200,
            locallyChanged = false,
            deleted = false,
            seen = true,
            maxAltitude = 20.0,
            minAltitude = 15.0,
            extensionsFetched = false,
            suuntoTags = listOf(),
            userTags = listOf(
                UserTag(id = 1, key = "key1", name = "name1")
            )
        )
    }
}
